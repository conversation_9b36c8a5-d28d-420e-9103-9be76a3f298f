import { AbstractFilterInterface } from "@/common/interface";
import { ProjectOutletInterface } from "@/routes/project/outlet/interface";
import { useApp } from "@/UseApp";
import { useQuery } from "@tanstack/react-query";
import { DashboardFilterInterface } from "../../interface";

export const useOosQuery = (
  projectId: number,
  dashboardId: number,
  filter?: DashboardFilterInterface & AbstractFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["oos", projectId, dashboardId, filter],
    queryFn: () =>
      axiosGet<{ entities: ProjectOutletInterface[]; count: number }, unknown>(
        `/projects/${projectId}/dashboards/${dashboardId}/charts/oos`,
        filter,
      ),
  });
};

export const useGroupedFeatureOosProductsQuery = (
  projectId: number,
  id: number,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["groupedFeatureOosProducts", projectId, id],
    queryFn: () =>
      axiosGet<
        {
          code: string;
          id: number;
          name: string;
          projectProductId: number;
          shortName: string;
        }[],
        unknown
      >(`/projects/${projectId}/dashboards/${id}/grouped-feature-oos-products`),
  });
};

export const useGroupedFeatureOosMergedProductsQuery = (
  projectId: number,
  id: number,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["groupedFeatureOosMergedProducts", projectId, id],
    queryFn: () =>
      axiosGet<
        {
          code: string;
          id: number;
          productCode: string;
          productName: string;
          productShortName: string;
        }[],
        unknown
      >(
        `/projects/${projectId}/dashboards/${id}/grouped-feature-oos-merged-products`,
      ),
  });
};
