import { DEFAULT_CURRENT_PAGE } from "@/common/constant";
import { Form } from "antd";
import dayjs from "dayjs";
import _ from "lodash";
import { useCallback, useEffect, useState } from "react";
import { AdvancedFilterFormValueInterface, AdvancedFilterInterface } from "../../../interface";
import { useAdvancedFilterFiledsStore } from "../../../state";
import { ADVANCED_FILTER_FIELDS } from "../constants";

/**
 * Custom hook for managing report filters
 */
export const useReportFilters = (
  advancedFilterValues: AdvancedFilterFormValueInterface,
  onFilterChange?: () => void
) => {
  const [filterForm] = Form.useForm();
  const [filter, setFilter] = useState<AdvancedFilterInterface>({
    attendanceStartDate: dayjs().startOf("date").toDate(),
    attendanceEndDate: dayjs().endOf("date").toDate(),
  });

  const { setFileds: setAdvancedFilterFileds } = useAdvancedFilterFiledsStore();

  const setFilterForQuery = useCallback(
    (values: AdvancedFilterFormValueInterface) => {
      if (_.isEqual(filter, values)) {
        onFilterChange?.();
      }
      setFilter(values);
    },
    [filter, onFilterChange]
  );

  const onFilterFormFinish = useCallback(() => {
    const values = filterForm.getFieldsValue();

    if (values.attendance) {
      const [attendanceStartDate, attendanceEndDate] = values.attendance;
      values.attendanceStartDate = attendanceStartDate
        ? dayjs(attendanceStartDate).startOf("date").toDate()
        : undefined;
      values.attendanceEndDate = attendanceEndDate
        ? dayjs(attendanceEndDate).endOf("date").toDate()
        : undefined;

      delete values.attendance;
    }
    setFilterForQuery(values);
  }, [filterForm, setFilterForQuery]);

  // Set advanced filter fields on mount
  useEffect(() => {
    setAdvancedFilterFileds(ADVANCED_FILTER_FIELDS);
  }, [setAdvancedFilterFileds]);

  // Update filter when advanced filter values change
  useEffect(() => {
    setFilterForQuery(advancedFilterValues);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [advancedFilterValues]);

  return {
    filterForm,
    filter,
    onFilterFormFinish,
    setFilterForQuery,
  };
};
