import { useMutation, useQuery } from "@tanstack/react-query";
import { AbstractFilterInterface } from "@/common/interface.ts";
import { useApp } from "@/UseApp.tsx";
import { UnitInterface } from "./UnitPage.tsx";

export const useUnitsQuery = (
  filter?: AbstractFilterInterface & {
    clientId?: number;
    getInActive?: boolean;
  },
  enabled?: boolean,
) => {
  const { axiosGet } = useApp();
  const { getInActive, ...restFilter } = filter ?? {};

  const queryFilter = {
    ...(getInActive ? {} : { isActive: true }),
    ...restFilter,
  };

  return useQuery({
    queryKey: ["units", filter],
    queryFn: async () =>
      axiosGet<
        {
          entities: UnitInterface[];
          count: number;
        },
        unknown
      >("/units", queryFilter),
    enabled,
  });
};

export const useDeleteUnitMutation = () => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deleteUnit"],
    mutationFn: async (id: number) => axiosDelete(`/units/${id}`),
  });
};

export const useCreateUnitMutation = () => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createUnit"],
    mutationFn: async (data: {
      clientId: number;
      name: string;
      description?: string;
    }) => axiosPost("/units", data),
  });
};

export const useUpdateUnitMutation = () => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["updateUnit"],
    mutationFn: async ({
      id,
      data,
    }: {
      id: number;
      data: {
        name?: string;
        description?: string;
        isActive?: boolean;
      };
    }) => axiosPatch(`/units/${id}`, data),
  });
};
