import {
  CHUNK_SIZE,
  DATETIME_FORMAT,
  DEFAULT_CURRENT_PAGE,
  DEFAULT_PAGE_SIZE,
} from "@/common/constant";
import { createExcelFile, downloadFile } from "@/common/export-excel.helper";
import { removeVietnameseTones } from "@/common/helper";
import { FeatureUrgencyInterface } from "@/routes/project/component/feature/config/types/urgency/interface";
import getColumnsTableReport from "@project/report/ColumnsTableReport";
import FilterReportZone from "@project/report/FilterReportZone";
import {
  AdvancedFilterFormValueInterface,
  AdvancedFilterInterface,
} from "@project/report/interface";
import { useAdvancedFilterFiledsStore } from "@project/report/state.ts";
import { useProjectReportOutletContext } from "@project/report/UseProjectReportOutletContext.tsx";
import { Form, Table } from "antd";
import dayjs from "dayjs";
import _ from "lodash";
import { useCallback, useEffect, useMemo, useState } from "react";
import {
  useGetReportUrgenciesMutation,
  useReportUrgenciesQuery,
} from "./service";
import UrgencyTimer from "./UrgencyTimer";

const ReportUrgencyPage = () => {
  const {
    componentFeatureQuery,
    projectId,
    componentFeatureId,
    advancedFilterValues,
    project,
  } = useProjectReportOutletContext();

  const { setFileds: setAdvancedFilterFileds } = useAdvancedFilterFiledsStore();

  const [filterForm] = Form.useForm();
  const [filter, setFilter] = useState<AdvancedFilterInterface>({
    attendanceStartDate: dayjs().startOf("date").toDate(),
    attendanceEndDate: dayjs().endOf("date").toDate(),
  });
  const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [isExport, setIsExport] = useState(false);

  const reportUrgenciesQuery = useReportUrgenciesQuery(
    projectId,
    componentFeatureId,
    {
      ...filter,
      take: pageSize,
      skip: (currentPage - 1) * pageSize,
    },
  );

  const getReportUrgenciesMutation = useGetReportUrgenciesMutation(
    projectId,
    componentFeatureId,
  );

  const pagination = useMemo(() => {
    return {
      current: currentPage,
      total: reportUrgenciesQuery.data?.count,
      pageSize: pageSize,
      onChange: (page: number) => {
        setCurrentPage(page);
      },
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: (_current: number, size: number) => {
        setPageSize(size);
        setCurrentPage(DEFAULT_CURRENT_PAGE);
      },
    };
  }, [currentPage, pageSize, reportUrgenciesQuery.data?.count]);

  const setFilterForQuery = useCallback(
    async (values: AdvancedFilterFormValueInterface) => {
      setCurrentPage(DEFAULT_CURRENT_PAGE);
      if (_.isEqual(filter, values)) {
        await reportUrgenciesQuery.refetch();
      }
      setFilter(values);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [filter],
  );

  const onFilterFormFinish = useCallback(() => {
    const values = filterForm.getFieldsValue();

    if (values.attendance) {
      const [attendanceStartDate, attendanceEndDate] = values.attendance;
      values.attendanceStartDate = attendanceStartDate
        ? dayjs(attendanceStartDate).startOf("date").toDate()
        : undefined;
      values.attendanceEndDate = attendanceEndDate
        ? dayjs(attendanceEndDate).endOf("date").toDate()
        : undefined;

      delete values.attendance;
    }
    setFilterForQuery(values);
  }, [filterForm, setFilterForQuery]);

  useEffect(() => {
    setFilterForQuery(advancedFilterValues);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [advancedFilterValues]);

  useEffect(() => {
    setAdvancedFilterFileds([
      "Agency phụ trách",
      "Role ghi nhận",
      "Ngày chấm công",
      "Nhân viên ghi nhận",
      "Thông tin khách",
      "Mã/ Tên outlet",
      "Tỉnh/ TP",
      "Quận/ Huyện",
      "Kênh",
      "Nhóm",
      "Loại booth",
      "Trưởng nhóm quản lý",
    ]);
  }, [setAdvancedFilterFileds]);

  useEffect(() => {
    filterForm.resetFields();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location.pathname]);

  const onExport = useCallback(async () => {
    const total = pagination.total!;

    if (total === 0) {
      return;
    }

    const fetchAllData = async () => {
      const requests = [];

      for (let skip = 0; skip < total; skip += CHUNK_SIZE) {
        requests.push(
          getReportUrgenciesMutation.mutateAsync({
            take: CHUNK_SIZE,
            skip: skip,
            ...filter,
          }),
        );
      }

      const results = await Promise.all(requests);
      const allEntities = results.flatMap((result) => result.entities);

      return allEntities;
    };

    setIsExport(true);

    try {
      const allData = await fetchAllData();

      const data = allData.map((recordUrgency) => {
        const { featureUrgency, projectRecordFeature, note, startAt, endAt } =
          recordUrgency;
        const { projectRecordEmployee, projectRecord, attendance } =
          projectRecordFeature;
        const { projectOutlet, projectBooth, projectAgency, leader } =
          projectRecord;
        const { in: attendanceIn, out: attendanceOut } = attendance;

        let isMoreThanMaxDuration = false;

        if (endAt) {
          const duration = dayjs(endAt).diff(dayjs(startAt), "seconds");
          isMoreThanMaxDuration = featureUrgency.maxDuration
            ? duration > featureUrgency.maxDuration * 60
            : false;
        } else {
          const duration = dayjs().diff(dayjs(startAt), "seconds");
          isMoreThanMaxDuration = featureUrgency.maxDuration
            ? duration > featureUrgency.maxDuration * 60
            : false;
        }

        return [
          project?.id,
          project?.name,
          projectOutlet.code,
          projectOutlet.name,
          projectBooth.name,
          dayjs(attendanceIn.deviceTime).add(7, "hour").toDate(),
          attendanceOut?.deviceTime
            ? dayjs(attendanceOut?.deviceTime).add(7, "hour").toDate()
            : "",
          projectOutlet.province?.name,
          projectOutlet.district?.name,
          projectOutlet.projectAgencyChannel.channel.name,
          projectOutlet.subChannel?.name,
          projectAgency.agency.name,
          projectRecordEmployee.employee.role.name,
          projectRecordEmployee.employee.user.id,
          projectRecordEmployee.employee.user.name,
          leader.id,
          leader.user.name,
          featureUrgency.name,
          note,
          dayjs(startAt).add(7, "hour").toDate(),
          endAt ? dayjs(endAt).add(7, "hour").toDate() : "",
          dayjs()
            .set("hour", 7)
            .set("minute", 0)
            .set("second", 0)
            .set("millisecond", 0)
            .add(featureUrgency.maxDuration, "minute")
            .toDate(),
          "",
          endAt ? "Đã kết thúc" : "Đang diễn ra",
          isMoreThanMaxDuration ? "Quá giờ" : "",
        ];
      });

      const headers = [
        "ID dự án",
        "Tên dự án",
        "Mã outlet",
        "Tên outlet",
        "Loại booth",
        "Thời gian chấm công vào",
        "Thời gian chấm công ra",
        "Tỉnh/ TP",
        "Quận/ Huyện",
        "Kênh",
        "Nhóm",
        "Agency phụ trách",
        "Role nhân viên chấm công",
        "ID nhân viên chấm công",
        "Họ tên nhân viên chấm công",
        "ID trưởng nhóm quản lý",
        "Họ tên trưởng nhóm quản lý",
        "Lý do rời vị trí",
        "Chi tiết lí do",
        "Thời gian bắt đầu",
        "Thời gian kết thúc",
        "Thời gian tiêu chuẩn",
        "Thời gian rời vị trí",
        "Tình trạng rời vị trí",
        "Quá giờ",
      ];

      const fileName = removeVietnameseTones(
        componentFeatureQuery.data?.name ?? "",
      );
      const workbook = await createExcelFile({
        data,
        headers,
        dateTimeColumns: [6, 7, 20, 21],
      });

      for (let i = 1; i <= data.length; i++) {
        const index = i + 1;
        if (data[i - 1][20]) {
          const cell = workbook?.getWorksheet(1)?.getCell(`W${index}`);
          if (cell) {
            cell.value = {
              formula: `U${index} - T${index}`,
            };
            cell.numFmt = "hh:mm:ss";
          }
        }

        {
          const cell = workbook?.getWorksheet(1)?.getCell(`V${index}`);
          if (cell) {
            cell.numFmt = "hh:mm:ss";
          }
        }
      }

      downloadFile(workbook, `${fileName} ${dayjs().format("DDMMYY")}.xlsx`);
    } catch (e) {
      console.error(e);
    } finally {
      setIsExport(false);
    }
  }, [
    componentFeatureQuery.data?.name,
    filter,
    getReportUrgenciesMutation,
    pagination.total,
    project?.id,
    project?.name,
  ]);

  return (
    <div>
      <h2>{componentFeatureQuery.data?.name}</h2>
      <div className="bg-white p-10 rounded">
        <FilterReportZone
          form={filterForm}
          loading={
            reportUrgenciesQuery.isFetching ||
            reportUrgenciesQuery.isLoading ||
            isExport
          }
          fields={["keyword", "roleId", "urgencies.status", "attendance"]}
          onFinish={onFilterFormFinish}
          onExport={onExport}
        />

        <Table
          rowKey={"id"}
          pagination={pagination}
          scroll={{
            x: "max-content",
            y: pagination.total ? "80vh" : undefined,
          }}
          className="mt-3"
          dataSource={reportUrgenciesQuery.data?.entities.map((item) => ({
            id: item.id,
            projectOutlet:
              item.projectRecordFeature.projectRecord.projectOutlet,
            projectBooth: item.projectRecordFeature.projectRecord.projectBooth,
            projectRecordEmployee:
              item.projectRecordFeature.projectRecordEmployee,
            attendanceIn: item.projectRecordFeature.attendance.in ?? undefined,
            attendanceOut:
              item.projectRecordFeature.attendance.out ?? undefined,
            projectAgency:
              item.projectRecordFeature.projectRecord.projectAgency,
            leader: item.projectRecordFeature.projectRecord.leader,
            featureUrgency: item.featureUrgency,
            startAt: item.startAt,
            endAt: item.endAt,
            note: item.note,
          }))}
          columns={[
            ...getColumnsTableReport([
              { tableColumn: "outletCode" },
              { tableColumn: "outletName" },
              { tableColumn: "boothName" },
              { tableColumn: "attendance" },
              { tableColumn: "dataTimestamp" },
              { tableColumn: "address" },
              { tableColumn: "channelName" },
              { tableColumn: "subChannelName" },
              { tableColumn: "agencyName" },
              { tableColumn: "teamLeader" },
              { tableColumn: "recordEmployee", fixed: "right" },
            ]),
            {
              title: "Lý do rời vị trí",
              className: "min-w-[100px] max-w-[250px]",
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              render: (_value: any, record: any) => {
                const {
                  featureUrgency,
                  note,
                }: {
                  featureUrgency: FeatureUrgencyInterface;
                  note: string | null;
                } = record;

                return (
                  <>
                    <p className="mb-0 font-medium">{featureUrgency.name}</p>
                    <p className="mt-0 text-hint font-normal">{note}</p>
                  </>
                );
              },
              fixed: "right",
            },
            {
              title: "Thời gian bắt đầu",
              className: "min-w-[100px]",
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              render: (_value: any, record: any) => {
                const { startAt }: { startAt: string } = record;

                return dayjs(startAt).format(DATETIME_FORMAT);
              },
              fixed: "right",
            },
            {
              title: "Thời gian kết thúc",
              className: "min-w-[100px]",
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              render: (_value: any, record: any) => {
                const { endAt }: { endAt: string } = record;
                if (endAt) return dayjs(endAt).format(DATETIME_FORMAT);
                return "-";
              },
              fixed: "right",
            },
            {
              title: "Thời gian rời vị trí",
              className: "min-w-[200px]",
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              render: (_value: any, record: any) => {
                const {
                  startAt,
                  endAt,
                  featureUrgency,
                }: {
                  startAt: string;
                  endAt: string;
                  featureUrgency: FeatureUrgencyInterface;
                } = record;

                return (
                  <UrgencyTimer
                    startAt={startAt}
                    endAt={endAt}
                    featureUrgency={featureUrgency}
                    key={record.id}
                  />
                );
              },
              fixed: "right",
            },
          ]}
        />
      </div>
    </div>
  );
};
export default ReportUrgencyPage;
