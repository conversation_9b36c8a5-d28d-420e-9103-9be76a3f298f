import { findObjectDifferences } from "@/common/helper.ts";
import { getImageVariants } from "@/common/image.helper.ts";
import { FormPhotoInterface } from "@/common/interface.ts";
import ImagesGrid from "@/components/ImagesGrid.tsx";
import renderStatus from "@/components/renderStatus.tsx";
import { useApp } from "@/UseApp.tsx";
import { CloseOutlined, LeftOutlined } from "@ant-design/icons";
import { Button, Form, Modal, Space, Table } from "antd";
import TextArea from "antd/es/input/TextArea";
import _ from "lodash";
import { useCallback, useEffect, useMemo, useState } from "react";
import { ProfileInterface } from "../employee/interface.ts";
import { ProfileRequestInterface } from "./interface.ts";
import {
  useApproveUserProfilesMutation,
  useRejectUserProfilesMutation,
  useUserProfileLinkedQuery,
  useUserProfileQuery,
} from "./service.ts";

const DATA_KEYS = [
  {
    key: "fullName",
    label: "Họ và tên",
  },
  {
    key: "identityCardNumber",
    label: "Số CMND/CCCD",
  },
  {
    key: "phoneNumber",
    label: "Số điện thoại",
  },
  {
    key: "email",
    label: "Email",
  },
  {
    key: "birthdate",
    label: "Ngày sinh",
  },
  {
    key: "birthplace",
    label: "Nơi sinh",
  },
  {
    key: "socialInsuranceNumber",
    label: "Số BHXH",
  },
  {
    key: "personalTaxCode",
    label: "Mã số thuế",
  },
  {
    key: "permanentAddress",
    label: "Địa chỉ thường trú",
  },
  {
    key: "shirtSize",
    label: "Cỡ áo",
  },
  {
    key: "pantsSize",
    label: "Cỡ quần",
  },
  {
    key: "dressSize",
    label: "Cỡ đầm",
  },
  {
    key: "shoeSize",
    label: "Cỡ giày",
  },
  {
    key: "gender",
    label: "Giới tính",
  },
];

const DATA_KEYS_PHOTO = [
  {
    key: "PORTRAIT",
    label: "Ảnh chân dung",
  },
  {
    key: "FULLBODY",
    label: "Ảnh toàn thân",
  },
  {
    key: "IDFRONT",
    label: "Ảnh CMND/CCCD mặt trước",
  },
  {
    key: "IDBACK",
    label: "Ảnh CMND/CCCD mặt sau",
  },
  {
    key: "CV",
    label: "Ảnh sơ yếu lý lịch",
  },
];

const DATA_KEYS_ORDER = [
  "fullName",
  "identityCardNumber",
  "phoneNumber",
  "email",
  "PORTRAIT",
  "gender",
  "birthdate",
  "birthplace",
  "socialInsuranceNumber",
  "personalTaxCode",
  "permanentAddress",
  "shirtSize",
  "pantsSize",
  "dressSize",
  "shoeSize",
  "FULLBODY",
  "IDFRONT",
  "IDBACK",
  "CV",
];

interface ProjectProfileModalProps {
  isOpen: boolean;
  profileRequest?: ProfileRequestInterface;
  projectId: number;
  onCloseCb?: () => void;
  setIsOpen: (isOpen: boolean) => void;
}

const ProjectProfileModal = ({
  isOpen,
  profileRequest = undefined,
  projectId,
  onCloseCb,
  setIsOpen,
}: ProjectProfileModalProps) => {
  const { showNotification } = useApp();

  const [dataSources, setDataSources] = useState<
    {
      id: string;
      label: string | undefined;
      value: FormPhotoInterface[] | undefined;
      linkedValue: FormPhotoInterface[] | undefined;
    }[]
  >([]);
  const [isApproving, setIsApproving] = useState(false);
  const [isRejecting, setIsRejecting] = useState(false);
  const [rejectForm] = Form.useForm();

  const userProfileQuery = useUserProfileQuery(projectId, profileRequest?.id);
  const userProfileLinkedQuery = useUserProfileLinkedQuery(
    projectId,
    profileRequest?.id,
  );

  const approveUserProfileMutation = useApproveUserProfilesMutation(projectId);
  const rejectUserProfilesMutation = useRejectUserProfilesMutation(projectId);

  const loading = useMemo(
    () =>
      approveUserProfileMutation.isPending ||
      rejectUserProfilesMutation.isPending,
    [
      approveUserProfileMutation.isPending,
      rejectUserProfilesMutation.isPending,
    ],
  );

  useEffect(() => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const emptyObject: Record<string, any> = {};
    DATA_KEYS.forEach((item) => {
      emptyObject[item.key] = null;
    });

    const getAddressString = (profile?: ProfileInterface) => {
      const arr = [];
      if (profile?.permanentWard)
        arr.push(`${profile?.address ?? ""} ${profile.permanentWard.name}`);
      if (profile?.permanentDistrict) arr.push(profile.permanentDistrict.name);
      if (profile?.permanentProvince) arr.push(profile.permanentProvince.name);

      return arr.join(", ");
    };

    const differences = findObjectDifferences(
      {
        ...userProfileQuery.data,
        permanentAddress: getAddressString(userProfileQuery.data),
      },
      _.isEmpty(userProfileLinkedQuery.data)
        ? emptyObject
        : {
            ...userProfileLinkedQuery.data,
            permanentAddress: getAddressString(userProfileLinkedQuery.data),
          },
      DATA_KEYS.map((dataKey) => dataKey.key),
    );

    const groupedProfilePhotos = _.groupBy(
      userProfileQuery.data?.userProfilePhotos?.map((userProfilePhoto) => ({
        type: userProfilePhoto.type,
        id: userProfilePhoto.image.id,
      })),
      (o) => o.type,
    );

    const groupedProfilePhotosLinked = _.groupBy(
      userProfileLinkedQuery.data?.userProfilePhotos?.map(
        (userProfilePhoto) => ({
          type: userProfilePhoto.type,
          id: userProfilePhoto.image.id,
        }),
      ),
      (o) => o.type,
    );

    const differencesWithUserProfilePhotos = findObjectDifferences(
      groupedProfilePhotos,
      groupedProfilePhotosLinked,
    );

    const differencesPhotosType = [];

    for (const item of differencesWithUserProfilePhotos) {
      differencesPhotosType.push(item.key.split(".")[0]);
    }

    const data = [];

    for (const photoType of _.uniq(differencesPhotosType)) {
      data.push({
        id: photoType,
        label: DATA_KEYS_PHOTO.find((dataKey) => dataKey.key === photoType)
          ?.label,
        value: userProfileQuery.data?.userProfilePhotos?.filter(
          (photo) => photo.type === photoType,
        ),
        linkedValue: userProfileLinkedQuery.data?.userProfilePhotos?.filter(
          (photo) => photo.type === photoType,
        ),
      });
    }

    differences
      .map((item) => ({
        id: item.key,
        label: DATA_KEYS.find((dataKey) => dataKey.key === item.key)?.label,
        value: item.valueInObj1,
        linkedValue: item.valueInObj2,
      }))
      .forEach((item) => data.push(item));

    const dataSource = _.orderBy(data, (o) => DATA_KEYS_ORDER.indexOf(o.id));
    setDataSources(dataSource);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userProfileLinkedQuery.data, userProfileQuery.data]);

  const onClose = useCallback(() => {
    onCloseCb?.();
  }, [onCloseCb]);

  const onApprove = useCallback(() => {
    setIsApproving(true);
    setIsOpen(false);
  }, [setIsOpen]);

  const onReject = useCallback(() => {
    setIsRejecting(true);
    setIsOpen(false);
  }, [setIsOpen]);

  const onApproveConfirm = useCallback(async () => {
    if (!profileRequest?.id) return;

    await approveUserProfileMutation.mutateAsync({
      ids: [profileRequest.id],
    });

    showNotification({
      type: "success",
      message: "Duyệt thành công",
    });
    onClose();
  }, [
    approveUserProfileMutation,
    onClose,
    profileRequest?.id,
    showNotification,
  ]);

  const onRejectConfirm = useCallback(async () => {
    if (!profileRequest?.id) return;

    await rejectForm.validateFields();
    await rejectUserProfilesMutation.mutateAsync({
      ids: [profileRequest?.id],
      reason: rejectForm.getFieldValue("reason"),
    });
    showNotification({
      type: "success",
      message: `Từ chối yêu cầu thay đổi thông tin thành công`,
    });

    onClose();
  }, [
    onClose,
    profileRequest?.id,
    rejectForm,
    rejectUserProfilesMutation,
    showNotification,
  ]);

  const onBack = useCallback(() => {
    setIsRejecting(false);
    setIsApproving(false);
    setIsOpen(true);
  }, [setIsOpen]);

  return (
    <>
      <Modal
        open={isOpen}
        footer={null}
        closeIcon={null}
        styles={{ content: { padding: 0 } }}
        width={900}
      >
        <div className="pl-10 pr-10 pt-3 pb-[29px]">
          <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
            <h2 className="text-2xl font-semibold">
              Yêu cầu thay đổi profile nhân viên
              {profileRequest?.status === "PENDING" &&
                renderStatus(
                  "Chờ xét duyệt",
                  "#FFFFFF",
                  "#8C8C8D",
                  "rounded-[16px] ml-3 text-[12px]",
                )}
              {profileRequest?.status === "APPROVED" &&
                renderStatus(
                  "Đã duyệt",
                  "#FFFFFF",
                  "#008916",
                  "rounded-[16px] ml-3 text-[12px]",
                )}
              {profileRequest?.status === "REJECTED" &&
                renderStatus(
                  "Từ chối",
                  "#FFFFFF",
                  "#F92525",
                  "rounded-[16px] ml-3 text-[12px]",
                )}
            </h2>
            <div className="pt-5">
              <Button
                type="text"
                onClick={onClose}
                size="large"
                icon={<CloseOutlined />}
                className={"text-[#393939]"}
                loading={loading}
              />
            </div>
          </div>
          <p>
            Phía dưới là các thông tin bắt buộc và quan trọng mà nhân viên đã
            thay đổi
          </p>
          <Table
            scroll={{
              y: 600,
            }}
            dataSource={dataSources}
            rowKey={(o) => o.id}
            columns={[
              {
                title: "Thông tin",
                dataIndex: "label",
              },
              {
                title: "Thông tin cũ",
                dataIndex: "linkedValue",
                render: (value, record) => {
                  if (typeof value === "string") {
                    if (record.id === "gender") {
                      if (value === "MALE") return "Nam";
                      if (value === "FEMALE") return "Nữ";
                    }
                    return value;
                  }
                  if (typeof value === "number") return value;
                  if (Array.isArray(value)) {
                    return (
                      <ImagesGrid
                        images={value.map((item) => ({
                          preview: getImageVariants(
                            item?.image?.variants ?? [],
                            "public",
                          ),
                          thumbnail: getImageVariants(
                            item?.image?.variants ?? [],
                            "thumbnail",
                          ),
                        }))}
                        maxImagesPerRow={2}
                        height={96}
                        width={96}
                      />
                    );
                  }

                  return "";
                },
              },
              {
                title: "Thông tin mới",
                dataIndex: "value",
                render: (value, record) => {
                  if (typeof value === "string") {
                    if (record.id === "gender") {
                      if (value === "MALE") return "Nam";
                      if (value === "FEMALE") return "Nữ";
                    }
                    return value;
                  }
                  if (typeof value === "number") return value;
                  if (Array.isArray(value)) {
                    return (
                      <ImagesGrid
                        images={value.map((item) => ({
                          preview: getImageVariants(
                            item?.image?.variants ?? [],
                            "public",
                          ),
                          thumbnail: getImageVariants(
                            item?.image?.variants ?? [],
                            "thumbnail",
                          ),
                        }))}
                        maxImagesPerRow={2}
                        width={96}
                        height={96}
                      />
                    );
                  }
                  return "";
                },
              },
            ]}
            pagination={false}
          />
        </div>

        <div className="flex justify-end gap-4 py-4 rounded max-md:max-w-full max-md:flex-wrap pl-10 bg-[#F7F8FA] pr-10 pb-4">
          {userProfileQuery.data?.status === "PENDING" && (
            <>
              <Button htmlType="button" loading={loading} onClick={onReject}>
                Từ chối thay đổi
              </Button>
              <Button
                htmlType="submit"
                type={"primary"}
                loading={loading}
                onClick={onApprove}
              >
                Đồng ý thay đổi
              </Button>
            </>
          )}

          {userProfileQuery.data?.status !== "PENDING" && (
            <Button htmlType="button" loading={loading} onClick={onClose}>
              Đóng
            </Button>
          )}
        </div>
      </Modal>

      <Modal
        open={isApproving}
        footer={null}
        closeIcon={null}
        styles={{ content: { padding: 0 } }}
      >
        <div className="pl-10 pr-10 pt-3 pb-[29px]">
          <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
            <h2 className="text-2xl font-semibold">
              Duyệt yêu cầu thay đổi thông tin
            </h2>
            <div className="pt-5">
              <Button
                type="text"
                onClick={onClose}
                size="large"
                icon={<CloseOutlined />}
                className={"text-[#393939]"}
                loading={loading}
              />
            </div>
          </div>
          <p>
            Profile của nhân viên{" "}
            <span className={"font-semibold"}>
              {userProfileQuery.data?.fullName}
            </span>{" "}
            sẽ được thay đổi trên hệ thống khi bạn nhấn nút xác nhận. Bạn vẫn
            muốn tiến hành chứ?
          </p>
        </div>
        <div className="flex justify-between gap-4 py-4 rounded max-md:max-w-full max-md:flex-wrap pl-10 bg-[#F7F8FA] pr-10 pb-4">
          <Button
            htmlType="button"
            loading={loading}
            onClick={onBack}
            icon={<LeftOutlined />}
          >
            Quay lại
          </Button>

          <Space>
            <Button htmlType="button" loading={loading} onClick={onClose}>
              Đóng
            </Button>
            <Button
              htmlType="submit"
              type={"primary"}
              loading={loading}
              onClick={onApproveConfirm}
            >
              Xác nhận
            </Button>
          </Space>
        </div>
      </Modal>

      <Modal
        open={isRejecting}
        footer={null}
        closeIcon={null}
        styles={{ content: { padding: 0 } }}
      >
        <div className="pl-10 pr-10 pt-3 pb-[29px]">
          <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
            <h2 className="text-2xl font-semibold">
              <span className={"text-primary"}>
                Từ chối yêu cầu thay đổi thông tin
              </span>
            </h2>
            <div className="pt-5">
              <Button
                type="text"
                onClick={onClose}
                size="large"
                icon={<CloseOutlined />}
                className={"text-[#393939]"}
                loading={loading}
              />
            </div>
          </div>
          <Form layout={"vertical"} form={rejectForm}>
            <Form.Item
              name={"reason"}
              label={"Lý do từ chối"}
              rules={[{ required: true }]}
            >
              <TextArea showCount maxLength={256} />
            </Form.Item>
          </Form>
        </div>
        <div className="flex justify-between gap-4 py-4 rounded max-md:max-w-full max-md:flex-wrap pl-10 bg-[#F7F8FA] pr-10 pb-4">
          <Button
            htmlType="button"
            loading={loading}
            onClick={onBack}
            icon={<LeftOutlined />}
          >
            Quay lại
          </Button>

          <Space>
            <Button htmlType="button" loading={loading} onClick={onClose}>
              Đóng
            </Button>
            <Button
              htmlType="submit"
              type={"primary"}
              loading={loading}
              onClick={onRejectConfirm}
            >
              Xác nhận
            </Button>
          </Space>
        </div>
      </Modal>
    </>
  );
};

export default ProjectProfileModal;
