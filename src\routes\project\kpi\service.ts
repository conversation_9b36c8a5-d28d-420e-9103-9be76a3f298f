import { useApp } from "@/UseApp";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  ItemKpiTypeEnum,
  KPIInterface,
  KPIScopeEnum,
  PeriodTypeEnum,
} from "./interface";

export const useProjectKPIQuery = (
  projectId: number,
  scope?: KPIScopeEnum,
  periodType?: PeriodTypeEnum,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["projectKPI", projectId, scope, periodType],
    queryFn: () =>
      axiosGet<KPIInterface[], unknown>(`/projects/${projectId}/kpis`, {
        scope,
        periodType,
      }),
  });
};

export const useCreateProjectKPIMutation = (projectId: number) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createProjectKPI", projectId],
    mutationFn: (data: {
      hit?: number | null;
      order?: number | null;
      session?: number | null;
      salesRevenue?: number | null;
      salesVolume?: number | null;
      projectAgencyId?: number | null;
      provinceId?: number | null;
      channelId?: number | null;
      projectOutletId?: number | null;
      items?: {
        projectProductId?: number;
        projectItemId?: number;
        type: ItemKpiTypeEnum;
        kpi: number | null;
      }[];
      periodType?: PeriodTypeEnum;
    }) => axiosPost(`/projects/${projectId}/kpis`, data),
  });
};

export type PropagationKpiMutationDataType = {
  sourceKpi: {
    projectAgencyId?: number;
    provinceId?: number;
    channelId?: number;
    projectOutletId?: number;
    periodType?: PeriodTypeEnum;
  };
  targetKpis: {
    projectAgencyId?: number;
    provinceId?: number;
    channelId?: number;
    projectOutletId?: number;
    periodType?: PeriodTypeEnum;
  }[];
  targetKpiTypes: ItemKpiTypeEnum[];
};

export const usePropagationKpiMutation = (projectId: number) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["propagationKpi", projectId],
    mutationFn: (data: PropagationKpiMutationDataType) =>
      axiosPost(`/projects/${projectId}/kpis/propagation`, data),
  });
};
