import ChartContanier from "@/routes/project/chart/ChartContanier";
import { Area } from "@ant-design/charts";
import { Col, Row } from "antd";
import { useCallback } from "react";
import DashboardFilterZone from "../../DashboardFilterZone";

const MainshelfTab = () => {
  const handleApply = useCallback(() => {}, []);

  const config = {
    data: [
      { week: "W1", brand: "Sabeco", value: 1.3 },
      { week: "W1", brand: "Heineken", value: 0.8 },
      { week: "W1", brand: "Tiger", value: 0.5 },
      { week: "W1", brand: "Larue", value: 0.3 },
      { week: "W1", brand: "Sài G n Chill", value: 0.2 },
      { week: "W2", brand: "Sabeco", value: 1.2 },
      { week: "W2", brand: "Heineken", value: 0.9 },
      { week: "W2", brand: "Tiger", value: 0.7 },
      { week: "W2", brand: "Larue", value: 0.4 },
      { week: "W2", brand: "Sài G n Chill", value: 0.3 },
      { week: "W3", brand: "Sabeco", value: 1.1 },
      { week: "W3", brand: "Heineken", value: 1 },
      { week: "W3", brand: "Tiger", value: 0.9 },
      { week: "W3", brand: "Larue", value: 0.5 },
      { week: "W3", brand: "Sài G n Chill", value: 0.4 },
      { week: "W4", brand: "Sabeco", value: 1 },
      { week: "W4", brand: "Heineken", value: 1.1 },
      { week: "W4", brand: "Tiger", value: 1 },
      { week: "W4", brand: "Larue", value: 0.6 },
      { week: "W4", brand: "Sài G n Chill", value: 0.5 },
      { week: "W5", brand: "Sabeco", value: 0.9 },
      { week: "W5", brand: "Heineken", value: 1.2 },
      { week: "W5", brand: "Tiger", value: 1.1 },
      { week: "W5", brand: "Larue", value: 0.7 },
      { week: "W5", brand: "Sài G n Chill", value: 0.6 },
    ],
    xField: "week",
    yField: "value",
    colorField: "brand",
    normalize: true,
    stack: true,
    tooltip: { channel: "y0", valueFormatter: ".0%" },
    title: {
      title: "% Display share",
      subtitle: "Tỷ lệ display share giữa Sabeco và đối thủ",
    },
    label: {},
    legend: {
      color: {
        position: "bottom",
        layout: {
          justifyContent: "center",
        },
      },
    },
  };

  return (
    <>
      <DashboardFilterZone handleApply={handleApply} />

      <Row gutter={[16, { xs: 16, sm: 16 }]}>
        <Col md={12} xs={24}>
          <ChartContanier>
            <Area {...config} />
          </ChartContanier>
        </Col>

        <Col md={12} xs={24}>
          <ChartContanier>
            <Area {...config} />
          </ChartContanier>
        </Col>
      </Row>
    </>
  );
};

export default MainshelfTab;
