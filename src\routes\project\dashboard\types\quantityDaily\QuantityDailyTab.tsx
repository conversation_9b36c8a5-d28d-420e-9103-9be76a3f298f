import { randomColor } from "@/common/helper";
import ChartContanier from "@/routes/project/chart/ChartContanier";
import { Line } from "@ant-design/charts";
import { Form } from "antd";
import dayjs from "dayjs";
import _ from "lodash";
import { useCallback, useMemo, useState } from "react";
import DashboardFilterZone from "../../DashboardFilterZone";
import { DashboardFilterInterface } from "../../interface";
import { useQuantityDailyTotalQuery } from "./service";

interface QuantityDailyTabProps {
  projectId: number;
  dashboardId: number;
}

const QuantityDailyTab = ({
  projectId,
  dashboardId,
}: QuantityDailyTabProps) => {
  const [form] = Form.useForm();
  const [filter, setFilter] = useState<DashboardFilterInterface>({});

  const quantityDailyTotalQuery = useQuantityDailyTotalQuery(
    projectId,
    dashboardId,
    filter,
  );

  const handleApply = useCallback(() => {
    const values = form.getFieldsValue();

    const {
      date,
      projectRegionIds,
      projectOutletIds,
      channelIds,
      provinceIds,
      leaderIds,
    } = values;

    const filterValue = {
      startDate: date?.[0] ? date[0].format("YYYY-MM-DD") : null,
      endDate: date?.[1] ? date[1].format("YYYY-MM-DD") : null,
      projectRegionIds,
      projectOutletIds,
      channelIds,
      provinceIds,
      leaderIds: leaderIds?.map((item: { value: number }) => item.value),
    };
    if (_.isEqual(filter, filterValue)) {
      quantityDailyTotalQuery.refetch();
    } else setFilter(filterValue);
  }, [filter, form, quantityDailyTotalQuery]);

  const data = useMemo(() => {
    const result: {
      date: string;
      groupName: string;
      backgroundColor: string;
      total: number;
    }[] = [];

    const group = _.groupBy(quantityDailyTotalQuery.data, "date");

    Object.entries(group).forEach(([date, values]) => {
      const valuesGroup = _.groupBy(values, "groupName");

      Object.entries(valuesGroup).forEach(([groupName, items]) => {
        result.push({
          date,
          groupName,
          total: _.sumBy(items, "total"),
          backgroundColor: items[0].backgroundColor,
        });
      });
    });

    return result;
  }, [quantityDailyTotalQuery.data]);

  const config = useMemo(
    () => ({
      data,
      xField: "date",
      yField: "total",
      colorField: "groupName",
      legend: {
        color: {
          position: "bottom",
          layout: {
            justifyContent: "center",
            alignItems: "center",
            flexDirection: "column",
          },
          itemMarker: "square",
        },
      },
      point: {
        size: 5,
        shape: "circle",
        style: {
          fill: "white",
          stroke: "#5B8FF9",
          lineWidth: 2,
        },
      },
      title: {
        title: "Sell out",
        subtitle: "Tính theo thùng",
      },
      label: {
        textBaseline: "bottom",
        textAlign: "center",
        style: {
          dy: -5,
        },
      },
      tooltip: { channel: "y" },
      style: {
        lineWidth: 4,
      },
      shape: "smooth",
      scale: {
        color: {
          palette: data.map((item) => item.backgroundColor ?? randomColor()),
        },
      },
      axis: {
        x: {
          labelFormatter: (d: string) => dayjs(d).format("DD/MM"),
        },
      },
    }),
    [data],
  );

  return (
    <>
      <DashboardFilterZone
        handleApply={handleApply}
        fields={["date", "region", "province", "chain", "leader", "outlet"]}
        form={form}
        projectId={projectId}
        setFilter={setFilter}
      />

      <ChartContanier>
        <Line {...config} height={600} />
      </ChartContanier>
    </>
  );
};

export default QuantityDailyTab;
