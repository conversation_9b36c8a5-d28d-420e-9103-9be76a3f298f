import { AbstractEntityInterface } from "@/common/interface";
import { ProjectItemInterface } from "@/routes/project/item/interface";
import { ProjectOutletInterface } from "@/routes/project/outlet/interface";
import { ProjectProductInterface } from "@/routes/project/product/interface";

export enum NumericSheetStepEnum {
  GROUP = "group",
  DATA = "data",
  BINDING = "binding",
  OUTLET = "outlet",
}

export interface FeatureNumericSheetInterface extends AbstractEntityInterface {
  name: string;
  featureNumericSheetOutletsCount: number;
}

export interface NumericSheetNumericInterface extends AbstractEntityInterface {
  ordinal: number;
  projectItem: ProjectItemInterface | null;
  projectProduct: ProjectProductInterface | null;
}

export enum NumericSheetGroupCollapseChildActionEnum {
  INACTIVE = "INACTIVE",
  ACTIVE = "ACTIVE",
  DELETE = "DELETE",
}

export enum FeatureNumericAttributeTypeEnum {
  INTEGER = "integer",
  DECIMAL = "decimal",
}

export interface FeatureNumericAttributeInterface
  extends AbstractEntityInterface {
  type: FeatureNumericAttributeTypeEnum;
  name: string;
  minimum: number;
  maximum: number;
  ordinal: number;
}

export interface FeatureNumericSheetOutletInterface
  extends AbstractEntityInterface {
  featureNumericSheet: FeatureNumericSheetInterface;
  projectOutlet: ProjectOutletInterface;
}

export interface FeatureNumericSheetOutletAvailableInterface
  extends ProjectOutletInterface {
  featureNumericSheetOutlets: FeatureNumericSheetOutletInterface[];
}
