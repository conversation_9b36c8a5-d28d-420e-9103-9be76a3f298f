import { CURD } from "@/common/constant";
import { filterOption, formErrorResponse<PERSON><PERSON><PERSON> } from "@/common/helper";
import FormNumberInput from "@/components/FormNumberInput";
import ModalCURD from "@/components/ModalCURD";
import TableActionCell from "@/components/TableActionCell";
import { useLuckyDrawsQuery } from "@/routes/project/configLuckyWheel/service";
import { ProjectAgencyInterface } from "@/routes/project/interface";
import {
  useProjectItemsQuery,
  useProjectItemsTypesQuery,
} from "@/routes/project/item/services";
import { ProjectProductInterface } from "@/routes/project/product/interface";
import { useProjectProductsQuery } from "@/routes/project/product/service";
import { useApp } from "@/UseApp";
import { DeleteOutlined } from "@ant-design/icons";
import {
  Button,
  Checkbox,
  Col,
  Form,
  Input,
  Row,
  Select,
  Table,
  Tooltip,
} from "antd";
import Jo<PERSON> from "joi";
import _ from "lodash";
import { useCallback, useMemo, useState } from "react";
import { FeatureSchemeInterface } from "../../../interface";
import { useAddSchemeExchangeMutation } from "../service";
interface ExchangeTabConditionModalProps {
  open: boolean;
  projectAgency: ProjectAgencyInterface;
  selectedScheme: FeatureSchemeInterface | null;
  componentFeatureId: number;
  cb: () => void;
  cancelCb: () => void;
}

const ExchangeTabConditionModal = ({
  open,
  projectAgency,
  selectedScheme,
  componentFeatureId,
  cb,
  cancelCb,
}: ExchangeTabConditionModalProps) => {
  const SELECT_ALL = 0;
  const PRODUCT_SELECTED = 0;
  const { projectId } = projectAgency;
  const { showNotification } = useApp();

  const [formAddCondition] = Form.useForm();
  const [orProducts, setOrProducts] = useState<ProjectProductInterface[]>([]);
  const [relaxedProducts, setRelaxedProducts] = useState<
    ProjectProductInterface[]
  >([]);

  const projectProductsQuery = useProjectProductsQuery(
    projectId,
    { take: 0 },
    open,
  );
  const itemTypesQuery = useProjectItemsTypesQuery(projectId, open);
  const projectItemsQuery = useProjectItemsQuery(
    projectId,
    {
      take: 0,
      skip: 0,
    },
    open,
  );
  const luckyDrawsQuery = useLuckyDrawsQuery(projectId);

  const addSchemeExchangeMutation =
    useAddSchemeExchangeMutation(componentFeatureId);

  const brandOptions = useMemo(() => {
    return _.uniqBy(
      projectProductsQuery.data?.entities.map((projectProduct) => ({
        label: projectProduct.product.brand.name,
        value: projectProduct.product.brand.id,
      })),
      "value",
    );
  }, [projectProductsQuery.data?.entities]);

  const onSelectedRelaxedProducts = useCallback(
    (projectProductId: number) => {
      if (projectProductId === SELECT_ALL) {
        const brandId = formAddCondition.getFieldValue("relaxedBrand");
        setRelaxedProducts((values) =>
          _.uniqBy(
            [
              ...values,
              ...(projectProductsQuery.data?.entities.filter(
                (projectProduct) =>
                  brandId ? projectProduct.product.brand.id === brandId : true,
              ) ?? []),
            ],
            "id",
          ),
        );
      } else {
        setRelaxedProducts((values) =>
          _.uniqBy(
            [
              ...values,
              ...(projectProductsQuery.data?.entities.filter(
                (projectProduct) => projectProduct.id === projectProductId,
              ) ?? []),
            ],
            "id",
          ),
        );
      }

      formAddCondition.resetFields(["relaxedSelectedProduct"]);
    },
    [formAddCondition, projectProductsQuery.data?.entities],
  );

  const onSelectedOrProducts = useCallback(
    (projectProductId: number) => {
      if (projectProductId === SELECT_ALL) {
        const brandId = formAddCondition.getFieldValue("orBrand");
        setOrProducts((values) =>
          _.uniqBy(
            [
              ...values,
              ...(projectProductsQuery.data?.entities.filter(
                (projectProduct) =>
                  brandId ? projectProduct.product.brand.id === brandId : true,
              ) ?? []),
            ],
            "id",
          ),
        );
      } else {
        setOrProducts((values) =>
          _.uniqBy(
            [
              ...values,
              ...(projectProductsQuery.data?.entities.filter(
                (projectProduct) => projectProduct.id === projectProductId,
              ) ?? []),
            ],
            "id",
          ),
        );
      }

      formAddCondition.resetFields(["orSelectedProduct"]);
    },
    [formAddCondition, projectProductsQuery.data?.entities],
  );

  const typeOptions = useMemo(() => {
    const data = itemTypesQuery.data?.map((itemType) => ({
      label: itemType.name,
      value: itemType.id,
    }));
    data?.unshift({ label: "Sản phẩm", value: PRODUCT_SELECTED });
    return data;
  }, [itemTypesQuery.data]);

  const selectGiftProductsOptions = useMemo(() => {
    return (
      projectProductsQuery.data?.entities.map((projectProduct) => ({
        label: `${projectProduct.product.code} - ${projectProduct.productPackaging?.unit.name} - ${projectProduct.product.name}`,
        value: projectProduct.id,
      })) ?? []
    );
  }, [projectProductsQuery.data?.entities]);

  const formAddConditionContent = (
    <>
      <Form.Item
        label={"Tên điều kiện nhận quà"}
        name={"name"}
        rules={[{ required: true }]}
      >
        <Input />
      </Form.Item>

      <p className="text-primary font-semibold pt-0 mt-0">ĐIỀU KIỆN NHẬN QUÀ</p>
      <Form.Item noStyle dependencies={["or", "hasPlayedGame", "relaxed"]}>
        {() => (
          <Form.Item
            className="bg-input-disabled pl-4 pr-4 pt-2 pb-2"
            name={"money"}
            valuePropName="checked"
          >
            <Checkbox
              disabled={
                formAddCondition.getFieldValue("or") ||
                formAddCondition.getFieldValue("relaxed") ||
                formAddCondition.getFieldValue("hasPlayedGame")
              }
            >
              <Tooltip
                title={
                  formAddCondition.getFieldValue("or")
                    ? "Không thể áp dụng đồng thời điều kiện mua đạt giá trị hóa đơn và mua sản phẩm bắt buộc"
                    : ""
                }
              >
                <span className="text-text-color">Mua đạt giá trị hóa đơn</span>
              </Tooltip>
            </Checkbox>
          </Form.Item>
        )}
      </Form.Item>

      <Form.Item noStyle dependencies={["money"]}>
        {() => (
          <>
            {formAddCondition.getFieldValue("money") && (
              <Form.Item
                name={"reachAmount"}
                label="Giá trị hóa đơn cần đạt để nhận quà (VNĐ)"
                rules={[
                  {
                    required: true,
                    message: "Giá tiền cần đạt để nhận quà không được để trống",
                  },
                ]}
              >
                <FormNumberInput className="w-[50%]" />
              </Form.Item>
            )}
          </>
        )}
      </Form.Item>

      <Form.Item noStyle dependencies={["or", "hasPlayedGame", "relaxed"]}>
        {() => (
          <Form.Item
            className="bg-input-disabled pl-4 pr-4 pt-2 pb-2"
            name={"and"}
            valuePropName="checked"
          >
            <Checkbox
              disabled={
                formAddCondition.getFieldValue("or") ||
                formAddCondition.getFieldValue("relaxed") ||
                formAddCondition.getFieldValue("hasPlayedGame")
              }
            >
              <Tooltip
                title={
                  formAddCondition.getFieldValue("or")
                    ? "Không thể áp dụng đồng thời điều kiện mua sản phẩm bắt buộc và mua đạt số lượng trong danh sách sản phẩm quy định"
                    : ""
                }
              >
                <span className="text-text-color">Mua sản phẩm bắt buộc</span>
              </Tooltip>
            </Checkbox>
          </Form.Item>
        )}
      </Form.Item>

      <Form.Item noStyle dependencies={["and", "money", "relaxed"]}>
        {() => (
          <>
            {formAddCondition.getFieldValue("and") && (
              <Form.List name="andExchangeConditions" initialValue={[{}]}>
                {(fields, { add, remove }) => (
                  <>
                    {fields.map(({ key, name, ...restField }, index) => (
                      <Row key={key} justify={"space-between"}>
                        <Col md={3}>
                          <Form.Item
                            {...restField}
                            label="SL mua"
                            name={[name, "quantity"]}
                            rules={[
                              {
                                required: true,
                                message: "Số lượng là bắt buộc",
                              },
                            ]}
                            style={{ width: "100%" }}
                          >
                            <FormNumberInput className="w-full" />
                          </Form.Item>
                        </Col>

                        <Col md={19}>
                          <Form.Item
                            {...restField}
                            label="Sản phẩm cần mua"
                            name={[name, "projectProductId"]}
                            rules={[
                              {
                                required: true,
                                message: "Sản phẩm cần mua là bắt buộc",
                              },
                            ]}
                          >
                            {(() => {
                              const andExchangeConditions: {
                                projectProductId: number;
                              }[] = formAddCondition.getFieldValue(
                                "andExchangeConditions",
                              );

                              const options =
                                projectProductsQuery.data?.entities
                                  .filter(
                                    (projectProduct) =>
                                      !andExchangeConditions
                                        .flatMap(
                                          (item) => item?.projectProductId,
                                        )
                                        .includes(projectProduct.id),
                                  )
                                  .map((projectProduct) => ({
                                    label: `${projectProduct.product.code} - ${projectProduct.productPackaging?.unit.name} - ${projectProduct.product.name}`,
                                    value: projectProduct.id,
                                  }));

                              return (
                                <Select
                                  options={options}
                                  style={{ width: "100%" }}
                                  showSearch
                                  optionFilterProp="children"
                                  filterOption={filterOption}
                                />
                              );
                            })()}
                          </Form.Item>
                        </Col>

                        <Col className="flex" md={1}>
                          {index > 0 && (
                            <DeleteOutlined onClick={() => remove(name)} />
                          )}
                        </Col>
                      </Row>
                    ))}

                    <Form.Item>
                      <Button
                        type="link"
                        onClick={() => add()}
                        className="text-[#1D8EE6]"
                      >
                        Thêm sản phẩm bắt buộc
                      </Button>
                    </Form.Item>
                  </>
                )}
              </Form.List>
            )}
          </>
        )}
      </Form.Item>

      <Form.Item
        noStyle
        dependencies={["and", "money", "hasPlayedGame", "relaxed"]}
      >
        {() => {
          const moneyTooltipTitle = formAddCondition.getFieldValue("money")
            ? "Không thể áp dụng đồng thời điều kiện mua đạt giá trị hóa đơn và mua đạt điều kiện hoặc"
            : "";

          return (
            <Form.Item
              className="bg-input-disabled pl-4 pr-4 pt-2 pb-2"
              name={"or"}
              valuePropName="checked"
            >
              <Checkbox
                disabled={
                  formAddCondition.getFieldValue("and") ||
                  formAddCondition.getFieldValue("hasPlayedGame") ||
                  formAddCondition.getFieldValue("money") ||
                  formAddCondition.getFieldValue("relaxed")
                }
              >
                <Tooltip
                  title={
                    formAddCondition.getFieldValue("and")
                      ? "Không thể áp dụng đồng thời điều kiện mua sản phẩm bắt buộc và mua đạt điều kiện hoặc"
                      : moneyTooltipTitle
                  }
                >
                  <span className="text-text-color">
                    Mua đạt điều kiện hoặc
                  </span>
                </Tooltip>
              </Checkbox>
            </Form.Item>
          );
        }}
      </Form.Item>

      <Form.Item noStyle dependencies={["or"]}>
        {() => (
          <>
            {formAddCondition.getFieldValue("or") && (
              <>
                <Row justify={"start"}>
                  <Col md={4}>
                    <Form.Item label="Nhãn hàng" name={"orBrand"}>
                      <Select
                        options={brandOptions}
                        style={{ width: "100%" }}
                        showSearch
                        optionFilterProp="children"
                        filterOption={filterOption}
                      />
                    </Form.Item>
                  </Col>

                  <Form.Item noStyle dependencies={["orBrand"]}>
                    {() => {
                      const orBrand = formAddCondition.getFieldValue("orBrand");

                      const orProductOptions =
                        projectProductsQuery.data?.entities
                          .filter(
                            (projectProduct) =>
                              !_.includes(
                                orProducts.map((item) => item.id),
                                projectProduct.id,
                              ),
                          )
                          .filter((projectProduct) =>
                            orBrand
                              ? projectProduct.product.brand.id === orBrand
                              : true,
                          )
                          .map((projectProduct) => ({
                            label: `${projectProduct.product?.code} - ${projectProduct.productPackaging?.unit.name} - ${projectProduct.product.name}`,
                            value: projectProduct.id,
                          }));

                      if (orProductOptions?.length) {
                        orProductOptions?.unshift({
                          label: "Chọn tất cả sản phẩm thuộc nhãn hàng",
                          value: SELECT_ALL,
                        });
                      }

                      return (
                        <Col md={15} offset={1}>
                          <Form.Item
                            label="Chọn sản phẩm thêm vào danh sách"
                            name={"orSelectedProduct"}
                          >
                            <Select
                              options={orProductOptions}
                              style={{ width: "100%" }}
                              showSearch
                              optionFilterProp="children"
                              filterOption={filterOption}
                              onSelect={onSelectedOrProducts}
                            />
                          </Form.Item>
                        </Col>
                      );
                    }}
                  </Form.Item>
                </Row>

                <Table
                  rowKey="id"
                  dataSource={orProducts}
                  pagination={false}
                  className={"mb-3"}
                  columns={[
                    {
                      render: (_, record) => (
                        <Form.Item
                          name={[
                            "orProductsWithQuantity",
                            record.id,
                            "quantity",
                          ]}
                          rules={[
                            {
                              required: true,
                              message: "Số lượng không được để trống",
                            },
                          ]}
                        >
                          <FormNumberInput />
                        </Form.Item>
                      ),
                    },
                    {
                      title: "Tên sản phẩm cần mua",
                      dataIndex: "product",
                      render: (product) => product?.name,
                    },
                    {
                      title: "Mã sản phẩm",
                      dataIndex: "product",
                      render: (product) => product?.name,
                    },
                    {
                      title: "Nhãn hàng",
                      dataIndex: "product",
                      render: (product) => product?.brand?.name,
                    },
                    {
                      title: "Đơn vị tính",
                      dataIndex: "productPackaging",
                      render: (productPackaging) => productPackaging?.unit.name,
                    },
                    {
                      render: (_, record) => {
                        const handleDelete = (
                          record: ProjectProductInterface,
                        ) => {
                          setOrProducts(
                            orProducts.filter(
                              (product) => product.id !== record.id,
                            ),
                          );
                        };

                        return (
                          <TableActionCell
                            actions={[
                              {
                                key: "DELETE",
                                action: handleDelete,
                              },
                            ]}
                            items={[
                              {
                                key: "DELETE",
                                label: (
                                  <>
                                    <DeleteOutlined /> Xóa
                                  </>
                                ),
                              },
                            ]}
                            record={record}
                          />
                        );
                      },
                    },
                  ]}
                />
              </>
            )}
          </>
        )}
      </Form.Item>

      <Form.Item noStyle dependencies={["and", "money", "or"]}>
        {() => (
          <Form.Item
            name={"hasPlayedGame"}
            className="bg-input-disabled pl-4 pr-4 pt-2 pb-2 mb-[10px]"
            valuePropName="checked"
          >
            <Checkbox
              disabled={
                formAddCondition.getFieldValue("and") ||
                formAddCondition.getFieldValue("or") ||
                formAddCondition.getFieldValue("money")
              }
            >
              <span className="text-text-color">Đã chơi game</span>
            </Checkbox>
          </Form.Item>
        )}
      </Form.Item>
      <p className={"mt-0 pt-0 text-hint"}>
        Nếu không chọn điều kiện nhận quà hoặc chọn điều kiện là đã chơi game
        thì nhân viên field sẽ tự quản lý điều kiện khi phát quà cho khách
      </p>

      <Form.Item noStyle dependencies={["and", "money", "or"]}>
        {() => (
          <Form.Item
            name={"relaxed"}
            className="bg-input-disabled pl-4 pr-4 pt-2 pb-2 mb-[10px]"
            valuePropName="checked"
          >
            <Checkbox
              disabled={
                formAddCondition.getFieldValue("and") ||
                formAddCondition.getFieldValue("or") ||
                formAddCondition.getFieldValue("money")
              }
            >
              <span className="text-text-color">Relaxed</span>
            </Checkbox>
          </Form.Item>
        )}
      </Form.Item>

      <Form.Item noStyle dependencies={["relaxed"]}>
        {() => (
          <>
            {formAddCondition.getFieldValue("relaxed") && (
              <>
                <Row>
                  <Col md={4}>
                    <Form.Item
                      label="Số lượng cần mua"
                      name={"reachQuantity"}
                      rules={[
                        {
                          required: true,
                        },
                      ]}
                    >
                      <FormNumberInput className="w-full" />
                    </Form.Item>
                  </Col>
                </Row>
                <Row justify={"start"}>
                  <Col md={4}>
                    <Form.Item label="Nhãn hàng" name={"relaxedBrand"}>
                      <Select
                        options={brandOptions}
                        style={{ width: "100%" }}
                        showSearch
                        optionFilterProp="children"
                        filterOption={filterOption}
                      />
                    </Form.Item>
                  </Col>

                  <Form.Item noStyle dependencies={["relaxedBrand"]}>
                    {() => {
                      const relaxedBrand =
                        formAddCondition.getFieldValue("relaxedBrand");

                      const relaxedProductOptions =
                        projectProductsQuery.data?.entities
                          .filter(
                            (projectProduct) =>
                              !_.includes(
                                relaxedProducts.map((item) => item.id),
                                projectProduct.id,
                              ),
                          )
                          .filter((projectProduct) =>
                            relaxedBrand
                              ? projectProduct.product.brand.id === relaxedBrand
                              : true,
                          )
                          .map((projectProduct) => ({
                            label: `${projectProduct.product?.code} - ${projectProduct.productPackaging?.unit.name} - ${projectProduct.product.name}`,
                            value: projectProduct.id,
                          }));

                      if (relaxedProductOptions?.length) {
                        relaxedProductOptions?.unshift({
                          label: "Chọn tất cả sản phẩm thuộc nhãn hàng",
                          value: SELECT_ALL,
                        });
                      }

                      return (
                        <Col md={15} offset={1}>
                          <Form.Item
                            label="Chọn sản phẩm thêm vào danh sách"
                            name={"relaxedSelectedProduct"}
                          >
                            <Select
                              options={relaxedProductOptions}
                              style={{ width: "100%" }}
                              showSearch
                              optionFilterProp="children"
                              filterOption={filterOption}
                              onSelect={onSelectedRelaxedProducts}
                            />
                          </Form.Item>
                        </Col>
                      );
                    }}
                  </Form.Item>
                </Row>

                <Table
                  rowKey="id"
                  dataSource={relaxedProducts}
                  pagination={false}
                  className={"mb-3"}
                  columns={[
                    {
                      render: (_, record) => (
                        <Form.Item
                          name={["relaxedProducts", record.id, "quantity"]}
                          rules={[
                            {
                              required: true,
                              message: "Số lượng không được để trống",
                            },
                          ]}
                        >
                          <FormNumberInput />
                        </Form.Item>
                      ),
                    },
                    {
                      title: "Tag",
                      render: (_, record) => (
                        <Form.Item name={["relaxedProducts", record.id, "tag"]}>
                          <Input />
                        </Form.Item>
                      ),
                    },
                    {
                      title: "Tên sản phẩm cần mua",
                      dataIndex: "product",
                      render: (product) => product?.name,
                    },
                    {
                      title: "Mã sản phẩm",
                      dataIndex: "product",
                      render: (product) => product?.name,
                    },
                    {
                      title: "Nhãn hàng",
                      dataIndex: "product",
                      render: (product) => product?.brand?.name,
                    },
                    {
                      title: "Đơn vị tính",
                      dataIndex: "productPackaging",
                      render: (productPackaging) => productPackaging?.unit.name,
                    },
                    {
                      render: (_, record) => {
                        const handleDelete = (
                          record: ProjectProductInterface,
                        ) => {
                          setRelaxedProducts(
                            relaxedProducts.filter(
                              (product) => product.id !== record.id,
                            ),
                          );
                        };

                        return (
                          <TableActionCell
                            actions={[
                              {
                                key: "DELETE",
                                action: handleDelete,
                              },
                            ]}
                            items={[
                              {
                                key: "DELETE",
                                label: (
                                  <>
                                    <DeleteOutlined /> Xóa
                                  </>
                                ),
                              },
                            ]}
                            record={record}
                          />
                        );
                      },
                    },
                  ]}
                />
              </>
            )}
          </>
        )}
      </Form.Item>

      <p className="text-primary font-semibold pt-0 mt-0">QUÀ NHẬN ĐƯỢC</p>
      <Form.List name="exchangeProceeds" initialValue={[{}]}>
        {(fields, { add, remove }) => (
          <>
            {fields.map(({ key, name, ...restField }, index) => (
              <Row key={key} justify={"space-between"}>
                <Col md={4}>
                  <Form.Item
                    {...restField}
                    label="Số lượng"
                    name={[name, "quantity"]}
                    rules={[]}
                  >
                    <FormNumberInput className="w-full" />
                  </Form.Item>
                </Col>

                <Col md={6}>
                  <Form.Item
                    {...restField}
                    label="Loại quà"
                    name={[name, "type"]}
                    rules={[]}
                  >
                    <Select
                      options={typeOptions}
                      style={{ width: "100%" }}
                      onChange={() => {
                        formAddCondition.resetFields([
                          ["exchangeProceeds", name, "itemIdOrProductId"],
                        ]);
                      }}
                      showSearch
                      optionFilterProp="children"
                      filterOption={filterOption}
                    />
                  </Form.Item>
                </Col>

                <Form.Item
                  noStyle
                  dependencies={[["exchangeProceeds", name, "type"]]}
                >
                  {() => {
                    let exchangeProceedsSelectOptions: {
                      label: string;
                      value: number;
                    }[] = [];
                    const exchangeProceeds = formAddCondition.getFieldValue(
                      "exchangeProceeds",
                    ) as {
                      type: number;
                      itemIdOrProductId: number;
                    }[];

                    const gift: { type: number } = exchangeProceeds[index];

                    if (gift) {
                      if (gift.type === PRODUCT_SELECTED) {
                        exchangeProceedsSelectOptions =
                          selectGiftProductsOptions.filter(
                            (selectGiftProductsOption) =>
                              !exchangeProceeds
                                .filter(
                                  (exchangeProceed) =>
                                    exchangeProceed?.type === PRODUCT_SELECTED,
                                )
                                .map(
                                  (exchangeProceed) =>
                                    exchangeProceed?.itemIdOrProductId,
                                )
                                .includes(selectGiftProductsOption.value),
                          );
                      }
                      if (gift.type && gift.type !== PRODUCT_SELECTED) {
                        exchangeProceedsSelectOptions =
                          projectItemsQuery.data?.entities
                            ?.filter(
                              (projectItem) =>
                                projectItem?.item?.itemType?.id === gift.type,
                            )
                            ?.filter(
                              (projectItem) =>
                                !exchangeProceeds
                                  .map(
                                    (exchangeProceed) =>
                                      exchangeProceed?.itemIdOrProductId,
                                  )
                                  .includes(projectItem.id),
                            )
                            .map((projectItem) => ({
                              label: `${projectItem.item.code} - ${projectItem.item.unit?.name} - ${projectItem.item.name}`,
                              value: projectItem.id,
                            })) ?? [];
                      }
                    }

                    return (
                      <Col md={12}>
                        <Form.Item
                          {...restField}
                          label="Tên quà"
                          name={[name, "itemIdOrProductId"]}
                          rules={[]}
                        >
                          <Select
                            style={{ width: "100%" }}
                            options={exchangeProceedsSelectOptions}
                            showSearch
                            optionFilterProp="children"
                            filterOption={filterOption}
                          />
                        </Form.Item>
                      </Col>
                    );
                  }}
                </Form.Item>

                <Col className="flex" md={1}>
                  {index > 0 && <DeleteOutlined onClick={() => remove(name)} />}
                </Col>
              </Row>
            ))}

            <Form.Item>
              <Button
                type="link"
                onClick={() => add()}
                className="text-[#1D8EE6]"
              >
                Thêm quà
              </Button>
            </Form.Item>
          </>
        )}
      </Form.List>

      <Form.Item
        label="SL phần quà tối đa / hóa đơn"
        name="maxReceiveQuantity"
        rules={[]}
      >
        <FormNumberInput className="w-[50%]" />
      </Form.Item>

      <p className="text-primary font-semibold pt-0 mt-8">
        Lucky draw nhận được
      </p>

      <Row gutter={16}>
        <Col md={4}>
          <Form.Item label="Số lượng lucky draw" name={"luckyDrawCount"}>
            <FormNumberInput className="w-full" />
          </Form.Item>
        </Col>
        <Col md={20}>
          <Form.Item label="Loại lucky draw" name={"luckyDrawId"}>
            <Select
              allowClear
              options={luckyDrawsQuery.data?.entities.map((luckyDraw) => ({
                label: luckyDraw.name,
                value: luckyDraw.id,
              }))}
            />
          </Form.Item>
        </Col>
      </Row>
    </>
  );

  const addConditionFinish = useCallback(async () => {
    if (!selectedScheme) {
      return;
    }

    try {
      const or = formAddCondition.getFieldValue("or");
      const and = formAddCondition.getFieldValue("and");
      const hasPlayedGame = formAddCondition.getFieldValue("hasPlayedGame");
      const relaxed = formAddCondition.getFieldValue("relaxed");
      const maxReceiveQuantity =
        formAddCondition.getFieldValue("maxReceiveQuantity");
      const reachAmount = formAddCondition.getFieldValue("reachAmount");
      const andExchangeConditions = formAddCondition.getFieldValue(
        "andExchangeConditions",
      );
      let exchangeProceeds = formAddCondition.getFieldValue("exchangeProceeds");
      const luckyDrawId = formAddCondition.getFieldValue("luckyDrawId");
      const luckyDrawCount = formAddCondition.getFieldValue("luckyDrawCount");
      const reachQuantity = formAddCondition.getFieldValue("reachQuantity");

      let logical: undefined | "and" | "or" | "relaxed" = undefined;
      if (and) {
        logical = "and";
      } else if (or) {
        logical = "or";
      } else if (relaxed) {
        logical = "relaxed";
      }

      let exchangeConditions = [];
      if (logical === "and") {
        exchangeConditions = andExchangeConditions;
      } else if (logical === "or") {
        const orProductsWithQuantity = formAddCondition.getFieldValue(
          "orProductsWithQuantity",
        );
        exchangeConditions = orProducts.map((product) => ({
          projectProductId: product.id,
          quantity: orProductsWithQuantity?.[product.id]?.quantity,
        }));
      } else if (logical === "relaxed") {
        const relaxedProductsWithQuantity =
          formAddCondition.getFieldValue("relaxedProducts");
        exchangeConditions = relaxedProducts.map((product) => ({
          projectProductId: product.id,
          quantity: relaxedProductsWithQuantity?.[product.id]?.quantity,
          tag: relaxedProductsWithQuantity?.[product.id]?.tag,
        }));
      }

      for (const exchangeProceed of exchangeProceeds) {
        if (exchangeProceed.type === PRODUCT_SELECTED) {
          exchangeProceed.projectItemId = null;
          exchangeProceed.projectProductId = exchangeProceed.itemIdOrProductId;
        } else {
          exchangeProceed.projectProductId = null;
          exchangeProceed.projectItemId = exchangeProceed.itemIdOrProductId;
        }
      }

      if (
        exchangeProceeds.length === 1 &&
        !exchangeProceeds[0].projectItemId &&
        !exchangeProceeds[0].projectProductId
      ) {
        exchangeProceeds = [];
      }

      const schema = Joi.object({
        andExchangeConditions: Joi.array()
          .unique()
          .message("Sản phẩm cần mua không được trùng nhau."),
        exchangeProceeds: Joi.array()
          .unique()
          .message("Quà nhận được không được trùng nhau."),
      });

      const { error } = schema.validate(
        {
          andExchangeConditions: exchangeConditions.map(
            (item: { projectProductId: number }) => item.projectProductId,
          ),
          exchangeProceeds: exchangeProceeds.map(
            (item: { itemIdOrProductId: number }) => item.itemIdOrProductId,
          ),
        },
        { abortEarly: false },
      );

      if (error) {
        error.details.forEach((detail) => {
          const lastPath =
            detail.path[0] === "andExchangeConditions"
              ? "projectProductId"
              : "itemIdOrProductId";
          formAddCondition.setFields([
            {
              name: [...detail.path, lastPath],
              errors: [detail.message],
            },
          ]);
        });

        return;
      }

      await addSchemeExchangeMutation.mutateAsync({
        name: formAddCondition.getFieldValue("name"),
        schemeId: selectedScheme.id,
        exchangeConditions,
        exchangeProceeds,
        maxReceiveQuantity,
        logical,
        reachAmount,
        hasPlayedGame,
        luckyDrawId,
        luckyDrawCount,
        reachQuantity,
      });

      showNotification({
        type: "success",
        message: "Thêm điều kiện nhận quà thành công",
      });

      formAddCondition.resetFields();
      setOrProducts([]);
      cb();
    } catch (e) {
      formErrorResponseHandler(formAddCondition, e);
    }
  }, [
    addSchemeExchangeMutation,
    cb,
    formAddCondition,
    orProducts,
    relaxedProducts,
    selectedScheme,
    showNotification,
  ]);

  return (
    <ModalCURD
      title={"Thêm điều kiện nhận quà"}
      isOpen={open}
      formContent={formAddConditionContent}
      form={formAddCondition}
      onFinish={addConditionFinish}
      action={CURD.CREATE}
      width={1000}
      onCancelCb={() => {
        setOrProducts([]);
        cancelCb();
      }}
    />
  );
};

export default ExchangeTabConditionModal;
