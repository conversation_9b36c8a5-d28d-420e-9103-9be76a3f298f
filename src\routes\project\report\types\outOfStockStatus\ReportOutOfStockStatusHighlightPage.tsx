import { CHUNK_SIZE } from "@/common/constant";
import {
  downloadFile,
  formatHeader,
  numToCol,
} from "@/common/export-excel.helper";
import { getImageVariants } from "@/common/image.helper";
import CustomTable from "@/components/CustomTable/CustomTable";
import ImageItem from "@/components/ImageItem";
import InnerContainer from "@/components/InnerContainer/InnerContainer";
import usePagination from "@/hooks/usePagination";
import {
  ProjectOutletInterface,
  ProjectOutletStockInterface,
} from "@/routes/project/outlet/interface";
import { EditOutlined } from "@ant-design/icons";
import type { TableProps } from "antd";
import { Button, Form } from "antd";
import dayjs from "dayjs";
import Excel from "exceljs";
import _ from "lodash";
import { useCallback, useEffect, useMemo, useState } from "react";
import FilterReportZone from "../../FilterReportZone";
import { useProjectReportOutletContext } from "../../UseProjectReportOutletContext";
import {
  NOT_HAVE_VALUE_BG_COLOR,
  NOT_HAVE_VALUE_TEXT_COLOR,
  NOT_SALE_VALUE_BG_COLOR,
  NOT_SALE_VALUE_TEXT_COLOR,
} from "./interface";
import ReportOutOfStockStatusHighlightModal from "./ReportOutOfStockStatusHighlightModal";
import {
  useGetReportOOSStatusesHighlightsMutation,
  useReportOOSStatusesHighlightsQuery,
  useReportOOSStatusesMergedProductsQuery,
  useReportOOSStatusesProductsQuery,
} from "./service";
import StockStatusCell from "./StockStatusCell";

interface ReportOutOfStockStatusHighlightPageProps {
  isSale?: boolean;
}

const ReportOutOfStockStatusHighlightPage = ({
  isSale,
}: ReportOutOfStockStatusHighlightPageProps) => {
  const { componentFeatureQuery, componentFeatureId, projectId, project } =
    useProjectReportOutletContext();

  const [filterForm] = Form.useForm();
  const [filter, setFilter] = useState({
    stockDate: dayjs().startOf("date").toDate(),
    isMergedProduct: !isSale,
  });
  const { getPagination, currentPage, pageSize } = usePagination({
    defaultPageSize: 50,
  });
  const isMergedProduct = Form.useWatch("isMergedProduct", filterForm);
  const date = Form.useWatch("date", filterForm);
  const howToDisplay = Form.useWatch("howToDisplay", filterForm);

  const [isExport, setIsExport] = useState(false);
  const [selectedProjectOutlet, setSelectedProjectOutlet] =
    useState<ProjectOutletInterface | null>(null);

  const reportOOSStatusesHighlightsQuery = useReportOOSStatusesHighlightsQuery(
    projectId,
    componentFeatureId,
    {
      ...filter,
      take: pageSize,
      skip: (currentPage - 1) * pageSize,
    },
  );
  const reportOOSStatusesProductsQuery = useReportOOSStatusesProductsQuery(
    projectId,
    componentFeatureId,
  );
  const reportOOSStatusesMergedProductsQuery =
    useReportOOSStatusesMergedProductsQuery(projectId, componentFeatureId);

  const getReportOOSStatusesHighlightsMutation =
    useGetReportOOSStatusesHighlightsMutation(projectId, componentFeatureId);

  const mergedProductsColumns = useMemo(
    () =>
      reportOOSStatusesMergedProductsQuery.data?.entities?.map((item) => {
        const variants = item.image?.variants ?? [];
        return {
          key: item.code,
          title: <div className="m-1 leading-4">{item.productName}</div>,
          className: "min-w-[100px] !p-0 align-top",
          align: "center" as unknown as "center",
          children: [
            {
              dataIndex: "projectOutletStocks",
              render: (projectOutletStocks: ProjectOutletStockInterface[]) => (
                <StockStatusCell
                  projectOutletStocks={projectOutletStocks}
                  mergedProductCode={item.code}
                />
              ),
              className: "min-w-[100px] !p-0",
              align: "center" as unknown as "center",
              title: (
                <ImageItem
                  thumbnail={getImageVariants(variants, "thumbnail")}
                  preview={getImageVariants(variants, "public")}
                  options={{
                    border: false,
                  }}
                />
              ),
            },
          ],
        };
      }) ?? [],
    [reportOOSStatusesMergedProductsQuery.data?.entities],
  );

  const productsColumns = useMemo(
    () =>
      reportOOSStatusesProductsQuery.data?.entities?.map((item) => {
        const variants = item.projectProduct.product.image?.variants ?? [];

        return {
          key: item.projectProduct.id,
          title: (
            <div className="m-1 leading-4">
              {item.projectProduct.product.shortName}
            </div>
          ),
          className: "min-w-[100px] !p-0 align-top",
          dataIndex: "projectOutletStocks",
          align: "center" as unknown as "center",
          children: [
            {
              dataIndex: "projectOutletStocks",
              render: (projectOutletStocks: ProjectOutletStockInterface[]) => (
                <StockStatusCell
                  projectOutletStocks={projectOutletStocks}
                  projectProductId={item.projectProduct.id}
                  isSale={isSale}
                  howToDisplay={howToDisplay}
                  notRenderColor
                />
              ),
              className: "min-w-[100px] !p-0",
              align: "center" as unknown as "center",
              title: (
                <ImageItem
                  thumbnail={getImageVariants(variants, "thumbnail")}
                  preview={getImageVariants(variants, "public")}
                  options={{
                    border: false,
                  }}
                />
              ),
            },
          ],
        };
      }) ?? [],
    [howToDisplay, isSale, reportOOSStatusesProductsQuery.data?.entities],
  );

  const [productsAndMergedProductsColumn, setProductsAndMergedProductsColumn] =
    useState<TableProps<ProjectOutletInterface>["columns"]>(
      mergedProductsColumns,
    );

  const onFilterFormFinish = useCallback(() => {
    const values = filterForm.getFieldsValue();

    if (values.isMergedProduct) {
      setProductsAndMergedProductsColumn(mergedProductsColumns);
    } else {
      setProductsAndMergedProductsColumn(productsColumns);
    }

    if (values.date) {
      values.stockDate = dayjs(values.date).startOf("date").toDate();
    }
    if (_.isEqual(values, filter)) {
      reportOOSStatusesHighlightsQuery.refetch();
    }
    setFilter(values);
  }, [
    filter,
    filterForm,
    mergedProductsColumns,
    productsColumns,
    reportOOSStatusesHighlightsQuery,
  ]);

  const pagination = useMemo(
    () => getPagination(reportOOSStatusesHighlightsQuery.data?.count ?? 0),
    [getPagination, reportOOSStatusesHighlightsQuery.data?.count],
  );

  const loading = useMemo(
    () =>
      reportOOSStatusesHighlightsQuery.isFetching ||
      reportOOSStatusesHighlightsQuery.isRefetching ||
      isExport,
    [
      isExport,
      reportOOSStatusesHighlightsQuery.isFetching,
      reportOOSStatusesHighlightsQuery.isRefetching,
    ],
  );

  const onExport = useCallback(async () => {
    const total = pagination.total!;

    if (total === 0) {
      return;
    }

    const fetchAllData = async () => {
      const requests = [];

      for (let skip = 0; skip < total; skip += CHUNK_SIZE) {
        requests.push(
          getReportOOSStatusesHighlightsMutation.mutateAsync({
            take: CHUNK_SIZE,
            skip: skip,
            ...filter,
          }),
        );
      }

      const results = await Promise.all(requests);
      const allEntities = results.flatMap((result) => result.entities);

      return allEntities;
    };

    const formatNumberCell = (
      cell: Excel.Cell,
      {
        backgroundColor,
        foregroundColor,
      }: {
        backgroundColor: string | undefined;
        foregroundColor: string | undefined;
      },
    ) => {
      cell.style = {
        border: {
          bottom: {
            style: "thin",
            color: {
              argb: "BEBEBE",
            },
          },
          left: {
            style: "thin",
            color: {
              argb: "BEBEBE",
            },
          },
          right: {
            style: "thin",
            color: {
              argb: "BEBEBE",
            },
          },
          top: {
            style: "thin",
            color: {
              argb: "BEBEBE",
            },
          },
        },
        fill: {
          type: "pattern",
          pattern: "solid",
          fgColor: {
            argb: (backgroundColor ?? "ffffff").replace("#", ""),
          },
        },
        font: {
          color: {
            argb: (foregroundColor ?? "000000").replace("#", ""),
          },
        },
        alignment: {
          vertical: "middle",
          horizontal: "right",
        },
      };
    };

    try {
      setIsExport(true);
      const allData = await fetchAllData();

      const workbook = new Excel.Workbook();
      const worksheet = workbook.addWorksheet("data", {});

      // Insert Header
      const headers = [
        "ID dự án",
        "Tên dự án",
        "Mã outlet",
        "Tên outlet",
        "Vị trí ghi nhận",
        "Ngày",
        "Region",
        "Tỉnh/ TP",
        "Kênh",
      ];

      worksheet.insertRow(2, headers);
      headers.forEach((_, index) => {
        const header = worksheet.getCell(`${numToCol(index + 1)}2`);
        formatHeader(header);
      });

      if (isMergedProduct) {
        reportOOSStatusesMergedProductsQuery.data?.entities.forEach(
          (item, index) => {
            const colIndex = headers.length + index + 1;
            const header = worksheet.getCell(`${numToCol(colIndex)}2`);
            header.value = item.productName;
            formatHeader(header);
          },
        );

        const indexFistProduct = headers.length + 1;
        const cell = worksheet.getCell(`${numToCol(indexFistProduct)}1`);

        worksheet.mergeCells(
          `${numToCol(indexFistProduct)}1:${numToCol(indexFistProduct + (reportOOSStatusesMergedProductsQuery.data?.entities.length ?? 0) - 1)}1`,
        );

        cell.value = "Danh sách sản phẩm gộp";
        formatHeader(cell, { horizontal: "center" });
      } else {
        reportOOSStatusesProductsQuery.data?.entities.forEach((item, index) => {
          const colIndex = headers.length + index + 1;
          const header = worksheet.getCell(`${numToCol(colIndex)}2`);
          header.value = item.projectProduct.product.name;
          formatHeader(header);
        });

        const indexFistProduct = headers.length + 1;
        const cell = worksheet.getCell(`${numToCol(indexFistProduct)}1`);

        worksheet.mergeCells(
          `${numToCol(indexFistProduct)}1:${numToCol(indexFistProduct + (reportOOSStatusesProductsQuery.data?.entities.length ?? 0) - 1)}1`,
        );

        cell.value = "Danh sách sản phẩm";
        formatHeader(cell, { horizontal: "center" });
      }

      allData.forEach(
        (
          { name, code, projectAgencyChannel, province, projectOutletStocks },
          index,
        ) => {
          const rowId = index + 1 + 2;

          const fixedData = [
            projectId,
            project?.name,
            code,
            name,
            projectOutletStocks?.[0]?.featureOosZone?.name ?? "-",
            dayjs(filter.stockDate).endOf("date").toDate(),
            "", // region
            province.name,
            projectAgencyChannel.channel.name,
          ];

          worksheet.insertRow(rowId, fixedData);

          if (isMergedProduct) {
            reportOOSStatusesMergedProductsQuery.data?.entities.forEach(
              (item, index) => {
                const colIndex = headers.length + index + 1;
                const cell = worksheet.getCell(`${numToCol(colIndex)}${rowId}`);
                const projectOutletStockDetail =
                  projectOutletStocks?.[0]?.projectOutletStockDetails?.find(
                    (stock) =>
                      stock.featureOosMergedProduct?.code === item.code,
                  );
                const featureOosLevel =
                  projectOutletStockDetail?.featureOosThreshold
                    ?.featureOosLevel;

                cell.value = projectOutletStockDetail?.quantity ?? "-";

                formatNumberCell(cell, {
                  backgroundColor: featureOosLevel?.backgroundColor,
                  foregroundColor: featureOosLevel?.foregroundColor,
                });
              },
            );
          } else {
            reportOOSStatusesProductsQuery.data?.entities.forEach(
              (item, index) => {
                const colIndex = headers.length + index + 1;
                const cell = worksheet.getCell(`${numToCol(colIndex)}${rowId}`);
                const projectOutletStockDetail =
                  projectOutletStocks?.[0]?.projectOutletStockDetails?.find(
                    (stock) =>
                      stock.featureOosProduct?.projectProductId ===
                      item.projectProductId,
                  );

                const whenKnownSaleViewAndThisNotUpdateBySale =
                  isSale &&
                  howToDisplay === "updatedOnly" &&
                  projectOutletStockDetail &&
                  !projectOutletStocks?.[0]?.finalized;

                cell.value = projectOutletStockDetail?.quantity ?? "-";

                const backgroundColor = NOT_HAVE_VALUE_BG_COLOR;
                const foregroundColor = NOT_HAVE_VALUE_TEXT_COLOR;

                formatNumberCell(cell, {
                  backgroundColor: whenKnownSaleViewAndThisNotUpdateBySale
                    ? NOT_SALE_VALUE_BG_COLOR
                    : backgroundColor,
                  foregroundColor: whenKnownSaleViewAndThisNotUpdateBySale
                    ? NOT_SALE_VALUE_TEXT_COLOR
                    : foregroundColor,
                });
              },
            );
          }
        },
      );

      downloadFile(workbook, componentFeatureQuery.data?.name ?? "");
    } catch (e) {
      console.error(e);
    } finally {
      setIsExport(false);
    }
  }, [
    componentFeatureQuery.data?.name,
    filter,
    getReportOOSStatusesHighlightsMutation,
    howToDisplay,
    isMergedProduct,
    isSale,
    pagination.total,
    project?.name,
    projectId,
    reportOOSStatusesMergedProductsQuery.data?.entities,
    reportOOSStatusesProductsQuery.data?.entities,
  ]);

  useEffect(() => {
    if (
      reportOOSStatusesMergedProductsQuery.isSuccess &&
      reportOOSStatusesProductsQuery.isSuccess
    ) {
      if (isSale) {
        setProductsAndMergedProductsColumn(productsColumns);
      } else {
        setProductsAndMergedProductsColumn(mergedProductsColumns);
      }
    }
  }, [
    isSale,
    mergedProductsColumns,
    productsColumns,
    reportOOSStatusesMergedProductsQuery.isSuccess,
    reportOOSStatusesProductsQuery.isSuccess,
  ]);

  const columns: TableProps<ProjectOutletInterface>["columns"] = useMemo(() => {
    const columns = [
      {
        title: "Outlet",
        dataIndex: "name",
        className: "min-w-[100px]",
        fixed: "left" as unknown as "left",
      },
      // {
      //   title: "Kênh",
      //   dataIndex: "projectAgencyChannel",
      //   key: "projectAgencyChannel",
      //   render: (value: ProjectAgencyChannelInterface) => value?.channel.name,
      //   className: "min-w-[100px]",
      // },
      // {
      //   title: "Tỉnh",
      //   dataIndex: "province",
      //   key: "province",
      //   render: renderTableCell,
      //   className: "min-w-[100px]",
      // },
      {
        title: "Vị trí",
        dataIndex: "projectOutletStocks",
        className: "min-w-[100px]",
        render: (projectOutletStocks: ProjectOutletStockInterface[]) =>
          projectOutletStocks?.[0]?.featureOosZone?.name ?? "-",
      },
      // {
      //   title: "Ngày",
      //   render: () => dayjs(filter.stockDate).format("DD/MM/YYYY"),
      // },
      ...(Array.isArray(productsAndMergedProductsColumn)
        ? productsAndMergedProductsColumn
        : []),
    ];

    // If this is the sales report and today is the same as the date we're
    // filtering for, then add a column with an edit button.
    // This is because we want to allow users to edit the sale data only for today.
    if (isSale && dayjs(date).isSame(dayjs(), "date")) {
      columns.push({
        className: "!py-0",
        align: "end",
        render: (record: ProjectOutletInterface) => (
          <Button
            type="default"
            className="bg-[#ECEDEF]"
            icon={<EditOutlined />}
            onClick={() => setSelectedProjectOutlet(record)}
          />
        ),
      });
    }

    return columns;
  }, [date, isSale, productsAndMergedProductsColumn]);

  return (
    <>
      <div>
        <h2>{componentFeatureQuery.data?.name}</h2>
        <InnerContainer>
          <FilterReportZone
            form={filterForm}
            loading={loading}
            fields={[
              "keyword",
              "outOfStockStatus.channelId",
              !isSale ? undefined : "outOfStockStatus.howToDisplay",
              "outOfStockStatus.date",
              isSale ? undefined : "outOfStockStatus.isMergedProduct",
            ]}
            onFinish={onFilterFormFinish}
            placeholder="Tìm theo tên, mã outlet"
            hideAdvancedFilter
            onExport={onExport}
          />

          <CustomTable
            className="mt-3"
            bordered
            rowKey={"id"}
            pagination={pagination}
            dataSource={reportOOSStatusesHighlightsQuery.data?.entities ?? []}
            scroll={{
              x: "max-content",
              y: pagination.total ? "80vh" : undefined,
            }}
            columns={columns}
          />
        </InnerContainer>
      </div>

      {isSale && selectedProjectOutlet && (
        <ReportOutOfStockStatusHighlightModal
          projectId={projectId}
          componentFeatureId={componentFeatureId}
          projectOutlet={selectedProjectOutlet}
          cancelCb={() => setSelectedProjectOutlet(null)}
          cb={() => {
            reportOOSStatusesHighlightsQuery.refetch();
            setSelectedProjectOutlet(null);
          }}
        />
      )}
    </>
  );
};

export default ReportOutOfStockStatusHighlightPage;
