import { stringIncludes } from "@/common/helper";
import { DownOutlined, SearchOutlined } from "@ant-design/icons";
import { Button, Checkbox, Col, Input, Popover, Row, Space } from "antd";
import { useCallback, useEffect, useMemo, useState } from "react";

interface PopoverCheckboxProps {
  options: { label: string; value: number; hidden?: boolean }[];
  buttonTitle: string;
  title: string;
  placeholder: string;
  onOkeCb: (values: number[]) => void;
}

const PopoverCheckbox = ({
  options,
  buttonTitle,
  title,
  placeholder,
  onOkeCb,
}: PopoverCheckboxProps) => {
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedOptions, setSelectedOptions] = useState<number[]>([]);
  const [applySelectedOptions, setApplySelectedOptions] = useState<number[]>(
    [],
  );
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");

  const debouncedSetSearchTerm = useCallback((value: string) => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(value);
    }, 300);

    return () => clearTimeout(timer);
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const value = e.target.value;
    setSearchTerm(value);
    debouncedSetSearchTerm(value);
  };

  const hide = useCallback(() => {
    setOpen(false);
  }, []);

  const handleOpenChange = useCallback(() => {
    setOpen(true);
  }, []);

  const filteredOptions = useMemo(() => {
    for (const option of options) {
      if (!stringIncludes(option.label, debouncedSearchTerm)) {
        option.hidden = true;
      } else {
        option.hidden = false;
      }
    }

    return options;
  }, [debouncedSearchTerm, options]);

  const onOk = useCallback(() => {
    setApplySelectedOptions(selectedOptions);
    onOkeCb(selectedOptions);
    setOpen(false);
  }, [onOkeCb, selectedOptions]);

  useEffect(() => {
    if (open) {
      setSelectedOptions(applySelectedOptions);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open]);

  const content = (
    <>
      <div className="p-6">
        <p className="pt-0 mt-0 text-[18px] font-semibold pb-0 mb-4">{title}</p>
        <Input
          placeholder={placeholder}
          className="mb-4"
          prefix={<SearchOutlined />}
          allowClear
          value={searchTerm}
          onChange={handleChange}
        />
        <div className="max-h-64 overflow-y-auto">
          <Checkbox
            className="mb-2"
            indeterminate={
              selectedOptions.length > 0 &&
              selectedOptions.length < options.length
            }
            onChange={(e) => {
              const checked = e.target.checked;
              if (checked) {
                setSelectedOptions(filteredOptions.map((item) => item.value));
              } else {
                setSelectedOptions([]);
              }
            }}
          >
            Tất cả
          </Checkbox>

          <Checkbox.Group
            style={{ width: "100%", display: "flex", flexDirection: "column" }}
            value={selectedOptions}
            onChange={setSelectedOptions}
          >
            {filteredOptions.map(({ label, value, hidden: disabled }) => {
              return (
                <Row
                  key={value}
                  className={`w-full mb-2 ${disabled ? "hidden" : ""}`}
                >
                  <Col>
                    <Checkbox value={value}>{label}</Checkbox>
                  </Col>
                </Row>
              );
            })}
          </Checkbox.Group>
        </div>
      </div>
      <div className="w-full h-[1.5px] bg-[#DDE1EA]"></div>
      <div className="bg-[#F7F8FA] rounded-b pl-6 pr-6 text-end pt-4 pb-4">
        <Space>
          <Button type="default" onClick={hide}>
            Đóng
          </Button>
          <Button type="primary" onClick={onOk}>
            Áp dụng
          </Button>
        </Space>
      </div>
    </>
  );

  return (
    <Popover
      content={content}
      trigger="click"
      arrow={false}
      placement="bottomRight"
      open={open}
      onOpenChange={handleOpenChange}
      overlayInnerStyle={{
        padding: 0,
        margin: 0,
        width: "300px",
      }}
    >
      <Button>
        <span className="mr-2">{buttonTitle}</span>
        <DownOutlined />
      </Button>
    </Popover>
  );
};

export default PopoverCheckbox;
