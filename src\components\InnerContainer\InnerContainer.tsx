import useDeviceType from "@/hooks/useDeviceType";
import _ from "lodash";

interface InnerContainerProps {
  children: React.ReactNode;
  className?: string;
}

const InnerContainer = ({ children, className }: InnerContainerProps) => {
  const isMobile = useDeviceType();

  return (
    <div
      className={[
        "bg-white rounded",
        className ?? "",
        isMobile ? "py-5 px-2" : "p-10",
      ]
        .filter((item) => !_.isEmpty(item))
        .join(" ")}
    >
      {children}
    </div>
  );
};
export default InnerContainer;
