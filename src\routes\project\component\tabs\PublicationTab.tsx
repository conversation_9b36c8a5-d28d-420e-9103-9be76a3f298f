import { useApp } from "@/UseApp";
import { DEFAULT_CURRENT_PAGE, DEFAULT_PAGE_SIZE } from "@/common/constant";
import { formatDateTime } from "@/common/helper";
import CustomTable from "@/components/CustomTable/CustomTable";
import FilterComponent from "@/components/FilterComponent";
import TableActionCell from "@/components/TableActionCell";
import renderStatus from "@/components/renderStatus.tsx";
import { CloseOutlined, FileSearchOutlined } from "@ant-design/icons";
import { Alert, Button, Form, Modal, Space } from "antd";
import _ from "lodash";
import React, { useCallback, useMemo, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useProjectReportComponentsQuery } from "../../report/service";
import {
  ProjectComponentBoothInterface,
  ProjectComponentPublicationInterface,
} from "../interface";
import {
  useCreateProjectComponentsPublicationsMutation,
  useProjectComponentsPublicationsQuery,
  useProjectComponentsQuery,
} from "../service";

export default function PublicationTab() {
  const projectId = parseInt(useParams().id ?? "0");
  const { showNotification } = useApp();
  const navigate = useNavigate();

  const [searchForm] = Form.useForm();
  const [isOpen, setIsOpen] = useState(false);
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
  const [filter, setFilter] = useState({});
  const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);

  const projectComponentsPublicationsQuery =
    useProjectComponentsPublicationsQuery(projectId, {
      ...filter,
      take: pageSize,
      skip: (currentPage - 1) * pageSize,
    });
  const projectComponentsQuery = useProjectComponentsQuery(projectId);
  const projectReportComponentsQuery =
    useProjectReportComponentsQuery(projectId);

  const createProjectComponentsPublicationsMutation =
    useCreateProjectComponentsPublicationsMutation(projectId);

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys: selectedKeys,
    onChange: onSelectChange,
  };

  const onModalClose = useCallback(() => {
    setIsOpen(false);
    setSelectedKeys([]);
  }, []);

  const onModalSubmitClick = useCallback(async () => {
    if (isOpen && selectedKeys.length) {
      await createProjectComponentsPublicationsMutation.mutateAsync({
        projectComponentIds: selectedKeys.map((key) => Number(key)),
      });

      showNotification({
        type: "success",
        message: `Phát hành ${selectedKeys.length} chức năng thành công`,
      });
      await projectComponentsPublicationsQuery.refetch();
      projectReportComponentsQuery.refetch();
      onModalClose();
    }
  }, [
    createProjectComponentsPublicationsMutation,
    isOpen,
    onModalClose,
    projectComponentsPublicationsQuery,
    projectReportComponentsQuery,
    selectedKeys,
    showNotification,
  ]);

  const handleActionViewClick = useCallback(
    (record: ProjectComponentPublicationInterface) => {
      navigate(`/project/${projectId}/component/${record.id}`, {
        state: record,
      });
    },
    [navigate, projectId],
  );

  const pagination = useMemo(() => {
    return {
      current: currentPage,
      total: projectComponentsPublicationsQuery.data?.count ?? 0,
      pageSize: pageSize,
      onChange: (page: number) => {
        setCurrentPage(page);
      },
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: (_current: number, size: number) => {
        setPageSize(size);
        setCurrentPage(DEFAULT_CURRENT_PAGE);
      },
    };
  }, [currentPage, pageSize, projectComponentsPublicationsQuery.data?.count]);

  return (
    <>
      <FilterComponent
        searchHandler={function (): void {
          const values = searchForm.getFieldsValue();
          if (_.isEqual(values, filter)) {
            projectComponentsPublicationsQuery.refetch();
          }
          setFilter({
            ...values,
          });
        }}
        searchForm={searchForm}
        filterOptions={[
          {
            label: "Tất cả",
            value: "all",
          },
          {
            label: "Tên nhóm chức năng",
            value: "name",
          },
          {
            label: "Loại booth áp dụng",
            value: "boothName",
          },
        ]}
        handleAddButtonClick={function (): void {
          setIsOpen(true);
        }}
        btnAddText="Thêm bản phát hành"
        className={"mb-6"}
      />

      <CustomTable
        rowKey={"id"}
        dataSource={projectComponentsPublicationsQuery.data?.entities}
        pagination={pagination}
        loading={
          projectComponentsPublicationsQuery.isLoading ||
          projectComponentsPublicationsQuery.isFetching ||
          projectComponentsPublicationsQuery.isRefetching
        }
        columns={[
          {
            title: "Mã",
            dataIndex: "code",
            align: "center",
            className: "min-w-[100px]",
          },
          {
            title: "Tên nhóm chức năng",
            dataIndex: "name",
            onCell: (record) => {
              return {
                onClick: () => {
                  handleActionViewClick(record);
                },
              };
            },
            className: "min-w-[100px]",
          },
          {
            title: "Loại booth áp dụng",
            dataIndex: "projectComponentBooths",
            render: (
              projectComponentBooths: ProjectComponentBoothInterface[],
            ) => {
              return (
                <ul>
                  {projectComponentBooths.map((projectComponentBooth) => (
                    <li key={projectComponentBooth.id}>
                      {projectComponentBooth.projectBooth.name}
                    </li>
                  ))}
                </ul>
              );
            },
            className: "min-w-[100px]",
          },
          {
            title: "Số chức năng",
            dataIndex: "projectFeaturesCount",
            render: (value: number) => {
              return value;
            },
            align: "right",
            className: "min-w-[100px]",
          },
          {
            title: "Thời gian phát hành",
            dataIndex: "publishedAt",
            render: (value) => formatDateTime(value),
            className: "min-w-[100px]",
          },
          {
            title: "Tình trạng bản phát hành",
            dataIndex: "isLatest",
            render: (isLatest) => {
              return isLatest
                ? renderStatus("Bản mới nhất", "#008916", "#E5F5E7")
                : "";
            },
            className: "min-w-[100px]",
          },
          {
            render: (_, record) => (
              <TableActionCell
                actions={[
                  {
                    key: "view",
                    action: handleActionViewClick,
                  },
                ]}
                items={[
                  {
                    key: "view",
                    label: "Chức năng tại booth",
                    icon: <FileSearchOutlined />,
                  },
                ]}
                record={record}
              />
            ),
          },
        ]}
      />

      <p className={"pb-0 mb-0"}>
        Số kết quả trả về: {projectComponentsPublicationsQuery.data?.count ?? 0}
      </p>

      <Modal
        open={isOpen}
        footer={null}
        closeIcon={null}
        width={870}
        styles={{ content: { padding: 0 } }}
      >
        <div className="pl-10 pr-10 pt-3 pb-5">
          <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
            <h2 className="text-neutral-700 text-2xl font-semibold ">
              Chọn tên nhóm chức năng để phát hành
            </h2>
            <div className="pt-5">
              <Button
                type="link"
                onClick={onModalClose}
                size="large"
                icon={<CloseOutlined />}
              />
            </div>
          </div>
          <div className="inline-block">
            <Alert
              type="info"
              message="Nhóm chức năng sau khi được phát hành sẽ khả dụng trên app của nhân viên field"
            />
          </div>

          <CustomTable
            className="mt-5"
            rowKey={"id"}
            dataSource={projectComponentsQuery.data?.entities}
            rowSelection={rowSelection}
            columns={[
              {
                title: "Mã",
                dataIndex: "code",
              },
              {
                title: "Tên nhóm chức năng",
                dataIndex: "name",
              },
              {
                title: "Loại booth áp dụng",
                dataIndex: "projectComponentBooths",
                render: (
                  projectComponentBooths: ProjectComponentBoothInterface[],
                ) => {
                  return (
                    <ul>
                      {projectComponentBooths.map((projectComponentBooth) => (
                        <li key={projectComponentBooth.id}>
                          {projectComponentBooth.projectBooth.name}
                        </li>
                      ))}
                    </ul>
                  );
                },
              },
            ]}
          />
        </div>
        <div className="flex justify-end pb-4 pt-4 bg-[#F7F8FA]">
          <Space className="pr-10">
            <Button onClick={onModalClose}>Đóng</Button>
            <Button
              type={"primary"}
              disabled={!selectedKeys.length}
              onClick={onModalSubmitClick}
              loading={createProjectComponentsPublicationsMutation.isPending}
            >
              Phát hành {selectedKeys.length} nhóm chức năng
            </Button>
          </Space>
        </div>
      </Modal>
    </>
  );
}
