import { Col, Row } from "antd";
import _ from "lodash";
import { getImageVariants } from "@/common/image.helper.ts";
import ImagesGrid from "@/components/ImagesGrid.tsx";
import { RecordOrderPhotoInterface } from "./interface";

const CustomerPhotosCell = ({
  orderPhotos,
}: {
  orderPhotos: RecordOrderPhotoInterface[];
}) => {
  if (orderPhotos.length === 0) return <></>;

  const group = _.groupBy(
    orderPhotos.filter((orderPhoto) => orderPhoto.recordPhoto),
    (o) => o?.recordPhoto?.featurePhoto?.id,
  );

  let divKey = 0;

  return (
    <>
      {Object.values(group).map((groupedPhotos) => (
        <Row key={++divKey} className="pb-2">
          <Col md={8} className="min-w-10 text-wrap ">
            {groupedPhotos[0].recordPhoto?.featurePhoto?.name}:
          </Col>
          <Col className="">
            <ImagesGrid
              key={++divKey}
              images={groupedPhotos.map((photo) => ({
                thumbnail: getImageVariants(
                  photo.recordPhoto?.image?.variants ?? [],
                  "thumbnail",
                ),
                preview: getImageVariants(
                  photo.recordPhoto?.image?.variants ?? [],
                  "public",
                ),
                alt: "Image 1",
              }))}
              maxImagesPerRow={3}
            />
          </Col>
        </Row>
      ))}
    </>
  );
};

export default CustomerPhotosCell;
