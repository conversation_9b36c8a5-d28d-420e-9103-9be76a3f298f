import FormNumberInput from "@/components/FormNumberInput";
import { Col, Form, Row } from "antd";
import { OosLevelInterface } from "../../interface";

interface ThresholdSectionProps {
  oosLevel: OosLevelInterface;
}

const ThresholdSection = ({ oosLevel }: ThresholdSectionProps) => {
  return (
    <div className="space-y-2 mb-5">
      <label className="text-sm font-medium">{oosLevel.name}</label>
      <Row>
        <Col md={5}>
          <Form.Item noStyle name={`${oosLevel.id}.upperValue`}>
            <FormNumberInput className="w-full" />
          </Form.Item>
        </Col>

        <Col
          md={2}
          className="rounded-md border-[#ECEDEF] border border-solid flex items-center justify-center"
          offset={1}
        >
          <span className="text-lg">&#8805;</span>
        </Col>
        <Col
          md={3}
          offset={1}
          className="bg-[#F0F8FF] text-blue rounded-md border-blue border border-solid  text-center"
        >
          <div className="px-4 py-1.5">Stock</div>
        </Col>
        <Col
          md={2}
          className="rounded-md border-[#ECEDEF] border border-solid flex items-center justify-center"
          offset={1}
        >
          <span className="text-lg">&#8805;</span>
        </Col>
        <Col md={5} offset={1}>
          <Form.Item noStyle name={`${oosLevel.id}.lowerValue`}>
            <FormNumberInput className="w-full" />
          </Form.Item>
        </Col>
      </Row>
    </div>
  );
};

export default ThresholdSection;
