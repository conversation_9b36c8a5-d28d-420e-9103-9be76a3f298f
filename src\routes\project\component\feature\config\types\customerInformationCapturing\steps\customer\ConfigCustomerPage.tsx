import { useApp } from "@/UseApp";
import { CURD } from "@/common/constant";
import { formErrorResponseHandler } from "@/common/helper";
import DragSortRowComponent from "@/components/DragSortRowComponent.tsx";
import ModalCURD from "@/components/ModalCURD";
import TableActionCell from "@/components/TableActionCell";
import { renderTableCell } from "@/components/table-cell";
import {
  DeleteOutlined,
  EditOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import { DndContext, DragEndEvent } from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
  arrayMove,
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { UseQueryResult } from "@tanstack/react-query";
import {
  Al<PERSON>,
  Button,
  Checkbox,
  Col,
  Form,
  Input,
  Modal,
  Radio,
  Row,
  Space,
  Switch,
  Table,
} from "antd";
import { useCallback, useEffect, useState } from "react";
import { useOutletContext, useParams } from "react-router-dom";
import StepLockPage from "../../StepLockPage";
import {
  ConfigCustomerEnum,
  FeatureCustomerActionEnumToLabel,
  FeatureCustomerDataTypeEnum,
  FeatureCustomerInterface,
  OrderInterface,
  StepLockEnum,
} from "../../interface";
import { useUpdateStepStatusMutation } from "../../service.ts";
import {
  useArrangementFeatureCustomerMutation,
  useCreateFeatureCustomerMutation,
  useDeleteFeatureCustomerMutation,
  useFeatureCustomersQuery,
  useUpdateFeatureCustomerMutation,
} from "./service";
import "./style.css";

export default function ConfigCustomerPage() {
  const [orderLatestQuery]: [UseQueryResult<OrderInterface, unknown>] =
    useOutletContext();
  const componentFeatureId = parseInt(useParams().componentFeatureId ?? "0");
  const { showNotification } = useApp();

  const [form] = Form.useForm();
  const [isOpen, setIsOpen] = useState(false);
  const [action, setAction] = useState<CURD | null>(null);
  const [modal, contextHolder] = Modal.useModal();
  const [hasOtp, setHasOtp] = useState(false);
  const [onlyForNewCustomer, setOnlyForNewCustomer] = useState(false);
  const [skipOtp, setSkipOtp] = useState(false);
  const dataType = Form.useWatch("dataType", form);

  const featureCustomersQuery = useFeatureCustomersQuery(componentFeatureId);
  const createFeatureCustomerMutation =
    useCreateFeatureCustomerMutation(componentFeatureId);
  const updateFeatureCustomerMutation =
    useUpdateFeatureCustomerMutation(componentFeatureId);
  const deleteFeatureCustomerMutation =
    useDeleteFeatureCustomerMutation(componentFeatureId);
  const arrangementFeatureCustomerMutation =
    useArrangementFeatureCustomerMutation(componentFeatureId);
  const updateStepStatusMutation =
    useUpdateStepStatusMutation(componentFeatureId);

  const [dataSource, setDataSource] = useState(
    featureCustomersQuery.data?.entities ?? [],
  );

  useEffect(() => {
    setDataSource(featureCustomersQuery.data?.entities ?? []);
  }, [featureCustomersQuery.data?.entities]);

  const onFinish = useCallback(async () => {
    if (action === CURD.CREATE) {
      const data = form.getFieldsValue();

      if (dataType === FeatureCustomerDataTypeEnum.PHONE_NUMBER && hasOtp) {
        data.verification = {
          isRequired: !skipOtp,
          mode: onlyForNewCustomer ? "new_customer_only" : "all_customers",
        };
      }

      try {
        await createFeatureCustomerMutation.mutateAsync(data);

        showNotification({
          type: "success",
          message: `Thêm thông tin cần ghi nhận thành công`,
        });
        setIsOpen(false);
        form.resetFields();
        await featureCustomersQuery.refetch();
        setAction(null);

        setHasOtp(false);
        setOnlyForNewCustomer(false);
        setSkipOtp(false);
      } catch (e) {
        formErrorResponseHandler(form, e);
      }
    }

    if (action === CURD.UPDATE) {
      const data = {
        id: form.getFieldValue("id"),
        name: form.getFieldValue("name"),
        description: form.getFieldValue("description"),
        verification: null as { isRequired: boolean; mode: string } | null,
      };
      try {
        if (dataType === FeatureCustomerDataTypeEnum.PHONE_NUMBER && hasOtp) {
          data.verification = {
            isRequired: !skipOtp,
            mode: onlyForNewCustomer ? "new_customer_only" : "all_customers",
          };
        }

        await updateFeatureCustomerMutation.mutateAsync(data);

        showNotification({
          type: "success",
          message: `Cập nhật thông tin cần ghi nhận thành công`,
        });
        setIsOpen(false);
        form.resetFields();
        await featureCustomersQuery.refetch();
        setAction(null);

        setHasOtp(false);
        setOnlyForNewCustomer(false);
        setSkipOtp(false);
      } catch (e) {
        formErrorResponseHandler(form, e);
      }
    }
  }, [
    action,
    createFeatureCustomerMutation,
    dataType,
    featureCustomersQuery,
    form,
    hasOtp,
    onlyForNewCustomer,
    showNotification,
    skipOtp,
    updateFeatureCustomerMutation,
  ]);

  const onClick = useCallback(() => {
    setAction(CURD.CREATE);
    setIsOpen(true);
  }, []);

  const formContent = (
    <>
      <Alert
        message="Thông tin được khởi tạo đầu tiên sẽ được chọn làm trường định danh cho khách hàng. Hãy khai báo thông tin có tính duy nhất như SĐT, CMND... làm trường định danh."
        type="info"
        showIcon
      />
      <Form.Item name={"id"} hidden>
        <Input hidden />
      </Form.Item>

      <Form.Item
        className="pb-0 mb-0 mt-3"
        label="Tên thông tin cần ghi nhận"
        name={"name"}
        rules={[
          {
            required: true,
            message: "Tên thông tin cần ghi nhận không được bỏ trống",
          },
        ]}
      >
        <Input />
      </Form.Item>

      <Form.Item dependencies={["isIdentity"]} noStyle>
        {() => (
          <Form.Item
            className="mt-0 pt-0"
            valuePropName="checked"
            name={"isRequired"}
          >
            <Checkbox
              disabled={
                action === CURD.UPDATE || form.getFieldValue("isIdentity")
              }
            >
              Bắt buộc nhập
            </Checkbox>
          </Form.Item>
        )}
      </Form.Item>

      <Form.Item dependencies={["isIdentity"]} noStyle>
        {() => (
          <Form.Item label="Loại data" name={"dataType"}>
            <Radio.Group disabled={action === CURD.UPDATE}>
              <Space direction="vertical">
                <Radio value={FeatureCustomerDataTypeEnum.PHONE_NUMBER}>
                  {
                    FeatureCustomerActionEnumToLabel[
                      FeatureCustomerDataTypeEnum.PHONE_NUMBER
                    ]
                  }
                  {dataType === FeatureCustomerDataTypeEnum.PHONE_NUMBER && (
                    <div>
                      <Checkbox
                        checked={hasOtp}
                        onChange={(e) => setHasOtp(e.target.checked)}
                      >
                        Xác minh thông qua SMS OTP
                      </Checkbox>
                      <div className="pl-5">
                        <Checkbox
                          disabled={!hasOtp}
                          onChange={(e) =>
                            setOnlyForNewCustomer(e.target.checked)
                          }
                          checked={onlyForNewCustomer}
                        >
                          Chỉ gửi OTP cho khách mới
                        </Checkbox>
                        <br />
                        <Checkbox
                          disabled={!hasOtp}
                          onChange={(e) => setSkipOtp(e.target.checked)}
                          checked={skipOtp}
                        >
                          Cho phép bỏ qua OTP
                        </Checkbox>
                      </div>
                    </div>
                  )}
                </Radio>

                <Radio value={FeatureCustomerDataTypeEnum.STRING}>
                  {
                    FeatureCustomerActionEnumToLabel[
                      FeatureCustomerDataTypeEnum.STRING
                    ]
                  }
                </Radio>
                <Radio value={FeatureCustomerDataTypeEnum.NUMBER}>
                  {
                    FeatureCustomerActionEnumToLabel[
                      FeatureCustomerDataTypeEnum.NUMBER
                    ]
                  }
                </Radio>
                <Radio value={FeatureCustomerDataTypeEnum.EMAIL}>
                  {
                    FeatureCustomerActionEnumToLabel[
                      FeatureCustomerDataTypeEnum.EMAIL
                    ]
                  }
                </Radio>
                <Radio
                  value={FeatureCustomerDataTypeEnum.RADIO}
                  disabled={form.getFieldValue("isIdentity")}
                >
                  {
                    FeatureCustomerActionEnumToLabel[
                      FeatureCustomerDataTypeEnum.RADIO
                    ]
                  }
                </Radio>
              </Space>
            </Radio.Group>
          </Form.Item>
        )}
      </Form.Item>

      <Form.Item dependencies={["dataType"]} className="ml-5">
        {() => (
          <>
            {form.getFieldValue("dataType") ===
              FeatureCustomerDataTypeEnum.RADIO && (
              <Form.List name="options">
                {(fields, { add, remove }) => (
                  <>
                    {fields.map(({ key, name: index, ...restField }) => {
                      return (
                        <Row key={key} justify={"space-between"}>
                          <Col md={21}>
                            <Form.Item
                              {...restField}
                              label={`Tùy chọn ${index + 1}`}
                              name={[index, "name"]}
                              rules={[
                                {
                                  required: true,
                                  message: "Tùy chọn là bắt buộc",
                                },
                              ]}
                              style={{ width: "100%" }}
                            >
                              <Input disabled={action === CURD.UPDATE} />
                            </Form.Item>
                          </Col>

                          <Col className="flex items-center" md={1}>
                            <Button
                              icon={<DeleteOutlined />}
                              onClick={() => remove(index)}
                              disabled={action === CURD.UPDATE}
                              type="link"
                            ></Button>
                          </Col>
                        </Row>
                      );
                    })}

                    <Form.Item>
                      <Button
                        disabled={action === CURD.UPDATE}
                        type="link"
                        onClick={() => add()}
                        className="text-[#1D8EE6]"
                      >
                        Thêm tùy chọn
                      </Button>
                    </Form.Item>
                  </>
                )}
              </Form.List>
            )}
          </>
        )}
      </Form.Item>

      <Form.Item dependencies={["dataType"]} noStyle>
        {() => (
          <Form.Item
            extra="Trường định danh sẽ được nhân viên nhập đầu tiên để kiểm tra thông tin của khách hàng đã tồn tại trên hệ thống hay chưa."
            valuePropName="checked"
            name={"isIdentity"}
          >
            <Checkbox
              disabled={
                action === CURD.UPDATE ||
                form.getFieldValue("dataType") ==
                  FeatureCustomerDataTypeEnum.RADIO
              }
              onChange={(value) => {
                if (value.target.checked) {
                  form.setFieldsValue({ isRequired: true });
                }
              }}
            >
              Trường định danh
            </Checkbox>
          </Form.Item>
        )}
      </Form.Item>
    </>
  );
  const handleActionEditClick = useCallback(
    (record: FeatureCustomerInterface) => {
      setIsOpen(true);
      setAction(CURD.UPDATE);
      form.setFieldsValue({
        id: record.id,
        name: record.name,
        isRequired: record.isRequired,
        dataType: record.dataType,
        isIdentity: record.isIdentity,
        options: record.featureCustomerOptions,
      });
      setHasOtp(!!record.featureCustomerVerification);
      setSkipOtp(!record.featureCustomerVerification?.isRequired);
      setOnlyForNewCustomer(
        record.featureCustomerVerification?.mode === "new_customer_only",
      );
    },
    [form],
  );

  const handleActionInActiveClick = useCallback(
    async (record: FeatureCustomerInterface) => {
      modal.confirm({
        title: `Ngừng hoạt động thông tin: ${record.name}`,
        content: `Bạn có chắc chắn muốn ngừng hoạt động thông tin ${record.name} này?`,
        okText: "Ngừng hoạt động",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await updateFeatureCustomerMutation.mutateAsync({
              id: record.id,
              isActive: false,
            });

            showNotification({
              type: "success",
              message: `Ngừng hoạt động thông tin ${record.name} thành công`,
            });

            await featureCustomersQuery.refetch();
          } catch (error) {
            console.error(error);

            showNotification({
              type: "error",
              message: `Ngừng hoạt động thông tin ${record.name} thất bại`,
            });
          }
        },
      });
    },
    [
      featureCustomersQuery,
      modal,
      showNotification,
      updateFeatureCustomerMutation,
    ],
  );

  const handleActionActiveClick = useCallback(
    (record: FeatureCustomerInterface) => {
      modal.confirm({
        title: `Kích hoạt thông tin: ${record.name}`,
        content: `Bạn có chắc chắn muốn kích hoạt thông tin ${record.name} này?`,
        okText: "Kích hoạt",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await updateFeatureCustomerMutation.mutateAsync({
              id: record.id,
              isActive: true,
            });

            showNotification({
              type: "success",
              message: `Kích hoạt thông tin ${record.name} thành công`,
            });

            await featureCustomersQuery.refetch();
          } catch (error) {
            console.error(error);

            showNotification({
              type: "error",
              message: `Kích hoạt thông tin ${record.name} thất bại`,
            });
          }
        },
      });
    },
    [
      featureCustomersQuery,
      modal,
      showNotification,
      updateFeatureCustomerMutation,
    ],
  );

  const handleActionDeleteClick = useCallback(
    (record: FeatureCustomerInterface) => {
      modal.confirm({
        title: `Xóa thông tin: ${record.name}`,
        content: `Bạn có chắc chắn muốn xóa thông tin ${record.name} này?`,
        okText: "Xóa",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await deleteFeatureCustomerMutation.mutateAsync(record.id);

            showNotification({
              type: "success",
              message: `Xóa thông tin ${record.name} thành công`,
            });

            await featureCustomersQuery.refetch();
          } catch (error) {
            console.error(error);

            showNotification({
              type: "error",
              message: `Xóa thông tin ${record.name} thất bại`,
            });
          }
        },
      });
    },
    [
      deleteFeatureCustomerMutation,
      featureCustomersQuery,
      modal,
      showNotification,
    ],
  );

  const actions = [
    {
      key: ConfigCustomerEnum.EDIT,
      action: handleActionEditClick,
    },
    {
      key: ConfigCustomerEnum.ACTIVE,
      action: handleActionActiveClick,
    },
    {
      key: ConfigCustomerEnum.INACTIVE,
      action: handleActionInActiveClick,
    },
    {
      key: ConfigCustomerEnum.DELETE,
      action: handleActionDeleteClick,
    },
  ];

  const onDragEnd = useCallback(
    async ({ active, over }: DragEndEvent) => {
      if (!over) {
        return;
      }
      if (active.id !== over?.id) {
        setDataSource((previous) => {
          const activeIndex = previous?.findIndex((i) => i.id === active.id);
          const overIndex = previous?.findIndex((i) => i.id === over?.id);
          return arrayMove(previous, activeIndex, overIndex);
        });

        await arrangementFeatureCustomerMutation.mutateAsync({
          id: active.id as number,
          overFeatureCustomerId: over?.id as number,
        });
      }
    },
    [arrangementFeatureCustomerMutation],
  );

  const onRequiredCustomerSwitchChange = useCallback(
    async (value: boolean) => {
      updateStepStatusMutation
        .mutateAsync({
          isCustomerRequired: !value,
        })
        .then(() => {
          orderLatestQuery.refetch();
        });
    },
    [orderLatestQuery, updateStepStatusMutation],
  );

  if (!orderLatestQuery.data?.hasCustomer) {
    return (
      <StepLockPage
        title="Chức năng ghi nhận thông tin khách"
        description="Chức năng cho phép cấu hình các trường thông tin cần thiết khi ghi nhận thông tin khách trong đơn hàng"
        type={StepLockEnum.Customer}
        orderLatestQuery={orderLatestQuery}
        locked={!orderLatestQuery.data?.hasCustomer}
      />
    );
  }

  return (
    <>
      <Row justify={"space-between"} align={"middle"}>
        <Col>
          <span className="mr-4">Cho phép skip trên app</span>
          <Switch
            value={!orderLatestQuery.data.isCustomerRequired}
            loading={updateStepStatusMutation.isPending}
            onChange={onRequiredCustomerSwitchChange}
          />
        </Col>
        <Col>
          <Button icon={<PlusOutlined />} type="primary" onClick={onClick}>
            Thêm mới
          </Button>
        </Col>
      </Row>

      <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
        <SortableContext
          items={dataSource.map((i) => i.id)}
          strategy={verticalListSortingStrategy}
        >
          <Table
            dataSource={dataSource}
            loading={
              featureCustomersQuery.isFetching ||
              featureCustomersQuery.isPending ||
              arrangementFeatureCustomerMutation.isPending
            }
            pagination={false}
            rowKey={"id"}
            className="mt-3"
            components={{
              body: {
                row: DragSortRowComponent,
              },
            }}
            columns={[
              {
                key: "sort",
              },
              {
                title: "Thông tin cần ghi nhận",
                dataIndex: "name",
                render: (name: string, record: FeatureCustomerInterface) => (
                  <>
                    {name}
                    &nbsp;&nbsp;
                    {record.isIdentity && (
                      <span className="text-blue bg-[#EEF4FF] rounded-full p-1">
                        Trường định danh
                      </span>
                    )}
                    {record.featureCustomerVerification && (
                      <>
                        &nbsp;
                        <span className="text-blue bg-[#EEF4FF] rounded-full p-1">
                          SMS OTP
                        </span>
                      </>
                    )}
                  </>
                ),
              },
              {
                title: "Kiểu data",
                dataIndex: "dataType",
                render: (value: FeatureCustomerDataTypeEnum) =>
                  FeatureCustomerActionEnumToLabel[value] ?? "",
              },
              {
                title: "Ràng buộc",
                dataIndex: "isRequired",
                render: (isRequired: boolean) =>
                  isRequired ? (
                    <p className="text-main-color bg-[#FFEEEE] rounded-3xl inline-block text-sm p-0 m-0 pl-2 pr-2">
                      Bắt buộc nhập
                    </p>
                  ) : (
                    <p className="bg-[#F5F5F5] rounded-3xl inline-block text-sm p-0 m-0 pl-2 pr-2">
                      Không bắt buộc nhập
                    </p>
                  ),
              },
              {
                title: "Tình trạng",
                dataIndex: "isActive",
                render: (value, record, index) => {
                  return renderTableCell(value, record, index, "isActive");
                },
              },
              {
                render: (_, record) => (
                  <TableActionCell
                    actions={actions}
                    items={[
                      {
                        key: ConfigCustomerEnum.EDIT,
                        label: "Chỉnh sửa",
                        icon: <EditOutlined />,
                      },
                      record.isActive
                        ? {
                            key: ConfigCustomerEnum.INACTIVE,
                            label: "Ngừng hoạt động",
                            icon: <PauseCircleOutlined />,
                          }
                        : {
                            key: ConfigCustomerEnum.ACTIVE,
                            label: "Kích hoạt",
                            icon: <PlayCircleOutlined />,
                          },
                      {
                        key: ConfigCustomerEnum.DELETE,
                        label: "Xóa khỏi chức năng",
                        icon: <DeleteOutlined />,
                      },
                    ]}
                    record={record}
                  />
                ),
              },
            ]}
          />
        </SortableContext>
      </DndContext>

      <ModalCURD
        title={"Thêm thông tin cần ghi nhận"}
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        formContent={formContent}
        form={form}
        onFinish={onFinish}
        action={action}
        btnConfirmLoading={createFeatureCustomerMutation.isPending}
      />

      {contextHolder}
    </>
  );
}
