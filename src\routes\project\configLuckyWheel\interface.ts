import { AbstractEntityInterface } from "@/common/interface";
import { ItemInterface } from "@/routes/item/interface";
import { ProjectItemInterface } from "../item/interface";
import { ProjectOutletInterface } from "../outlet/interface";

export interface ProjectLuckyDrawInterface extends AbstractEntityInterface {
  name: string;
  description: string;
}

export interface ProjectLuckyDrawItemAvaiIableInterface
  extends AbstractEntityInterface {
  item: ItemInterface;
  isAvailable: boolean;
}

export interface ProjectLuckDrawItemInterface extends AbstractEntityInterface {
  ordinal: number;
  projectItem: ProjectItemInterface;
}

export interface SelectedProjectOuutletLuckyDrawItemInterface {
  projectOutlet: ProjectOutletInterface;
  projectItem: ProjectItemInterface;
  projectLuckyDrawItemId: number;
  total?: {
    allocatedQuantity?: number;
    issuedQuantity?: number;
  };
}
