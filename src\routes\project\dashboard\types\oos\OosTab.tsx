import CustomTable from "@/components/CustomTable/CustomTable";
import usePagination from "@/hooks/usePagination";
import {
  ProjectOutletInterface,
  ProjectOutletStockInterface,
} from "@/routes/project/outlet/interface";
import StockStatusCell from "@/routes/project/report/types/outOfStockStatus/StockStatusCell";
import type { TableProps } from "antd";
import { Form } from "antd";
import _ from "lodash";
import { useCallback, useEffect, useMemo, useState } from "react";
import DashboardFilterZone from "../../DashboardFilterZone";
import { DashboardFilterInterface } from "../../interface";
import {
  useGroupedFeatureOosMergedProductsQuery,
  useGroupedFeatureOosProductsQuery,
  useOosQuery,
} from "./service";

interface OosTabProps {
  projectId: number;
  dashboardId: number;
}
const OosTab = ({ projectId, dashboardId }: OosTabProps) => {
  const [form] = Form.useForm();
  const [filter, setFilter] = useState<DashboardFilterInterface>({});
  const { getPagination, currentPage, pageSize } = usePagination({
    defaultPageSize: 50,
  });

  const oosQuery = useOosQuery(projectId, dashboardId, {
    ...filter,
    take: pageSize,
    skip: (currentPage - 1) * pageSize,
  });
  const groupedFeatureOosProductsQuery = useGroupedFeatureOosProductsQuery(
    projectId,
    dashboardId,
  );
  const groupedFeatureOosMergedProductsQuery =
    useGroupedFeatureOosMergedProductsQuery(projectId, dashboardId);

  const productsColumns = useMemo(
    () =>
      groupedFeatureOosProductsQuery.data?.map((item) => {
        return {
          key: item.projectProductId,
          title: (
            <div className="m-1 leading-4">{item.shortName ?? item.name}</div>
          ),
          className: "min-w-[100px] !p-0",
          dataIndex: "projectOutletStocks",
          align: "center" as unknown as "center",
          render: (projectOutletStocks: ProjectOutletStockInterface[]) => (
            <StockStatusCell
              projectOutletStocks={projectOutletStocks}
              projectProductId={item.projectProductId}
              notRenderColor
            />
          ),
        };
      }) ?? [],
    [groupedFeatureOosProductsQuery.data],
  );

  const mergedProductsColumns = useMemo(
    () =>
      groupedFeatureOosMergedProductsQuery.data?.map((item) => {
        return {
          key: item.code,
          title: (
            <div className="m-1 leading-4">
              {item.productShortName ?? item.productName}
            </div>
          ),
          className: "min-w-[100px] !p-0",
          align: "center" as unknown as "center",
          dataIndex: "projectOutletStocks",
          render: (projectOutletStocks: ProjectOutletStockInterface[]) => (
            <StockStatusCell
              projectOutletStocks={projectOutletStocks}
              mergedProductCode={item.code}
            />
          ),
        };
      }) ?? [],
    [groupedFeatureOosMergedProductsQuery.data],
  );

  const [productsAndMergedProductsColumn, setProductsAndMergedProductsColumn] =
    useState<TableProps<ProjectOutletInterface>["columns"]>(
      mergedProductsColumns,
    );

  const handleApply = useCallback(() => {
    const values = form.getFieldsValue();

    const {
      dateSingle,
      projectRegionIds,
      projectOutletIds,
      channelIds,
      provinceIds,
      isMergedProduct,
      leaderIds,
    } = values;

    if (isMergedProduct) {
      setProductsAndMergedProductsColumn(mergedProductsColumns);
    } else {
      setProductsAndMergedProductsColumn(productsColumns);
    }

    const filterValue = {
      startDate: dateSingle.format("YYYY-MM-DD"),
      endDate: dateSingle.format("YYYY-MM-DD"),
      projectRegionIds,
      projectOutletIds,
      channelIds,
      provinceIds,
      isMergedProduct,
      leaderIds: leaderIds?.map((item: { value: number }) => item.value),
    };

    if (_.isEqual(filter, filterValue)) {
      oosQuery.refetch();
    } else {
      setFilter(filterValue);
    }
  }, [filter, form, mergedProductsColumns, oosQuery, productsColumns]);

  const columns: TableProps<ProjectOutletInterface>["columns"] = useMemo(() => {
    const columns = [
      {
        title: "Outlet",
        dataIndex: "name",
        className: "min-w-[100px]",
        fixed: "left" as unknown as "left",
      },
      {
        title: "Vị trí",
        dataIndex: "projectOutletStocks",
        className: "min-w-[100px]",
        render: (projectOutletStocks: ProjectOutletStockInterface[]) =>
          projectOutletStocks?.[0]?.featureOosZone?.name ?? "-",
      },
      ...(Array.isArray(productsAndMergedProductsColumn)
        ? productsAndMergedProductsColumn
        : []),
    ];

    return columns;
  }, [productsAndMergedProductsColumn]);

  const pagination = useMemo(
    () => getPagination(oosQuery.data?.count ?? 0),
    [getPagination, oosQuery.data?.count],
  );

  useEffect(() => {
    if (
      groupedFeatureOosProductsQuery.isSuccess &&
      groupedFeatureOosMergedProductsQuery.isSuccess
    ) {
      setProductsAndMergedProductsColumn(mergedProductsColumns);
    }
  }, [
    mergedProductsColumns,
    productsColumns,
    groupedFeatureOosMergedProductsQuery.isSuccess,
    groupedFeatureOosProductsQuery.isSuccess,
  ]);

  return (
    <>
      <DashboardFilterZone
        handleApply={handleApply}
        fields={[
          "date.single",
          "region",
          "province",
          "chain",
          "leader",
          "outlet",
          "oos.productType",
        ]}
        form={form}
        projectId={projectId}
        setFilter={setFilter}
      />

      <CustomTable
        className="mt-3"
        bordered
        rowKey={"id"}
        pagination={pagination}
        dataSource={oosQuery.data?.entities ?? []}
        scroll={{
          x: "max-content",
          y: pagination.total ? "80vh" : undefined,
        }}
        columns={columns}
      />
    </>
  );
};

export default OosTab;
