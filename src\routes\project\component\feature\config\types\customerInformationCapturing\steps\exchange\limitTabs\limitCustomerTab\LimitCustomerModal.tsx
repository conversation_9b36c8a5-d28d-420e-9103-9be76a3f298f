import CustomModal from "@/components/CustomModal";
import FormNumberInput from "@/components/FormNumberInput";
import { Form, Select } from "antd";
import { useCallback } from "react";
import {
  getOrderLimitRestrictionLabel,
  OrderLimitRestrictionEnum,
  OrderLimitTypeEnum,
  OrderLimitTypeEnumToLabel,
} from "../../../../interface";
import { useCreateFeatureOrderLimitMutation } from "../../../../service";

interface LimitCustomerModalProps {
  open: boolean;
  componentFeatureId: number;
  type: OrderLimitTypeEnum;
  cb: () => void;
  onCancel: () => void;
}

const LimitCustomerModal = ({
  open,
  componentFeatureId,
  cb,
  onCancel,
  type,
}: LimitCustomerModalProps) => {
  const [form] = Form.useForm();

  const createFeatureOrderLimitMutation =
    useCreateFeatureOrderLimitMutation(componentFeatureId);

  const onSubmit = useCallback(
    async (values: {
      restriction: OrderLimitRestrictionEnum;
      periodDays?: number;
      maximum: number;
    }) => {
      await createFeatureOrderLimitMutation.mutateAsync({
        ...values,
        type,
      });

      cb();
    },
    [cb, createFeatureOrderLimitMutation, type],
  );

  return (
    <CustomModal
      title={`Thêm loại giới hạn ${OrderLimitTypeEnumToLabel[type]}`}
      isOpen={open}
      content={
        <Form layout="vertical" form={form} onFinish={onSubmit}>
          <Form.Item
            name={"restriction"}
            label="Chọn loại giới hạn"
            rules={[
              {
                required: true,
              },
            ]}
          >
            <Select
              options={Object.values(OrderLimitRestrictionEnum).map(
                (value) => ({
                  label: getOrderLimitRestrictionLabel(type, value),
                  value,
                }),
              )}
            />
          </Form.Item>

          <Form.Item label={"Giới hạn ngày"} name={"periodDays"}>
            <FormNumberInput className="w-[50%]" />
          </Form.Item>

          <Form.Item
            label={`Giới hạn ${OrderLimitTypeEnumToLabel[type]}`}
            name={"maximum"}
            rules={[
              {
                required: true,
              },
            ]}
          >
            <FormNumberInput className="w-[50%]" min={1} />
          </Form.Item>
        </Form>
      }
      confirmText="Thêm mới"
      onConfirm={() => form.submit()}
      loading={createFeatureOrderLimitMutation.isPending}
      onCancel={onCancel}
    />
  );
};

export default LimitCustomerModal;
