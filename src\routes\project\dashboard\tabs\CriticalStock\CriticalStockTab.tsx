import ChartContanier from "@/routes/project/chart/ChartContanier";
import { Pie } from "@ant-design/charts";
import { useCallback } from "react";
import DashboardFilterZone from "../../DashboardFilterZone";

const CriticalStockTab = () => {
  const handleApply = useCallback(() => {}, []);

  const data = [
    { type: "Safety", value: 40 },
    { type: "OOS", value: 30 },
    { type: "Alert", value: 30 },
  ];

  const config = {
    appendPadding: 10,
    data,
    angleField: "value",
    colorField: "type",
    radius: 0.8,
    interactions: [
      {
        type: "element-active",
      },
    ],
    label: {
      text: (d: { type: string; value: number }) => `${d.type}\n ${d.value}`,
      position: "spider",
    },
    legend: false,
    title: {
      title: "% Critical stock",
    },
    tooltip: {
      field: "value",
      title: (d: { type: string }) => d.type,
      value: (d: { value: number }) => d.value,
    },
    scale: { color: { palette: ["#21BF70", "#FC3407", "#FBC404"] } },
  };
  return (
    <>
      <DashboardFilterZone
        handleApply={handleApply}
        fields={["region", "province", "chain", "outlet", "date"]}
      />

      <ChartContanier>
        <Pie {...config} />
      </ChartContanier>
    </>
  );
};

export default CriticalStockTab;
