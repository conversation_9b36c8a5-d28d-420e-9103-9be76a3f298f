import { EllipsisOutlined } from "@ant-design/icons";
import { Button, Dropdown, MenuProps } from "antd";

interface TableActionCellProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  readonly actions: ({ key: string; action: (e: any) => void } | null)[];
  readonly items: ({
    key: string | number;
    label: JSX.Element | string;
    icon?: JSX.Element;
  } | null)[];
  readonly record: { id: number; isActive?: boolean };
}

export default function TableActionCell(props: TableActionCellProps) {
  const { record, actions, items } = props;

  const onMenuClick: MenuProps["onClick"] = (e) => {
    actions.forEach((action) => {
      if (action && action.key === e.key) {
        action.action(record);
      }
    });
  };

  const menuProps = {
    items: items,
    onClick: onMenuClick,
  };

  return (
    <Dropdown menu={menuProps}>
      <Button type="link">
        <EllipsisOutlined />
      </Button>
    </Dropdown>
  );
}
