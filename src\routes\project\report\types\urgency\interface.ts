import { AbstractEntityInterface } from "@/common/interface";
import { FeatureUrgencyInterface } from "@/routes/project/component/feature/config/types/urgency/interface";
import { ProjectRecordFeatureInterface } from "../../interface";

export interface RecordUrgencyInterface extends AbstractEntityInterface {
  projectRecordFeature: ProjectRecordFeatureInterface;
  featureUrgency: FeatureUrgencyInterface;
  auto: boolean;
  startAt: string;
  endAt: string | null;
  note: string | null;
}
