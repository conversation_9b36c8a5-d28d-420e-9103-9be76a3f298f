import { CURD } from "@/common/constant";
import { filterOption } from "@/common/helper";
import FormNumberInput from "@/components/FormNumberInput";
import ModalCURD from "@/components/ModalCURD";
import { useLuckyDrawsQuery } from "@/routes/project/configLuckyWheel/service";
import { Form, Select } from "antd";
import { useCallback, useEffect } from "react";
import { LuckyDrawLimitInterface } from "../../interface";
import {
  useCreateLuckyDrawOrderLimitMutation,
  useUpdateLuckyDrawOrderLimitMutation,
} from "../../service";

interface LimitLuckDrawModalProps {
  action: CURD | undefined;
  componentFeatureId: number;
  cancelCb: () => void;
  projectId: number;
  selectedLuckyDrawLimit: LuckyDrawLimitInterface | undefined;
  cb: () => void;
}
const LimitLuckDrawModal = ({
  action,
  componentFeatureId,
  cancelCb,
  projectId,
  selectedLuckyDrawLimit,
  cb,
}: LimitLuckDrawModalProps) => {
  const [form] = Form.useForm();

  const luckyDrawsQuery = useLuckyDrawsQuery(projectId);

  const createLuckyDrawOrderLimitMutation =
    useCreateLuckyDrawOrderLimitMutation(componentFeatureId);
  const updateLuckyDrawOrderLimitMutation =
    useUpdateLuckyDrawOrderLimitMutation(componentFeatureId);

  const content = (
    <>
      <Form.Item name={"projectLuckyDrawId"} label={"Vòng quay"}>
        <Select
          disabled={action === CURD.UPDATE}
          options={luckyDrawsQuery.data?.entities.map((item) => ({
            label: item.name,
            value: item.id,
          }))}
          showSearch
          filterOption={filterOption}
        />
      </Form.Item>

      <Form.Item name={"type"} label={"Loại giới hạn"}>
        <Select
          options={[
            {
              label: "number_of_products",
              value: "number_of_products",
            },
            {
              label: "number_of_times",
              value: "number_of_times",
            },
          ]}
        />
      </Form.Item>

      <Form.Item name={"maximum"} label={"Giá trị tối đa"}>
        <FormNumberInput className="w-full" />
      </Form.Item>
    </>
  );

  const onFinish = useCallback(async () => {
    if (action === CURD.CREATE) {
      await createLuckyDrawOrderLimitMutation.mutateAsync({
        ...form.getFieldsValue(),
      });
    }

    if (action === CURD.UPDATE) {
      await updateLuckyDrawOrderLimitMutation.mutateAsync({
        id: selectedLuckyDrawLimit?.id,
        ...form.getFieldsValue(),
      });
    }
    cb();
    cancelCb();
  }, [
    action,
    cancelCb,
    cb,
    createLuckyDrawOrderLimitMutation,
    form,
    selectedLuckyDrawLimit?.id,
    updateLuckyDrawOrderLimitMutation,
  ]);

  useEffect(() => {
    if (selectedLuckyDrawLimit && action === CURD.UPDATE) {
      form.setFieldsValue({
        // projectLuckyDrawId: selectedLuckyDrawLimit.projectLuckyDrawId,
        type: selectedLuckyDrawLimit.type,
        maximum: selectedLuckyDrawLimit.maximum,
      });
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <ModalCURD
      title={
        action === CURD.CREATE
          ? "Thêm giới hạn vòng quay"
          : "Cập nhật giới hạn vòng quay"
      }
      isOpen={true}
      formContent={content}
      form={form}
      onFinish={onFinish}
      onCancelCb={cancelCb}
      btnConfirmLoading={createLuckyDrawOrderLimitMutation.isPending}
      action={action}
    />
  );
};

export default LimitLuckDrawModal;
