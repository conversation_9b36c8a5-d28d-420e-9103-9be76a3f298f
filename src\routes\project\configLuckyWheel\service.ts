import { AbstractFilterInterface } from "@/common/interface";
import { useApp } from "@/UseApp";
import { useMutation, useQuery } from "@tanstack/react-query";
import { ProjectOutletInterface } from "../outlet/interface";
import {
  ProjectLuckDrawItemInterface,
  ProjectLuckyDrawInterface,
  ProjectLuckyDrawItemAvaiIableInterface,
} from "./interface";

export const useLuckyDrawsQuery = (
  projectId: number,
  filter?: AbstractFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["luckyDraws", projectId, filter],
    queryFn: () =>
      axiosGet<
        { entities: ProjectLuckyDrawInterface[]; count: number },
        unknown
      >(`/projects/${projectId}/lucky-draws`, filter),
  });
};

export const useCreateLuckyDrawMutation = (projectId: number) => {
  const { axiosPost, showNotification } = useApp();

  return useMutation({
    mutationKey: ["createLuckyDraw", projectId],
    mutationFn: (data: { name: string; description?: string }) =>
      axiosPost<ProjectLuckyDrawInterface, unknown>(
        `/projects/${projectId}/lucky-draws`,
        data,
      ),
    onSuccess: () => {
      showNotification({
        type: "success",
        message: `Tạo vòng quay may mắn thành công`,
      });
    },
  });
};

export const useUpdateLuckyDrawMutation = (projectId: number) => {
  const { axiosPatch, showNotification } = useApp();

  return useMutation({
    mutationKey: ["updateLuckyDraw", projectId],
    mutationFn: (data: { id: number } & Partial<ProjectLuckyDrawInterface>) =>
      axiosPatch<ProjectLuckyDrawInterface, unknown>(
        `/projects/${projectId}/lucky-draws/${data.id}`,
        data,
      ),
    onSuccess: () => {
      showNotification({
        type: "success",
        message: `Cập nhật vòng quay may mắn thành công`,
      });
    },
  });
};

export const useLuckyDrawItemsAvailableQuery = (
  projectId: number,
  luckyDrawId: number,
  filter?: AbstractFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["luckyDrawItems", projectId, luckyDrawId, filter],
    queryFn: () =>
      axiosGet<
        { entities: ProjectLuckyDrawItemAvaiIableInterface[]; count: number },
        unknown
      >(
        `/projects/${projectId}/lucky-draws/${luckyDrawId}/items/availables`,
        filter,
      ),
  });
};

export const useAddLuckyDrawItemsMutation = (
  projectId: number,
  luckyDrawId: number,
) => {
  const { axiosPost, showNotification } = useApp();

  return useMutation({
    mutationKey: ["addLuckyDrawItems", projectId, luckyDrawId],
    mutationFn: (data: { projectItemId: number }[]) =>
      axiosPost(
        `/projects/${projectId}/lucky-draws/${luckyDrawId}/items`,
        data,
      ),
    onSuccess: () => {
      showNotification({
        type: "success",
        message: `Thêm quà vào vòng quay may mắn thành công`,
      });
    },
  });
};

export const useLuckyDrawItemsQuery = (
  projectId: number,
  luckyDrawId: number,
  filter?: AbstractFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["luckyDrawItems", projectId, luckyDrawId, filter],
    queryFn: () =>
      axiosGet<
        { entities: ProjectLuckDrawItemInterface[]; count: number },
        unknown
      >(`/projects/${projectId}/lucky-draws/${luckyDrawId}/items`, filter),
  });
};

export const usePatchLuckyDrawItemMutation = (
  projectId: number,
  luckyDrawId: number,
) => {
  const { axiosPatch, showNotification } = useApp();

  return useMutation({
    mutationKey: ["patchLuckyDrawItem", projectId, luckyDrawId],
    mutationFn: (data: { isActive: boolean; id: number }) =>
      axiosPatch(
        `/projects/${projectId}/lucky-draws/${luckyDrawId}/items/${data.id}`,
        data,
      ),
    onSuccess: () => {
      showNotification({
        type: "success",
        message: `Cập nhật quà trong vòng quay may mắn thành công`,
      });
    },
  });
};

export const useDeleteLuckyDrawItemMutation = (
  projectId: number,
  luckyDrawId: number,
) => {
  const { axiosDelete, showNotification } = useApp();

  return useMutation({
    mutationKey: ["deleteLuckyDrawItem", projectId, luckyDrawId],
    mutationFn: (id: number) =>
      axiosDelete(
        `/projects/${projectId}/lucky-draws/${luckyDrawId}/items/${id}`,
      ),
    onSuccess: () => {
      showNotification({
        type: "success",
        message: `Xoá quà khỏi vòng quay may mắn thành công`,
      });
    },
  });
};
export const useLuckyDrawItemArrangementMutation = (
  projectId: number,
  luckyDrawId: number,
) => {
  const { axiosPut, showNotification } = useApp();

  return useMutation({
    mutationKey: ["patchLuckyDrawItemArrangement", projectId, luckyDrawId],
    mutationFn: (data: { id: number; arrangement: number }) =>
      axiosPut(
        `/projects/${projectId}/lucky-draws/${luckyDrawId}/items/${data.id}/arrangement`,
        {
          overProjectLuckyDrawItemId: data.arrangement,
        },
      ),
    onSuccess: () => {
      showNotification({
        type: "success",
        message: `Sấp xếp quà trong vòng quay may mắn thành công`,
      });
    },
  });
};

export const useLuckyDrawAllocationsQuery = (
  projectId: number,
  luckyDrawId: number,
  filter?: AbstractFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["luckyDrawAllocations", projectId, luckyDrawId, filter],
    queryFn: () =>
      axiosGet<{ entities: ProjectOutletInterface[]; count: number }, unknown>(
        `/projects/${projectId}/lucky-draws/${luckyDrawId}/allocations`,
        filter,
      ),
  });
};

export const useCreateLuckyDrawAllocationMutation = (
  projectId: number,
  luckyDrawId: number,
) => {
  const { axiosPost, showNotification } = useApp();

  return useMutation({
    mutationKey: ["createLuckyDrawAllocation", projectId, luckyDrawId],
    mutationFn: (data: {
      projectOutletId: number;
      projectLuckyDrawItemId: number;
      allocatedQuantity: number;
      allocatedDate: string | null;
    }) =>
      axiosPost(
        `/projects/${projectId}/lucky-draws/${luckyDrawId}/allocations`,
        data,
      ),
    onSuccess: () => {
      showNotification({
        type: "success",
        message: `Cập nhật phân bổ vòng quay may mắn thành công`,
      });
    },
  });
};
