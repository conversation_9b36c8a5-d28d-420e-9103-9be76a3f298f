import { DEFAULT_CURRENT_PAGE, DEFAULT_PAGE_SIZE } from "@/common/constant.ts";
import { filterOption } from "@/common/helper.ts";
import ProductItemCell from "@/components/ProductItemCell.tsx";
import {
  ProductInterface,
  ProductPackagingInterface,
} from "@/routes/product/interface";
import { useApp } from "@/UseApp.tsx";
import { CloseOutlined } from "@ant-design/icons";
import { useProjectBrandsQuery } from "@project/general/services.ts";
import { ProjectProductInterface } from "@project/product/interface.ts";
import {
  Button,
  Col,
  Form,
  Input,
  Modal,
  Row,
  Select,
  Space,
  Table,
} from "antd";
import _ from "lodash";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import {
  useCreateOrderProductMutation,
  useOrderProductsAvailableQuery,
} from "../service.ts";

const ModalConfigPurchaseAvailables = (
  props: Readonly<{
    projectId: number;
    componentFeatureId: number;
    isOpen: boolean;
    setIsOpen: (isOpen: boolean) => void;
    cb?: () => void;
  }>,
) => {
  const { isOpen, setIsOpen, projectId, cb, componentFeatureId } = props;

  const { showNotification } = useApp();

  const [searchForm] = Form.useForm();
  const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [selectedItemKeys, setSelectedItemKeys] = useState<React.Key[]>([]);
  const [filter, setFilter] = useState({});

  const orderProductsAvailableQuery = useOrderProductsAvailableQuery(
    componentFeatureId,
    {
      ...filter,
      take: pageSize,
      skip: (currentPage - 1) * pageSize,
    },
    isOpen,
  );
  const projectBrandsQuery = useProjectBrandsQuery(projectId, isOpen);

  const createOrderProductMutation =
    useCreateOrderProductMutation(componentFeatureId);

  const searchHandler = useCallback(async () => {
    const values = searchForm.getFieldsValue();
    if (values["isMainPackaging"] === "all") {
      values["isMainPackaging"] = undefined;
    }
    setCurrentPage(DEFAULT_CURRENT_PAGE);
    if (_.isEqual(filter, values)) {
      await orderProductsAvailableQuery.refetch();
    }
    setFilter(values);
  }, [orderProductsAvailableQuery, filter, searchForm]);

  const pagination = useMemo(() => {
    return {
      current: currentPage,
      total: orderProductsAvailableQuery.data?.count,
      pageSize: pageSize,
      onChange: (page: number) => setCurrentPage(page),
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: (_current: number, size: number) => {
        setPageSize(size);
        setCurrentPage(DEFAULT_CURRENT_PAGE);
      },
    };
  }, [currentPage, orderProductsAvailableQuery.data?.count, pageSize]);

  const onSelectChange = useCallback((newSelectedRowKeys: React.Key[]) => {
    setSelectedItemKeys(newSelectedRowKeys);
  }, []);

  const rowSelection = {
    selectedRowKeys: selectedItemKeys,
    onChange: onSelectChange,
    getCheckboxProps: (record: ProjectProductInterface) => {
      return {
        disabled: !record.product.isActive || !record.isAvailable,
      };
    },
  };

  const newSelectedItemKeys = useMemo(
    () =>
      selectedItemKeys.filter((key) => {
        const productPacking = orderProductsAvailableQuery.data?.entities?.find(
          (item) => item.id === key && item.isAvailable,
        );

        return !!productPacking;
      }),
    [orderProductsAvailableQuery.data?.entities, selectedItemKeys],
  );

  const onClose = useCallback(() => {
    setIsOpen(false);
    searchForm.resetFields();
    setFilter({});
    setPageSize(DEFAULT_PAGE_SIZE);
    setCurrentPage(DEFAULT_CURRENT_PAGE);
  }, [searchForm, setIsOpen]);

  const handleBtnAddItemClick = useCallback(async () => {
    try {
      await createOrderProductMutation.mutateAsync(
        newSelectedItemKeys.map((key) => Number(key)),
      );
      showNotification({
        type: "success",
        message: "Thêm sản phẩm vào chức năng thành công",
      });
      onClose();
      cb?.();
    } catch (e) {
      console.error(e);
    }
  }, [
    cb,
    createOrderProductMutation,
    newSelectedItemKeys,
    onClose,
    showNotification,
  ]);

  useEffect(() => {
    if (isOpen) {
      setSelectedItemKeys(
        orderProductsAvailableQuery.data?.entities
          .filter((projectProduct) => !projectProduct.isAvailable)
          .map((projectProduct) => projectProduct.id) ?? [],
      );
    }
  }, [isOpen, orderProductsAvailableQuery.data?.entities]);

  return (
    <Modal
      open={isOpen}
      closeIcon={null}
      footer={null}
      width={1000}
      styles={{ content: { padding: 0 } }}
      onCancel={onClose}
    >
      <div className="pl-10 pr-10 pt-3 pb-5">
        <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
          <h2 className="text-neutral-700 text-2xl font-semibold ">
            Thêm quy cách vào dự án
          </h2>
          <div className="pt-5">
            <Button
              type="link"
              onClick={onClose}
              size="large"
              icon={<CloseOutlined />}
            />
          </div>
        </div>
        <Form layout="vertical" form={searchForm} onFinish={searchHandler}>
          <Row justify={"space-between"}>
            <Col md={7}>
              <Form.Item name="projectBrandId" label="Nhãn hàng">
                <Select
                  allowClear
                  showSearch
                  optionFilterProp="children"
                  filterOption={filterOption}
                  options={projectBrandsQuery.data?.map((projectBrand) => ({
                    label: projectBrand.brand.name,
                    value: projectBrand.id,
                  }))}
                  popupMatchSelectWidth={false}
                  className={"h-10"}
                />
              </Form.Item>
            </Col>
            <Col md={7}>
              <Form.Item name="keyword" label="Sản phẩm">
                <Input
                  allowClear
                  placeholder={"Nhập tên hoặc mã sản phẩm"}
                  className={"h-10"}
                />
              </Form.Item>
            </Col>
            <Col md={5}>
              <Form.Item name="isMainPackaging" label="Loại quy cách">
                <Select
                  allowClear
                  showSearch
                  optionFilterProp="children"
                  filterOption={filterOption}
                  options={[
                    {
                      label: "Tất cả",
                      value: "all",
                    },
                    {
                      label: "Quy cách chuẩn",
                      value: "true",
                    },
                    {
                      label: "Thường",
                      value: "false",
                    },
                  ]}
                  popupMatchSelectWidth={false}
                  className={"h-10"}
                />
              </Form.Item>
            </Col>
            <Col>
              <Form.Item label=" ">
                <Button htmlType="submit">Tìm kiếm</Button>
              </Form.Item>
            </Col>
          </Row>
        </Form>

        <Table
          pagination={pagination}
          dataSource={orderProductsAvailableQuery.data?.entities ?? []}
          rowKey={"id"}
          rowSelection={rowSelection}
          columns={[
            {
              title: "Tên sản phẩm",
              dataIndex: "product",
              render: (product: ProductInterface, record) => {
                const { isAvailable } = record;

                return (
                  <ProductItemCell
                    variants={product?.image?.variants ?? []}
                    name={product.name}
                    isActive={record.product.isActive}
                    isAvailable={isAvailable}
                    isAvailableFalseText={"Đã thêm vào chức năng"}
                  />
                );
              },
            },
            {
              title: "Mã sản phẩm",
              dataIndex: "product",
              render: (product: ProductInterface) => product.code,
            },
            {
              title: "Nhãn hàng",
              dataIndex: "product",
              render: (product: ProductInterface) => product.brand.name,
            },
            {
              title: "Loại quy cách",
              dataIndex: "productPackaging",
              className: "w-[150px]",
              render: (productPackaging: ProductPackagingInterface) =>
                productPackaging.isMainPackaging ? "Quy cách chuẩn" : "Thường",
            },
            {
              title: "Quy cách",
              dataIndex: "productPackaging",
              render: (productPackaging: ProductPackagingInterface) =>
                productPackaging.unit.name,
            },
          ]}
        />
      </div>
      <div
        className="flex justify-end pb-4 pt-4"
        style={{ backgroundColor: "#F7F8FA", borderRadius: "4px" }}
      >
        <Space className="pr-10">
          <Button onClick={onClose}>Đóng</Button>
          <Button
            type={"primary"}
            disabled={newSelectedItemKeys.length === 0}
            onClick={handleBtnAddItemClick}
            loading={createOrderProductMutation.isPending}
          >
            Thêm {newSelectedItemKeys.length} quy cách vào dự án
          </Button>
        </Space>
      </div>
    </Modal>
  );
};

export default ModalConfigPurchaseAvailables;
