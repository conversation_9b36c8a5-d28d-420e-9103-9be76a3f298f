import { useApp } from "@/UseApp.tsx";
import { AppContextInterface } from "@/interface.ts";
import { useMutation, useQuery } from "@tanstack/react-query";
import { ProjectInterface } from "./interface";

export const getProject = (
  axiosGet: AppContextInterface["axiosGet"],
  projectId?: number,
) => {
  return axiosGet<ProjectInterface, unknown>(`/projects/${projectId}`);
};

export const getProjects = (
  axiosGet: AppContextInterface["axiosGet"],
  params?: {
    take?: number;
    skip?: number;
    search?: string;
  },
) => {
  return axiosGet<{ entities: ProjectInterface[]; count: number }, unknown>(
    `/projects`,
    params,
  );
};

export const useProjectsQuery = (params?: {
  take?: number;
  skip?: number;
  search?: string;
}) => {
  const { axiosGet } = useApp();
  return useQuery({
    queryKey: ["projects", params],
    queryFn: () => getProjects(axiosGet, params),
  });
};

export const useProjectQuery = (projectId?: number) => {
  const { axiosGet } = useApp();
  return useQuery({
    queryKey: ["project", projectId],
    queryFn: () => getProject(axiosGet, projectId),
  });
};

export const useCreateProjectMutation = () => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createProject"],
    mutationFn: (data: { name: string; clientId: number }) =>
      axiosPost("/projects", data),
  });
};

export const useUpdateProjectMutation = () => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["updateProject"],
    mutationFn: (data: { id: number; name: string; clientId: number }) =>
      axiosPatch(`/projects/${data.id}`, data),
  });
};
