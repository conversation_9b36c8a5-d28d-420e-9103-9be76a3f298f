import { useApp } from "@/UseApp";
import { CHUNK_SIZE } from "@/common/constant";
import { formError<PERSON>esponse<PERSON>and<PERSON> } from "@/common/helper";
import { AbstractFilterInterface } from "@/common/interface";
import { AppContextInterface } from "@/interface.ts";
import { useMutation, useQuery } from "@tanstack/react-query";
import { FormInstance } from "antd";
import React from "react";
import { ChannelInterface } from "../../channel/interface";
import { SubChannelInterface } from "../../subChannel/interface";
import { ProjectAgencyInterface } from "../interface";
import {
  ApiProjectOutletResponseInterface,
  ProjectOutletInterface,
} from "./interface";

/**
 * Returns a query for project outlets based on the project ID and filter.
 *
 * @param {number} projectId - The ID of the project
 * @param {unknown} filter - The filter for the query
 * @return {unknown} The result of the query
 */
export const useProjectOutletsQuery = (projectId: number, filter: unknown) => {
  const { axiosGet } = useApp();
  return useQuery({
    queryKey: ["projectOutlets", projectId, filter],
    queryFn: async () => {
      const response = await axiosGet<
        ApiProjectOutletResponseInterface,
        unknown
      >(`projects/${projectId}/outlets`, filter);
      if (!Array.isArray(response)) {
        return response;
      }
      return {
        entities: [],
        count: 0,
      };
    },
    enabled: !!projectId,
  });
};

/**
 * Creates a query to fetch project agency outlets.
 *
 * @param {number} projectId - The ID of the project
 * @param {number | undefined} projectAgencyId - The ID of the project agency (optional)
 * @param {unknown | undefined} filter - Additional filter (optional)
 * @return {QueryResult} The result of the query
 */
export const useProjectAgencyOutletsQuery = (
  projectId: number,
  projectAgencyId?: number,
  filter?: object & AbstractFilterInterface,
) => {
  const { axiosGet } = useApp();
  return useQuery({
    queryKey: ["projectAgencyOutlets", projectId, projectAgencyId, filter],
    queryFn: async () => {
      const response = await axiosGet<
        ApiProjectOutletResponseInterface,
        unknown
      >(`/projects/${projectId}/agencies/${projectAgencyId}/outlets`, filter);
      if (!Array.isArray(response)) {
        return response;
      }
      return {
        entities: [],
        count: 0,
      };
    },
    enabled: !!projectId && !!projectAgencyId,
  });
};

export const useProjectEmployeeLeaderOutletsQuery = (
  projectId: number,
  leaderId?: number,
  filter?: object & AbstractFilterInterface,
  enabled?: boolean,
) => {
  const { axiosGet } = useApp();
  return useQuery({
    queryKey: ["projectEmployeeLeaderOutlets", projectId, leaderId, filter],
    queryFn: async () => {
      const response = await axiosGet<
        ApiProjectOutletResponseInterface,
        unknown
      >(`/projects/${projectId}/employee-users/${leaderId}/outlets`, filter);
      if (!Array.isArray(response)) {
        return response;
      }
      return {
        entities: [],
        count: 0,
      };
    },
    enabled: !!projectId && !!leaderId && enabled,
  });
};

export const useUpdateOutletMutation = (
  projectId: number,
  form?: FormInstance,
) => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["updateOutlet", projectId],
    mutationFn: (data: {
      code?: string;
      name?: string;
      provinceId?: number;
      districtId?: number;
      wardId?: number;
      streetName?: string;
      houseNumber?: string;
      projectAgencyChannelId?: number;
      subChannelId?: number;
      projectAgencyId?: number;
      leaderId?: number;
      isActive?: boolean;
      id?: number;
      hasOvernightShift?: boolean;
    }) =>
      axiosPatch<ProjectOutletInterface, unknown>(
        `/projects/${projectId}/outlets/${data.id}`,
        data,
      ),
    onError(error) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      formErrorResponseHandler(error as any, form);
    },
  });
};

export const getProjectChannels = async (
  axiosGet: AppContextInterface["axiosGet"],
  projectId: number,
) => axiosGet<ChannelInterface[], unknown>(`/projects/${projectId}/channels`);

export const useProjectChannelsQuery = (
  projectId: number,
  enabled?: boolean,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["projectChannels", projectId],
    queryFn: async () => getProjectChannels(axiosGet, projectId),
    enabled,
  });
};

export const getProjectChannelAgencies = async (
  axiosGet: AppContextInterface["axiosGet"],
  projectId: number,
  channelId?: number | null,
) =>
  axiosGet<ProjectAgencyInterface[], unknown>(
    `/projects/${projectId}/channels/${channelId}/agencies`,
  );

export const useProjectChannelAgenciesQuery = (
  projectId: number,
  channelId?: number | null,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["projectChannelAgencies", projectId, channelId],
    queryFn: async () =>
      getProjectChannelAgencies(axiosGet, projectId, channelId),
    enabled: !!projectId && !!channelId,
  });
};

export const getProjectChannel = (
  axiosGet: AppContextInterface["axiosGet"],
  projectId: number,
  channelId?: number | null,
) =>
  axiosGet<ChannelInterface, unknown>(
    `/projects/${projectId}/channels/${channelId}`,
  );

export const useProjectChannelQuery = (
  projectId: number,
  channelId?: number | null,
) => {
  const { axiosGet } = useApp();
  return useQuery({
    enabled: !!projectId && !!channelId,
    queryKey: ["projectChannel", projectId, channelId],
    queryFn: async () => getProjectChannel(axiosGet, projectId, channelId),
  });
};

export const findChannelByName = async (
  axiosGet: AppContextInterface["axiosGet"],
  projectId: number,
  channels: React.MutableRefObject<ChannelInterface[]>,
  channelName: string,
) => {
  const channel = channels.current.find(
    (c) => c.name.toLocaleLowerCase() === channelName.toLocaleLowerCase(),
  );
  if (channel) {
    return channel;
  }
  if (channels.current.length > 0) {
    return null;
  }
  const response = await getProjectChannels(axiosGet, projectId);
  channels.current = response;

  const newChannel = response.find(
    (c) => c.name.toLocaleLowerCase() === channelName.toLocaleLowerCase(),
  );
  return newChannel;
};

export const findSubChannelIdByName = async (
  axiosGet: AppContextInterface["axiosGet"],
  projectId: number,
  channelId: number,
  subChannels: React.MutableRefObject<SubChannelInterface[]>,
  subChannelName: string,
) => {
  const subChannel = subChannels.current.find(
    (c) =>
      c.channelId === channelId &&
      c.name.toLocaleLowerCase() === subChannelName.toLocaleLowerCase(),
  );
  if (subChannel) {
    return subChannel.id;
  }
  const channel = await getProjectChannel(axiosGet, projectId, channelId);
  subChannels.current = [...subChannels.current, ...channel.subChannels];

  const newSubChannel = channel.subChannels.find(
    (c) => c.name.toLocaleLowerCase() === subChannelName.toLocaleLowerCase(),
  );
  return newSubChannel?.id;
};

export const findChannelProjectAgencyByName = async (
  axiosGet: AppContextInterface["axiosGet"],
  projectId: number,
  channel: ChannelInterface,
  channelProjectAgencies: React.MutableRefObject<
    {
      channel: ChannelInterface;
      projectAgency: ProjectAgencyInterface;
    }[]
  >,
  agencyName: string,
) => {
  const projectAgency = channelProjectAgencies.current.find(
    (c) =>
      channel.id === c.channel.id &&
      c.projectAgency.agency.name.toLocaleLowerCase() ===
        agencyName.toLocaleLowerCase(),
  );
  if (projectAgency) {
    return projectAgency;
  }
  const foundProjectAgencies = await getProjectChannelAgencies(
    axiosGet,
    projectId,
    channel.id,
  );
  channelProjectAgencies.current = [
    ...channelProjectAgencies.current,
    ...foundProjectAgencies.map((projectAgency) => ({
      channel,
      projectAgency,
    })),
  ];

  const newProjectAgency = foundProjectAgencies.find(
    (c) => c.agency.name.toLocaleLowerCase() === agencyName.toLocaleLowerCase(),
  );
  if (newProjectAgency) {
    return { channel, projectAgency: newProjectAgency };
  }
  return undefined;
};

export const useCreateOutletMutation = (
  projectId: number,
  form?: FormInstance,
) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createOutlet", projectId],
    mutationFn: (data: {
      code: string;
      name: string;
      provinceId: number;
      districtId: number;
      wardId?: number;
      streetName: string;
      houseNumber: string;
      projectAgencyChannelId: number;
      subChannelId: number;
      projectAgencyId: number;
      leaderId: number;
      hasOvernightShift: boolean;
      latitude?: number | string;
      longitude?: number | string;
    }) =>
      axiosPost<ProjectOutletInterface, unknown>(
        `/projects/${projectId}/outlets`,
        data,
      ),
    onError(error) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      if (form) formErrorResponseHandler(error as any, form);
    },
  });
};

export const useDeleteOutletMutation = (projectId: number) => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deleteOutlet", projectId],
    mutationFn: (id: number) =>
      axiosDelete(`/projects/${projectId}/outlets/${id}`),
  });
};

export const useGetProjectOutletsMutation = (projectId: number) => {
  const { axiosGet } = useApp();

  return useMutation({
    mutationKey: ["getProjectOutlets", projectId],
    mutationFn: (filter: AbstractFilterInterface) =>
      axiosGet<{ entities: ProjectOutletInterface[]; count: number }, unknown>(
        `projects/${projectId}/outlets`,
        filter,
      ),
  });
};

export const useGetProjectEmployeeLeaderOutletsMutation = (
  projectId: number,
  leaderId?: number,
) => {
  const { axiosGet } = useApp();
  return useMutation({
    mutationKey: ["projectEmployeeLeaderOutlets", projectId, leaderId],
    mutationFn: async (filter?: object & AbstractFilterInterface) => {
      return axiosGet<ApiProjectOutletResponseInterface, unknown>(
        `/projects/${projectId}/employee-users/${leaderId}/outlets`,
        filter,
      );
    },
  });
};

export const useAllOutletsQuery = (projectId: number) => {
  const getProjectOutletsMutation = useGetProjectOutletsMutation(projectId);

  return useQuery({
    queryKey: ["allOutlets", projectId],
    queryFn: async () => {
      const first = await getProjectOutletsMutation.mutateAsync({
        take: CHUNK_SIZE,
        skip: 0,
      });

      if (first.count < CHUNK_SIZE) {
        return first.entities;
      } else {
        const requests = [];
        for (let skip = CHUNK_SIZE; skip < first.count; skip += CHUNK_SIZE) {
          requests.push(
            getProjectOutletsMutation.mutateAsync({
              take: CHUNK_SIZE,
              skip: skip,
            }),
          );
        }
        const results = await Promise.all(requests);
        const allEntities = results.flatMap((result) => result.entities);
        return [...first.entities, ...allEntities];
      }
    },
  });
};
