import { Image } from "antd";
import { FALLBACK_IMAGE_STRING, IMAGE_SIZE_PREVIEW } from "../common/constant";

interface ImageItemProps {
  thumbnail?: string;
  preview?: string;
  width?: number;
  height?: number;
  options?: {
    border?: boolean;
  };
}

export default function ImageItem({
  thumbnail,
  preview,
  width = IMAGE_SIZE_PREVIEW,
  height = IMAGE_SIZE_PREVIEW,
  options = {
    border: true,
  },
}: Readonly<ImageItemProps>) {
  const { border } = options;
  return (
    <Image
      className={`rounded object-cover ${border ? "border border-solid border-[#DDE1EA]" : ""}`}
      src={thumbnail}
      width={width}
      height={height}
      preview={{
        src: preview,
      }}
      fallback={FALLBACK_IMAGE_STRING}
    />
  );
}
