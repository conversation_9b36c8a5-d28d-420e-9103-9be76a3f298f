import { BTN_CANCEL_TEXT, BTN_CONFIRM_TEXT } from "@/common/constant";
import DragSortRowComponent from "@/components/DragSortRowComponent";
import ProductItemCell from "@/components/ProductItemCell";
import { renderTableCell } from "@/components/table-cell";
import TableActionCell from "@/components/TableActionCell";
import { useApp } from "@/UseApp";
import {
  DeleteOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined,
} from "@ant-design/icons";
import { DndContext, DragEndEvent } from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
  arrayMove,
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { Button, Modal, Table } from "antd";
import { useCallback, useEffect, useState } from "react";
import {
  NumericSheetGroupCollapseChildActionEnum,
  NumericSheetNumericInterface,
} from "../../interface";
import {
  useArrangeNumericSheetNumericMutation,
  useDeleteNumericSheetNumericMutation,
  useNumericSheetNumericsQuery,
  useUpdateNumericSheetNumericMutation,
} from "../../service";
import NumericSheetGroupAvailablesModal from "./NumericSheetGroupAvailablesModal";

interface NumericSheetGroupCollapseChildProps {
  componentFeatureId: number;
  numericSheetId: number;
}

const NumericSheetGroupCollapseChild = ({
  componentFeatureId,
  numericSheetId,
}: NumericSheetGroupCollapseChildProps) => {
  const { showNotification, openDeleteModal } = useApp();

  const [open, setOpen] = useState(false);

  const numericSheetNumericsQuery = useNumericSheetNumericsQuery(
    componentFeatureId,
    numericSheetId,
  );

  const arrangeNumericSheetNumericMutation =
    useArrangeNumericSheetNumericMutation(componentFeatureId, numericSheetId);
  const updateNumericSheetNumericMutation =
    useUpdateNumericSheetNumericMutation(componentFeatureId, numericSheetId);
  const deleteNumericSheetNumericMutation =
    useDeleteNumericSheetNumericMutation(componentFeatureId, numericSheetId);

  const [dataSource, setDataSource] = useState(
    numericSheetNumericsQuery.data?.entities ?? [],
  );

  const onAdd = useCallback(() => {
    setOpen(true);
  }, []);

  const onDragEnd = useCallback(
    async ({ active, over }: DragEndEvent) => {
      if (!over) {
        return;
      }
      if (active.id !== over?.id) {
        setDataSource((previous) => {
          const activeIndex = previous?.findIndex((i) => i.id === active.id);
          const overIndex = previous?.findIndex((i) => i.id === over?.id);
          return arrayMove(previous, activeIndex, overIndex);
        });

        await arrangeNumericSheetNumericMutation.mutateAsync({
          id: active.id as number,
          overId: over?.id as number,
        });
      }
    },
    [arrangeNumericSheetNumericMutation],
  );

  useEffect(() => {
    setDataSource(
      (numericSheetNumericsQuery.data?.entities ?? []).sort(
        (a, b) => a.ordinal - b.ordinal,
      ),
    );
  }, [numericSheetNumericsQuery.data?.entities]);

  const activeClick = useCallback(
    (record: NumericSheetNumericInterface) => {
      const { projectProduct, projectItem } = record;
      const name = projectProduct?.product.name ?? projectItem?.item.name;

      Modal.confirm({
        title: `Kích hoạt item: ${name}`,
        content: "Bạn có chắc chắn muốn kích hoạt item này?",
        okText: BTN_CONFIRM_TEXT,
        cancelText: BTN_CANCEL_TEXT,
        onOk: async () => {
          await updateNumericSheetNumericMutation.mutateAsync({
            isActive: true,
            id: record.id,
          });

          showNotification({
            type: "success",
            message: "Ngừng hoạt động thành công.",
          });
          await numericSheetNumericsQuery.refetch();
        },
      });
    },
    [
      numericSheetNumericsQuery,
      showNotification,
      updateNumericSheetNumericMutation,
    ],
  );

  const inActiveClick = useCallback(
    (record: NumericSheetNumericInterface) => {
      const { projectProduct, projectItem } = record;
      const name = projectProduct?.product.name ?? projectItem?.item.name;

      Modal.confirm({
        title: `Ngừng hoạt động item: ${name}`,
        content: "Bạn có chắc chắn muốn ngừng hoạt động item này?",
        okText: BTN_CONFIRM_TEXT,
        cancelText: BTN_CANCEL_TEXT,
        onOk: async () => {
          await updateNumericSheetNumericMutation.mutateAsync({
            isActive: false,
            id: record.id,
          });

          showNotification({
            type: "success",
            message: "Ngừng hoạt động thành công.",
          });
          await numericSheetNumericsQuery.refetch();
        },
      });
    },
    [
      numericSheetNumericsQuery,
      showNotification,
      updateNumericSheetNumericMutation,
    ],
  );

  const deleteClick = useCallback(
    (record: NumericSheetNumericInterface) => {
      const { projectProduct, projectItem } = record;
      const name = projectProduct?.product.name ?? projectItem?.item.name;

      openDeleteModal({
        content: (
          <p>
            Bạn muốn xóa item <span className={"font-semibold"}>{name}</span>{" "}
            khỏi nhóm item?
          </p>
        ),
        deleteText: "Xác nhận xóa",
        loading: false,
        onCancel(): void {},
        onDelete: async () => {
          await deleteNumericSheetNumericMutation.mutateAsync(record.id);

          showNotification({
            type: "success",
            message: "Xóa item thành công",
          });

          await numericSheetNumericsQuery.refetch();
        },
        title: `Xóa item`,
        titleError: "Không thể xóa item",
        contentHeader: (
          <>
            Không thể xóa item <span className="font-semibold">{name}</span> bởi
            vì:
          </>
        ),
      });
    },
    [
      deleteNumericSheetNumericMutation,
      numericSheetNumericsQuery,
      openDeleteModal,
      showNotification,
    ],
  );

  const actionActions = [
    {
      key: NumericSheetGroupCollapseChildActionEnum.ACTIVE,
      action: activeClick,
    },
    {
      key: NumericSheetGroupCollapseChildActionEnum.INACTIVE,
      action: inActiveClick,
    },
    {
      key: NumericSheetGroupCollapseChildActionEnum.DELETE,
      action: deleteClick,
    },
  ];

  const actionItems = [
    {
      key: NumericSheetGroupCollapseChildActionEnum.INACTIVE,
      label: "Ngừng hoạt động",
      icon: <PauseCircleOutlined />,
    },
    {
      key: NumericSheetGroupCollapseChildActionEnum.ACTIVE,
      label: "Hoạt động trở lại",
      icon: <PlayCircleOutlined />,
    },
    {
      key: NumericSheetGroupCollapseChildActionEnum.DELETE,
      label: "Xóa khỏi chức năng",
      icon: <DeleteOutlined />,
    },
  ];

  const ACTION_ACTIVE = [
    NumericSheetGroupCollapseChildActionEnum.INACTIVE,
    NumericSheetGroupCollapseChildActionEnum.DELETE,
  ];

  const ACTION_INACTIVE = [
    NumericSheetGroupCollapseChildActionEnum.ACTIVE,
    NumericSheetGroupCollapseChildActionEnum.DELETE,
  ];

  return (
    <>
      <div className="flex justify-end">
        <Button type="link" className="text-blue" onClick={onAdd}>
          Thêm item
        </Button>
      </div>

      <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
        <SortableContext
          items={dataSource.map((i) => i.id)}
          strategy={verticalListSortingStrategy}
        >
          <Table
            dataSource={dataSource}
            rowKey={(o) => o.id}
            pagination={false}
            loading={
              numericSheetNumericsQuery.isFetching ||
              arrangeNumericSheetNumericMutation.isPending
            }
            components={{
              body: {
                row: DragSortRowComponent,
              },
            }}
            columns={[
              {
                key: "sort",
              },
              {
                title: "Tên",
                className: "min-w-[100px]",
                render: (_, record: NumericSheetNumericInterface) => {
                  const { projectProduct, projectItem } = record ?? {};

                  if (projectProduct) {
                    return (
                      <ProductItemCell
                        name={projectProduct?.product.name}
                        variants={projectProduct.product.image?.variants ?? []}
                      />
                    );
                  }

                  return (
                    <ProductItemCell
                      name={projectItem?.item.name ?? ""}
                      variants={projectItem?.item.image?.variants ?? []}
                    />
                  );
                },
              },
              {
                title: "Mã",
                className: "min-w-[100px]",
                render: (_, record: NumericSheetNumericInterface) => {
                  if (record.projectProduct) {
                    return record.projectProduct?.product.code;
                  }

                  return record.projectItem?.item?.code;
                },
              },
              {
                title: "Loại",
                className: "min-w-[100px]",
                render: (_, record: NumericSheetNumericInterface) => {
                  if (record.projectProduct) {
                    return "Sản phẩm";
                  }

                  return record.projectItem?.item?.itemType?.name;
                },
              },
              {
                title: "Nhãn hàng",
                className: "min-w-[100px]",
                render: (_, record: NumericSheetNumericInterface) => {
                  if (record.projectProduct) {
                    return record.projectProduct?.product?.brand?.name;
                  }

                  return "-";
                },
              },
              {
                key: "isActive",
                className: "min-w-[100px]",
                title: "Tình trạng",
                dataIndex: "isActive",
                render: (value, record, index) => {
                  return renderTableCell(value, record, index, "isActive");
                },
              },
              {
                key: "actions",
                render: (_, record) => {
                  const actionKeys = record.isActive
                    ? ACTION_ACTIVE
                    : ACTION_INACTIVE;
                  const items = actionItems.filter((item) =>
                    actionKeys.includes(item.key),
                  );
                  return (
                    <TableActionCell
                      actions={actionActions}
                      items={items}
                      record={record}
                    />
                  );
                },
                width: 100,
              },
            ]}
          />
        </SortableContext>
      </DndContext>

      {open && (
        <NumericSheetGroupAvailablesModal
          onModalClose={() => {
            setOpen(false);
          }}
          cb={() => {
            setOpen(false);
            numericSheetNumericsQuery.refetch();
          }}
          componentFeatureId={componentFeatureId}
          numericSheetId={numericSheetId}
        />
      )}
    </>
  );
};

export default NumericSheetGroupCollapseChild;
