import { useApp } from "@/UseApp.tsx";
import { CURD, SELECT_ALL, SELECT_ALL_LABEL } from "@/common/constant";
import { getImageVariants } from "@/common/image.helper.ts";
import BarcodeView from "@/components/BarcodeView.tsx";
import FilterComponent from "@/components/FilterComponent.tsx";
import ProductItemCell from "@/components/ProductItemCell.tsx";
import { renderTableCell } from "@/components/table-cell";
import { renderTableOptionCell } from "@/components/table-option-cell";
import { useUrlFilters } from "@/hooks/useUrlFilters.ts";
import { Form, Modal, Statistic, Table } from "antd";
import { ColumnsType } from "antd/es/table";
import React, { useCallback, useMemo, useState } from "react";
import { BrandInterface } from "../brand/interface.ts";
import ProductModal from "./ProductModal.tsx";
import { ProductInterface } from "./interface.ts";
import {
  useDeleteProductMutation,
  useGetProductByIdMutation,
  useProductsQuery,
  useUpdateProductMutation,
} from "./service.ts";

const ProductPage: React.FC = () => {
  const { showNotification, openDeleteModal } = useApp();

  const [searchForm] = Form.useForm();
  const [form] = Form.useForm();
  const [isModalAddOrUpdateOpen, setIsModalAddOrUpdateOpen] = useState(false);
  const [formAction, setFormAction] = useState<CURD | null>(null);
  const [modalTitle, setModalTitle] = useState("");
  const [modal, contextHolder] = Modal.useModal();
  const { filter, currentPage, pageSize, handleSearch, getPaginationProps } =
    useUrlFilters({
      formInstance: searchForm,
      handleSearchCallback: () => {
        productsQuery.refetch();
      },
    });

  const productsQuery = useProductsQuery({
    ...filter,
    take: pageSize,
    skip: (currentPage - 1) * pageSize,
  });

  const getProductByIdMutation = useGetProductByIdMutation();
  const deleteProductMutation = useDeleteProductMutation();
  const updateProductMutation = useUpdateProductMutation();

  const handleBtnEditClick = useCallback(
    async ({ id }: ProductInterface) => {
      setIsModalAddOrUpdateOpen(true);
      setFormAction(CURD.UPDATE);
      setModalTitle("Cập nhật sản phẩm");

      const record = await getProductByIdMutation.mutateAsync(Number(id));

      form.setFieldsValue({
        ...record,
        brandId: record.brand.id,
        mainPackagingId: record.packagings.find(
          (packing) => packing.isMainPackaging,
        )?.unit.id,
        packagings: record.packagings.map((packaging) => ({
          ...packaging,
          unitId: packaging.unit.id,
        })),
        clientId: record.brand.client.id,
        imageUrl: getImageVariants(record.image?.variants ?? [], "thumbnail"),
      });
    },
    [form, getProductByIdMutation],
  );

  const handleBtnInactiveClick = useCallback(
    (record: ProductInterface) => {
      modal.confirm({
        title: `Ngừng hoạt động product: ${record.name}`,
        content: "Bạn có chắc chắn muốn ngừng hoạt động product này?",
        okText: "Ngừng hoạt động",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await updateProductMutation.mutateAsync({
              id: record.id,
              isActive: false,
            });
            showNotification({
              type: "success",
              message: "Ngừng hoạt động product thành công",
            });

            productsQuery.refetch();
          } catch (error) {
            console.log(error);

            showNotification({
              type: "error",
              message: "Ngừng hoạt động product thất bại",
            });
          }
        },
      });
    },
    [modal, productsQuery, showNotification, updateProductMutation],
  );

  const handleBtnActiveClick = useCallback(
    (record: ProductInterface) => {
      modal.confirm({
        title: `Kích hoạt product: ${record.name}`,
        content: "Bạn có chắc chắn muốn kích hoạt product này?",
        okText: "Kích hoạt",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await updateProductMutation.mutateAsync({
              id: record.id,
              isActive: true,
            });

            showNotification({
              type: "success",
              message: "Kích hoạt product thành công",
            });
          } catch (error) {
            console.error(error);

            showNotification({
              type: "error",
              message: "Kích hoạt product thất bại",
            });
          }

          productsQuery.refetch();
        },
      });
    },
    [modal, productsQuery, showNotification, updateProductMutation],
  );

  const handleBtnDeleteClick = useCallback(
    (record: ProductInterface) => {
      openDeleteModal({
        content: (
          <>
            <p>
              Sản phẩm sẽ được xóa khỏi hệ thống vĩnh viễn và không thể khôi
              phục
            </p>
            <p>
              Bạn vẫn muốn xóa sản phẩm{" "}
              <span className={"font-semibold"}>{record.name}</span> khỏi hệ
              thống?
            </p>
          </>
        ),
        deleteText: "Xác nhận xóa",
        loading: deleteProductMutation.isPending,
        onCancel(): void {},
        onDelete: async () => {
          await deleteProductMutation.mutateAsync(record.id);
          showNotification({
            type: "success",
            message: "Xóa sản phẩm thành công",
          });

          productsQuery.refetch();
        },
        title: `Xóa sản phẩm`,
        titleError: "Không thể xóa sản phẩm",
        contentHeader: (
          <>
            Không thể xóa sản phẩm{" "}
            <span className="font-semibold">{record.name}</span> bởi vì:
          </>
        ),
      });
    },
    [deleteProductMutation, openDeleteModal, productsQuery, showNotification],
  );

  const columns: ColumnsType<ProductInterface> = [
    {
      title: "Tên sản phẩm",
      key: "name",
      dataIndex: "name",
      className: "min-w-[100px]",
      render: (_, record: ProductInterface) => {
        return (
          <ProductItemCell
            variants={record?.image?.variants ?? []}
            name={record.name}
          />
        );
      },
    },
    {
      title: "Mã sản phẩm",
      key: "code",
      dataIndex: "code",
      render: renderTableCell,
      className: "min-w-[100px]",
    },
    {
      title: "Barcode",
      key: "packaging.barcode",
      className: "min-w-[100px]",
      render: (_: number, record: ProductInterface) => {
        const { packagings } = record;
        const packaging = packagings.find(
          (packaging) => packaging.isMainPackaging,
        );
        if (packaging) return <BarcodeView packaging={packaging} />;

        return "-";
      },
    },
    {
      title: "Giá quy cách chuẩn",
      key: "packaging.price",
      dataIndex: "mainPackagingId",
      render: (mainPackagingId: number, record: ProductInterface) => {
        const { packagings } = record;
        const packaging = packagings.find(
          (packaging) => packaging.unitId === mainPackagingId,
        );
        if (packaging)
          return (
            <div className="flex place-items-center">
              <Statistic
                valueStyle={{ fontSize: 14 }}
                value={packaging?.price}
                suffix="vnđ"
              />{" "}
              &nbsp;/ {packaging?.unit?.name}
            </div>
          );
        return "-";
      },
      className: "min-w-[100px]",
    },
    {
      title: "Nhãn hàng trực thuộc",
      key: "brand.name",
      dataIndex: "brand",
      render: renderTableCell,
      className: "min-w-[100px]",
    },
    {
      title: "Client",
      key: "client",
      dataIndex: "brand",
      className: "min-w-[100px]",
      render: (brand: BrandInterface) => brand.client.name,
    },
    {
      key: "isActive",
      title: "Tình trạng",
      dataIndex: "isActive",
      className: "min-w-[100px]",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "isActive");
      },
    },
    {
      key: "createdAt",
      title: "Thời gian tạo",
      dataIndex: "createdAt",
      className: "min-w-[100px]",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "datetime");
      },
    },
    {
      className: "min-w-[100px]",
      key: "createdByUser.name",
      title: "Người tạo",
      dataIndex: "createdByUser",
      render: renderTableCell,
    },
    {
      key: "updatedAt",
      title: "Thời gian cập nhật",
      dataIndex: "updatedAt",
      className: "min-w-[100px]",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "datetime");
      },
    },
    {
      className: "min-w-[100px]",
      key: "updatedByUser.name",
      title: "Người cập nhật",
      dataIndex: "updatedByUser",
      render: renderTableCell,
    },
    {
      key: "actions",
      render: (_, record) => {
        return renderTableOptionCell(
          record,
          handleBtnEditClick,
          handleBtnInactiveClick,
          handleBtnActiveClick,
          handleBtnDeleteClick,
        );
      },
      fixed: "right",
      width: 50,
    },
  ];

  const filterOptions = columns
    .filter(
      (item) =>
        ![
          "isActive",
          "updatedAt",
          "createdAt",
          "actions",
          "client",
          "packaging.price",
        ].includes(item.key as string),
    )
    .map((column) => {
      return {
        label: column.title as string,
        value: column.key as string | number,
      };
    });

  filterOptions.unshift({ label: SELECT_ALL_LABEL, value: SELECT_ALL });

  const handleAddButtonClick = () => {
    setIsModalAddOrUpdateOpen(true);
    setFormAction(CURD.CREATE);
    setModalTitle("Thêm sản phẩm");
  };

  const pagination = useMemo(
    () => getPaginationProps(productsQuery.data?.count),
    [getPaginationProps, productsQuery.data?.count],
  );

  const loading = useMemo(
    () =>
      productsQuery.isLoading ||
      productsQuery.isFetching ||
      updateProductMutation.isPending ||
      deleteProductMutation.isPending,
    [
      productsQuery.isLoading,
      productsQuery.isFetching,
      updateProductMutation.isPending,
      deleteProductMutation.isPending,
    ],
  );

  return (
    <div>
      <h2>Sản phẩm</h2>
      <div className="bg-white p-10 rounded">
        <div className="pb-6">
          <FilterComponent
            filterOptions={filterOptions}
            searchHandler={handleSearch}
            handleAddButtonClick={handleAddButtonClick}
            searchForm={searchForm}
            hasClient
            btnLoading={loading}
          />
        </div>
        <Table
          dataSource={productsQuery.data?.entities}
          columns={columns}
          rowKey={(record) => record.id}
          scroll={{
            x: "max-content",
            y: pagination.total ? "80vh" : undefined,
          }}
          pagination={pagination}
          loading={loading}
        />
      </div>

      <ProductModal
        isOpen={isModalAddOrUpdateOpen}
        modalTitle={modalTitle}
        setIsOpen={setIsModalAddOrUpdateOpen}
        form={form}
        formAction={formAction}
        setModalTitle={setModalTitle}
        callback={() => {
          productsQuery.refetch();
        }}
        disabledSelectBrand={formAction === CURD.UPDATE}
      />
      {contextHolder}
    </div>
  );
};

export default ProductPage;
