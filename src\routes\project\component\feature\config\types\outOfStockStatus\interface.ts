import { AbstractEntityInterface, ImageInterface } from "@/common/interface";
import {
  ProductInterface,
  ProductPackagingInterface,
} from "@/routes/product/interface";
import { ProjectBrandInterface } from "@/routes/project/interface";
import { ProjectOutletInterface } from "@/routes/project/outlet/interface";
import { ProjectProductInterface } from "@/routes/project/product/interface";
import { UnitInterface } from "@/routes/unit/UnitPage";

export enum OutOfStockStatusStepEnum {
  GROUP = "group",
  STATUS = "status",
  OUTLET = "outlet",
  MERGED_PRODUCT = "mergedProduct",
  ZONE = "zone",
  THRESHOLD_PRODUCT = "thresholdProduct",
  THRESHOLD_MERGED_PRODUCT = "thresholdMergedProduct",
}

export interface OosLevelInterface extends AbstractEntityInterface {
  name: string;
  backgroundColor: string;
  foregroundColor: string;
  isStandard: boolean;
  ordinal: number;
}

export interface OosGroupInterface extends AbstractEntityInterface {
  name: string;
}

export interface OosGroupOutletInterface extends AbstractEntityInterface {
  featureOosGroup: OosGroupInterface;
  projectOutlet: ProjectOutletInterface;
}

export interface OosThresholdInterface extends AbstractEntityInterface {
  featureOosLevelId: number;
  upperValue?: number;
  lowerValue: number;
  featureOosLevel?: OosLevelInterface;
}

export interface OosProductInterface extends AbstractEntityInterface {
  ordinal: number;
  projectProduct: ProjectProductInterface;
  featureOosMergedProduct?: OosMergedProductInterface;
  isAvailable?: boolean;
  featureOosThresholds: OosThresholdInterface[];
  projectProductId: number;
  expectedStock: number;
}

export enum OosGroupCollapseChildActionEnum {
  INACTIVE = "INACTIVE",
  ACTIVE = "ACTIVE",
  DELETE = "DELETE",
}

export interface OosMergedProductInterface extends AbstractEntityInterface {
  productCode: string;
  productName: string;
  ordinal: number;
  projectBrand: ProjectBrandInterface;
  unit: UnitInterface;
  image?: ImageInterface;
  featureOosMergedProductItemsCount?: number;
  featureOosThresholds: OosThresholdInterface[];
  code: string;
  productShortName: string;
}

export interface OosZoneInterface extends AbstractEntityInterface {
  name: string;
  description: string;
  ordinal: number;
  code: string;
}

export enum OosThresholdTypeEnum {
  PRODUCTS = "products",
  MERGED_PRODUCTS = "merged-products",
}

export interface OosMergedProductItemAvailableInterface
  extends AbstractEntityInterface {
  product: ProductInterface;
  productPackaging: ProductPackagingInterface;
  featureOosMergedProductItems: OosMergedProductInterface[];
  isAvailable: boolean;
}
