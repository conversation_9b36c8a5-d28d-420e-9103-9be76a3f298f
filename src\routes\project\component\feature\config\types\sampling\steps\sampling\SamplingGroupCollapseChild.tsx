import { BTN_CANCEL_TEXT, BTN_CONFIRM_TEXT, CURD } from "@/common/constant";
import DragSortRowComponent from "@/components/DragSortRowComponent.tsx";
import ProductItemCell from "@/components/ProductItemCell.tsx";
import { renderTableCell } from "@/components/table-cell.tsx";
import { renderTableOptionCell } from "@/components/table-option-cell.tsx";
import { useApp } from "@/UseApp.tsx";
import { ArrowRightOutlined } from "@ant-design/icons";
import { DndContext, DragEndEvent } from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
  arrayMove,
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { ProjectProductInterface } from "@project/product/interface.ts";
import { But<PERSON>, Form, Modal, Table } from "antd";
import { useCallback, useEffect, useState } from "react";
import { FeatureSamplingInterface } from "../../interface.ts";
import {
  useArrangementSamplingGroupSamplingsMutation,
  useDeleteSamplingInSamplingGroupMutation,
  useSamplingGroupSamplingsQuery,
  useUpdateSamplingInSamplingGroupMutation,
} from "../../service";
import ConfigSamplingModal from "./ConfigSamplingModal";

interface SamplingGroupCollapseChildProps {
  componentFeatureId: number;
  samplingGroupId: number;
  projectId: number;
}

const SamplingGroupCollapseChild = ({
  componentFeatureId,
  samplingGroupId,
  projectId,
}: SamplingGroupCollapseChildProps) => {
  const { showNotification, openDeleteModal } = useApp();

  const [isOpen, setIsOpen] = useState(false);
  const [action, setAction] = useState<CURD | null>(null);
  const [form] = Form.useForm();

  const samplingGroupSamplingsQuery = useSamplingGroupSamplingsQuery(
    componentFeatureId,
    samplingGroupId,
    { take: 0, skip: 0 },
  );
  const [dataSource, setDataSource] = useState(
    samplingGroupSamplingsQuery.data?.entities ?? [],
  );

  const updateSamplingInSamplingGroupMutation =
    useUpdateSamplingInSamplingGroupMutation(
      componentFeatureId,
      samplingGroupId,
    );
  const deleteSamplingInSamplingGroupMutation =
    useDeleteSamplingInSamplingGroupMutation(
      componentFeatureId,
      samplingGroupId,
    );
  const arrangementSamplingGroupSamplingsMutation =
    useArrangementSamplingGroupSamplingsMutation(
      componentFeatureId,
      samplingGroupId,
    );

  const onAddSampling = useCallback(() => {
    setIsOpen(true);
    setAction(CURD.CREATE);
  }, []);

  const handleBtnEditClick = useCallback(
    (record: FeatureSamplingInterface) => {
      setIsOpen(true);
      setAction(CURD.UPDATE);
      form.setFieldsValue({
        ...record,
        brandId: record.projectProduct.product.brand.id,
        projectProductId: record.projectProduct.id,
        unitName: record.projectProduct.productPackaging?.unit.name,
        unitId: record.unit.id,
      });
    },
    [form],
  );

  const handleBtnInactiveClick = useCallback(
    (record: FeatureSamplingInterface) => {
      Modal.confirm({
        title: `Ngừng hoạt động sampling: ${record.projectProduct.product.name}`,
        content: "Bạn có chắc chắn muốn ngừng hoạt động sampling này?",
        okText: BTN_CONFIRM_TEXT,
        cancelText: BTN_CANCEL_TEXT,
        onOk: async () => {
          await updateSamplingInSamplingGroupMutation.mutateAsync({
            isActive: false,
            id: record.id,
          });

          showNotification({
            type: "success",
            message: "Ngừng hoạt động thành công.",
          });
          await samplingGroupSamplingsQuery.refetch();
        },
      });
    },
    [
      samplingGroupSamplingsQuery,
      showNotification,
      updateSamplingInSamplingGroupMutation,
    ],
  );

  const handleBtnActiveClick = useCallback(
    (record: FeatureSamplingInterface) => {
      Modal.confirm({
        title: `Kích hoạt sampling: ${record.projectProduct.product.name}`,
        content: "Bạn có chắc chắn muốn kích hoạt  sampling này?",
        okText: BTN_CONFIRM_TEXT,
        cancelText: BTN_CANCEL_TEXT,
        onOk: async () => {
          await updateSamplingInSamplingGroupMutation.mutateAsync({
            isActive: true,
            id: record.id,
          });

          showNotification({
            type: "success",
            message: "Kích hoạt thành công.",
          });
          await samplingGroupSamplingsQuery.refetch();
        },
      });
    },
    [
      samplingGroupSamplingsQuery,
      showNotification,
      updateSamplingInSamplingGroupMutation,
    ],
  );

  const handleBtnDeleteClick = useCallback(
    (record: FeatureSamplingInterface) => {
      openDeleteModal({
        content: (
          <p>
            Bạn muốn xóa sampling{" "}
            <span className={"font-semibold"}>
              {record.projectProduct.product.name}
            </span>{" "}
            khỏi dự án?
          </p>
        ),
        deleteText: "Xác nhận xóa",
        loading: false,
        onCancel(): void {},
        onDelete: async () => {
          await deleteSamplingInSamplingGroupMutation.mutateAsync(record.id);

          showNotification({
            type: "success",
            message: "Xóa sampling thành công",
          });

          await samplingGroupSamplingsQuery.refetch();
        },
        title: `Xóa sampling`,
        titleError: "Không thể xóa sampling",
        contentHeader: (
          <>
            Không thể xóa sampling{" "}
            <span className="font-semibold">
              {record.projectProduct.product.name}
            </span>{" "}
            bởi vì:
          </>
        ),
      });
    },
    [
      deleteSamplingInSamplingGroupMutation,
      openDeleteModal,
      samplingGroupSamplingsQuery,
      showNotification,
    ],
  );

  const onDragEnd = useCallback(
    async ({ active, over }: DragEndEvent) => {
      if (!over) {
        return;
      }
      if (active.id !== over?.id) {
        setDataSource((previous) => {
          const activeIndex = previous?.findIndex((i) => i.id === active.id);
          const overIndex = previous?.findIndex((i) => i.id === over?.id);
          return arrayMove(previous, activeIndex, overIndex);
        });

        await arrangementSamplingGroupSamplingsMutation.mutateAsync({
          activeId: active.id as number,
          overId: over?.id as number,
        });
      }
    },
    [arrangementSamplingGroupSamplingsMutation],
  );

  useEffect(() => {
    setDataSource(
      (samplingGroupSamplingsQuery.data?.entities ?? []).sort(
        (a, b) => a.ordinal - b.ordinal,
      ),
    );
  }, [samplingGroupSamplingsQuery.data]);

  return (
    <>
      <div className="flex justify-end">
        <Button type="link" className="text-blue" onClick={onAddSampling}>
          Thêm sampling
        </Button>
      </div>

      <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
        <SortableContext
          items={dataSource.map((i) => i.id)}
          strategy={verticalListSortingStrategy}
        >
          <Table
            dataSource={dataSource}
            rowKey={(o) => o.id}
            pagination={false}
            loading={
              samplingGroupSamplingsQuery.isFetching ||
              arrangementSamplingGroupSamplingsMutation.isPending
            }
            components={{
              body: {
                row: DragSortRowComponent,
              },
            }}
            columns={[
              {
                key: "sort",
              },
              {
                title: "Tên",
                dataIndex: "projectProduct",
                render: (projectProduct: ProjectProductInterface) => {
                  const { product } = projectProduct;
                  return (
                    <ProductItemCell
                      variants={product?.image?.variants ?? []}
                      name={product.name}
                    />
                  );
                },
              },
              {
                title: "Mã",
                dataIndex: "projectProduct",
                render: (projectProduct: ProjectProductInterface) =>
                  projectProduct.product.code,
              },
              {
                title: "Nhãn hàng",
                dataIndex: "projectProduct",
                render: (projectProduct: ProjectProductInterface) =>
                  projectProduct.product.brand.name,
              },
              {
                title: "Tỷ lệ quy đổi",
                render: (_, record) => {
                  const { numerator, projectProduct, denominator, unit } =
                    record;
                  return (
                    <>
                      {numerator} {projectProduct?.productPackaging?.unit.name}{" "}
                      <ArrowRightOutlined /> {denominator} {unit.name}
                    </>
                  );
                },
              },
              {
                key: "isActive",
                title: "Tình trạng",
                dataIndex: "isActive",
                render: (value, record, index) => {
                  return renderTableCell(value, record, index, "isActive");
                },
              },
              {
                key: "actions",
                render: (_, record) => {
                  return renderTableOptionCell(
                    record,
                    handleBtnEditClick,
                    handleBtnInactiveClick,
                    handleBtnActiveClick,
                    handleBtnDeleteClick,
                  );
                },
                fixed: "right",
                width: 100,
              },
            ]}
          />
        </SortableContext>
      </DndContext>

      {isOpen && action && (
        <ConfigSamplingModal
          action={action}
          isOpen={isOpen}
          projectId={projectId}
          componentFeatureId={componentFeatureId}
          samplingGroupId={samplingGroupId}
          onCancelCb={async () => {
            setIsOpen(false);
            setAction(null);
            await samplingGroupSamplingsQuery.refetch();
            form.resetFields();
          }}
          form={form}
        />
      )}
    </>
  );
};

export default SamplingGroupCollapseChild;
