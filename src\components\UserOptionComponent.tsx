import { Avatar, Space, Tag } from "antd";
import React from "react";
import renderStatusOnTopCell from "./renderStatusOnTopCell";

interface UserOptionComponentProps {
  readonly avatarUrl?: string;
  readonly name?: string;
  readonly phone?: string;
  readonly email?: string;
  readonly className?: string;
  readonly style?: React.CSSProperties;
  readonly isAvailable?: boolean;
  readonly roleName?: string;
  readonly isInOtherGroup?: boolean;
  readonly isInThisGroup?: boolean;
  readonly isInActive?: boolean;
  readonly roleLocation?: "top" | "bottom";
  readonly id?: number | string;
}

export default function UserOptionComponent(props: UserOptionComponentProps) {
  const {
    avatarUrl,
    name,
    phone,
    email,
    className,
    style,
    isAvailable,
    roleName,
    isInOtherGroup,
    isInThisGroup,
    isInActive,
    roleLocation,
    id,
  } = props;

  return (
    <Space className={className} style={style}>
      <Avatar src={avatarUrl}>
        {avatarUrl ? "" : name?.charAt(0).toUpperCase()}
      </Avatar>
      <div className="pl-3">
        {isAvailable === false &&
          renderStatusOnTopCell("Đã thêm vào dự án", "#808080", "#E5E5E5")}
        {isInOtherGroup &&
          renderStatusOnTopCell("Đã thêm vào nhóm khác", "#393939", "#F5F5F5")}
        {isInThisGroup &&
          renderStatusOnTopCell(
            "Đã thêm vào nhóm hiện tại",
            "#008916",
            "#E5F5E7",
          )}
        {isInActive &&
          renderStatusOnTopCell("Ngừng hoạt động", "#DF3C3C", "#FEE")}

        <p className="p-0 m-0 text-black">
          <span className="font-normal"> {name} </span>
          {roleName && (!roleLocation || roleLocation === "top") && (
            <Tag color="#126BFB" className="rounded-xl ml-1">
              {roleName}
            </Tag>
          )}
        </p>

        {id && <p className="p-0 m-0 text-sm text-gray-400">ID: {id}</p>}

        <p className="p-0 m-0 text-sm text-gray-400">{phone}</p>

        <p className="p-0 m-0 text-sm text-gray-400">{email}</p>

        {roleName && roleLocation === "bottom" && (
          <p className="p-0 m-0 text-xs font-bold">
            <Tag color="#126BFB" className="rounded-xl">
              {roleName}
            </Tag>
          </p>
        )}
      </div>
    </Space>
  );
}
