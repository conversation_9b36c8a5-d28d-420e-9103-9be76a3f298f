import { UploadFile } from "antd/lib";
import React from "react";

export const validateExcelData = (
  data: object[],
  headers: { header: string }[],
) => {
  if (data.length === 0) {
    throw new Error("File không có dữ liệu.");
  }
  const valuesHeadersData = Object.values(data[0]);

  headers.forEach(({ header }, index) => {
    if (header.trim() !== valuesHeadersData[index]?.trim()) {
      throw new Error("Template import không đúng định dạng.");
    }
  });

  data.splice(0, 2);
};

export const setExcelError = (
  message: string,
  setFileList: (value: React.SetStateAction<UploadFile<unknown>[]>) => void,
  setIsFileError: (value: React.SetStateAction<boolean>) => void,
  setExcelErrorMessage: (value: React.SetStateAction<string>) => void,
) => {
  setFileList((fileList) => {
    if (fileList.length > 0) {
      const file = fileList[0];
      setIsFileError(true);
      setExcelErrorMessage(
        message === 'Worksheet "Data" not found in workbook.'
          ? "Template import không đúng định dạng."
          : message,
      );

      return [
        {
          ...file,
          status: "error",
          response: message,
        },
      ];
    }

    return [];
  });
};
