import { AbstractEntityInterface } from "@/common/interface";
import { UnitInterface } from "@/routes/unit/UnitPage";
import { ProjectItemInterface } from "@project/item/interface";
import { ProjectOutletInterface } from "@project/outlet/interface";
import { ProjectProductInterface } from "@project/product/interface";

export interface OrderInterface extends AbstractEntityInterface {
  projectFeatureId: number;
  hasPurchase: boolean;
  hasExchange: boolean;
  hasCustomer: boolean;
  hasPhoto: boolean;
  hasSampling: boolean;
  isCustomerRequired: boolean;
  isPurchaseRequired: boolean;
  isExchangeRequired: boolean;
  isSamplingRequired: boolean;
  isPhotoRequired: boolean;
  isIdentity: boolean;
}

export enum StepLockEnum {
  Purchase = "purchase",
  Exchange = "exchange",
  Customer = "customer",
  Photo = "photo",
  Sampling = "sampling",
}

export const OrderEnumToProperty = {
  [StepLockEnum.Purchase]: "hasPurchase",
  [StepLockEnum.Exchange]: "hasExchange",
  [StepLockEnum.Customer]: "hasCustomer",
  [StepLockEnum.Photo]: "hasPhoto",
  [StepLockEnum.Sampling]: "hasSampling",
};

export interface ExchangeProceedInterface extends AbstractEntityInterface {
  featureSchemeExchangeId: number;
  projectProductId: number | null;
  projectProduct: ProjectProductInterface | null;
  projectItemId: number | null;
  projectItem: ProjectItemInterface | null;
  quantity: number;
}

export interface ExchangeConditionInterface extends AbstractEntityInterface {
  featureSchemeExchangeId: number;
  projectProductId: number;
  projectProduct: ProjectProductInterface;
  quantity: number;
  tag?: string;
}

export interface FeatureSchemeExchangeInterface
  extends AbstractEntityInterface {
  name: string;
  code: string;
  description?: string;
  featureSchemeId: number;
  hasPlayedGame: boolean;
  maxReceiveQuantity: number;
  reachAmount?: number;
  logical: "or" | "and" | "relaxed";
  reachQuantity: number;
  exchangeConditions: ExchangeConditionInterface[];
  exchangeProceeds: ExchangeProceedInterface[];
  luckyDraw: {
    name: string;
    id: number;
  };
  luckyDrawCount: number;
  luckyDrawId?: number;
}

export interface FeatureSchemeInterface extends AbstractEntityInterface {
  name: string;
  code: string;
  description?: string;
  projectFeatureId: number;
  projectAgencyId: number;
  maxReceiveQuantity: number;
  featureSchemeOutletsCount: number;
  featureSchemeExchanges: FeatureSchemeExchangeInterface[];
  featureSchemeOutlets: FeatureSchemeOutletInterface[];
}

export enum ConfigExchangeTabEnum {
  ACTIVE = "ACTIVE",
  INACTIVE = "INACTIVE",
  DELETE = "DELETE",
  EDIT = "EDIT",
}

export enum ConfigOutletTabEnum {
  ADD = "ADD",
  OUTLET_LIST = "OUTLET_LIST",
  DETAIL = "DETAIL",
}

export interface FeatureSchemeOutletInterface extends AbstractEntityInterface {
  featureSchemeId: number;
  featureScheme: FeatureSchemeInterface;
  projectOutletId: number;
  projectOutlet: ProjectOutletInterface;
}

export enum FeatureCustomerDataTypeEnum {
  STRING = "string",
  NUMBER = "number",
  RADIO = "radio",
  EMAIL = "email",
  DATE = "date",
  CHECKBOX = "checkbox",
  PHONE_NUMBER = "phoneNumber",
}

export const FeatureCustomerActionEnumToLabel = {
  [FeatureCustomerDataTypeEnum.STRING]: "Văn bản",
  [FeatureCustomerDataTypeEnum.NUMBER]: "Chữ số",
  [FeatureCustomerDataTypeEnum.RADIO]: "Lựa chọn",
  [FeatureCustomerDataTypeEnum.EMAIL]: "Email",
  [FeatureCustomerDataTypeEnum.DATE]: "Ngày/Tháng/Năm",
  [FeatureCustomerDataTypeEnum.CHECKBOX]: "Checkbox",
  [FeatureCustomerDataTypeEnum.PHONE_NUMBER]: "Số điện thoại",
};

export interface FeatureCustomerOptionInterface
  extends AbstractEntityInterface {
  name: string;
}

export interface FeatureCustomerVerificationInterface
  extends AbstractEntityInterface {
  mode: string;
  isRequired: boolean;
}

export interface FeatureCustomerInterface extends AbstractEntityInterface {
  featureSchemeId: number;
  featureScheme: FeatureSchemeInterface;
  projectFeatureId: number;
  name: string;
  description?: string;
  ordinal: number;
  dataType: FeatureCustomerDataTypeEnum;
  isIdentity: boolean;
  isRequired: boolean;
  featureCustomerOptions: FeatureCustomerOptionInterface[];
  code: string;
  featureCustomerVerification?: FeatureCustomerVerificationInterface;
}

export enum ConfigCustomerEnum {
  EDIT = "EDIT",
  ACTIVE = "ACTIVE",
  INACTIVE = "INACTIVE",
  DELETE = "DELETE",
}

export interface CustomerFeatureSamplingInterface
  extends AbstractEntityInterface {
  projectFeatureId: number;
  ordinal: number;
  projectProductId: number;
  projectProduct: ProjectProductInterface;
  unit: UnitInterface;
}

export enum ConfigSamplingEnum {
  DELETE = "DELETE",
  ACTIVE = "ACTIVE",
  INACTIVE = "INACTIVE",
}

export interface OrderProductInterface extends AbstractEntityInterface {
  featureOrderId: number;
  projectProductId: number;
  projectProduct: ProjectProductInterface;
  price: number;
}

export enum OrderLimitRestrictionEnum {
  PROJECT = "project",
  AGENCY = "agency",
  OUTLET = "outlet",
  BOOTH = "booth",
}

export enum OrderLimitTypeEnum {
  EXCHANGE = "exchange",
  PURCHASE = "purchase",
  ORDER = "order",
  LUCKY_DRAW = "lucky_draw",
}

export const OrderLimitRestrictionEnumToLabel = {
  [OrderLimitRestrictionEnum.PROJECT]: " / Khách / Project",
  [OrderLimitRestrictionEnum.AGENCY]: " / Khách / Agency",
  [OrderLimitRestrictionEnum.OUTLET]: " / Khách / Outlet",
  [OrderLimitRestrictionEnum.BOOTH]: " / Khách / Booth",
};

export const OrderLimitTypeEnumToLabel = {
  [OrderLimitTypeEnum.ORDER]: "đơn",
  [OrderLimitTypeEnum.EXCHANGE]: "quà",
  [OrderLimitTypeEnum.PURCHASE]: "sản phẩm",
  [OrderLimitTypeEnum.LUCKY_DRAW]: "vòng quay",
};

export const OrderLimitTypeEnumToHeaderLabel = {
  [OrderLimitTypeEnum.ORDER]: "Đơn hàng",
  [OrderLimitTypeEnum.EXCHANGE]: "Quà",
  [OrderLimitTypeEnum.PURCHASE]: "Sản phẩm",
  [OrderLimitTypeEnum.LUCKY_DRAW]: "Vòng quay",
};

export const getOrderLimitRestrictionLabel = (
  orderLimitType: OrderLimitTypeEnum,
  orderLimitRestriction: OrderLimitRestrictionEnum,
) => {
  return (
    OrderLimitTypeEnumToHeaderLabel[orderLimitType] +
    OrderLimitRestrictionEnumToLabel[orderLimitRestriction]
  );
};
