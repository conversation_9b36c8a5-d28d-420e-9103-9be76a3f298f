import { AbstractFilterInterface } from "@/common/interface.ts";
import { useApp } from "@/UseApp.tsx";
import {
  OosGroupInterface,
  OosProductInterface,
} from "@project/component/feature/config/types/outOfStockStatus/interface.ts";
import { ProjectProductInterface } from "@project/product/interface.ts";
import { useMutation, useQuery } from "@tanstack/react-query";

export const useOosGroupsQuery = (
  featureId: number,
  filter?: AbstractFilterInterface & object,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["oosGroups", featureId, filter],
    queryFn: () =>
      axiosGet<
        {
          entities: OosGroupInterface[];
          count: number;
        },
        unknown
      >(`/features/${featureId}/oos-groups`, filter),
  });
};

export const useCreateOosGroupMutation = (featureId: number) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createOosGroup", featureId],
    mutationFn: (data: { name: string }) =>
      axiosPost(`/features/${featureId}/oos-groups`, data),
  });
};

export const useUpdateOosGroupMutation = (featureId: number) => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["updateOosGroup", featureId],
    mutationFn: (data: { name?: string; isActive?: boolean; id: number }) =>
      axiosPatch(`/features/${featureId}/oos-groups/${data.id}`, data),
  });
};

export const useDeleteOosGroupMutation = (featureId: number) => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deleteOosGroup", featureId],
    mutationFn: (id: number) =>
      axiosDelete(`/features/${featureId}/oos-groups/${id}`),
  });
};

export const useCreateOosGroupProductMutation = (
  featureId: number,
  oosGroupId: number,
) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createOosGroupProduct", featureId, oosGroupId],
    mutationFn: (data: { projectProductId: number }[]) =>
      axiosPost(
        `/features/${featureId}/oos-groups/${oosGroupId}/products`,
        data,
      ),
  });
};

export const useOosGroupProductAvailablesQuery = (
  featureId: number,
  oosGroupId: number,
  filter?: AbstractFilterInterface & object,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["oosGroupProductAvailables", featureId, oosGroupId, filter],
    queryFn: () =>
      axiosGet<
        {
          entities: ProjectProductInterface[];
          count: number;
        },
        unknown
      >(
        `/features/${featureId}/oos-groups/${oosGroupId}/products/availables`,
        filter,
      ),
  });
};

export const useOosGroupProductsQuery = (
  featureId: number,
  oosGroupId: number,
  filter?: AbstractFilterInterface & object,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["oosGroupProducts", featureId, oosGroupId, filter],
    queryFn: () =>
      axiosGet<{ entities: OosProductInterface[]; count: number }, unknown>(
        `/features/${featureId}/oos-groups/${oosGroupId}/products`,
        filter,
      ),
  });
};

export const useUpdateOosGroupProductMutation = (
  featureId: number,
  oosGroupId: number,
) => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["updateOosGroupProduct", featureId, oosGroupId],
    mutationFn: (data: { id: number; isActive?: boolean }) =>
      axiosPatch(
        `/features/${featureId}/oos-groups/${oosGroupId}/products/${data.id}`,
        data,
      ),
  });
};

export const useDeleteOosGroupProductMutation = (
  featureId: number,
  oosGroupId: number,
) => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deleteOosGroupProduct", featureId, oosGroupId],
    mutationFn: (id: number) =>
      axiosDelete(
        `/features/${featureId}/oos-groups/${oosGroupId}/products/${id}`,
      ),
  });
};

export const useArrangeOosGroupProductMutation = (
  featureId: number,
  oosGroupId: number,
) => {
  const { axiosPut } = useApp();

  return useMutation({
    mutationKey: ["arrangeOosGroupProduct", featureId, oosGroupId],
    mutationFn: (data: { id: number; overFeatureOosProductId: number }) =>
      axiosPut(
        `/features/${featureId}/oos-groups/${oosGroupId}/products/${data.id}/arrangement`,
        data,
      ),
  });
};
