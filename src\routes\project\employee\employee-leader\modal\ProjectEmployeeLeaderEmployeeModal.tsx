import { useApp } from "@/UseApp";
import { DEFAULT_CURRENT_PAGE, DEFAULT_PAGE_SIZE } from "@/common/constant";
import { filterOption } from "@/common/helper";
import FilterClassicComponent from "@/components/FilterClassicComponent";
import UserOptionComponent from "@/components/UserOptionComponent";
import { renderTableCell } from "@/components/table-cell";
import { UserInterface } from "@/routes/user/interface";
import { CloseOutlined, SearchOutlined } from "@ant-design/icons";
import { RoleInterface } from "@project/role/interface";
import { getProjectRoles } from "@project/role/service";
import { Button, Form, Input, Modal, Select, Table } from "antd";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import {
  ApiProjectEmployeeUserResponseInterface,
  ProjectEmployeeUserInterface,
} from "../../interface";
import { useAddEmployeeToLeaderMutation } from "../../service";

interface ProjectEmployeeLeaderEmployeeModalProps {
  readonly isOpen: boolean;
  readonly setIsOpen: (isOpen: boolean) => void;
  readonly leader?: ProjectEmployeeUserInterface;
  readonly projectId: number;
  readonly cb: () => void;
}

const ProjectEmployeeLeaderEmployeeModal = (
  props: ProjectEmployeeLeaderEmployeeModalProps,
) => {
  const { isOpen, setIsOpen, leader, projectId, cb } = props;

  const { loading, setLoading, axiosGet, showNotification } = useApp();

  const [searchForm] = Form.useForm();
  const [roles, setRoles] = useState<RoleInterface[]>([]);
  const [selectedEmployeeKeys, setSelectedEmployeeKeys] = useState<React.Key[]>(
    [],
  );
  const [data, setData] = useState<ProjectEmployeeUserInterface[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);

  const addEmployeeToLeaderMutation = useAddEmployeeToLeaderMutation(
    projectId,
    leader?.id,
  );

  const onCancel = useCallback(() => {
    setIsOpen(false);
    setSelectedEmployeeKeys([]);
    setData([]);
  }, [setIsOpen]);

  const fetchData = useCallback(
    async (currentPageInput?: number, pageSizeInput?: number) => {
      setLoading(true);

      const filter = {
        take: pageSizeInput ?? pageSize,
        skip: pageSizeInput
          ? 0
          : (currentPageInput ?? currentPage - 1) * pageSize,
        ...searchForm.getFieldsValue(),
      };
      const response = await axiosGet<
        ApiProjectEmployeeUserResponseInterface,
        unknown
      >(
        `/projects/${projectId}/agencies/${leader?.projectAgencyId}/members`,
        filter,
      );

      if (!Array.isArray(response)) {
        setData(response.entities);
        setTotal(response.count);
      }
      setLoading(false);
    },
    [
      axiosGet,
      currentPage,
      leader?.projectAgencyId,
      pageSize,
      projectId,
      searchForm,
      setLoading,
    ],
  );

  const fetchRoles = useCallback(async () => {
    const response = await getProjectRoles(axiosGet, projectId, {
      take: 0,
      skip: 0,
      type: "EMPLOYEE",
    });
    if (!Array.isArray(response)) {
      setRoles(response.entities.filter((role) => role.id !== leader?.role.id));
    }
  }, [axiosGet, leader?.role.id, projectId]);

  const searchOnSelectHandler = useCallback(() => {
    fetchData(DEFAULT_CURRENT_PAGE, pageSize);
    setCurrentPage(DEFAULT_CURRENT_PAGE);
  }, [fetchData, pageSize]);

  const searchOnSelectContent = (
    <>
      <Form.Item name="keyword">
        <Input
          placeholder="Tìm theo tên, sđt, email, username"
          allowClear
          prefix={<SearchOutlined />}
        />
      </Form.Item>
      <Form.Item name="roleId">
        <Select
          allowClear
          placeholder="Vị trí"
          showSearch
          optionFilterProp="children"
          filterOption={filterOption}
          options={roles.map((role) => ({
            label: role.name,
            value: role.id,
          }))}
          popupMatchSelectWidth={false}
        />
      </Form.Item>
    </>
  );

  const columns = [
    {
      title: "Nhân viên",
      dataIndex: "user",
      key: "user.name",
      className: "min-w-[100px]",
      render: (value: UserInterface, record: ProjectEmployeeUserInterface) => {
        return (
          <UserOptionComponent
            avatarUrl={value.picture}
            name={value.name}
            phone={value.phone}
            email={value.email}
            isInOtherGroup={
              !!record?.projectEmployeeUser?.id &&
              record?.projectEmployeeUser?.id !== leader?.id
            }
            isInThisGroup={record?.projectEmployeeUser?.id === leader?.id}
            isInActive={!record.isActive}
          />
        );
      },
    },
    {
      title: "Username",
      dataIndex: "user",
      key: "user.username",
      className: "min-w-[100px]",
      render: (value: UserInterface) => value?.username,
    },
    {
      title: "Vị trí",
      dataIndex: "role",
      key: "role",
      className: "min-w-[100px]",
      render: renderTableCell,
    },
    {
      title: "Trưởng nhóm quản lý",
      dataIndex: "projectEmployeeUser",
      key: "projectEmployeeUser",
      className: "min-w-[100px]",
      render: (value?: ProjectEmployeeUserInterface) => {
        const user = value?.user;
        if (!user) return "";
        return (
          <UserOptionComponent
            avatarUrl={user.picture}
            name={user.name}
            phone={user.phone}
            email={user.email}
          />
        );
      },
    },
  ];

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedEmployeeKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys: selectedEmployeeKeys,
    onChange: onSelectChange,
    getCheckboxProps: (record: ProjectEmployeeUserInterface) => {
      return {
        disabled: !!record?.projectEmployeeUser?.id || !record?.isActive,
      };
    },
  };

  const handlePaginationChange = useCallback(
    (page: number) => {
      setCurrentPage(page);
      fetchData(page);
    },
    [setCurrentPage, fetchData],
  );

  const handlePageSizeChange = useCallback(
    (_current: number, size: number) => {
      setPageSize(size);
      setCurrentPage(DEFAULT_CURRENT_PAGE);
      fetchData(DEFAULT_CURRENT_PAGE, size);
    },
    [setPageSize, setCurrentPage, fetchData],
  );

  const pagination = useMemo(() => {
    return {
      current: currentPage,
      total: total,
      pageSize: pageSize,
      onChange: handlePaginationChange,
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: handlePageSizeChange,
    };
  }, [
    currentPage,
    total,
    pageSize,
    handlePaginationChange,
    handlePageSizeChange,
  ]);

  useEffect(() => {
    setSelectedEmployeeKeys(
      data
        .filter((item) => item?.projectEmployeeUser?.id === leader?.id)
        .map((item) => item.id),
    );
  }, [data, leader?.id]);

  const newSelectedEmloyeeKeys = useMemo(() => {
    return data
      .filter((item) => selectedEmployeeKeys.includes(item.id))
      .filter((item) => !item.projectEmployeeUser?.id)
      .map((item) => item.id);
  }, [data, selectedEmployeeKeys]);

  const onConfirm = useCallback(async () => {
    setLoading(true);

    try {
      await addEmployeeToLeaderMutation.mutateAsync(newSelectedEmloyeeKeys);

      setIsOpen(false);
      fetchData();
      setSelectedEmployeeKeys([]);
      showNotification({
        type: "success",
        message: "Thêm nhân viên thành công.",
      });
      cb();
    } catch (e) {
      console.error(e);
    } finally {
      setLoading(false);
    }
  }, [
    addEmployeeToLeaderMutation,
    cb,
    fetchData,
    newSelectedEmloyeeKeys,
    setIsOpen,
    setLoading,
    showNotification,
  ]);

  useEffect(() => {
    if (isOpen && leader) {
      fetchData();
      fetchRoles();
    }
  }, [fetchData, fetchRoles, isOpen, leader]);

  return (
    <Modal
      open={isOpen}
      footer={null}
      closeIcon={null}
      styles={{ content: { padding: 0 } }}
      width={1000}
      onCancel={onCancel}
    >
      <div className="pl-10 pr-10">
        <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
          <h2 className="text-neutral-700 text-2xl font-semibold ">
            Thêm nhân viên vào nhóm
          </h2>
          <div className="pt-5">
            <Button
              type="link"
              onClick={onCancel}
              size="large"
              icon={<CloseOutlined />}
            />
          </div>
        </div>
        <h3>Trưởng nhóm</h3>
        <div className="table">
          <div className="border-solid border-2 border-[#C4D6FF] p-2 table-cell bg-[#F0F8FF] rounded">
            <UserOptionComponent
              avatarUrl={leader?.user.picture}
              name={leader?.user.name}
              phone={leader?.user.phone}
              email={leader?.user.email}
              roleName={leader?.role.name}
            />
          </div>
          &nbsp; &nbsp; &nbsp;
          <div className="border-solid border-2 border-[#C4D6FF] p-2 table-cell bg-[#F0F8FF] rounded pl-5 pr-10">
            <p className="text-[#8C8C8D] m-0">Trực thuộc Agency</p>
            <p className="text-[#393939] m-0">
              {leader?.projectAgency?.agency.name}
            </p>
          </div>
        </div>

        <FilterClassicComponent
          searchHandler={searchOnSelectHandler}
          searchForm={searchForm}
          content={searchOnSelectContent}
          className="pb-5 pt-5"
          handleAddButtonClick={() => {
            setIsOpen(true);
          }}
        />

        <Table
          dataSource={data}
          columns={columns}
          rowKey={(record) => record.id}
          rowSelection={rowSelection}
          scroll={{
            x: "max-content",
            y: pagination.total ? "80vh" : undefined,
          }}
          pagination={pagination}
          loading={loading}
        />
      </div>
      <div className="flex justify-end gap-4 py-4 max-md:max-w-full max-md:flex-wrap pb-4 pl-10 pr-10 bg-[#F7F8FA] rounded">
        <Button htmlType="button" loading={loading} onClick={onCancel}>
          Đóng
        </Button>
        <Button
          htmlType="submit"
          type={"primary"}
          loading={loading}
          disabled={newSelectedEmloyeeKeys.length === 0}
          onClick={onConfirm}
        >
          {`Thêm ${newSelectedEmloyeeKeys.length} nhân viên vào nhóm`}
        </Button>
      </div>
    </Modal>
  );
};

export default ProjectEmployeeLeaderEmployeeModal;
