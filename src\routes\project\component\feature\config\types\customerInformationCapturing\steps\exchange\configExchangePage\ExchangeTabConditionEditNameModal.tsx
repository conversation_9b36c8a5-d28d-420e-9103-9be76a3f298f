import ModalCURD from "@/components/ModalCURD";
import { useApp } from "@/UseApp";
import { Form, Input } from "antd";
import { FormInstance } from "antd/lib";
import { useCallback } from "react";
import { useUpdateSchemeExchangeMutation } from "../service";

interface ExchangeTabConditionEditNameModalProps {
  open: boolean;
  componentFeatureId: number;
  cb: () => void;
  form: FormInstance;
}

const ExchangeTabConditionEditNameModal = ({
  open,
  componentFeatureId,
  cb,
  form,
}: ExchangeTabConditionEditNameModalProps) => {
  const { showNotification } = useApp();

  const updateSchemeExchangeMutation =
    useUpdateSchemeExchangeMutation(componentFeatureId);

  const formEditSchemeContent = (
    <>
      <Form.Item name={"id"} hidden>
        <Input />
      </Form.Item>
      <Form.Item name={"featureSchemeId"} hidden>
        <Input />
      </Form.Item>
      <Form.Item
        name={"name"}
        label={"Tên điều kiện nhận quà"}
        rules={[{ required: true }]}
      >
        <Input />
      </Form.Item>
    </>
  );

  const onFinishEditScheme = useCallback(async () => {
    const { id, featureSchemeId, name } = form.getFieldsValue();

    await updateSchemeExchangeMutation.mutateAsync({
      name,
      schemeId: featureSchemeId,
      exchangeId: id,
    });

    showNotification({
      type: "success",
      message: "Chỉnh sửa scheme thành công",
    });
    cb();
  }, [cb, form, showNotification, updateSchemeExchangeMutation]);

  return (
    <ModalCURD
      title={`Cập nhật điều kiện nhận quà`}
      isOpen={open}
      formContent={formEditSchemeContent}
      form={form}
      onFinish={onFinishEditScheme}
      onCancelCb={cb}
    />
  );
};

export default ExchangeTabConditionEditNameModal;
