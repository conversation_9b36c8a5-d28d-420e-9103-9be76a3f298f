import { AbstractEntityInterface } from "@/common/interface.ts";
import { ClientInterface } from "../client/interface.ts";
import { SubChannelInterface } from "../subChannel/interface.ts";

export interface ChannelInterface extends AbstractEntityInterface {
  name: string;
  code: string;
  subChannels: SubChannelInterface[];
  client: ClientInterface;
}

export interface ApiChannelResponseInterface {
  entities: ChannelInterface[];
  count: number;
}
