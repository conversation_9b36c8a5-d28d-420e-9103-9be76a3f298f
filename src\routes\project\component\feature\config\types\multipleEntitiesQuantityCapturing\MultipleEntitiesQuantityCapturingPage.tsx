import { DEFAULT_CURRENT_PAGE, DEFAULT_PAGE_SIZE } from "@/common/constant";
import DragSortRowComponent from "@/components/DragSortRowComponent.tsx";
import FilterClassicComponent from "@/components/FilterClassicComponent";
import ProductItemCell from "@/components/ProductItemCell";
import { renderTableCell } from "@/components/table-cell";
import TableActionCell from "@/components/TableActionCell";
import { useItemTypesQuery } from "@/routes/item-type/services";
import { useApp } from "@/UseApp";
import {
  CloseOutlined,
  DeleteOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined,
} from "@ant-design/icons";
import { DndContext, DragEndEvent } from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
  arrayMove,
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { Button, Form, Input, Modal, Select, Space, Table } from "antd";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import { QuantityActionEnum, QuantityEntityInterface } from "./interface";
import {
  useArrangeQuantityMutation,
  useCreateQuantitiesMutation,
  useDeleteQuantityMutation,
  useQuantitiesAvailableQuery,
  useQuantitiesQuery,
  useUpdateIsActiveQuantityMutation,
} from "./service";

/**
 * Ghi nhận data từ nguồn vật phẩm, sampling, sản phẩm trong dự án (theo lần chấm công)
 *
 * @return {JSX.Element} The rendered MultipleEntitiesQuantityCapturingPage component
 */
export default function MultipleEntitiesQuantityCapturingPage(): JSX.Element {
  const componentFeatureId = parseInt(useParams().componentFeatureId ?? "0");

  const { showNotification } = useApp();

  const [searchForm] = Form.useForm();
  const [isOpen, setIsOpen] = useState(false);
  const [filterModal, setFilterModal] = useState<{
    isProduct?: boolean;
    itemTypeId?: number;
    keyword?: string;
    take: number;
    skip: number;
  }>({
    take: DEFAULT_PAGE_SIZE,
    skip: 0,
  });
  const [filter, setFilter] = useState({});
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
  const [currentPageModal, setCurrentPageModal] =
    useState(DEFAULT_CURRENT_PAGE);
  const [pageSizeModal, setPageSizeModal] = useState(DEFAULT_PAGE_SIZE);
  const [newSelectedKeys, setNewSelectedKeys] = useState<React.Key[]>([]);
  const [modal, contextHolder] = Modal.useModal();
  const [searchModalForm] = Form.useForm();

  const quantitiesAvailableQuery = useQuantitiesAvailableQuery(
    componentFeatureId,
    filterModal,
    isOpen,
  );
  const quantitiesQuery = useQuantitiesQuery(componentFeatureId, {
    ...filter,
    take: 0,
  });
  const itemTypesQuery = useItemTypesQuery({ take: 50, skip: 0 });

  const createQuantitiesMutation =
    useCreateQuantitiesMutation(componentFeatureId);
  const updateIsActiveQuantityMutation =
    useUpdateIsActiveQuantityMutation(componentFeatureId);
  const deleteQuantityMutation = useDeleteQuantityMutation(componentFeatureId);
  const arrangeQuantityMutation =
    useArrangeQuantityMutation(componentFeatureId);

  const [dataSource, setDataSource] = useState(
    quantitiesQuery.data?.entities ?? [],
  );

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys: selectedKeys,
    onChange: onSelectChange,
    getCheckboxProps: (record: QuantityEntityInterface) => {
      return {
        disabled: !record.isAvailable || !record.isActive,
      };
    },
  };

  const handleModalSubmitClick = useCallback(async () => {
    if (newSelectedKeys.length === 0) {
      return;
    }
    const data = [];
    for (const element of newSelectedKeys) {
      const arr = element.toString().split("-");
      const projectProductId = Number(arr[0]) || null;
      const projectItemId = Number(arr[1]) || null;
      data.push({
        projectProductId,
        projectItemId,
      });
    }

    await createQuantitiesMutation.mutateAsync(data);
    showNotification({
      type: "success",
      message: "Thêm item vào chức năng thành công",
    });
    setIsOpen(false);
    setSelectedKeys([]);
    quantitiesQuery.refetch();
  }, [
    createQuantitiesMutation,
    newSelectedKeys,
    quantitiesQuery,
    showNotification,
  ]);

  const searchModalHandler = useCallback(() => {
    const type = searchForm.getFieldValue("type");
    let itemTypeId = undefined;
    let isProduct = undefined;
    if (type && type !== 0) {
      itemTypeId = type;
    }

    if (type === 0) {
      isProduct = true;
    }
    setFilter({
      ...searchForm.getFieldsValue(),
      itemTypeId,
      isProduct,
    });
  }, [searchForm]);

  const typeOptions = useMemo(() => {
    const data = itemTypesQuery.data?.entities.map((item) => ({
      label: item.name,
      value: item.id,
    }));
    data?.unshift({ label: "Sản phẩm", value: 0 });
    return data;
  }, [itemTypesQuery.data?.entities]);

  const searchContent = useMemo(() => {
    return (
      <>
        <Form.Item name={"keyword"}>
          <Input placeholder={"Tìm theo mã, tên, đơn vị tính"} allowClear />
        </Form.Item>
        <Form.Item name={"type"}>
          <Select
            placeholder={"Loại"}
            options={typeOptions}
            style={{ width: "150px" }}
            allowClear
          />
        </Form.Item>
        <Form.Item name={"isActive"}>
          <Select
            placeholder={"Tình trạng"}
            options={[
              {
                label: "Đang hoạt động",
                value: true,
              },
              {
                label: "Ngừng hoạt động",
                value: false,
              },
            ]}
            allowClear
          />
        </Form.Item>
      </>
    );
  }, [typeOptions]);

  const btnAddHandle = useCallback(() => {
    setIsOpen(true);
  }, []);

  useEffect(() => {
    setSelectedKeys(
      quantitiesAvailableQuery.data?.entities
        .filter((item) => !item.isAvailable)
        .map((item) => `${item.id}-${item.isAvailable}`) ?? [],
    );
  }, [quantitiesAvailableQuery.data]);

  useEffect(() => {
    const updatedKeys = selectedKeys.filter(
      (key) => key.toString().split("-")[2] === "true",
    );
    setNewSelectedKeys(updatedKeys);
  }, [selectedKeys]);

  const onModalFormFinish = useCallback(
    (values: { type?: number; keyword?: string }) => {
      const { type, keyword } = values;
      let itemTypeId = undefined;
      let isProduct = undefined;
      if (type && type !== 0) {
        itemTypeId = type;
      }

      if (type === 0) {
        isProduct = true;
      }
      setCurrentPageModal(DEFAULT_CURRENT_PAGE);
      setFilterModal({
        itemTypeId,
        isProduct,
        keyword,
        take: pageSizeModal,
        skip: 0,
      });
    },
    [pageSizeModal],
  );

  const handlePaginationModalChange = useCallback(
    (page: number) => {
      setCurrentPageModal(page);
      setFilterModal({
        ...filterModal,
        take: pageSizeModal,
        skip: (page - 1) * pageSizeModal,
      });
    },
    [filterModal, pageSizeModal],
  );

  const handlePageSizeModalChange = useCallback(
    (_current: number, size: number) => {
      setPageSizeModal(size);
      setCurrentPageModal(DEFAULT_CURRENT_PAGE);
      setFilterModal({
        ...filterModal,
      });
    },
    [filterModal],
  );

  const paginationModal = useMemo(() => {
    return {
      current: currentPageModal,
      total: quantitiesAvailableQuery.data?.count ?? 0,
      pageSize: pageSizeModal,
      onChange: handlePaginationModalChange,
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: handlePageSizeModalChange,
    };
  }, [
    currentPageModal,
    quantitiesAvailableQuery.data?.count,
    pageSizeModal,
    handlePaginationModalChange,
    handlePageSizeModalChange,
  ]);

  const handleActionActiveClick = useCallback(
    (record: QuantityEntityInterface) => {
      modal.confirm({
        title: `Kích hoạt item: ${
          record.projectProduct?.id
            ? record.projectProduct?.product.name
            : record.projectItem?.item?.name
        }`,
        content: `Bạn có chắc chắn muốn kích hoạt item: ${
          record.projectProduct?.id
            ? record.projectProduct?.product.name
            : record.projectItem?.item?.name
        } này?`,
        okText: "Kích hoạt",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await updateIsActiveQuantityMutation.mutateAsync({
              id: record.id,
              isActive: true,
            });

            showNotification({
              type: "success",
              message: `Kích hoạt item: ${
                record.projectProduct?.id
                  ? record.projectProduct?.product.name
                  : record.projectItem?.item?.name
              } thành công`,
            });

            quantitiesQuery.refetch();
          } catch (error) {
            console.error(error);

            showNotification({
              type: "error",
              message: `Kích hoạt item: ${
                record.projectProduct?.id
                  ? record.projectProduct?.product.name
                  : record.projectItem?.item?.name
              } thất bại`,
            });
          }
        },
      });
    },
    [modal, quantitiesQuery, showNotification, updateIsActiveQuantityMutation],
  );

  const handleActionInactiveClick = useCallback(
    (record: QuantityEntityInterface) => {
      modal.confirm({
        title: `Ngừng hoạt động item: ${
          record.projectProduct?.id
            ? record.projectProduct?.product.name
            : record.projectItem?.item?.name
        }`,
        content: `Bạn có chắc chắn muốn ngừng hoạt động item: ${
          record.projectProduct?.id
            ? record.projectProduct?.product.name
            : record.projectItem?.item?.name
        } này?`,
        okText: "Ngừng hoạt động",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await updateIsActiveQuantityMutation.mutateAsync({
              id: record.id,
              isActive: false,
            });

            showNotification({
              type: "success",
              message: `Ngừng hoạt động item: ${
                record.projectProduct?.id
                  ? record.projectProduct?.product.name
                  : record.projectItem?.item?.name
              } thành công`,
            });

            quantitiesQuery.refetch();
          } catch (error) {
            console.error(error);

            showNotification({
              type: "error",
              message: `Ngừng hoạt động item: ${
                record.projectProduct?.id
                  ? record.projectProduct?.product.name
                  : record.projectItem?.item?.name
              } thất bại`,
            });
          }
        },
      });
    },
    [modal, quantitiesQuery, showNotification, updateIsActiveQuantityMutation],
  );

  const handleActionDeleteClick = useCallback(
    (record: QuantityEntityInterface) => {
      modal.confirm({
        title: `Xóa item: ${
          record.projectProduct?.id
            ? record.projectProduct?.product.name
            : record.projectItem?.item?.name
        }`,
        content: `Bạn có chắc chắn muốn xóa item: ${
          record.projectProduct?.id
            ? record.projectProduct?.product.name
            : record.projectItem?.item?.name
        } này?`,
        okText: "Xóa",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await deleteQuantityMutation.mutateAsync(record.id);

            showNotification({
              type: "success",
              message: `Xóa item: ${
                record.projectProduct?.id
                  ? record.projectProduct?.product.name
                  : record.projectItem?.item?.name
              } thành công`,
            });

            quantitiesQuery.refetch();
          } catch (error) {
            console.error(error);

            showNotification({
              type: "error",
              message: `Xóa item: ${
                record.projectProduct?.id
                  ? record.projectProduct?.product.name
                  : record.projectItem?.item?.name
              } thất bại`,
            });
          }
        },
      });
    },
    [deleteQuantityMutation, modal, quantitiesQuery, showNotification],
  );

  const actionItems = [
    {
      key: QuantityActionEnum.INACTIVE,
      label: "Ngừng hoạt động",
      icon: <PauseCircleOutlined />,
    },
    {
      key: QuantityActionEnum.ACTIVE,
      label: "Hoạt động trở lại",
      icon: <PlayCircleOutlined />,
    },
    {
      key: QuantityActionEnum.DELETE,
      label: "Xóa khỏi chức năng",
      icon: <DeleteOutlined />,
    },
  ];

  const actionActions = [
    {
      key: QuantityActionEnum.ACTIVE,
      action: handleActionActiveClick,
    },
    {
      key: QuantityActionEnum.INACTIVE,
      action: handleActionInactiveClick,
    },
    {
      key: QuantityActionEnum.DELETE,
      action: handleActionDeleteClick,
    },
  ];

  const ACTION_ACTIVE = [
    QuantityActionEnum.INACTIVE,
    QuantityActionEnum.DELETE,
  ];

  const ACTION_INACTIVE = [
    QuantityActionEnum.ACTIVE,
    QuantityActionEnum.DELETE,
  ];

  const onModalClose = useCallback(() => {
    setIsOpen(false);
    searchModalForm.resetFields();
    setSelectedKeys([]);
    setNewSelectedKeys([]);
    setCurrentPageModal(DEFAULT_CURRENT_PAGE);
    setFilterModal({
      take: pageSizeModal,
      skip: 0,
    });
  }, [pageSizeModal, searchModalForm]);

  const onDragEnd = useCallback(
    async ({ active, over }: DragEndEvent) => {
      if (!over) {
        return;
      }
      if (active.id !== over?.id) {
        setDataSource((previous) => {
          const activeIndex = previous?.findIndex((i) => i.id === active.id);
          const overIndex = previous?.findIndex((i) => i.id === over?.id);
          return arrayMove(previous, activeIndex, overIndex);
        });

        await arrangeQuantityMutation.mutateAsync({
          id: active.id as number,
          overId: over?.id as number,
        });
      }
    },
    [arrangeQuantityMutation],
  );

  useEffect(() => {
    setDataSource(quantitiesQuery.data?.entities ?? []);
  }, [quantitiesQuery.data]);

  return (
    <>
      <div className="bg-white pt-10 pl-10 pr-10 rounded pb-6">
        <FilterClassicComponent
          searchHandler={searchModalHandler}
          searchForm={searchForm}
          content={searchContent}
          showAddButton
          handleAddButtonClick={btnAddHandle}
          className={"mb-6"}
        />
        <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
          <SortableContext
            items={dataSource.map((i) => i.id)}
            strategy={verticalListSortingStrategy}
          >
            <Table
              columns={[
                {
                  key: "sort",
                },
                {
                  title: "Tên",
                  render: (_, record: QuantityEntityInterface) => {
                    if (record.projectProduct) {
                      return (
                        <ProductItemCell
                          name={record.projectProduct?.product.name}
                          variants={
                            record.projectProduct.product.image?.variants ?? []
                          }
                        />
                      );
                    }

                    return (
                      <ProductItemCell
                        name={record.projectItem?.item.name ?? ""}
                        variants={
                          record.projectItem?.item.image?.variants ?? []
                        }
                      />
                    );
                  },
                },
                {
                  title: "Mã",
                  render: (_, record: QuantityEntityInterface) => {
                    if (record.projectProduct) {
                      return record.projectProduct?.product.code;
                    }

                    return record.projectItem?.item?.code;
                  },
                },
                {
                  title: "Loại",
                  render: (_, record: QuantityEntityInterface) => {
                    if (record.projectProduct) {
                      return "Sản phẩm";
                    }

                    return record.projectItem?.item?.itemType?.name;
                  },
                },
                {
                  title: "Đơn vị tính",
                  render: (_, record: QuantityEntityInterface) => {
                    if (record.projectProduct) {
                      return record.projectProduct?.productPackaging?.unit.name;
                    }

                    return record.projectItem?.item?.unit?.name;
                  },
                },
                {
                  title: "Tình trạng",
                  dataIndex: "isActive",
                  render: (value, record, index) => {
                    return renderTableCell(value, record, index, "isActive");
                  },
                },
                {
                  key: "actions",
                  render: (_, record) => {
                    const actionKeys = record.isActive
                      ? ACTION_ACTIVE
                      : ACTION_INACTIVE;
                    const items = actionItems.filter((item) =>
                      actionKeys.includes(item.key),
                    );
                    return (
                      <TableActionCell
                        actions={actionActions}
                        items={items}
                        record={record}
                      />
                    );
                  },
                  width: 100,
                },
              ]}
              dataSource={dataSource}
              loading={
                quantitiesQuery.isFetching || arrangeQuantityMutation.isPending
              }
              pagination={false}
              rowKey={"id"}
              components={{
                body: {
                  row: DragSortRowComponent,
                },
              }}
            />
          </SortableContext>
        </DndContext>
      </div>

      <Modal
        open={isOpen}
        footer={null}
        closeIcon={null}
        width={870}
        styles={{ content: { padding: 0 } }}
      >
        <div className="pl-10 pr-10 pt-3 pb-5">
          <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
            <h2 className="text-neutral-700 text-2xl font-semibold ">
              Thêm item vào chức năng
            </h2>
            <div className="pt-5">
              <Button
                type="link"
                onClick={onModalClose}
                size="large"
                icon={<CloseOutlined />}
              />
            </div>
          </div>
          <Form
            layout="vertical"
            onFinish={onModalFormFinish}
            form={searchModalForm}
          >
            <Space>
              <Form.Item label="Loại" name={"type"}>
                <Select
                  style={{ width: "200px" }}
                  placeholder={"Tất cả"}
                  allowClear
                  options={typeOptions}
                />
              </Form.Item>
              <Form.Item label="Tên item" name={"keyword"}>
                <Input placeholder="Nhập tên hoặc mã" allowClear />
              </Form.Item>
              <Form.Item label=" ">
                <Button htmlType="submit">Tìm kiếm</Button>
              </Form.Item>
            </Space>
          </Form>

          <Table
            dataSource={quantitiesAvailableQuery.data?.entities}
            loading={quantitiesAvailableQuery.isFetching}
            columns={[
              {
                title: "Tên",
                dataIndex: "name",
                render: (_, record: QuantityEntityInterface) => {
                  return (
                    <ProductItemCell
                      variants={record?.image?.variants ?? []}
                      name={record.name}
                      isActive={record.isActive}
                      isAvailable={record.isAvailable}
                    />
                  );
                },
              },
              {
                title: "Mã",
                dataIndex: "code",
              },
              {
                title: "Loại",
                render: (_, record: QuantityEntityInterface) => {
                  return record.itemTypeId ? record.itemTypeName : "Sản phẩm";
                },
              },
              {
                title: "Đơn vị tính",
                dataIndex: "unitName",
              },
            ]}
            rowSelection={rowSelection}
            rowKey={(record) => {
              return `${record.id}-${record.isAvailable}`;
            }}
            pagination={paginationModal}
          />
        </div>
        <div className="flex justify-end pb-4 pt-4 bg-[#F7F8FA]">
          <Space className="pr-10">
            <Button onClick={onModalClose}>Đóng</Button>
            <Button
              type={"primary"}
              disabled={!newSelectedKeys.length}
              onClick={handleModalSubmitClick}
              loading={createQuantitiesMutation.isPending}
            >
              Thêm {newSelectedKeys.length} item vào chức năng
            </Button>
          </Space>
        </div>
      </Modal>

      {contextHolder}
    </>
  );
}
