import {
  CURD,
  DEFAULT_CURRENT_PAGE,
  DEFAULT_PAGE_SIZE,
  SELECT_ALL,
  SELECT_ALL_LABEL,
} from "@/common/constant";
import { formErrorResponse<PERSON>andler } from "@/common/helper";
import FilterComponent from "@/components/FilterComponent.tsx";
import { renderTableCell } from "@/components/table-cell";
import { renderTableOptionCell } from "@/components/table-option-cell";
import { useApp } from "@/UseApp.tsx";
import { CloseOutlined } from "@ant-design/icons";
import { Button, Form, Input, Modal, Table } from "antd";
import { ColumnsType } from "antd/es/table";
import _ from "lodash";
import React, { useCallback, useMemo, useState } from "react";
import { UserInterface } from "./interface.ts";
import {
  useCreateUserMutation,
  useUpdateUserMutation,
  useUsersQuery,
} from "./services.ts";

const UserPage: React.FC = () => {
  const { showNotification } = useApp();

  const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [searchForm] = Form.useForm();
  const [form] = Form.useForm();
  const [isModalAddOrUpdateOpen, setIsModalAddOrUpdateOpen] = useState(false);
  const [formAction, setFormAction] = useState<CURD | null>(null);
  const [modalTitle, setModalTitle] = useState("");
  const [modal, contextHolder] = Modal.useModal();
  const [filter, setFilter] = useState({});

  const usersQuery = useUsersQuery({
    take: pageSize,
    skip: (currentPage - 1) * pageSize,
    ...filter,
  });

  const updateUserMutation = useUpdateUserMutation();
  const createUserMutation = useCreateUserMutation();

  const loading = useMemo(
    () =>
      usersQuery.isLoading ||
      usersQuery.isFetching ||
      updateUserMutation.isPending ||
      createUserMutation.isPending,
    [
      createUserMutation.isPending,
      updateUserMutation.isPending,
      usersQuery.isFetching,
      usersQuery.isLoading,
    ],
  );

  const handlePaginationChange = useCallback(
    (page: number) => {
      setCurrentPage(page);
    },
    [setCurrentPage],
  );

  const handlePageSizeChange = useCallback(
    (_current: number, size: number) => {
      setPageSize(size);
      setCurrentPage(DEFAULT_CURRENT_PAGE);
    },
    [setPageSize, setCurrentPage],
  );

  const handleBtnEditClick = (record: UserInterface) => {
    setIsModalAddOrUpdateOpen(true);
    setFormAction(CURD.UPDATE);
    setModalTitle("Cập nhật tài khoản");
    form.setFieldsValue(record);
  };

  const handleBtnInactiveClick = (record: UserInterface) => {
    modal.confirm({
      title: `Ngừng hoạt động user: ${record.name}`,
      content: "Bạn có chắc chắn muốn ngừng hoạt động user này?",
      okText: "Ngừng hoạt động",
      cancelText: "Hủy",
      onOk: async () => {
        try {
          await updateUserMutation.mutateAsync({
            id: record.id,
            isActive: false,
          });

          showNotification({
            type: "success",
            message: "Ngừng hoạt động user thành công",
          });

          await usersQuery.refetch();
        } catch (error) {
          console.error(error);
          showNotification({
            type: "error",
            message: "Ngừng hoạt động user thất bại",
          });
        }
      },
    });
  };

  const handleBtnActiveClick = (record: UserInterface) => {
    modal.confirm({
      title: `Kích hoạt user: ${record.name}`,
      content: "Bạn có chắc chắn muốn kích hoạt user này?",
      okText: "Kích hoạt",
      cancelText: "Hủy",
      onOk: async () => {
        try {
          await updateUserMutation.mutateAsync({
            id: record.id,
            isActive: true,
          });

          showNotification({
            type: "success",
            message: "Kích hoạt user thành công",
          });

          await usersQuery.refetch();
        } catch (error) {
          console.error(error);
          showNotification({
            type: "error",
            message: "Kích hoạt user thất bại",
          });
        }
      },
    });
  };

  const columns: ColumnsType<UserInterface> = [
    {
      title: "Họ tên",
      key: "name",
      dataIndex: "name",
      render: renderTableCell,
      className: "min-w-[100px]",
    },
    {
      title: "Username",
      key: "username",
      dataIndex: "username",
      render: renderTableCell,
      className: "min-w-[100px]",
    },
    {
      title: "Số điện thoại",
      key: "phone",
      dataIndex: "phone",
      render: renderTableCell,
      className: "min-w-[100px]",
    },
    {
      title: "Email",
      key: "email",
      dataIndex: "email",
      render: renderTableCell,
      className: "min-w-[100px]",
    },
    {
      title: "UserType",
      key: "type",
      dataIndex: "type",
      className: "min-w-[100px]",
      render: renderTableCell,
    },
    {
      key: "isActive",
      title: "Tình trạng",
      dataIndex: "isActive",
      className: "min-w-[100px]",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "isActive");
      },
    },
    {
      key: "createdAt",
      title: "Thời gian tạo",
      dataIndex: "createdAt",
      className: "min-w-[100px]",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "datetime");
      },
    },
    {
      key: "createdByUser.name",
      title: "Người tạo",
      dataIndex: "createdByUser",
      render: renderTableCell,
      className: "min-w-[100px]",
    },
    {
      key: "updatedAt",
      title: "Thời gian cập nhật",
      dataIndex: "updatedAt",
      className: "min-w-[100px]",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "datetime");
      },
    },
    {
      key: "updatedByUser.name",
      title: "Người cập nhật",
      dataIndex: "updatedByUser",
      render: renderTableCell,
      className: "min-w-[100px]",
    },
    {
      key: "actions",
      render: (_, record) => {
        return renderTableOptionCell(
          record,
          handleBtnEditClick,
          handleBtnInactiveClick,
          handleBtnActiveClick,
        );
      },
      fixed: "right",
      width: 100,
    },
  ];

  const filterOptions = columns
    .filter(
      (item) =>
        !["isActive", "updatedAt", "createdAt", "actions"].includes(
          item.key as string,
        ),
    )
    .map((column) => {
      return {
        label: column.title as string,
        value: column.key as string | number,
      };
    });

  filterOptions.unshift({ label: SELECT_ALL_LABEL, value: SELECT_ALL });

  const handleAddOrUpdateFormSubmit = useCallback(async () => {
    try {
      await form.validateFields();
      const data = form.getFieldsValue();
      const id = form.getFieldValue("id");
      if (!data.password) {
        delete data.password;
      }

      data.type = "EMPLOYEE";

      switch (formAction) {
        case CURD.CREATE:
          await createUserMutation.mutateAsync(data);

          showNotification({
            type: "success",
            message: "Thêm user thành công",
          });
          break;
        case CURD.UPDATE:
          await updateUserMutation.mutateAsync({
            id,
            ...data,
          });

          showNotification({
            type: "success",
            message: "Cập nhật user thành công",
          });
          break;
        default:
          return;
      }

      form.resetFields();
      setIsModalAddOrUpdateOpen(false);
      setModalTitle("");
      await usersQuery.refetch();

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      formErrorResponseHandler(form, error);
    }
  }, [
    createUserMutation,
    form,
    formAction,
    showNotification,
    updateUserMutation,
    usersQuery,
  ]);

  const searchHandler = useCallback(async () => {
    setCurrentPage(DEFAULT_CURRENT_PAGE);
    const values = searchForm.getFieldsValue();
    if (_.isEqual(values, filter)) {
      await usersQuery.refetch();
    }
    setFilter(values);
  }, [filter, searchForm, usersQuery]);

  const handleAddButtonClick = () => {
    setIsModalAddOrUpdateOpen(true);
    setFormAction(CURD.CREATE);
    setModalTitle("Thêm tài khoản");
  };
  const pagination = React.useMemo(() => {
    return {
      current: currentPage,
      total: usersQuery.data?.count,
      pageSize: pageSize,
      onChange: handlePaginationChange,
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: handlePageSizeChange,
    };
  }, [
    currentPage,
    usersQuery.data?.count,
    pageSize,
    handlePaginationChange,
    handlePageSizeChange,
  ]);

  return (
    <div>
      <h2>Tài khoản</h2>
      <div className="bg-white p-10 rounded">
        <div className="pb-6">
          <FilterComponent
            filterOptions={filterOptions}
            searchHandler={searchHandler}
            handleAddButtonClick={handleAddButtonClick}
            searchForm={searchForm}
          />
        </div>

        <Table
          dataSource={usersQuery.data?.entities}
          columns={columns}
          rowKey={(record) => record.id}
          scroll={{
            x: "max-content",
            y: pagination.total ? "80vh" : undefined,
          }}
          pagination={pagination}
          loading={usersQuery.isLoading || usersQuery.isFetching}
        />
      </div>
      <Modal
        open={isModalAddOrUpdateOpen}
        footer={null}
        closeIcon={null}
        styles={{ content: { padding: 0 } }}
      >
        <div className={"pl-10 pr-10"}>
          <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
            <h2 className="text-neutral-700 text-2xl font-semibold ">
              {modalTitle}
            </h2>
            <div className="pt-5">
              <Button
                type="link"
                onClick={() => {
                  setIsModalAddOrUpdateOpen(false);
                }}
                size="large"
                icon={<CloseOutlined />}
              />
            </div>
          </div>
        </div>

        <Form
          name="userForm"
          onFinish={handleAddOrUpdateFormSubmit}
          layout={"vertical"}
          form={form}
        >
          <div className={"pl-10 pr-10"}>
            <Form.Item name={"id"} hidden={true}></Form.Item>

            <Form.Item
              name="name"
              label={"Họ tên"}
              rules={[
                {
                  required: true,
                  message: "Vui lòng nhập họ tên.",
                },
              ]}
            >
              <Input />
            </Form.Item>

            <Form.Item
              name="username"
              label={"Username"}
              rules={[
                {
                  required: true,
                  message: "Vui lòng nhập username.",
                },
              ]}
            >
              <Input />
            </Form.Item>

            <Form.Item
              name="phone"
              label={"Số điện thoại"}
              rules={[
                {
                  required: true,
                  message: "Vui lòng nhập username.",
                },
              ]}
            >
              <Input />
            </Form.Item>

            <Form.Item
              name="email"
              label={"Email"}
              rules={[
                {
                  required: true,
                  message: "Vui lòng nhập email.",
                },
              ]}
            >
              <Input />
            </Form.Item>

            <Form.Item name="password" label={"Mật khẩu"}>
              <Input.Password />
            </Form.Item>
          </div>
          <div className="flex justify-end gap-4 py-4 rounded max-md:max-w-full max-md:flex-wrap pb-4 pl-10 pr-10 bg-[#F7F8FA]">
            <Button
              htmlType="button"
              onClick={() => {
                setIsModalAddOrUpdateOpen(false);
                form.resetFields();
              }}
            >
              Đóng
            </Button>
            <Button htmlType="submit" type={"primary"} loading={loading}>
              {formAction === CURD.CREATE ? "Thêm mới" : "Cập nhật"}
            </Button>
          </div>
        </Form>
      </Modal>
      {contextHolder}
    </div>
  );
};

export default UserPage;
