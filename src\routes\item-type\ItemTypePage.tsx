import { useApp } from "@/UseApp.tsx";
import {
  CURD,
  DEFAULT_CURRENT_PAGE,
  DEFAULT_PAGE_SIZE,
  SELECT_ALL,
  SELECT_ALL_LABEL,
} from "@/common/constant";
import { filterOption } from "@/common/helper.ts";
import FilterComponent from "@/components/FilterComponent.tsx";
import { renderTableCell } from "@/components/table-cell";
import { renderTableOptionCell } from "@/components/table-option-cell";
import {
  AlignRightOutlined,
  CloseOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import { Button, Form, Input, Modal, Select, Table } from "antd";
import { ColumnsType } from "antd/es/table";
import React, { useCallback, useEffect, useState } from "react";
import { createSearchParams, useNavigate } from "react-router-dom";
import { useClientsQuery } from "../client/service.ts";

import _ from "lodash";
import { ItemModal } from "../item/ItemModal.tsx";
import { ItemTypeInterface } from "./interface.ts";
import { getItemTypes, useDeleteItemTypeMutation } from "./services.ts";

const ItemTypePage: React.FC = () => {
  const { axiosGet, axiosPost, axiosPatch, showNotification, openDeleteModal } =
    useApp();
  const navigate = useNavigate();

  const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [searchForm] = Form.useForm();
  const [form] = Form.useForm();
  const [data, setData] = useState<ItemTypeInterface[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [isModalAddOrUpdateOpen, setIsModalAddOrUpdateOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [formAction, setFormAction] = useState<CURD | null>(null);
  const [modalTitle, setModalTitle] = useState("");
  const [modal, contextHolder] = Modal.useModal();
  const [isItemModalOpen, setIsItemModalOpen] = useState(false);
  const [itemForm] = Form.useForm();
  const [selectedItemType, setSelectedItemType] = useState<
    ItemTypeInterface | undefined
  >(undefined);

  const clientsQuery = useClientsQuery({
    take: 0,
    skip: 0,
  });

  const deleteItemTypeMutation = useDeleteItemTypeMutation();

  const fetchData = useCallback(
    async (
      currentPageInput?: number,
      pageSizeInput?: number,
    ): Promise<void> => {
      setLoading(true);
      const filter = {
        take: pageSizeInput ?? pageSize,
        skip: pageSizeInput
          ? 0
          : (currentPageInput ?? currentPage - 1) * pageSize,
        ...searchForm.getFieldsValue(),
      };
      try {
        const response = await getItemTypes(axiosGet, filter);
        if (!Array.isArray(response)) {
          const { entities, count } = response;
          setData(entities);
          setTotal(count);
        }
      } catch (e) {
        console.error(e);
      } finally {
        setLoading(false);
      }
    },
    [axiosGet, currentPage, searchForm, pageSize],
  );

  const handlePaginationChange = useCallback(
    (page: number) => {
      setCurrentPage(page);
      fetchData(page);
    },
    [setCurrentPage, fetchData],
  );

  const handlePageSizeChange = useCallback(
    (_current: number, size: number) => {
      setPageSize(size);
      setCurrentPage(DEFAULT_CURRENT_PAGE);
      fetchData(DEFAULT_CURRENT_PAGE, size);
    },
    [setPageSize, setCurrentPage, fetchData],
  );

  const handleBtnEditClick = useCallback(
    (record: ItemTypeInterface) => {
      setSelectedItemType(record);
      setIsModalAddOrUpdateOpen(true);
      setFormAction(CURD.UPDATE);
      setModalTitle("Cập nhật loại vật phẩm");
      form.setFieldsValue({ ...record, clientId: record.client.id });
    },
    [form],
  );

  const handleBtnInactiveClick = useCallback(
    (record: ItemTypeInterface) => {
      modal.confirm({
        title: `Ngừng hoạt động loại vật phẩm: ${record.name}`,
        content: "Bạn có chắc chắn muốn ngừng hoạt động loại vật phẩm này?",
        okText: "Ngừng hoạt động",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await axiosPatch(`/item-types/${record.id}`, { isActive: false });
            showNotification({
              type: "success",
              message: "Ngừng hoạt động loại vật phẩm thành công",
            });
            fetchData();
          } catch (error) {
            console.error(error);
            showNotification({
              type: "error",
              message: "Ngừng hoạt động loại vật phẩm thất bại",
            });
          }
        },
      });
    },
    [axiosPatch, fetchData, modal, showNotification],
  );

  const handleBtnActiveClick = useCallback(
    (record: ItemTypeInterface) => {
      modal.confirm({
        title: `Kích hoạt loại vật phẩm: ${record.name}`,
        content: "Bạn có chắc chắn muốn kích hoạt loại vật phẩm này?",
        okText: "Kích hoạt",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await axiosPatch(`/item-types/${record.id}`, { isActive: true });
            showNotification({
              type: "success",
              message: "Kích hoạt loại vật phẩm thành công",
            });
          } catch (error) {
            console.error(error);
            showNotification({
              type: "error",
              message: "Kích hoạt loại vật phẩm thất bại",
            });
          }
          fetchData();
        },
      });
    },
    [axiosPatch, fetchData, modal, showNotification],
  );

  const handleBtnDeleteClick = useCallback(
    (record: ItemTypeInterface) => {
      openDeleteModal({
        content: (
          <>
            <p>
              Loại vật phẩm sẽ được xóa khỏi hệ thống vĩnh viễn và không thể
              khôi phục
            </p>
            <p>
              Bạn vẫn muốn xóa loại vật phẩm{" "}
              <span className={"font-semibold"}>{record.name}</span> khỏi hệ
              thống?
            </p>
          </>
        ),
        deleteText: "Xác nhận xóa",
        loading: deleteItemTypeMutation.isPending,
        onCancel(): void {},
        onDelete: async () => {
          await deleteItemTypeMutation.mutateAsync(record.id);
          showNotification({
            type: "success",
            message: "Xóa loại vật phẩm thành công",
          });
          await fetchData();
        },
        title: `Xóa loại vật phẩm`,
        titleError: "Không thể xóa loại vật phẩm",
        contentHeader: (
          <>
            Không thể xóa loại vật phẩm{" "}
            <span className="font-semibold">{record.name}</span> bởi vì:
          </>
        ),
      });
    },
    [deleteItemTypeMutation, fetchData, openDeleteModal, showNotification],
  );

  const handleBtnAddItemClick = useCallback(
    (record: ItemTypeInterface) => {
      setIsItemModalOpen(true);
      itemForm.setFields([
        {
          name: "itemTypeId",
          value: record.id,
        },
        {
          name: "clientId",
          value: record.client.id,
        },
      ]);
    },
    [itemForm],
  );

  const handleBtnViewItem = useCallback(
    (record: ItemTypeInterface) => {
      navigate({
        pathname: "/item",
        search: createSearchParams({
          filterValue: record.name,
          filterField: "itemType.name",
        }).toString(),
      });
    },
    [navigate],
  );

  const extraActionInActive = {
    items: [
      {
        key: "VIEW_ITEM",
        label: "Xem vật phẩm",
        icon: <AlignRightOutlined />,
      },
    ],
    actions: [
      {
        key: "VIEW_ITEM",
        action: handleBtnViewItem,
      },
    ],
  };

  const extraActionActive = {
    items: [
      {
        key: "ADD_ITEM",
        label: "Thêm vật phẩm",
        icon: <PlusOutlined />,
      },
      {
        key: "VIEW_ITEM",
        label: "Xem vật phẩm",
        icon: <AlignRightOutlined />,
      },
    ],
    actions: [
      {
        key: "ADD_ITEM",
        action: handleBtnAddItemClick,
      },
      {
        key: "VIEW_ITEM",
        action: handleBtnViewItem,
      },
    ],
  };

  const columns: ColumnsType<ItemTypeInterface> = [
    {
      title: "Tên loại vật phẩm",
      key: "name",
      dataIndex: "name",
      render: renderTableCell,
      className: "min-w-[100px]",
    },
    {
      title: "Mô tả",
      key: "description",
      dataIndex: "description",
      render: renderTableCell,
      className: "min-w-[100px]",
    },
    {
      title: "Vật phẩm bên trong",
      key: "items.name",
      dataIndex: "items",
      render: (value, record, index) => {
        return renderTableCell(
          value,
          record,
          index,
          "array",
          3,
          "vật phẩm khác",
        );
      },
      onCell: (record) => {
        return {
          onClick: () => {
            navigate({
              pathname: "/item",
              search: createSearchParams({
                filterValue: record.name,
                filterField: "itemType.name",
              }).toString(),
            });
          },
        };
      },
      className: "min-w-[100px]",
    },
    {
      title: "Client",
      key: "client",
      dataIndex: "client",
      render: renderTableCell,
      className: "min-w-[100px]",
    },
    {
      key: "isActive",
      title: "Tình trạng",
      dataIndex: "isActive",
      className: "min-w-[100px]",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "isActive");
      },
    },
    {
      key: "createdAt",
      title: "Thời gian tạo",
      className: "min-w-[100px]",
      dataIndex: "createdAt",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "datetime");
      },
    },
    {
      key: "createdByUser.name",
      title: "Người tạo",
      dataIndex: "createdByUser",
      render: renderTableCell,
      className: "min-w-[100px]",
    },
    {
      key: "updatedAt",
      title: "Thời gian cập nhật",
      className: "min-w-[100px]",
      dataIndex: "updatedAt",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "datetime");
      },
    },
    {
      key: "updatedByUser.name",
      title: "Người cập nhật",
      dataIndex: "updatedByUser",
      render: renderTableCell,
      className: "min-w-[100px]",
    },
    {
      key: "actions",
      render: (_, record) => {
        const extraAction = record.isActive
          ? extraActionActive
          : extraActionInActive;

        return renderTableOptionCell(
          record,
          handleBtnEditClick,
          handleBtnInactiveClick,
          handleBtnActiveClick,
          handleBtnDeleteClick,
          extraAction,
        );
      },
      fixed: "right",
      width: 100,
    },
  ];

  const filterOptions = columns
    .filter(
      (item) =>
        !["isActive", "updatedAt", "createdAt", "actions", "client"].includes(
          item.key as string,
        ),
    )
    .map((column) => {
      return {
        label: column.title as string,
        value: column.key as string | number,
      };
    });

  filterOptions.unshift({ label: SELECT_ALL_LABEL, value: SELECT_ALL });

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const onCancel = useCallback(() => {
    setIsModalAddOrUpdateOpen(false);
    form.resetFields();
    setModalTitle("");
    setSelectedItemType(undefined);
  }, [form]);

  const handleAddOrUpdateFormSubmit = useCallback(async () => {
    try {
      await form.validateFields();
      const data = form.getFieldsValue();
      const id = form.getFieldValue("id");
      setLoading(true);

      switch (formAction) {
        case CURD.CREATE:
          await axiosPost(`/item-types`, data);
          showNotification({
            type: "success",
            message: "Thêm loại vật phẩm thành công",
          });
          break;
        case CURD.UPDATE:
          await axiosPatch(`/item-types/${id}`, data);
          showNotification({
            type: "success",
            message: "Cập nhật loại vật phẩm thành công",
          });
          break;
        default:
          return;
      }

      onCancel();
      fetchData();

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      const data: { message: { field: string; message: string }[] } =
        error?.response?.data;
      const { message } = data;
      form.setFields([
        { name: message[0].field, errors: [message[0].message] },
      ]);
    } finally {
      setLoading(false);
    }
  }, [
    axiosPatch,
    axiosPost,
    fetchData,
    form,
    formAction,
    onCancel,
    showNotification,
  ]);

  const searchHandler = () => {
    fetchData(DEFAULT_CURRENT_PAGE, pageSize);
    setCurrentPage(DEFAULT_CURRENT_PAGE);
  };

  const handleAddButtonClick = () => {
    setIsModalAddOrUpdateOpen(true);
    setFormAction(CURD.CREATE);
    setModalTitle("Thêm loại vật phẩm");
  };
  const pagination = React.useMemo(() => {
    return {
      current: currentPage,
      total: total,
      pageSize: pageSize,
      onChange: handlePaginationChange,
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: handlePageSizeChange,
    };
  }, [
    currentPage,
    total,
    pageSize,
    handlePaginationChange,
    handlePageSizeChange,
  ]);

  return (
    <div>
      <h2>Loại vật phẩm</h2>
      <div className="bg-white p-10 rounded">
        <div className="pb-6">
          <FilterComponent
            filterOptions={filterOptions}
            searchHandler={searchHandler}
            handleAddButtonClick={handleAddButtonClick}
            searchForm={searchForm}
            hasClient
          />
        </div>

        <Table
          dataSource={data}
          columns={columns}
          rowKey={(record) => record.id}
          scroll={{
            x: "max-content",
            y: pagination.total ? "80vh" : undefined,
          }}
          pagination={pagination}
          loading={loading}
        />
      </div>
      <Modal
        open={isModalAddOrUpdateOpen}
        footer={null}
        closeIcon={null}
        styles={{ content: { padding: 0 } }}
      >
        <div className={"pl-10 pr-10"}>
          <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
            <h2 className="text-neutral-700 text-2xl font-semibold ">
              {modalTitle}
            </h2>
            <div className="pt-5">
              <Button
                type="link"
                onClick={onCancel}
                size="large"
                icon={<CloseOutlined />}
              />
            </div>
          </div>
        </div>

        <Form
          name="itemtypeForm"
          onFinish={handleAddOrUpdateFormSubmit}
          layout={"vertical"}
          form={form}
        >
          <div className={"pl-10 pr-10"}>
            <Form.Item name={"id"} hidden={true}></Form.Item>
            <Form.Item
              name="name"
              label={"Tên loại vật phẩm"}
              rules={[{ required: true }]}
            >
              <Input />
            </Form.Item>
            <Form.Item name="description" label={"Mô tả"}>
              <Input />
            </Form.Item>

            {(() => {
              const clientActiveOptions =
                clientsQuery.data?.entities.map((client) => ({
                  label: client.name,
                  value: client.id,
                })) ?? [];

              const { client } = selectedItemType ?? {};
              const selectedOptions = client
                ? [{ label: client?.name, value: client?.id }]
                : [];

              const clientOptions = _.unionBy(
                _.concat(clientActiveOptions, selectedOptions),
                (o) => o.value,
              );

              return (
                <Form.Item
                  name="clientId"
                  label={"Client"}
                  rules={[{ required: true }]}
                >
                  <Select
                    options={clientOptions}
                    showSearch
                    optionFilterProp="children"
                    filterOption={filterOption}
                    disabled={formAction === CURD.UPDATE}
                  />
                </Form.Item>
              );
            })()}
          </div>
          <div className="flex justify-end gap-4 py-4 rounded max-md:max-w-full max-md:flex-wrap pb-4 pl-10 pr-10 bg-[#F7F8FA]">
            <Button htmlType="button" onClick={onCancel}>
              Đóng
            </Button>
            <Button htmlType="submit" type={"primary"} loading={loading}>
              {formAction === CURD.CREATE ? "Thêm" : "Cập nhật"}
            </Button>
          </div>
        </Form>
      </Modal>

      <ItemModal
        form={itemForm}
        modalTitle="Thêm vật phẩm"
        isOpen={isItemModalOpen}
        setIsOpen={setIsItemModalOpen}
        disabledSelectItemType={true}
        formAction={CURD.CREATE}
        callback={fetchData}
      />

      {contextHolder}
    </div>
  );
};

export default ItemTypePage;
