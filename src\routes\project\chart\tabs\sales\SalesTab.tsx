import { useParams } from "react-router-dom";
import { StatisticsTypeEnum } from "../../interface";
import TabTwoChartBar from "../../types/tab-two-chart-bar/TabTwoChartBar";

interface SalesTabProps {
  sort: "asc" | "desc" | undefined;
  filter: { provinceIds?: number[]; channelIds?: number[] };
  activeKey: string;
}

const SalesTab = ({ sort, filter, activeKey }: SalesTabProps) => {
  const projectId = parseInt(useParams().id ?? "0");

  return (
    <TabTwoChartBar
      projectId={projectId}
      filter={filter}
      sort={sort}
      type={StatisticsTypeEnum.SALES_REVENUE}
      activeKey={activeKey}
    />
  );
};

export default SalesTab;
