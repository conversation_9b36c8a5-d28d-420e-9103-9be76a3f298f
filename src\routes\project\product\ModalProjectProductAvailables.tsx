import { DEFAULT_CURRENT_PAGE, DEFAULT_PAGE_SIZE } from "@/common/constant.ts";
import { filterOption } from "@/common/helper.ts";
import ProductItemCell from "@/components/ProductItemCell.tsx";
import { useApp } from "@/UseApp.tsx";
import { CloseOutlined } from "@ant-design/icons";
import {
  Button,
  Col,
  Form,
  Input,
  Modal,
  Row,
  Select,
  Space,
  Table,
} from "antd";
import _ from "lodash";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import {
  ProductInterface,
  ProductPackagingInterface,
} from "../../product/interface";
import { UnitInterface } from "../../unit/UnitPage.tsx";
import { useProjectBrandsQuery } from "../general/services.ts";
import {
  useCreateProjectProductMutation,
  useProjectProductsAvailablesQuery,
} from "./service.ts";

function ModalProjectProductAvailables(
  props: Readonly<{
    projectId: number;
    isOpen: boolean;
    setIsOpen: (isOpen: boolean) => void;
    cb?: () => void;
  }>,
) {
  const { isOpen, setIsOpen, projectId, cb } = props;
  const { showNotification } = useApp();

  const [searchForm] = Form.useForm();
  const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [filter, setFilter] = useState<{
    projectBrandId?: number;
    keyword?: string;
  }>({});
  const [selectedItemKeys, setSelectedItemKeys] = useState<React.Key[]>([]);

  const projectProductsAvailablesQuery = useProjectProductsAvailablesQuery(
    projectId,
    {
      ...filter,
      take: pageSize,
      skip: (currentPage - 1) * pageSize,
    },
    isOpen,
  );
  const projectBrandsQuery = useProjectBrandsQuery(projectId, isOpen);

  const createProjectProductMutation =
    useCreateProjectProductMutation(projectId);

  const searchHandler = useCallback(() => {
    const values = searchForm.getFieldsValue();
    if (values["isMainPackaging"] === "all") {
      values["isMainPackaging"] = undefined;
    }
    setCurrentPage(DEFAULT_CURRENT_PAGE);
    if (_.isEqual(filter, values)) {
      projectProductsAvailablesQuery.refetch();
    }
    setFilter(values);
  }, [projectProductsAvailablesQuery, filter, searchForm]);

  const pagination = useMemo(() => {
    return {
      current: currentPage,
      total: projectProductsAvailablesQuery.data?.count,
      pageSize: pageSize,
      onChange: (page: number) => setCurrentPage(page),
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: (_current: number, size: number) => {
        setPageSize(size);
        setCurrentPage(DEFAULT_CURRENT_PAGE);
      },
    };
  }, [currentPage, projectProductsAvailablesQuery.data?.count, pageSize]);

  const onSelectChange = useCallback((newSelectedRowKeys: React.Key[]) => {
    setSelectedItemKeys(newSelectedRowKeys);
  }, []);

  const rowSelection = {
    selectedRowKeys: selectedItemKeys,
    onChange: onSelectChange,
    getCheckboxProps: (record: ProductPackagingInterface) => {
      return {
        disabled: !record.isAvailable || !record.product.isActive,
      };
    },
  };

  const newSelectedItemKeys = useMemo(
    () =>
      selectedItemKeys.filter((key) => {
        const productPacking =
          projectProductsAvailablesQuery.data?.entities?.find(
            (item) => item.id === key && item.isAvailable,
          );

        return !!productPacking;
      }),
    [projectProductsAvailablesQuery.data?.entities, selectedItemKeys],
  );

  const onClose = useCallback(() => {
    setIsOpen(false);
    searchForm.resetFields();
    setFilter({});
    setCurrentPage(DEFAULT_CURRENT_PAGE);
    setPageSize(DEFAULT_PAGE_SIZE);
  }, [searchForm, setIsOpen]);

  const handleBtnAddItemClick = useCallback(async () => {
    try {
      await createProjectProductMutation.mutateAsync({
        productPackagingIds: newSelectedItemKeys.map((key) => Number(key)),
      });
      showNotification({
        type: "success",
        message: "Thêm quy cách sản phẩm vào dự án thành công",
      });
      onClose();
      cb?.();
    } catch (e) {
      console.error(e);
    }
  }, [
    cb,
    createProjectProductMutation,
    newSelectedItemKeys,
    onClose,
    showNotification,
  ]);

  useEffect(() => {
    setSelectedItemKeys(
      projectProductsAvailablesQuery.data?.entities
        ?.filter((productPacking) => !productPacking.isAvailable)
        .map((productPacking) => productPacking.id) ?? [],
    );
  }, [projectProductsAvailablesQuery.data?.entities]);

  return (
    <Modal
      open={isOpen}
      closeIcon={null}
      footer={null}
      width={1000}
      styles={{ content: { padding: 0 } }}
      onCancel={onClose}
    >
      <div className="pl-10 pr-10 pt-3 pb-5">
        <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
          <h2 className="text-neutral-700 text-2xl font-semibold ">
            Thêm quy cách vào dự án
          </h2>
          <div className="pt-5">
            <Button
              type="link"
              onClick={onClose}
              size="large"
              icon={<CloseOutlined />}
            />
          </div>
        </div>
        <Form layout="vertical" form={searchForm} onFinish={searchHandler}>
          <Row justify={"space-between"}>
            <Col md={7}>
              <Form.Item name="projectBrandId" label="Nhãn hàng">
                <Select
                  allowClear
                  showSearch
                  optionFilterProp="children"
                  filterOption={filterOption}
                  options={projectBrandsQuery.data?.map((projectBrand) => ({
                    label: projectBrand.brand.name,
                    value: projectBrand.id,
                  }))}
                  popupMatchSelectWidth={false}
                  className={"h-10"}
                />
              </Form.Item>
            </Col>
            <Col md={7}>
              <Form.Item name="keyword" label="Sản phẩm">
                <Input
                  allowClear
                  placeholder={"Nhập tên hoặc mã sản phẩm"}
                  className={"h-10"}
                />
              </Form.Item>
            </Col>
            <Col md={5}>
              <Form.Item name="isMainPackaging" label="Loại quy cách">
                <Select
                  allowClear
                  showSearch
                  optionFilterProp="children"
                  filterOption={filterOption}
                  options={[
                    {
                      label: "Tất cả",
                      value: "all",
                    },
                    {
                      label: "Quy cách chuẩn",
                      value: "true",
                    },
                    {
                      label: "Thường",
                      value: "false",
                    },
                  ]}
                  popupMatchSelectWidth={false}
                  className={"h-10"}
                />
              </Form.Item>
            </Col>
            <Col>
              <Form.Item label=" ">
                <Button htmlType="submit">Tìm kiếm</Button>
              </Form.Item>
            </Col>
          </Row>
        </Form>

        <Table
          pagination={pagination}
          dataSource={projectProductsAvailablesQuery.data?.entities ?? []}
          rowKey={"id"}
          rowSelection={rowSelection}
          columns={[
            {
              title: "Tên sản phẩm",
              dataIndex: "product",
              render: (product: ProductInterface, record) => {
                const { isAvailable } = record;

                return (
                  <ProductItemCell
                    variants={product?.image?.variants ?? []}
                    name={product.name}
                    isActive={record.product.isActive}
                    isAvailable={isAvailable}
                  />
                );
              },
            },
            {
              title: "Mã sản phẩm",
              dataIndex: "product",
              render: (product: ProductInterface) => product.code,
            },
            {
              title: "Nhãn hàng",
              dataIndex: "product",
              render: (product: ProductInterface) => product.brand.name,
            },
            {
              title: "Loại quy cách",
              dataIndex: "isMainPackaging",
              className: "w-[150px]",
              render: (isMainPackaging: boolean) =>
                isMainPackaging ? "Quy cách chuẩn" : "Thường",
            },
            {
              title: "Quy cách",
              dataIndex: "unit",
              render: (unit: UnitInterface) => unit.name,
            },
          ]}
        />
      </div>
      <div
        className="flex justify-end pb-4 pt-4"
        style={{ backgroundColor: "#F7F8FA", borderRadius: "4px" }}
      >
        <Space className="pr-10">
          <Button onClick={onClose}>Đóng</Button>
          <Button
            type={"primary"}
            disabled={newSelectedItemKeys.length === 0}
            onClick={handleBtnAddItemClick}
            loading={createProjectProductMutation.isPending}
          >
            Thêm {newSelectedItemKeys.length} quy cách vào dự án
          </Button>
        </Space>
      </div>
    </Modal>
  );
}

export default ModalProjectProductAvailables;
