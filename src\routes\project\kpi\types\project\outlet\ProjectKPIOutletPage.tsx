import {
  DATE_FORMAT,
  DEFAULT_CURRENT_PAGE,
  DEFAULT_PAGE_SIZE,
} from "@/common/constant";
import { formatNumber } from "@/common/helper";
import { useApp } from "@/UseApp";
import { DeleteOutlined, PlusOutlined } from "@ant-design/icons";
import { Button, Col, Row, Space, Table } from "antd";
import dayjs from "dayjs";
import { useCallback, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import { ItemKpiTypeEnum } from "../../../interface";
import {
  useDeleteKpiRollingMutation,
  useKpiRollingQuery,
} from "../rolling/service";
import ProjectKPIOutletImportModal from "./ProjectKPIOutletModal";

const ProjectKPIOutletPage = () => {
  const projectId = parseInt(useParams().id ?? "0");
  const { showNotification, openDeleteModal } = useApp();

  const [open, setOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);

  const kpiRollingQuery = useKpiRollingQuery(projectId, {
    type: ItemKpiTypeEnum.SESSION,
    take: pageSize,
    skip: (currentPage - 1) * pageSize,
  });

  const deleteKpiRollingMutation = useDeleteKpiRollingMutation(projectId);

  const pagination = useMemo(() => {
    return {
      current: currentPage,
      total: kpiRollingQuery.data?.count ?? 0,
      pageSize: pageSize,
      onChange: (page: number) => {
        setCurrentPage(page);
      },
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: (_current: number, size: number) => {
        setPageSize(size);
        setCurrentPage(DEFAULT_CURRENT_PAGE);
      },
    };
  }, [currentPage, kpiRollingQuery.data?.count, pageSize]);

  const onDelete = useCallback(async () => {
    openDeleteModal({
      content: (
        <>
          <p>
            Cấu hình KPI session theo outlet xóa khỏi hệ thống vĩnh viễn và
            không thể khôi phục
          </p>
          <p>Bạn vẫn muốn xóa khỏi hệ thống?</p>
        </>
      ),
      deleteText: "Xác nhận xóa",
      loading: false,
      onCancel(): void {},
      onDelete: async () => {
        await deleteKpiRollingMutation.mutateAsync(ItemKpiTypeEnum.SESSION);
        showNotification({
          type: "success",
          message: "Xóa cấu hình KPI session thành công",
        });
        await kpiRollingQuery.refetch();
      },
      title: `Xóa cấu hình KPI session thực hiện`,
      titleError: "Không thể xóa kênh thực hiện",
      contentHeader: <>Không thể xóa cấu hình KPI session bởi vì:</>,
    });
  }, [
    deleteKpiRollingMutation,
    kpiRollingQuery,
    openDeleteModal,
    showNotification,
  ]);

  return (
    <>
      <Row justify={"end"}>
        <Col>
          <Space>
            <Button icon={<DeleteOutlined />} onClick={onDelete}>
              Xóa tất cả
            </Button>

            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setOpen(true);
              }}
            >
              Import plan
            </Button>
          </Space>
        </Col>
      </Row>

      <Table
        className="mt-8"
        dataSource={kpiRollingQuery.data?.entities}
        rowKey={"id"}
        columns={[
          {
            title: "Ngày thực hiện",
            dataIndex: "targetDate",
            render: (targetDate) => dayjs(targetDate).format(DATE_FORMAT),
          },
          {
            title: "Kênh thực hiện",
            dataIndex: "channel",
            render: (channel) => channel.name,
          },
          {
            title: "Tên outlet",
            dataIndex: "targetName",
          },
          {
            title: "Tỉnh/ TP",
            dataIndex: "province",
            render: (province) => province.name,
          },
          {
            title: "Target staff working",
            dataIndex: "targetKpi",
            align: "right",
            render: (targetKpi) => formatNumber(targetKpi),
          },
        ]}
        pagination={pagination}
      />

      <ProjectKPIOutletImportModal
        open={open}
        setOpen={setOpen}
        projectId={projectId}
        cb={() => {
          kpiRollingQuery.refetch();
          showNotification({
            type: "success",
            message: "Import data successfully",
          });
        }}
      />
    </>
  );
};

export default ProjectKPIOutletPage;
