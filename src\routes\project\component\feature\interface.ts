import { AbstractEntityInterface } from "@/common/interface";
import { RoleInterface } from "@project/role/interface";
import { FeatureQuantityInterface } from "../../report/types/multipleEntitiesQuantityCapturing/interface.ts";
import { AttendanceClockingInterface } from "./config/types/attendanceClocking/interface.ts";
import {
  FeatureCustomerInterface,
  FeatureSchemeInterface,
  OrderInterface,
  OrderProductInterface,
} from "./config/types/customerInformationCapturing/interface.ts";
import {
  FeatureNumericAttributeInterface,
  FeatureNumericSheetInterface,
} from "./config/types/numericSheet/interface.ts";
import { PhotographyInterface } from "./config/types/photography/interface.ts";
import {
  FeatureSamplingGroupInterface,
  FeatureSamplingInterface,
} from "./config/types/sampling/interface.ts";

export interface FeatureInterface extends AbstractEntityInterface {
  featureType: FeatureTypeEnum;
  featureTypeName: string;
  isLeaderAllowed: boolean;
  isMemberAllowed: boolean;
  isAvailable: boolean;
}

export interface DependentOnFeaturesInterface extends AbstractEntityInterface {
  projectFeatureId: number;
  dependentProjectFeatureId: number;
  dependentProjectFeature: {
    name: string;
  };
}

export interface ComponentFeatureInterface extends AbstractEntityInterface {
  name: string;
  type: FeatureTypeEnum;
  projectFeatureRoles: number[];
  dependentOnFeatures: DependentOnFeaturesInterface[];
  ordinal: number;
  applicableRoles?: RoleInterface[];
  featureTypeName: string;
  employeeRoles?: RoleInterface[];
  featurePhotos?: PhotographyInterface[];
  featureOrder?: OrderInterface & {
    featureOrderProducts: OrderProductInterface[];
  };
  featureSchemes?: FeatureSchemeInterface[];
  featureCustomers?: FeatureCustomerInterface[];
  isImmediateSendRequired: boolean;
  featureSamplingGroups?: FeatureSamplingGroupInterface[];
  featureSamplings?: FeatureSamplingInterface[];
  featureQuantities?: FeatureQuantityInterface[];
  featureNumericSheets?: FeatureNumericSheetInterface[];
  featureNumericAttributes?: FeatureNumericAttributeInterface[];
  featureAttendance: AttendanceClockingInterface;
}

export enum ComponentFeatureActionEnum {
  EDIT = "EDIT",
  VIEW = "VIEW",
  INACTIVE = "INACTIVE",
  ACTIVE = "ACTIVE",
  DELETE = "DELETE",
  DEPENDENT = "DEPENDENT",
}

export enum FeatureTypeEnum {
  AttendanceClockingIn = "attendanceClockingIn",
  AttendanceClockingOut = "attendanceClockingOut",
  Photography = "photography",
  Sampling = "sampling",
  Synchronization = "synchronization",
  CustomerInformationCapturing = "customerInformationCapturing",
  MultiSubjectMultimediaInformationCapturing = "multiSubjectMultimediaInformationCapturing",
  MultipleEntitiesQuantityCapturing = "multipleEntitiesQuantityCapturing",
  OnlineIndividualSummaryReport = "onlineIndividualSummaryReport",
  OnlineTeamSummaryReport = "onlineTeamSummaryReport",
  OnlineTeamAttendanceReport = "onlineTeamAttendanceReport",
  SummaryReport = "summaryReport",
  Urgency = "urgency",
  NumericSheet = "numericSheet",
  OnlineTeamUrgencyReport = "onlineTeamUrgencyReport",
  OutOfStockStatus = "outOfStockStatus",
}

export interface FeatureScheduleInterface extends AbstractEntityInterface {
  startTime: string;
  endTime: string;
}
