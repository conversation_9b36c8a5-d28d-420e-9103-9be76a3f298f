import { CURD, DATETIME_FORMAT } from "@/common/constant";
import { formatNumber } from "@/common/helper";
import CustomTable from "@/components/CustomTable/CustomTable";
import InnerContainer from "@/components/InnerContainer/InnerContainer";
import TableActionCell from "@/components/TableActionCell";
import { RecordNumericValueInterface } from "@/routes/project/report/types/numericSheet/interface";
import { EditOutlined } from "@ant-design/icons";
import dayjs from "dayjs";
import _ from "lodash";
import { useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import AttendanceDetailRow from "../../AttendanceDetailRow";
import {
  useAttendanceFeatureDetailQuery,
  useAttendanceQuery,
} from "../../service";
import EditNumericSheetModal from "./EditNumericSheetModal";
import { useAttendanceNumericSheetsQuery } from "./service";

const EditNumericSheetPage = () => {
  const projectId = parseInt(useParams().id ?? "0");
  const attendanceId = parseInt(useParams().attendanceId ?? "0");
  const componentFeatureId = parseInt(useParams().componentFeatureId ?? "0");

  const [action, setAction] = useState<CURD | undefined>(undefined);

  const attendanceQuery = useAttendanceQuery(projectId, attendanceId);
  const attendanceFeatureDetailQuery = useAttendanceFeatureDetailQuery(
    projectId,
    attendanceId,
    componentFeatureId,
  );

  const attendanceNumericSheetsQuery = useAttendanceNumericSheetsQuery(
    projectId,
    attendanceId,
    componentFeatureId,
  );

  const numericAttributeName = useMemo(() => {
    return (
      attendanceFeatureDetailQuery.data?.featureNumericAttributes
        ?.sort((a, b) => a.ordinal - b.ordinal)
        .map((item) => item.name) ?? []
    );
  }, [attendanceFeatureDetailQuery.data?.featureNumericAttributes]);

  return (
    <>
      <h2>{attendanceFeatureDetailQuery.data?.name}</h2>
      <InnerContainer>
        <AttendanceDetailRow
          projectRecordEmployee={attendanceQuery.data?.projectRecordEmployee}
          createdAt={attendanceQuery.data?.timeIn}
          updatedAt={attendanceQuery.data?.timeOut}
        />

        <CustomTable
          className="mt-5"
          dataSource={
            attendanceNumericSheetsQuery.data
              ? [attendanceNumericSheetsQuery.data]
              : []
          }
          columns={[
            {
              title: attendanceFeatureDetailQuery.data?.name,
              render: (_value, record) => {
                const { recordNumericValues } = record ?? {};
                const groupRecordNumericValues = _.groupBy(
                  recordNumericValues,
                  "featureNumeric.id",
                );

                return Object.entries(groupRecordNumericValues).map(
                  // eslint-disable-next-line @typescript-eslint/no-unused-vars
                  ([_value, groupRecordNumericValues]) => {
                    const { projectItem, projectProduct, id } =
                      groupRecordNumericValues[0].featureNumeric;
                    const unitName =
                      projectItem?.item?.unit?.name ??
                      projectProduct?.productPackaging?.unit?.name;
                    const code =
                      projectItem?.item?.code ?? projectProduct?.product?.code;
                    const name =
                      projectItem?.item?.name ?? projectProduct?.product?.name;

                    return (
                      <p key={id}>
                        {unitName} - {code} - {name}
                      </p>
                    );
                  },
                );
              },
            },
            ...numericAttributeName.map((name) => ({
              title: name,
              className: "min-w-[100px]",
              fixed: "right" as const,
              render: (
                _value: unknown,
                {
                  recordNumericValues,
                }: { recordNumericValues: RecordNumericValueInterface[] },
              ) => {
                const groupRecordNumericValues = _.groupBy(
                  recordNumericValues,
                  "featureNumeric.id",
                );

                return Object.entries(groupRecordNumericValues).map(
                  ([index, groupRecordNumericValues]) => {
                    const recordNumericValue = groupRecordNumericValues.find(
                      (item) =>
                        _.isEqual(item.featureNumericAttribute.name, name),
                    );

                    if (!recordNumericValue) {
                      return (
                        <p key={index}>
                          <span className="text-blue font-semibold">_</span>
                        </p>
                      );
                    }
                    return (
                      <p key={index}>
                        <span className="text-blue font-semibold">
                          {recordNumericValue.value ||
                          recordNumericValue.value == 0
                            ? formatNumber(recordNumericValue.value)
                            : "_"}
                        </span>
                      </p>
                    );
                  },
                );
              },
            })),
            {
              title: "Thời gian dùng tool edit",
              render: (_, record) => {
                const { toolNumericSheet } = record ?? {};
                if (toolNumericSheet)
                  return dayjs(toolNumericSheet?.createdAt).format(
                    DATETIME_FORMAT,
                  );

                return "-";
              },
            },
            {
              title: "Người dùng tool edit",
              render: (_, record) => {
                const { toolNumericSheet } = record ?? {};
                if (toolNumericSheet)
                  return <>{toolNumericSheet?.createdByUser.name}</>;

                return "-";
              },
            },
            {
              render: (_, record) => {
                if (!record)
                  return (
                    <TableActionCell
                      actions={[
                        {
                          key: CURD.CREATE,
                          action: () => setAction(CURD.CREATE),
                        },
                      ]}
                      items={[
                        {
                          key: CURD.CREATE,
                          label: "Thêm mới",
                          icon: <EditOutlined />,
                        },
                      ]}
                      record={{ id: 0 }}
                    />
                  );

                return (
                  <TableActionCell
                    actions={[
                      {
                        key: CURD.UPDATE,
                        action: () => setAction(CURD.UPDATE),
                      },
                    ]}
                    items={[
                      {
                        key: CURD.UPDATE,
                        label: "Chỉnh sửa",
                        icon: <EditOutlined />,
                      },
                    ]}
                    record={record}
                  />
                );
              },
            },
          ]}
        />
      </InnerContainer>

      {action && (
        <EditNumericSheetModal
          action={action}
          cancelCb={() => setAction(undefined)}
          projectId={projectId}
          attendanceId={attendanceId}
          componentFeatureId={componentFeatureId}
          title={`Cập nhật ${attendanceFeatureDetailQuery.data?.name}`}
          recordNumericValues={
            attendanceNumericSheetsQuery.data?.recordNumericValues ?? []
          }
          featureNumericAttributes={
            attendanceFeatureDetailQuery.data?.featureNumericAttributes ?? []
          }
          projectAgencyId={
            attendanceQuery.data?.projectRecordEmployee.projectRecord
              .projectAgencyId
          }
          createdAt={attendanceQuery.data?.createdAt}
          cb={() => {
            setAction(undefined);
            attendanceNumericSheetsQuery.refetch();
          }}
        />
      )}
    </>
  );
};

export default EditNumericSheetPage;
