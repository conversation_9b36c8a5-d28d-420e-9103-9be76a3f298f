import { CURD } from "@/common/constant";
import { renderTableCell } from "@/components/table-cell";
import { renderTableOptionCell } from "@/components/table-option-cell";
import { PlusOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Col, Modal, Row, Table } from "antd";
import { useCallback, useState } from "react";
import { LuckyDrawLimitInterface } from "../../interface";
import {
  useDeleteLuckyDrawOrderLimitMutation,
  useLuckyDrawOrderLimitsQuery,
  useUpdateLuckyDrawOrderLimitMutation,
} from "../../service";
import CountProductsCell from "./CountProductsCell";
import LimitLuckDrawModal from "./LimitLuckDrawModal";
import LimitLuckyDrawProductsModal from "./LimitLuckyDrawProductsModal";

interface LimitLuckyDrawTabProps {
  componentFeatureId: number;
  activeKey: string;
  projectId: number;
}

const LimitLuckyDrawTab = ({
  componentFeatureId,
  projectId,
}: LimitLuckyDrawTabProps) => {
  const [action, setAction] = useState<CURD | undefined>(undefined);
  const [selectedLuckyDrawLimit, setSelectedLuckyDrawLimit] = useState<
    LuckyDrawLimitInterface | undefined
  >(undefined);
  const [modal, contextHolder] = Modal.useModal();
  const [isDetail, setIsDetail] = useState(false);

  const luckyDrawOrderLimitsQuery =
    useLuckyDrawOrderLimitsQuery(componentFeatureId);

  const updateLuckyDrawOrderLimitMutation =
    useUpdateLuckyDrawOrderLimitMutation(componentFeatureId);
  const deleteLuckyDrawOrderLimitMutation =
    useDeleteLuckyDrawOrderLimitMutation(componentFeatureId);

  const update = useCallback((record: LuckyDrawLimitInterface) => {
    setAction(CURD.UPDATE);
    setSelectedLuckyDrawLimit(record);
  }, []);

  const inActive = useCallback(
    async (record: LuckyDrawLimitInterface) => {
      modal.confirm({
        title: `Ngừng hoạt động giới hạn: `,
        content: "Bạn có chắc chắn muốn ngừng hoạt động giới hạn này?",
        okText: "Ngừng hoạt động",
        cancelText: "Hủy",
        onOk: async () => {
          await updateLuckyDrawOrderLimitMutation.mutateAsync({
            id: record.id,
            isActive: false,
          });

          luckyDrawOrderLimitsQuery.refetch();
        },
      });
    },
    [luckyDrawOrderLimitsQuery, modal, updateLuckyDrawOrderLimitMutation],
  );

  const active = useCallback(
    async (record: LuckyDrawLimitInterface) => {
      modal.confirm({
        title: `Kích hoạt giới hạn: `,
        content: "Bạn có chắc chắn muốn kích hoạt giới hạn này?",
        okText: "Kích hoạt",
        cancelText: "Hủy",
        onOk: async () => {
          await updateLuckyDrawOrderLimitMutation.mutateAsync({
            id: record.id,
            isActive: true,
          });

          luckyDrawOrderLimitsQuery.refetch();
        },
      });
    },
    [luckyDrawOrderLimitsQuery, modal, updateLuckyDrawOrderLimitMutation],
  );

  const onDelete = useCallback(
    async (record: LuckyDrawLimitInterface) => {
      modal.confirm({
        title: `Xóa giới hạn: `,
        content: "Bạn có chắc chắn muốn xóa giới hạn này?",
        okText: "Xóa",
        cancelText: "Hủy",
        onOk: async () => {
          await deleteLuckyDrawOrderLimitMutation.mutateAsync(record.id);

          luckyDrawOrderLimitsQuery.refetch();
        },
      });
    },
    [deleteLuckyDrawOrderLimitMutation, luckyDrawOrderLimitsQuery, modal],
  );

  return (
    <>
      <Row justify={"end"}>
        <Col>
          <Button
            icon={<PlusOutlined />}
            type="primary"
            onClick={() => setAction(CURD.CREATE)}
          >
            Thêm giới hạn
          </Button>
        </Col>
      </Row>

      <Table
        className="mt-5"
        dataSource={luckyDrawOrderLimitsQuery.data?.entities}
        columns={[
          {
            title: "Vòng quay",
            dataIndex: "projectLuckyDraw",
            render: (value) => value?.name,
          },
          {
            title: "Loại giới hạn",
            dataIndex: "type",
          },
          {
            title: "Số lượng sản phẩm",
            className: "min-w-[100px] cursor-pointer",
            onCell: (record) => ({
              onClick: () => {
                setIsDetail(true);
                setSelectedLuckyDrawLimit(record);
              },
            }),
            dataIndex: "id",
            render: (value) => {
              return (
                <CountProductsCell
                  componentFeatureId={componentFeatureId}
                  orderLuckyDrawLimitId={value}
                />
              );
            },
          },
          {
            title: "Số lượng tối đa",
            dataIndex: "maximum",
          },
          {
            key: "isActive",
            title: "Tình trạng",
            className: "min-w-[100px]",
            dataIndex: "isActive",
            render: (value, record, index) => {
              return renderTableCell(value, record, index, "isActive");
            },
          },
          {
            render: (_, record) => {
              return renderTableOptionCell(
                record,
                update,
                inActive,
                active,
                onDelete,
              );
            },
          },
        ]}
        rowKey={"id"}
        loading={luckyDrawOrderLimitsQuery.isFetching}
        pagination={false}
      />

      {action && (
        <LimitLuckDrawModal
          action={action}
          componentFeatureId={componentFeatureId}
          cancelCb={() => {
            setAction(undefined);
            setSelectedLuckyDrawLimit(undefined);
          }}
          projectId={projectId}
          selectedLuckyDrawLimit={selectedLuckyDrawLimit}
          cb={() => {
            luckyDrawOrderLimitsQuery.refetch();
          }}
        />
      )}

      {isDetail && (
        <LimitLuckyDrawProductsModal
          componentFeatureId={componentFeatureId}
          orderLuckyDrawLimitId={selectedLuckyDrawLimit?.id}
          onCancel={() => {
            setIsDetail(false);
            setSelectedLuckyDrawLimit(undefined);
          }}
          cb={() => {
            luckyDrawOrderLimitsQuery.refetch();
          }}
        />
      )}

      {contextHolder}
    </>
  );
};

export default LimitLuckyDrawTab;
