import { HOME_PAGE } from "@/common/url.helper";
import useMenuItems from "@/hooks/useMenuItems";
import { CloseOutlined, MenuOutlined } from "@ant-design/icons";
import { useAuth0 } from "@auth0/auth0-react";
import { But<PERSON>, Menu } from "antd";
import { memo, useCallback, useState } from "react";
import { useNavigate } from "react-router-dom";

interface MobileMenuProps {
  onClick?: (info: { key?: string }) => void;
  style?: React.CSSProperties;
  logo?: React.ReactNode;
  className?: string;
  headerClassName?: string;
  menuClassName?: string;
  menuItemClassName?: string;
  selectedItemClassName?: string;
  hamburgerIconClassName?: string;
  closeIconClassName?: string;
  groupTitleClassName?: string;
  projectId: number;
}

const MobileMenu = ({
  style = {},
  logo = "Logo",
  className = "",
  headerClassName = "",
  menuClassName = "",
  hamburgerIconClassName = "",
  closeIconClassName = "",
  projectId,
}: Readonly<MobileMenuProps>) => {
  const { user, logout } = useAuth0();
  const navigate = useNavigate();
  const [isOpen, setIsOpen] = useState(false);
  const { menuItems, selectedKeys } = useMenuItems(projectId);

  const toggleMenu = useCallback(() => {
    setIsOpen((prev) => !prev);
  }, []);

  const handleLogout = useCallback(() => {
    logout({
      logoutParams: {
        returnTo: HOME_PAGE,
      },
    });
  }, [logout]);

  const setRouteActive = (value: string) => {
    navigate(value);
  };

  return (
    <div className={`relative ${className}`}>
      <header
        className={`flex items-center justify-between p-4 bg-white shadow-md ${headerClassName}`}
      >
        <div className="text-xl font-bold">{logo}</div>
        <Button
          onClick={toggleMenu}
          className={`text-gray-600 hover:text-gray-800 focus:outline-none focus:ring
            ${isOpen ? closeIconClassName : hamburgerIconClassName}`}
          aria-label="Toggle menu"
          aria-expanded={isOpen}
        >
          {isOpen ? <CloseOutlined /> : <MenuOutlined />}
        </Button>
      </header>

      <nav
        className={`absolute top-full left-0 right-0 bg-white shadow-md transition-transform duration-300 ease-in-out
          ${isOpen ? "translate-y-0 opacity-100" : "-translate-y-2 opacity-0 pointer-events-none"}
          ${menuClassName}`}
        style={style}
        aria-hidden={!isOpen}
      >
        <Menu
          mode="inline"
          defaultSelectedKeys={[location.pathname]}
          onClick={({ key }) => {
            setRouteActive(key);
            setIsOpen(false);
          }}
          items={menuItems}
          selectedKeys={selectedKeys}
          style={{
            paddingLeft: "10px",
            paddingRight: "10px",
          }}
        />

        <div className="p-4">
          <Button type="primary" className="w-full" onClick={handleLogout}>
            Đăng xuất ({user?.name})
          </Button>
        </div>
      </nav>
    </div>
  );
};

export default memo(MobileMenu);
