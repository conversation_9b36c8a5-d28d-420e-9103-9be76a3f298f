import { AbstractEntityInterface } from "@/common/interface";
import {
  FeatureNumericAttributeInterface,
  NumericSheetNumericInterface,
} from "@/routes/project/component/feature/config/types/numericSheet/interface";
import { ProjectRecordFeatureInterface } from "../../interface";

export interface RecordNumericValueInterface extends AbstractEntityInterface {
  featureNumericId: number;
  featureNumericAttributeId: number;
  value: number;
  featureNumeric: NumericSheetNumericInterface;
  featureNumericAttribute: FeatureNumericAttributeInterface;
}

export interface RecordNumericInterface extends AbstractEntityInterface {
  dataUuid: string;
  dataTimestamp: string;
  recordNumericValues: RecordNumericValueInterface[];
  projectRecordFeature: ProjectRecordFeatureInterface;
}
