import { CURD } from "@/common/constant";
import CustomModal from "@/components/CustomModal";
import ProductItemCell from "@/components/ProductItemCell";
import TableActionCell from "@/components/TableActionCell";
import { ProjectProductInterface } from "@/routes/project/product/interface";
import { DeleteOutlined, PlusOutlined } from "@ant-design/icons";
import { Button, Col, Modal, Row, Table } from "antd";
import { useState } from "react";
import {
  useDeleteLuckyDrawOrderLimitProductMutation,
  useLuckyDrawOrderLimitProductsQuery,
} from "../../service";
import LimitLuckyDrawProductAvailablesModal from "./LimitLuckyDrawProductAvailablesModal";

interface LimitLuckyDrawProductsModalProps {
  componentFeatureId: number;
  orderLuckyDrawLimitId: number | undefined;
  onCancel: () => void;
  cb: () => void;
}
const LimitLuckyDrawProductsModal = ({
  componentFeatureId,
  orderLuckyDrawLimitId,
  onCancel,
  cb,
}: LimitLuckyDrawProductsModalProps) => {
  const [open, setOpen] = useState(false);
  const [modal, contentHolder] = Modal.useModal();

  const luckyDrawOrderLimitProductsQuery = useLuckyDrawOrderLimitProductsQuery(
    componentFeatureId,
    orderLuckyDrawLimitId ?? 0,
  );

  const deleteLuckyDrawOrderLimitProductMutation =
    useDeleteLuckyDrawOrderLimitProductMutation(
      componentFeatureId,
      orderLuckyDrawLimitId ?? 0,
    );

  const content = (
    <>
      <Row justify={"end"}>
        <Col>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setOpen(true)}
          >
            Thêm sản phẩm
          </Button>
        </Col>
      </Row>

      <Table
        pagination={false}
        className="mt-3"
        rowKey={"id"}
        columns={[
          {
            title: "Tên sản phẩm",
            dataIndex: "name",
            key: "name",
            render: (_, record) => {
              return (
                <ProductItemCell
                  variants={
                    record?.projectProduct.product.image?.variants ?? []
                  }
                  name={record.projectProduct.product.name}
                />
              );
            },
            className: "min-w-[50px]",
          },
          {
            title: "Mã sản phẩm",
            dataIndex: "projectProduct",
            key: "product.code",
            render: (projectProduct: ProjectProductInterface) =>
              projectProduct?.product.code,
            className: "min-w-[50px]",
          },
          {
            title: "Barcode",
            dataIndex: "projectProduct",
            key: "product.code",
            render: (projectProduct: ProjectProductInterface) =>
              projectProduct?.productPackaging?.barcode,
            className: "min-w-[50px]",
          },
          {
            title: "Nhãn hàng",
            dataIndex: "projectProduct",
            key: "product.brand",
            render: (projectProduct: ProjectProductInterface) =>
              projectProduct?.product.brand.name,
            className: "min-w-[50px]",
          },
          {
            title: "Quy cách",
            dataIndex: "projectProduct",
            key: "product.code",
            render: (projectProduct: ProjectProductInterface) =>
              projectProduct?.productPackaging?.unit.name,
            className: "min-w-[50px]",
          },
          {
            render: (_, record) => {
              return (
                <TableActionCell
                  actions={[
                    {
                      key: CURD.DELETE,
                      action: (record: {
                        id: number;
                        projectProduct: ProjectProductInterface;
                      }) => {
                        const name = record.projectProduct.product.name;

                        modal.confirm({
                          title: `Xóa sản phẩm: ${name}`,
                          content: `Bạn có chắc chắn muốn xóa sản phẩm ${name}?`,
                          okText: "Xóa",
                          cancelText: "Hủy",
                          onOk: async () => {
                            await deleteLuckyDrawOrderLimitProductMutation.mutateAsync(
                              record.id,
                            );
                            luckyDrawOrderLimitProductsQuery.refetch();
                          },
                        });
                      },
                    },
                  ]}
                  items={[
                    {
                      key: CURD.DELETE,
                      label: "Xóa",
                      icon: <DeleteOutlined />,
                    },
                  ]}
                  record={record}
                />
              );
            },
            fixed: "right",
          },
        ]}
        dataSource={luckyDrawOrderLimitProductsQuery.data?.entities}
      />
    </>
  );

  return (
    <>
      <CustomModal
        width={900}
        title={"Danh sách giới hạn"}
        isOpen={true}
        content={content}
        hideConfirm
        onCancel={onCancel}
      />

      {open && (
        <LimitLuckyDrawProductAvailablesModal
          componentFeatureId={componentFeatureId}
          orderLuckyDrawLimitId={orderLuckyDrawLimitId ?? 0}
          onCancel={() => {
            setOpen(false);
          }}
          cb={() => {
            luckyDrawOrderLimitProductsQuery.refetch();
            cb();
          }}
        />
      )}

      {contentHolder}
    </>
  );
};

export default LimitLuckyDrawProductsModal;
