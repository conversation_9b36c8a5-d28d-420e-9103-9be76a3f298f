import { useApp } from "@/UseApp";
import { useQuery } from "@tanstack/react-query";
import { DashboardFilterInterface } from "../../interface";

export const useNumericSheetTotalQuery = (
  projectId: number,
  dashboardId: number,
  filter?: DashboardFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["numericSheetTotal", projectId, dashboardId, filter],
    queryFn: () =>
      axiosGet<
        {
          name: string;
          code: string;
          attribute: {
            name: string;
            backgroundColor: string | null;
            foregroundColor: string | null;
          };
          total: number;
          backgroundColor: string | null;
          foregroundColor: string | null;
        }[],
        unknown
      >(
        `/projects/${projectId}/dashboards/${dashboardId}/charts/numeric-sheet-total`,
        filter,
      ),
  });
};
