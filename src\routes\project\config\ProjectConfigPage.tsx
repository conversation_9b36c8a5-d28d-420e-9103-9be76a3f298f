import InnerContainer from "@/components/InnerContainer/InnerContainer";
import { Col, Row, Table } from "antd";
import { useParams } from "react-router-dom";
import ConfigSitecheckSection from "./ConfigSitecheckSection";
import ConfigSmsTopSection from "./ConfigSmsTopSection";
import ConfigToolSection from "./ConfigToolSection";

const ProjectConfigPage = () => {
  const projectId = parseInt(useParams().id ?? "0");

  return (
    <div>
      <h2>C<PERSON>u hình</h2>
      <InnerContainer>
        <Row>
          <Col md={8}>
            <h4>Tài khoản nhân viên</h4>
            <p className="text-gray-400 max-w-[350px]">
              Tài khoản nhân viên field và nhân viên cấp quản lý đang hoạt động
              và có thể truy cập hệ thống
            </p>
          </Col>
          <Col md={16}>
            <Table
              columns={[
                {
                  title: "Agency",
                },
                {
                  title: "Tài khoản nhân viên field",
                },
                {
                  title: "Tài khoản quản lý web admin",
                },
              ]}
            />
          </Col>
        </Row>
      </InnerContainer>

      <ConfigToolSection projectId={projectId} />

      <ConfigSitecheckSection projectId={projectId} />

      <ConfigSmsTopSection projectId={projectId} />
    </div>
  );
};

export default ProjectConfigPage;
