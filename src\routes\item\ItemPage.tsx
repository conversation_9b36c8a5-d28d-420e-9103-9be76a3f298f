import {
  CURD,
  DEFAULT_CURRENT_PAGE,
  DEFAULT_PAGE_SIZE,
  SELECT_ALL,
  SELECT_ALL_LABEL,
} from "@/common/constant";
import FilterComponent from "@/components/FilterComponent.tsx";
import ProductItemCell from "@/components/ProductItemCell.tsx";
import { renderTableCell } from "@/components/table-cell";
import { renderTableOptionCell } from "@/components/table-option-cell";
import { useApp } from "@/UseApp.tsx";
import { Form, Modal, Table } from "antd";
import { ColumnsType } from "antd/es/table";
import React, { useCallback, useEffect, useState } from "react";
import { useLocation, useSearchParams } from "react-router-dom";
import { ItemTypeInterface } from "../item-type/interface.ts";
import { ItemInterface } from "./interface.ts";
import { ItemModal } from "./ItemModal.tsx";
import { getItems, useDeleteItemMutation } from "./services.ts";

const ItemPage: React.FC = () => {
  const {
    axiosGet,
    axiosPatch,
    showNotification,
    loading,
    setLoading,
    openDeleteModal,
  } = useApp();

  const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [searchForm] = Form.useForm();
  const [form] = Form.useForm();
  const [data, setData] = useState<ItemInterface[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [isModalAddOrUpdateOpen, setIsModalAddOrUpdateOpen] = useState(false);
  const [formAction, setFormAction] = useState<CURD | null>(null);
  const [modalTitle, setModalTitle] = useState("");
  const [modal, contextHolder] = Modal.useModal();
  const location = useLocation();
  const [item, setItem] = useState<ItemInterface>();
  const [searchParams, setSearchParams] = useSearchParams();

  const deleteItemMutation = useDeleteItemMutation();

  const fetchData = useCallback(
    async (
      currentPageInput?: number,
      pageSizeInput?: number,
    ): Promise<void> => {
      setLoading(true);
      const filter = {
        take: pageSizeInput ?? pageSize,
        skip: pageSizeInput
          ? 0
          : (currentPageInput ?? currentPage - 1) * pageSize,
        ...searchForm.getFieldsValue(),
      };
      try {
        const response = await getItems(axiosGet, filter);
        if (!Array.isArray(response)) {
          const { entities, count } = response;
          setData(entities);
          setTotal(count);
        }
      } catch (e) {
        console.error(e);
      } finally {
        setLoading(false);
      }
    },
    [setLoading, pageSize, currentPage, searchForm, axiosGet],
  );

  const handlePaginationChange = useCallback(
    (page: number) => {
      setCurrentPage(page);
      fetchData(page);
    },
    [setCurrentPage, fetchData],
  );

  const handlePageSizeChange = useCallback(
    (_current: number, size: number) => {
      setPageSize(size);
      setCurrentPage(DEFAULT_CURRENT_PAGE);
      fetchData(DEFAULT_CURRENT_PAGE, size);
    },
    [setPageSize, setCurrentPage, fetchData],
  );

  const handleBtnEditClick = useCallback(
    (record: ItemInterface) => {
      setIsModalAddOrUpdateOpen(true);
      setFormAction(CURD.UPDATE);
      setModalTitle("Cập nhật vật phẩm");
      form.setFieldsValue({
        ...record,
        unitId: record?.unit?.id,
        itemTypeId: record?.itemType?.id,
        clientId: record?.itemType?.client?.id,
      });
      setItem(record);
    },
    [form],
  );

  const handleBtnInactiveClick = useCallback(
    (record: ItemInterface) => {
      modal.confirm({
        title: `Ngừng hoạt động vật phẩm: ${record.name}`,
        content: "Bạn có chắc chắn muốn ngừng hoạt động vật phẩm này?",
        okText: "Ngừng hoạt động",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await axiosPatch(`/items/${record.id}`, { isActive: false });
            showNotification({
              type: "success",
              message: "Ngừng hoạt động vật phẩm thành công",
            });
            fetchData();
          } catch (error) {
            console.error(error);
            showNotification({
              type: "error",
              message: "Ngừng hoạt động vật phẩm thất bại",
            });
          }
        },
      });
    },
    [axiosPatch, fetchData, modal, showNotification],
  );

  const handleBtnActiveClick = useCallback(
    (record: ItemInterface) => {
      modal.confirm({
        title: `Kích hoạt vật phẩm: ${record.name}`,
        content: "Bạn có chắc chắn muốn kích hoạt vật phẩm này?",
        okText: "Kích hoạt",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await axiosPatch(`/items/${record.id}`, { isActive: true });
            showNotification({
              type: "success",
              message: "Kích hoạt vật phẩm thành công",
            });
          } catch (error) {
            console.error(error);
            showNotification({
              type: "error",
              message: "Kích hoạt vật phẩm thất bại",
            });
          }
          fetchData();
        },
      });
    },
    [axiosPatch, fetchData, modal, showNotification],
  );

  const handleBtnDeleteClick = useCallback(
    (record: ItemInterface) => {
      openDeleteModal({
        content: (
          <>
            <p>
              Vật phẩm sẽ được xóa khỏi hệ thống vĩnh viễn và không thể khôi
              phục
            </p>
            <p>
              Bạn vẫn muốn xóa vật phẩm{" "}
              <span className={"font-semibold"}>{record.name}</span> khỏi hệ
              thống?
            </p>
          </>
        ),
        deleteText: "Xác nhận xóa",
        loading: deleteItemMutation.isPending,
        onCancel(): void {},
        onDelete: async () => {
          await deleteItemMutation.mutateAsync(record.id);
          showNotification({
            type: "success",
            message: "Xóa vật phẩm thành công",
          });
          await fetchData();
        },
        title: `Xóa vật phẩm`,
        titleError: "Không thể xóa vật phẩm",
        contentHeader: (
          <>
            Không thể xóa vật phẩm{" "}
            <span className="font-semibold">{record.name}</span> bởi vì:
          </>
        ),
      });
    },
    [deleteItemMutation, fetchData, openDeleteModal, showNotification],
  );

  const columns: ColumnsType<ItemInterface> = [
    {
      title: "Tên vật phẩm",
      key: "name",
      dataIndex: "name",
      className: "min-w-[100px]",
      render: (_, record: ItemInterface) => {
        return (
          <ProductItemCell
            variants={record?.image?.variants ?? []}
            name={record.name}
          />
        );
      },
    },
    {
      title: "Mã vật phẩm",
      key: "code",
      dataIndex: "code",
      render: renderTableCell,
      className: "min-w-[100px]",
    },
    {
      title: "Đơn vị tính",
      key: "unit.name",
      dataIndex: "unit",
      render: renderTableCell,
      className: "min-w-[100px]",
    },
    {
      title: "Loại vật phẩm",
      key: "itemType.name",
      dataIndex: "itemType",
      render: renderTableCell,
      className: "min-w-[100px]",
    },
    {
      title: "Client",
      key: "client",
      dataIndex: "itemType",
      render: (itemType: ItemTypeInterface) => itemType.client?.name,
      className: "min-w-[100px]",
    },
    {
      key: "isActive",
      title: "Tình trạng",
      className: "min-w-[100px]",
      dataIndex: "isActive",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "isActive");
      },
    },
    {
      key: "createdAt",
      title: "Thời gian tạo",
      className: "min-w-[100px]",
      dataIndex: "createdAt",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "datetime");
      },
    },
    {
      key: "createdByUser.name",
      title: "Người tạo",
      dataIndex: "createdByUser",
      render: renderTableCell,
      className: "min-w-[100px]",
    },
    {
      key: "updatedAt",
      title: "Thời gian cập nhật",
      className: "min-w-[100px]",
      dataIndex: "updatedAt",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "datetime");
      },
    },
    {
      key: "updatedByUser.name",
      title: "Người cập nhật",
      dataIndex: "updatedByUser",
      render: renderTableCell,
      className: "min-w-[100px]",
    },
    {
      key: "actions",
      render: (_, record) => {
        return renderTableOptionCell(
          record,
          handleBtnEditClick,
          handleBtnInactiveClick,
          handleBtnActiveClick,
          handleBtnDeleteClick,
        );
      },
      fixed: "right",
      width: 100,
    },
  ];

  const filterOptions = columns
    .filter(
      (item) =>
        !["isActive", "updatedAt", "createdAt", "actions"].includes(
          item.key as string,
        ),
    )
    .map((column) => {
      return {
        label: column.title as string,
        value: column.key as string | number,
      };
    });

  filterOptions.unshift({ label: SELECT_ALL_LABEL, value: SELECT_ALL });

  useEffect(() => {
    searchParams.forEach((value, key) => {
      searchForm.setFieldValue(key, value);
    });
    fetchData();
  }, [fetchData, searchForm, location, searchParams]);

  const searchHandler = () => {
    fetchData(DEFAULT_CURRENT_PAGE, pageSize);
    setCurrentPage(DEFAULT_CURRENT_PAGE);
    setSearchParams({});
  };

  const handleAddButtonClick = () => {
    setIsModalAddOrUpdateOpen(true);
    setFormAction(CURD.CREATE);
    setModalTitle("Thêm vật phẩm");
  };
  const pagination = React.useMemo(() => {
    return {
      current: currentPage,
      total: total,
      pageSize: pageSize,
      onChange: handlePaginationChange,
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: handlePageSizeChange,
    };
  }, [
    currentPage,
    total,
    pageSize,
    handlePaginationChange,
    handlePageSizeChange,
  ]);

  return (
    <div>
      <h2>Vật phẩm</h2>
      <div className="bg-white p-10 rounded">
        <div className="pb-6">
          <FilterComponent
            filterOptions={filterOptions}
            searchHandler={searchHandler}
            handleAddButtonClick={handleAddButtonClick}
            searchForm={searchForm}
            hasClient
          />
        </div>

        <Table
          dataSource={data}
          columns={columns}
          rowKey={(record) => record.id}
          scroll={{
            x: "max-content",
            y: pagination.total ? "80vh" : undefined,
          }}
          pagination={pagination}
          loading={loading}
        />

        <ItemModal
          isOpen={isModalAddOrUpdateOpen}
          modalTitle={modalTitle}
          setIsOpen={setIsModalAddOrUpdateOpen}
          form={form}
          formAction={formAction}
          setModalTitle={setModalTitle}
          callback={fetchData}
          disabledSelectItemType={formAction === CURD.UPDATE}
          item={item}
        />
      </div>

      {contextHolder}
    </div>
  );
};

export default ItemPage;
