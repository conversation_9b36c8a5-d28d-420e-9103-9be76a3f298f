import { FormPhotoInterface } from "@/common/interface";
import { create } from "zustand";

type FormPhotosStoreState = {
  formPhotos: FormPhotoInterface[];
};

type FormPhotosStoreAction = {
  setFormPhotos: (photos: FormPhotoInterface[]) => void;
};

export const useFormPhotosStore = create<
  FormPhotosStoreState & FormPhotosStoreAction
>((set) => ({
  formPhotos: [],
  setFormPhotos: (photos) => set({ formPhotos: photos }),
}));
