import { CURD, SELECT_ALL, SELECT_ALL_LABEL } from "@/common/constant";
import CustomTable from "@/components/CustomTable/CustomTable.tsx";
import FilterComponent from "@/components/FilterComponent.tsx";
import InnerContainer from "@/components/InnerContainer/InnerContainer.tsx";
import { renderTableCell } from "@/components/table-cell";
import { renderTableOptionCell } from "@/components/table-option-cell";
import { useUrlFilters } from "@/hooks/useUrlFilters.ts";
import { useApp } from "@/UseApp.tsx";
import { Form, Modal } from "antd";
import { ColumnsType } from "antd/es/table";
import React, { useCallback, useMemo, useState } from "react";
import { ChannelInterface } from "../channel/interface.ts";
import { SubChannelInterface } from "./interface.ts";
import {
  useDeleteSubChannelMutation,
  useSubChannelsQuery,
  useUpdateSubChannelMutation,
} from "./service.ts";
import SubChannelModal from "./SubChannelModal.tsx";

const SubChannelPage: React.FC = () => {
  const { showNotification, openDeleteModal } = useApp();

  const [searchForm] = Form.useForm();
  const [form] = Form.useForm();
  const [isModalAddOrUpdateOpen, setIsModalAddOrUpdateOpen] = useState(false);
  const [formAction, setFormAction] = useState<CURD | null>(null);
  const [modalTitle, setModalTitle] = useState("");
  const [modal, contextHolder] = Modal.useModal();
  const { filter, currentPage, pageSize, handleSearch, getPaginationProps } =
    useUrlFilters({
      formInstance: searchForm,
      handleSearchCallback: () => {
        subChannelsQuery.refetch();
      },
    });

  const subChannelsQuery = useSubChannelsQuery({
    ...filter,
    take: pageSize,
    skip: (currentPage - 1) * pageSize,
    getInActive: true,
  });

  const updateSubChannelMutation = useUpdateSubChannelMutation();
  const deleteSubChannelMutation = useDeleteSubChannelMutation();

  const handleBtnEditClick = useCallback(
    (record: SubChannelInterface) => {
      setIsModalAddOrUpdateOpen(true);
      setFormAction(CURD.UPDATE);
      setModalTitle("Cập nhật kênh nhóm");
      form.setFieldsValue({
        ...record,
        channelId: record.channel.id,
        clientId: record.channel.client.id,
      });
    },
    [form],
  );

  const handleBtnInactiveClick = useCallback(
    (record: SubChannelInterface) => {
      modal.confirm({
        title: `Ngừng hoạt động subchannel: ${record.name}`,
        content: "Bạn có chắc chắn muốn ngừng hoạt động subchannel này?",
        okText: "Ngừng hoạt động",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await updateSubChannelMutation.mutateAsync({
              id: record.id,
              isActive: false,
            });

            showNotification({
              type: "success",
              message: "Ngừng hoạt động subchannel thành công",
            });

            subChannelsQuery.refetch();
          } catch (error) {
            console.error(error);
            showNotification({
              type: "error",
              message: "Ngừng hoạt động subchannel thất bại",
            });
          }
        },
      });
    },
    [modal, showNotification, subChannelsQuery, updateSubChannelMutation],
  );

  const handleBtnActiveClick = useCallback(
    (record: SubChannelInterface) => {
      modal.confirm({
        title: `Kích hoạt subchannel: ${record.name}`,
        content: "Bạn có chắc chắn muốn kích hoạt subchannel này?",
        okText: "Kích hoạt",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await updateSubChannelMutation.mutateAsync({
              id: record.id,
              isActive: true,
            });

            showNotification({
              type: "success",
              message: "Kích hoạt subchannel thành công",
            });
          } catch (error) {
            console.error(error);
            showNotification({
              type: "error",
              message: "Kích hoạt subchannel thất bại",
            });
          }

          subChannelsQuery.refetch();
        },
      });
    },
    [modal, showNotification, subChannelsQuery, updateSubChannelMutation],
  );

  const handleBtnDeleteClick = useCallback(
    (record: SubChannelInterface) => {
      openDeleteModal({
        content: (
          <>
            <p>
              Nhóm thực hiện sẽ được xóa khỏi hệ thống vĩnh viễn và không thể
              khôi phục
            </p>
            <p>
              Bạn vẫn muốn xóa nhóm{" "}
              <span className={"font-semibold"}>{record.name}</span> khỏi hệ
              thống?
            </p>
          </>
        ),
        deleteText: "Xác nhận xóa",
        loading: deleteSubChannelMutation.isPending,
        onCancel(): void {},
        onDelete: async () => {
          await deleteSubChannelMutation.mutateAsync(record.id);
          showNotification({
            type: "success",
            message: "Xóa nhóm thành công",
          });

          subChannelsQuery.refetch();
        },
        title: `Xóa nhóm thực hiện`,
        titleError: "Không thể xóa nhóm thực hiện",
        contentHeader: (
          <>
            Không thể xóa nhóm{" "}
            <span className="font-semibold">{record.name}</span> bởi vì:
          </>
        ),
      });
    },
    [
      deleteSubChannelMutation,
      openDeleteModal,
      showNotification,
      subChannelsQuery,
    ],
  );

  const columns: ColumnsType<SubChannelInterface> = [
    {
      title: "Tên nhóm",
      key: "name",
      dataIndex: "name",
      className: "min-w-[100px]",
      render: renderTableCell,
    },
    {
      title: "Mã nhóm",
      key: "code",
      dataIndex: "code",
      className: "min-w-[100px]",
      render: renderTableCell,
    },
    {
      title: "Kênh thực hiện",
      key: "channel.name",
      dataIndex: "channel",
      className: "min-w-[100px]",
      render: renderTableCell,
    },
    {
      title: "Client",
      key: "client",
      className: "min-w-[100px]",
      dataIndex: "channel",
      render: (channel: ChannelInterface) => channel?.client.name,
    },
    {
      key: "isActive",
      title: "Tình trạng",
      className: "min-w-[100px]",
      dataIndex: "isActive",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "isActive");
      },
    },
    {
      key: "createdAt",
      className: "min-w-[100px]",
      title: "Thời gian tạo",
      dataIndex: "createdAt",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "datetime");
      },
    },
    {
      key: "createdByUser.name",
      title: "Người tạo",
      dataIndex: "createdByUser",
      className: "min-w-[100px]",
      render: renderTableCell,
    },
    {
      key: "updatedAt",
      title: "Thời gian cập nhật",
      className: "min-w-[100px]",
      dataIndex: "updatedAt",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "datetime");
      },
    },
    {
      key: "updatedByUser.name",
      title: "Người cập nhật",
      dataIndex: "updatedByUser",
      render: renderTableCell,
      className: "min-w-[100px]",
    },
    {
      key: "actions",
      render: (_, record) => {
        return renderTableOptionCell(
          record,
          handleBtnEditClick,
          handleBtnInactiveClick,
          handleBtnActiveClick,
          handleBtnDeleteClick,
        );
      },
      fixed: "right",
      width: 50,
      align: "center",
    },
  ];

  const filterOptions = columns
    .filter(
      (item) =>
        !["isActive", "updatedAt", "createdAt", "actions", "client"].includes(
          item.key as string,
        ),
    )
    .map((column) => {
      return {
        label: column.title as string,
        value: column.key as string | number,
      };
    });

  filterOptions.unshift({ label: SELECT_ALL_LABEL, value: SELECT_ALL });

  const handleAddButtonClick = () => {
    setIsModalAddOrUpdateOpen(true);
    setFormAction(CURD.CREATE);
    setModalTitle("Thêm kênh nhóm");
  };

  const pagination = useMemo(
    () => getPaginationProps(subChannelsQuery.data?.count),
    [getPaginationProps, subChannelsQuery.data?.count],
  );

  const loading = useMemo(
    () =>
      subChannelsQuery.isLoading ||
      subChannelsQuery.isFetching ||
      updateSubChannelMutation.isPending ||
      deleteSubChannelMutation.isPending,
    [
      subChannelsQuery.isLoading,
      subChannelsQuery.isFetching,
      updateSubChannelMutation.isPending,
      deleteSubChannelMutation.isPending,
    ],
  );
  return (
    <div>
      <h2>Nhóm thực hiện</h2>
      <InnerContainer>
        <FilterComponent
          filterOptions={filterOptions}
          searchHandler={handleSearch}
          handleAddButtonClick={handleAddButtonClick}
          searchForm={searchForm}
          btnLoading={loading}
          hasClient
        />

        <CustomTable<SubChannelInterface>
          dataSource={subChannelsQuery.data?.entities}
          columns={columns}
          scroll={{
            x: "max-content",
            y: pagination.total ? "80vh" : undefined,
          }}
          pagination={pagination}
          loading={loading}
        />
      </InnerContainer>

      <SubChannelModal
        form={form}
        isOpen={isModalAddOrUpdateOpen}
        setIsOpen={setIsModalAddOrUpdateOpen}
        modalTitle={modalTitle}
        formAction={formAction}
        setModalTitle={setModalTitle}
        callback={() => subChannelsQuery.refetch()}
      />
      {contextHolder}
    </div>
  );
};

export default SubChannelPage;
