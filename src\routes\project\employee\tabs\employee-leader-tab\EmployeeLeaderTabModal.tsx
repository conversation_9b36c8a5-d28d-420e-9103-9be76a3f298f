import { CURD } from "@/common/constant.ts";
import { filterOption, formErrorResponseHandler } from "@/common/helper.ts";
import { getImageVariants } from "@/common/image.helper.ts";
import { uploadImage } from "@/common/upload-image.helper.ts";
import ModalCURD from "@/components/ModalCURD.tsx";
import UploadImageComponent from "@/components/UploadImageComponent.tsx";
import {
  useCreateUserMutation,
  useUpdateUserMutation,
} from "@/routes/user/services.ts";
import { useApp } from "@/UseApp.tsx";
import {
  GENDER_ENUM_TO_LABEL,
  GenderEnum,
  ProjectEmployeeUserInterface,
} from "@project/employee/interface.ts";
import {
  useCreateEmployeeMutation,
  useUpdateEmployeeMutation,
} from "@project/employee/service.ts";
import { useProjectAgenciesQuery } from "@project/general/services.ts";
import { RoleInterface } from "@project/role/interface.ts";
import { Alert, Form, Input, Radio, Select } from "antd";
import { useCallback, useEffect, useState } from "react";
import { useParams } from "react-router-dom";

interface EmployeeLeaderTabModalProps {
  action: CURD | null;
  role: RoleInterface;
  cb: () => void;
  cancelCb: () => void;
  selectedProjectEmployeeUser?: ProjectEmployeeUserInterface;
}

const EmployeeLeaderTabModal = ({
  action,
  role,
  cb,
  cancelCb,
  selectedProjectEmployeeUser,
}: EmployeeLeaderTabModalProps) => {
  const { showNotification, axiosPost } = useApp();
  const projectId = parseInt(useParams().id ?? "0");

  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const projectAgenciesQuery = useProjectAgenciesQuery(projectId);

  const createUserMutation = useCreateUserMutation();
  const updateUserMutation = useUpdateUserMutation();
  const createEmployeeMutation = useCreateEmployeeMutation(projectId);
  const updateEmployeeMutation = useUpdateEmployeeMutation(projectId);

  const formContent = (
    <>
      <Alert
        message="Nhân viên chưa có trong hệ thống nên cần cung cấp thông tin ở phía dưới."
        type="info"
        showIcon
      />
      <Form.Item name={"employeeId"} hidden>
        <Input />
      </Form.Item>
      <Form.Item
        className="pt-5"
        label="Họ và tên"
        name="name"
        rules={[{ required: true, message: "Họ và tên không được để trống." }]}
      >
        <Input />
      </Form.Item>

      <Form.Item
        label="Số điện thoại"
        name="phone"
        rules={[
          {
            required: true,
          },
          {
            pattern: new RegExp(/^0\d+$/),
            message: "Vui lòng nhập đúng định dạng số điện thoại",
          },
          {
            len: 10,
          },
        ]}
        extra="Nhân viên sẽ sử dụng số điện thoại này để đăng nhập vào app"
      >
        <Input disabled={action === CURD.UPDATE} />
      </Form.Item>
      <Form.Item
        label="Email"
        name="email"
        rules={[
          {
            required: true,
            message: "Email không được để trống.",
          },
          {
            type: "email",
          },
        ]}
        extra="Nhân viên sẽ nhận được lời mời qua địa chỉ email này"
      >
        <Input />
      </Form.Item>
      <Form.Item label="Mã nhân viên" name="code">
        <Input />
      </Form.Item>
      <Form.Item label="Giới tính" name="gender">
        <Radio.Group>
          {Object.values(GenderEnum).map((gender) => (
            <Radio key={gender} value={gender}>
              {GENDER_ENUM_TO_LABEL[gender]}
            </Radio>
          ))}
        </Radio.Group>
      </Form.Item>

      <UploadImageComponent
        form={form}
        fieldLabel="Ảnh đại diện"
        imageUrlFieldName="imageUrl"
      />
      <hr color="#DDE1EA" />
      <Form.Item
        className="pt-5"
        label="Agency quản lý nhân viên này"
        name="projectAgencyId"
        rules={[
          {
            required: true,
            message: "Agency quản lý không được để trống.",
          },
        ]}
      >
        <Select
          showSearch
          optionFilterProp="children"
          filterOption={filterOption}
          options={projectAgenciesQuery.data?.map((projectAgency) => ({
            label: projectAgency.agency.name,
            value: projectAgency.id,
          }))}
          disabled={action === CURD.UPDATE}
        />
      </Form.Item>
    </>
  );

  const formLeaderSubmit = useCallback(async () => {
    try {
      await form.validateFields();
      const data = form.getFieldsValue();
      const id = form.getFieldValue("id");

      if (data.imageFile) {
        setLoading(true);
        data.picture = getImageVariants(
          (await uploadImage(axiosPost, data.imageFile.file))?.result
            .variants ?? [],
          "public",
        );
        delete data.imageFile;
        setLoading(false);
      }

      switch (action) {
        case CURD.CREATE:
          {
            const user = await createUserMutation.mutateAsync({
              ...data,
              username: data.phone,
              type: "EMPLOYEE",
              password: "123456",
            });

            if (user) {
              await createEmployeeMutation.mutateAsync({
                projectAgencyId: form.getFieldValue("projectAgencyId"),
                userId: user.id,
                roleId: role.id,
              });

              showNotification({
                type: "success",
                message: `Thêm ${role.name} thành công`,
              });

              form.resetFields();
              cb();
            }
          }
          break;
        case CURD.UPDATE:
          await updateUserMutation.mutateAsync({
            id,
            ...data,
            username: data.phone,
          });

          await updateEmployeeMutation.mutateAsync({
            id: form.getFieldValue("employeeId"),
            projectAgencyId: data.projectAgencyId,
          });

          showNotification({
            type: "success",
            message: `Cập nhật ${role.name} thành công`,
          });

          form.resetFields();
          cb();
          break;
        default:
          return;
      }
    } catch (error) {
      formErrorResponseHandler(form, error);
    }
  }, [
    action,
    axiosPost,
    cb,
    createEmployeeMutation,
    createUserMutation,
    form,
    role.id,
    role.name,
    showNotification,
    updateEmployeeMutation,
    updateUserMutation,
  ]);

  useEffect(() => {
    if (action === CURD.UPDATE && selectedProjectEmployeeUser) {
      const { user, projectAgency, id } = selectedProjectEmployeeUser;

      form.setFieldsValue({
        name: user.name,
        phone: user.phone,
        email: user.email,
        username: user.username,
        initUrl: user.picture,
        projectAgencyId: projectAgency?.id,
        id: user.id,
        employeeId: id,
        gender: user.gender,
        code: user.code,
      });
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [action, selectedProjectEmployeeUser]);

  return (
    <ModalCURD
      title={
        action === CURD.CREATE ? `Thêm ${role.name}` : `Thông tin ${role.name}`
      }
      isOpen={!!action}
      formContent={formContent}
      form={form}
      onFinish={formLeaderSubmit}
      action={action}
      btnConfirmLoading={
        createUserMutation.isPending ||
        updateUserMutation.isPending ||
        createEmployeeMutation.isPending ||
        updateEmployeeMutation.isPending ||
        loading
      }
      onCancelCb={() => {
        form.resetFields();
        cancelCb();
      }}
    />
  );
};

export default EmployeeLeaderTabModal;
