import useDeviceType from "@/hooks/useDeviceType";
import {
  CaretDownOutlined,
  ImportOutlined,
  PlusOutlined,
  SearchOutlined,
  VerticalAlignBottomOutlined,
} from "@ant-design/icons";
import {
  Button,
  Col,
  Dropdown,
  Form,
  FormInstance,
  Input,
  Modal,
  Row,
  Space,
} from "antd";
import { MenuProps } from "antd/lib";
import React, { useCallback, useState } from "react";
import { useApp } from "../UseApp";
import { SELECT_ALL } from "../common/constant";

interface FilterClassicComponentProps {
  searchHandler: () => void;
  searchForm: FormInstance;
  handleExcelButtonClick?: () => void;
  handleAddButtonClick?: () => void;
  style?: React.CSSProperties;
  className?: string;
  content: React.ReactNode;
  showAddButton?: boolean;
  showExportButton?: boolean;
  btnLoading?: boolean;
  onImportClick?: () => void;
  disableAddButton?: boolean;
}

const FilterClassicComponent = (props: FilterClassicComponentProps) => {
  const { loading } = useApp();
  const isMobile = useDeviceType();

  const [open, setOpen] = useState(false);

  const {
    searchHandler,
    searchForm,
    handleExcelButtonClick,
    handleAddButtonClick,
    style,
    className,
    content,
    showAddButton,
    showExportButton,
    btnLoading,
    onImportClick,
    disableAddButton,
  } = props;

  const onImportClickHandler = useCallback(() => {
    if (onImportClick) {
      onImportClick();
      setOpen(false);
    }
  }, [onImportClick]);

  const onHandleExcelButtonClick = useCallback(() => {
    if (handleExcelButtonClick) {
      handleExcelButtonClick();
      setOpen(false);
    }
  }, [handleExcelButtonClick]);

  const onHandleAddButtonClick = useCallback(() => {
    if (handleAddButtonClick) {
      handleAddButtonClick();
      setOpen(false);
    }
  }, [handleAddButtonClick]);

  const dropdownOnClick: MenuProps["onClick"] = useCallback(
    (e: { key: string }) => {
      if (e.key === "import" && onImportClick) {
        onImportClickHandler();
      }
    },
    [onImportClick, onImportClickHandler],
  );

  const dropdownItems = onImportClick
    ? [
        {
          key: "import",
          label: "Nhập từ excel",
        },
      ]
    : [];

  if (isMobile) {
    return (
      <div className={`w-full ${className}`}>
        <Button
          className="w-full bg-input-disabled"
          icon={<SearchOutlined />}
          type="text"
          onClick={() => setOpen(true)}
        >
          Filter
        </Button>

        <Modal
          open={open}
          footer={null}
          onCancel={() => setOpen(false)}
          title={"Filter"}
        >
          <div className="mt-5">
            <Form
              form={searchForm}
              onFinish={() => {
                searchHandler();
                setOpen(false);
              }}
            >
              {content}

              <Form.Item>
                <Button
                  htmlType="submit"
                  type="default"
                  loading={btnLoading ?? loading}
                  className="w-full bg-[#F0F1F5]"
                >
                  Tìm kiếm
                </Button>
              </Form.Item>

              {showExportButton && (
                <Form.Item>
                  <Button
                    icon={<VerticalAlignBottomOutlined />}
                    onClick={onHandleExcelButtonClick}
                    loading={btnLoading ?? loading}
                    className="w-full"
                  >
                    Tải excel
                  </Button>
                </Form.Item>
              )}

              {showAddButton && (
                <>
                  <Form.Item>
                    <Button
                      type="primary"
                      onClick={onHandleAddButtonClick}
                      loading={btnLoading ?? loading}
                      disabled={disableAddButton}
                      className="w-full"
                      icon={<PlusOutlined />}
                    >
                      Thêm mới
                    </Button>
                  </Form.Item>

                  <Form.Item>
                    <Button
                      type="dashed"
                      onClick={onImportClickHandler}
                      loading={btnLoading ?? loading}
                      disabled={disableAddButton}
                      className="w-full"
                      icon={<ImportOutlined />}
                    >
                      Import
                    </Button>
                  </Form.Item>
                </>
              )}
            </Form>
          </div>
        </Modal>
      </div>
    );
  }

  return (
    <Row justify={"space-between"} style={style} className={className}>
      <Col md={20}>
        <Form layout="inline" form={searchForm} onFinish={searchHandler}>
          <Space size={0}>{content}</Space>

          <Form.Item>
            <Button
              htmlType="submit"
              type="default"
              loading={btnLoading ?? loading}
            >
              Tìm kiếm
            </Button>
          </Form.Item>
          <Form.Item name="filterField" initialValue={SELECT_ALL}>
            <Input type="hidden" value={SELECT_ALL} />
          </Form.Item>
        </Form>
      </Col>
      <Col md={4}>
        <Space>
          {showExportButton && (
            <Button
              icon={<VerticalAlignBottomOutlined />}
              onClick={handleExcelButtonClick}
              loading={btnLoading ?? loading}
            >
              Tải excel
            </Button>
          )}

          {showAddButton && (
            <Dropdown.Button
              menu={{
                items: dropdownItems,
                onClick: dropdownOnClick,
              }}
              icon={<CaretDownOutlined />}
              type="primary"
              onClick={handleAddButtonClick}
              loading={btnLoading ?? loading}
              disabled={disableAddButton}
            >
              Thêm mới
            </Dropdown.Button>
          )}
        </Space>
      </Col>
    </Row>
  );
};

export default FilterClassicComponent;
