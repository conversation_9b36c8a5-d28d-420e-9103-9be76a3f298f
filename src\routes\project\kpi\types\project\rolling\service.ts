import { AbstractFilterInterface } from "@/common/interface";
import { useApp } from "@/UseApp";
import { useMutation, useQuery } from "@tanstack/react-query";
import { ItemKpiTypeEnum, KpiRollingInterface } from "../../../interface";

export const useDeleteKpiRollingMutation = (projectId: number) => {
  const { axiosDelete } = useApp();
  return useMutation({
    mutationKey: ["deleteKpiRolling", projectId],
    mutationFn: async (type: ItemKpiTypeEnum.HIT | ItemKpiTypeEnum.SESSION) =>
      axiosDelete(`/projects/${projectId}/kpis/rollings?type=${type}`),
  });
};

export const useKpiRollingQuery = (
  projectId: number,
  filter: AbstractFilterInterface & {
    type: ItemKpiTypeEnum.HIT | ItemKpiTypeEnum.SESSION;
    provinceId?: number;
    channelId?: number;
  },
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["kpiRolling", projectId, filter],
    queryFn: async () =>
      axiosGet<{ entities: KpiRollingInterface[]; count: number }, unknown>(
        `/projects/${projectId}/kpis/rollings`,
        filter,
      ),
  });
};

export const useCreateKpiRollingMutation = (projectId: number) => {
  const { axiosPost } = useApp();
  return useMutation({
    mutationKey: ["createKpiRolling", projectId],
    mutationFn: async (data: {
      provinceId: number;
      channelId: number;
      type: ItemKpiTypeEnum.HIT | ItemKpiTypeEnum.SESSION;
      targetName: string;
      targetDate: string;
      targetKpi: number;
      projectOutletId?: number;
    }) => axiosPost(`/projects/${projectId}/kpis/rollings`, data),
  });
};
