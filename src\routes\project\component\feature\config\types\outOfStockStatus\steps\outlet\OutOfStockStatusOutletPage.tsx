import TableActionCell from "@/components/TableActionCell.tsx";
import { FileSearchOutlined, PlusOutlined } from "@ant-design/icons";
import { FeatureNumericSheetInterface } from "@project/component/feature/config/types/numericSheet/interface.ts";
import { OosGroupInterface } from "@project/component/feature/config/types/outOfStockStatus/interface.ts";
import { useOosGroupsQuery } from "@project/component/feature/config/types/outOfStockStatus/steps/group/service.ts";
import OutOfStockStatusOutletViewModal from "@project/component/feature/config/types/outOfStockStatus/steps/outlet/OutOfStockStatusOutletViewModal.tsx";
import { Table } from "antd";
import { useCallback, useState } from "react";
import { useParams } from "react-router-dom";
import OutOfStockStatusOutletAddModal from "./OutOfStockStatusOutletAddModal";

const OutOfStockStatusOutletPage = () => {
  const projectId = parseInt(useParams().id ?? "0");
  const componentFeatureId = parseInt(useParams().componentFeatureId ?? "0");

  const [isAddOpen, setIsAddOpen] = useState(false);
  const [isViewOpen, setIsViewOpen] = useState(false);
  const [selectedOosGroup, setSelectedOosGroup] = useState<
    OosGroupInterface | undefined
  >(undefined);

  const oosGroupsQuery = useOosGroupsQuery(componentFeatureId, {
    take: 0,
  });

  const addOutlet = useCallback((record: FeatureNumericSheetInterface) => {
    setIsAddOpen(true);
    setSelectedOosGroup(record);
  }, []);

  const viewOutlet = useCallback((record: FeatureNumericSheetInterface) => {
    setSelectedOosGroup(record);
    setIsViewOpen(true);
  }, []);

  return (
    <>
      <Table
        dataSource={oosGroupsQuery.data?.entities}
        rowKey={"id"}
        columns={[
          {
            title: "Tên nhóm sản phẩm",
            dataIndex: "name",
            className: "min-w-[100px]",
          },
          {
            title: "Outlet đã phân bổ",
            dataIndex: "featureOosGroupOutletsCount",
            align: "right",
            className: "min-w-[100px]",
          },
          {
            render: (record) => {
              return (
                <TableActionCell
                  actions={[
                    {
                      key: "add",
                      action: addOutlet,
                    },
                    {
                      key: "view",
                      action: viewOutlet,
                    },
                  ]}
                  items={[
                    {
                      key: "add",
                      label: "Thêm outlet vào nhóm",
                      icon: <PlusOutlined />,
                    },
                    {
                      key: "view",
                      label: "Danh sách outlet trong nhóm",
                      icon: <FileSearchOutlined />,
                    },
                  ]}
                  record={record}
                />
              );
            },
          },
        ]}
        pagination={false}
      />

      {isAddOpen && selectedOosGroup && (
        <OutOfStockStatusOutletAddModal
          isOpen={true}
          oosGroup={selectedOosGroup}
          projectId={projectId}
          componentFeatureId={componentFeatureId}
          onCancelCb={() => {
            setIsAddOpen(false);
            setSelectedOosGroup(undefined);
          }}
          cb={() => {
            setIsAddOpen(false);
            setSelectedOosGroup(undefined);
            oosGroupsQuery.refetch();
          }}
        />
      )}

      {isViewOpen && selectedOosGroup && (
        <OutOfStockStatusOutletViewModal
          isOpen={true}
          projectId={projectId}
          componentFeatureId={componentFeatureId}
          oosGroup={selectedOosGroup}
          onCancelCb={() => {
            setIsViewOpen(false);
            setSelectedOosGroup(undefined);
          }}
          cb={() => {
            setIsViewOpen(false);
            setSelectedOosGroup(undefined);
            oosGroupsQuery.refetch();
          }}
        />
      )}
    </>
  );
};

export default OutOfStockStatusOutletPage;
