import { useApp } from "@/UseApp.tsx";
import { AbstractFilterInterface } from "@/common/interface.ts";
import { AppContextInterface } from "@/interface.ts";
import { useMutation, useQuery } from "@tanstack/react-query";
import { ApiAgencyResponseInterface } from "./interface";

export const getAgencies = async (
  axiosGet: AppContextInterface["axiosGet"],
  filter: unknown,
) => {
  return await axiosGet<ApiAgencyResponseInterface, unknown>(
    "/agencies",
    filter,
  );
};

export const useAgenciesQuery = (filter: object & AbstractFilterInterface) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["agencies", filter],
    queryFn: () => getAgencies(axiosGet, filter),
  });
};

export const useDeleteAgencyMutation = () => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deleteAgency"],
    mutationFn: (id: number) => axiosDelete(`/agencies/${id}`),
  });
};

export const useCreateAgencyMutation = () => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createAgency"],
    mutationFn: async (data: {
      name: string;
      email: string;
      phone: string;
      address: string;
    }) => {
      return await axiosPost("/agencies", data);
    },
  });
};

export const useUpdateAgencyMutation = () => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["updateAgency"],
    mutationFn: async (data: {
      id: number;
      name?: string;
      email?: string;
      phone?: string;
      address?: string;
      isActive?: boolean;
    }) => {
      return await axiosPatch(`/agencies/${data.id}`, data);
    },
  });
};
