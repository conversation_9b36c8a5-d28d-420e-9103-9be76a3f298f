import useMenuItems from "@/hooks/useMenuItems.tsx";
import { Menu } from "antd";
import { useLocation, useNavigate } from "react-router-dom";

interface LeftMenuProps {
  projectId: number;
}

const LeftMenu = ({ projectId }: Readonly<LeftMenuProps>) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { menuItems, selectedKeys } = useMenuItems(projectId);

  const setRouteActive = (value: string) => {
    navigate(value);
  };

  return (
    <Menu
      mode="inline"
      defaultSelectedKeys={[location.pathname]}
      onClick={({ key }) => setRouteActive(key)}
      items={menuItems}
      selectedKeys={selectedKeys}
      style={{
        paddingLeft: "10px",
        paddingRight: "10px",
      }}
    />
  );
};

export default LeftMenu;
