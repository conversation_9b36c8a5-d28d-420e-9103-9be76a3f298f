import type { InputNumberProps } from "antd";
import { InputNumber } from "antd";
import React from "react";

interface FormNumberInputProps
  extends Omit<InputNumberProps, "controls" | "formatter" | "parser"> {
  hasDot?: boolean;
  decimalPlaces?: number;
}

const FormNumberInput: React.FC<FormNumberInputProps> = ({
  hasDot = false,
  decimalPlaces = 2,
  ...props
}) => {
  const ALLOW_KEY = [
    "Backspace",
    "Delete",
    "ArrowLeft",
    "ArrowRight",
    "Control",
    "Tab",
  ];

  if (hasDot) {
    ALLOW_KEY.push(".");
  }

  return (
    <InputNumber
      {...props}
      controls={false}
      step={0}
      precision={hasDot ? decimalPlaces : 0}
      formatter={(value) => {
        if (value === null || value === undefined) return "";

        const parts = value.toString().split(".");
        const integerPart = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");

        if (!hasDot || parts.length === 1) return integerPart;

        const decimalPart = parts[1] || "";
        return `${integerPart}.${decimalPart}`;
      }}
      parser={(value) => {
        if (!value) return "";
        const parsed = value.replace(/,/g, "");
        if (!hasDot) return parseInt(parsed, 10);
        return parseFloat(parsed);
      }}
      onKeyDown={(event) => {
        const { key } = event;

        if (ALLOW_KEY.includes(key)) return;

        // Allow Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
        if (event.ctrlKey && ["a", "c", "v", "x"].includes(key.toLowerCase())) {
          return;
        }

        // Handle decimal input
        if (hasDot && key === ".") {
          const value = (event.target as HTMLInputElement).value;
          if (value.includes(".")) {
            event.preventDefault(); // Prevent multiple decimal points
          }
          return;
        }

        // Prevent input if not a number
        if (!/\d/.test(key)) {
          event.preventDefault();
        }
      }}
    />
  );
};

export default FormNumberInput;
