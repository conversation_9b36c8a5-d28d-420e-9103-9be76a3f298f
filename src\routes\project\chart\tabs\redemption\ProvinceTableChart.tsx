import _ from "lodash";
import { useEffect, useMemo } from "react";
import TableChart from "../../chart-types/TableChart";
import ChartContanier from "../../ChartContanier";
import { StatisticsTypeEnum } from "../../interface";
import { useProvinceStatisticsQuery } from "../../service";

interface ProvinceTableChartProps {
  projectId: number;
  type: StatisticsTypeEnum;
  filter: { provinceIds?: number[]; channelIds?: number[] };
  sort: "asc" | "desc" | undefined;
  title: string;
  activeKey: string;
}

const ProvinceTableChart = ({
  projectId,
  type,
  filter,
  sort,
  title,
  activeKey,
}: ProvinceTableChartProps) => {
  const provinceStatisticsQuery = useProvinceStatisticsQuery(
    projectId,
    type,
    filter,
  );

  const statistics = useMemo(() => {
    const data = _.map(
      _.groupBy(provinceStatisticsQuery.data ?? [], "provinceName"),
      (item, key) => ({
        label: key,
        items: item,
      }),
    );

    if (sort === "asc") {
      data.sort(
        (a, b) =>
          a.items.reduce((sum, item) => sum + item.totalValue, 0) -
          b.items.reduce((sum, item) => sum + item.totalValue, 0),
      );
    } else if (sort === "desc") {
      data.sort(
        (a, b) =>
          b.items.reduce((sum, item) => sum + item.totalValue, 0) -
          a.items.reduce((sum, item) => sum + item.totalValue, 0),
      );
    }

    return data;
  }, [provinceStatisticsQuery.data, sort]);

  useEffect(() => {
    if (activeKey === "REDEMPTION") {
      provinceStatisticsQuery.refetch();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeKey]);

  return (
    <ChartContanier>
      <TableChart
        data={statistics}
        sort={sort}
        title={title}
        subtitle={"Subtitle"}
        loading={
          provinceStatisticsQuery.isLoading ||
          provinceStatisticsQuery.isFetching
        }
        firstColumnTitle="Tỉnh"
      />
    </ChartContanier>
  );
};

export default ProvinceTableChart;
