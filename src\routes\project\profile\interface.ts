import { AbstractEntityInterface } from "@/common/interface.ts";
import { UserInterface } from "../../user/interface.ts";

export enum ProfileRequestStatus {
  PENDING = "PENDING",
  APPROVED = "APPROVED",
  REJECTED = "REJECTED",
}

export interface ProfileRequestInterface extends AbstractEntityInterface {
  status: ProfileRequestStatus;
  reason: string;
  reviewedAt: string;
  reviewedByUser: UserInterface;
  fullName: string;
  identityCardNumber: string;
  phoneNumber: string;
  email: string;
  user: UserInterface;
}
