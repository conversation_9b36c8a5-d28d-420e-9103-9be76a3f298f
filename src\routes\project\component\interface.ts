import { AbstractEntityInterface } from "@/common/interface";
import { ProjectBoothInterface } from "@/routes/project/configOutlet/interface.ts";
import { ComponentFeatureInterface } from "./feature/interface.ts";

export interface ProjectComponentBoothInterface
  extends AbstractEntityInterface {
  projectComponentId: number;
  projectBoothId: number;
  projectBooth: ProjectBoothInterface;
}

export interface ProjectComponentInterface extends AbstractEntityInterface {
  name: string;
  projectBooths: ProjectBoothInterface[];
  projectComponentBooths: ProjectComponentBoothInterface[];
  projectFeaturesCount: number;
  projectFeatures: ComponentFeatureInterface[];
}

export interface ApiProjectComponentResponseInterface {
  entities: ProjectComponentInterface[];
  count: number;
}

export enum ProjectComponentActionEnum {
  EDIT = "EDIT",
  VIEW = "VIEW",
  INACTIVE = "INACTIVE",
  ACTIVE = "ACTIVE",
  DELETE = "DELETE",
  PUBLICATION = "PUBLICATION",
}

export interface ProjectComponentPublicationInterface
  extends AbstractEntityInterface {
  projectId: number;
  code: string;
  name: string;
  projectComponentBooths: ProjectComponentBoothInterface[];
  projectFeaturesCount: number;
  isLatest: boolean;
  publishedAt: string;
}
