import { useCallback, useEffect, useMemo } from "react";
import <PERSON><PERSON><PERSON> from "../../chart-types/BarChart";
import ChartContanier from "../../ChartContanier";
import { ChartDataInterface, StatisticsTypeEnum } from "../../interface";
import { useChannelStatisticsQuery } from "../../service";

interface ChannelBarChartProps {
  projectId: number;
  type: StatisticsTypeEnum;
  filter?: { provinceIds?: number[]; channelIds?: number[] };
  sort: "asc" | "desc" | undefined;
  title: string;
  activeKey: string;
}

const ChannelBarChart = ({
  projectId,
  type,
  filter,
  sort,
  title,
  activeKey,
}: ChannelBarChartProps) => {
  const channelStatisticsQuery = useChannelStatisticsQuery(
    projectId,
    type,
    filter,
    true,
  );

  const toThreeItems = useCallback(
    (item: ChartDataInterface, labelKey: "channelName" | "provinceName") => {
      const { kpi, totalValue, todayValue, rollingTarget } = item;
      return [
        {
          label: item[labelKey],
          type: "KPI",
          value: kpi,
          KPI: kpi,
          UTD: totalValue,
          Today: todayValue,
        },
        {
          label: item[labelKey],
          type: "Rolling Target",
          value: rollingTarget,
          KPI: kpi,
          UTD: totalValue,
          Today: todayValue,
        },
        {
          label: item[labelKey],
          type: "UTD",
          value: totalValue,
          KPI: kpi,
          UTD: totalValue,
          Today: todayValue,
        },
      ];
    },
    [],
  );

  const statistics = useMemo(() => {
    const data = channelStatisticsQuery.data ?? [];
    if (sort === undefined)
      return data.flatMap((item) => toThreeItems(item, "channelName"));

    const sortedData = [...data];
    if (sort === "asc") {
      sortedData.sort((a, b) => a.totalValue - b.totalValue);
    } else {
      sortedData.sort((a, b) => b.totalValue - a.totalValue);
    }

    return sortedData.flatMap((item) => toThreeItems(item, "channelName"));
  }, [channelStatisticsQuery.data, sort, toThreeItems]);

  useEffect(() => {
    if (activeKey === "SAMPLING/ACTIVITIES") {
      channelStatisticsQuery.refetch();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeKey]);

  return (
    <ChartContanier>
      <BarChart
        title={title}
        subtitle="Subtitle"
        data={statistics}
        xField="label"
        yField="value"
        loading={
          channelStatisticsQuery.isLoading || channelStatisticsQuery.isFetching
        }
        colorField="type"
      />
    </ChartContanier>
  );
};

export default ChannelBarChart;
