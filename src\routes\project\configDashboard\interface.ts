import { AbstractEntityInterface } from "@/common/interface";

export enum ProjectDashboardTypeEnum {
  ATTENDANCE_DAILY = "attendanceDaily",
  QUANTITY_DAILY = "quantityDaily",
  URGENCY = "urgency",
  NUMERIC = "numeric",
  OOS = "oos",
  QUANTITY_AVERAGE = "quantityAverage",
  NUMERIC_SHEET = "numericSheet",
  NUMERIC_SHEET_WEEKLY_AVERAGE = "numericSheetWeeklyAverage",
  INVENTORY = "inventory",
  OOS_STORE_LEVEL = "oosStoreLevel",
  NUMERIC_SHEET_EACH_ATTRIBUTE = "numericSheetEachAttribute",
}

export interface ProjectDashboardInterface extends AbstractEntityInterface {
  name: string;
  description: string;
  type: ProjectDashboardTypeEnum;
  ordinal: number;
}
