import { AbstractEntityInterface } from "@/common/interface";
import { RecordSamplingValueInterface } from "@/routes/project/report/types/sampling/interface";
import { UserInterface } from "@/routes/user/interface";

interface ToolSamplingInterface extends AbstractEntityInterface {
  createdByUser: UserInterface;
}

export interface EditSamplingInterface extends AbstractEntityInterface {
  recordSamplingValues: RecordSamplingValueInterface[];
  dataUuid: string;
  dataTimestamp: string;
  createdByUser: UserInterface;
  toolSampling?: ToolSamplingInterface;
}
