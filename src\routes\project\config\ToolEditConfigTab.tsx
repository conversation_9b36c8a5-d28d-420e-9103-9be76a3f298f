import { DATE_FORMAT } from "@/common/constant";
import CustomModal from "@/components/CustomModal";
import InputNumberInCell from "@/components/InputNumberInCell";
import { useApp } from "@/UseApp";
import {
  DeleteOutlined,
  EllipsisOutlined,
  LoadingOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import {
  Button,
  Col,
  DatePicker,
  Dropdown,
  Form,
  Row,
  Space,
  Switch,
  Table,
} from "antd";
import dayjs from "dayjs";
import { useCallback, useMemo, useState } from "react";
import { FeatureTypeEnum } from "../component/feature/interface";
import { ToolSettingSettingTypeEnum } from "./interface";
import {
  useCreateProjectToolAgencyMutation,
  useCreateToolPermittedDateMutation,
  useCreateToolSettingMutation,
  useDeleteProjectToolAgencyMutation,
  useDeleteToolPermittedDateMutation,
  useDeleteToolSettingMutation,
  useProjectToolAgencyDetailQuery,
  useToolPermittedDatesQuery,
  useToolSettingsQuery,
  useUpdateProjectToolAgencyMutation,
} from "./service";

interface ToolEditConfigTabProps {
  projectId: number;
  projectAgencyId: number;
}

const ToolEditConfigTab = ({
  projectId,
  projectAgencyId,
}: ToolEditConfigTabProps) => {
  const { openDeleteModal, showNotification } = useApp();

  const [open, setOpen] = useState(false);
  const [form] = Form.useForm();

  const projectToolAgencyDetailQuery = useProjectToolAgencyDetailQuery(
    projectId,
    projectAgencyId,
  );
  const toolPermittedDatesQuery = useToolPermittedDatesQuery(
    projectToolAgencyDetailQuery.data?.id ?? 0,
    {
      take: 50,
      skip: 0,
    },
    !!projectToolAgencyDetailQuery.data?.id,
  );
  const toolSettingsQuery = useToolSettingsQuery(
    projectToolAgencyDetailQuery.data?.id ?? 0,
  );

  const createProjectToolAgencyMutation =
    useCreateProjectToolAgencyMutation(projectId);
  const deleteProjectToolAgencyMutation =
    useDeleteProjectToolAgencyMutation(projectId);
  const updateProjectToolAgencyMutation =
    useUpdateProjectToolAgencyMutation(projectId);
  const createToolPermittedDateMutation = useCreateToolPermittedDateMutation(
    projectToolAgencyDetailQuery.data?.id,
  );
  const deleteToolPermittedDateMutation = useDeleteToolPermittedDateMutation(
    projectToolAgencyDetailQuery.data?.id,
  );
  const createToolSettingMutation = useCreateToolSettingMutation(
    projectToolAgencyDetailQuery.data?.id,
  );
  const deleteToolSettingMutation = useDeleteToolSettingMutation(
    projectToolAgencyDetailQuery.data?.id,
  );

  const onSwitchChange = useCallback(
    async (value: boolean) => {
      if (value) {
        await createProjectToolAgencyMutation.mutateAsync({
          allowedDays: 0,
          projectAgencyId,
        });
      } else {
        await deleteProjectToolAgencyMutation.mutateAsync(projectAgencyId);
      }

      projectToolAgencyDetailQuery.refetch();
    },
    [
      createProjectToolAgencyMutation,
      deleteProjectToolAgencyMutation,
      projectAgencyId,
      projectToolAgencyDetailQuery,
    ],
  );

  const inputNumberSubmit = useCallback(
    async (value: number | null) => {
      await updateProjectToolAgencyMutation.mutateAsync({
        allowedDays: value ?? 0,
        projectAgencyId,
      });

      await projectToolAgencyDetailQuery.refetch();
    },
    [
      projectAgencyId,
      projectToolAgencyDetailQuery,
      updateProjectToolAgencyMutation,
    ],
  );

  const isInputNumberLoading = useMemo(
    () =>
      updateProjectToolAgencyMutation.isPending ||
      projectToolAgencyDetailQuery.isLoading ||
      projectToolAgencyDetailQuery.isRefetching ||
      projectToolAgencyDetailQuery.isPending,
    [
      projectToolAgencyDetailQuery.isLoading,
      projectToolAgencyDetailQuery.isPending,
      projectToolAgencyDetailQuery.isRefetching,
      updateProjectToolAgencyMutation.isPending,
    ],
  );

  const confirmAddPermittedDate = useCallback(async () => {
    await form.validateFields();

    const [startDate, endDate] = form.getFieldValue("rangeDate");

    await createToolPermittedDateMutation.mutateAsync({
      startDate: dayjs(startDate).startOf("d").toISOString(),
      endDate: dayjs(endDate).startOf("d").toISOString(),
    });

    form.resetFields();

    setOpen(false);
    toolPermittedDatesQuery.refetch();
  }, [createToolPermittedDateMutation, form, toolPermittedDatesQuery]);

  const createToolSetting = useCallback(
    async (
      featureType: FeatureTypeEnum,
      settingType: ToolSettingSettingTypeEnum,
    ) => {
      await createToolSettingMutation.mutateAsync({
        featureType,
        settingType,
        enabled: true,
      });

      toolSettingsQuery.refetch();
      showNotification({
        type: "success",
        message: "Mở cấu hình thành công",
      });
    },
    [createToolSettingMutation, showNotification, toolSettingsQuery],
  );

  const deleteToolSetting = useCallback(
    async (id: number) => {
      await deleteToolSettingMutation.mutateAsync(id);

      toolSettingsQuery.refetch();
      showNotification({
        type: "success",
        message: "Tắt cấu hình thành công",
      });
    },
    [deleteToolSettingMutation, showNotification, toolSettingsQuery],
  );

  const findSettingConfig = useCallback(
    (featureType: FeatureTypeEnum, settingType: ToolSettingSettingTypeEnum) => {
      return toolSettingsQuery.data?.find(
        (toolSetting) =>
          toolSetting.featureType === featureType &&
          toolSetting.settingType === settingType,
      );
    },
    [toolSettingsQuery.data],
  );

  return (
    <>
      <p>
        Tool chỉnh dữ liệu
        <Switch
          className="ml-5"
          value={!!projectToolAgencyDetailQuery.data?.id}
          onChange={onSwitchChange}
          loading={
            createProjectToolAgencyMutation.isPending ||
            deleteProjectToolAgencyMutation.isPending
          }
        />
      </p>

      {projectToolAgencyDetailQuery.data && (
        <>
          <Form layout="vertical" className="mt-10">
            <Form.Item
              label={
                <span className="font-semibold">
                  Thời gian cho phép sử dụng tool
                </span>
              }
              required
            >
              <Space>
                <div>
                  {isInputNumberLoading && <LoadingOutlined />}
                  {!isInputNumberLoading && (
                    <InputNumberInCell
                      initValue={projectToolAgencyDetailQuery.data.allowedDays}
                      onSubmit={inputNumberSubmit}
                    />
                  )}
                </div>
                <div>
                  Ngày gần nhất{" "}
                  <span className="text-gray-400">(Tính từ ngày hiện tại)</span>
                </div>
              </Space>
            </Form.Item>
          </Form>
          <Table
            className="mt-10"
            rowKey={"id"}
            columns={[
              {
                title: "Ngày ngoại lệ cho phép dùng tool",
                render: (_value, record) => {
                  const startDate = dayjs(record.startDate).format(DATE_FORMAT);
                  const endDate = dayjs(record.endDate).format(DATE_FORMAT);
                  return (
                    <>
                      {startDate === endDate ? (
                        startDate
                      ) : (
                        <>
                          {startDate} - {endDate}
                        </>
                      )}
                    </>
                  );
                },
              },
              {
                title: "",
                className: "min-w-[200px]",
                align: "right",
                render: (_, record) => {
                  const startDate = dayjs(record.startDate).format(DATE_FORMAT);
                  const endDate = dayjs(record.endDate).format(DATE_FORMAT);

                  return (
                    <Dropdown
                      menu={{
                        items: [
                          {
                            key: "delete",
                            icon: <DeleteOutlined />,
                            label: "Xóa",
                            onClick: () => {
                              openDeleteModal({
                                title: "Ngày ngoại lệ cho phép dùng tool",
                                content: (
                                  <div>
                                    <span className="font-semibold">
                                      Ngày ngoại lệ cho phép dùng tool:
                                    </span>{" "}
                                    {startDate === endDate
                                      ? startDate
                                      : `${startDate} - ${endDate}`}
                                  </div>
                                ),
                                onDelete: async () => {
                                  await deleteToolPermittedDateMutation.mutateAsync(
                                    record.id,
                                  );

                                  showNotification({
                                    type: "success",
                                    message: `Xóa ngày ngoại lệ cho phép dùng tool thành công`,
                                  });

                                  toolPermittedDatesQuery.refetch();
                                },
                                deleteText: "Xóa",
                                onCancel: () => {},
                              });
                            },
                          },
                        ],
                      }}
                    >
                      <Button type="link">
                        <EllipsisOutlined />
                      </Button>
                    </Dropdown>
                  );
                },
              },
            ]}
            dataSource={toolPermittedDatesQuery.data?.entities ?? []}
            loading={
              toolPermittedDatesQuery.isFetching ||
              toolPermittedDatesQuery.isRefetching
            }
            pagination={false}
          />
          <p
            onClick={() => setOpen(true)}
            className={
              "text-[#1D8EE6] cursor-pointer p-0 m-0 mt-4 font-semibold"
            }
          >
            <PlusOutlined color="#1D8EE6" /> Thêm ngày ngoại lệ
          </p>

          <p className="font-semibold mt-8">Chỉnh sửa sampling đã phát</p>

          {!toolSettingsQuery.isLoading && (
            <>
              <Row justify={"space-between"}>
                <Col md={8} xs={20}>
                  Không được chỉnh khi data rỗng
                </Col>
                <Col md={16} xs={4}>
                  <Switch
                    onChange={(value) =>
                      value
                        ? createToolSetting(
                            FeatureTypeEnum.Sampling,
                            ToolSettingSettingTypeEnum.READONLY_IF_NULL,
                          )
                        : deleteToolSetting(
                            findSettingConfig(
                              FeatureTypeEnum.Sampling,
                              ToolSettingSettingTypeEnum.READONLY_IF_NULL,
                            )?.id ?? 0,
                          )
                    }
                    loading={
                      createToolSettingMutation.isPending ||
                      deleteToolSettingMutation.isPending
                    }
                    defaultChecked={
                      findSettingConfig(
                        FeatureTypeEnum.Sampling,
                        ToolSettingSettingTypeEnum.READONLY_IF_NULL,
                      )?.enabled ?? false
                    }
                  />
                </Col>
              </Row>
              <Row className="mt-2" justify={"space-between"}>
                <Col md={8} xs={20}>
                  Không được chỉnh khi data có giá trị = 0
                </Col>
                <Col md={16} xs={4}>
                  <Switch
                    onChange={(value) =>
                      value
                        ? createToolSetting(
                            FeatureTypeEnum.Sampling,
                            ToolSettingSettingTypeEnum.READONLY_IF_ZERO,
                          )
                        : deleteToolSetting(
                            findSettingConfig(
                              FeatureTypeEnum.Sampling,
                              ToolSettingSettingTypeEnum.READONLY_IF_ZERO,
                            )?.id ?? 0,
                          )
                    }
                    loading={
                      createToolSettingMutation.isPending ||
                      deleteToolSettingMutation.isPending
                    }
                    defaultChecked={
                      findSettingConfig(
                        FeatureTypeEnum.Sampling,
                        ToolSettingSettingTypeEnum.READONLY_IF_ZERO,
                      )?.enabled ?? false
                    }
                  />
                </Col>
              </Row>

              <p className="font-semibold mt-5">Data ghi nhận loại số lượng</p>
              <Row justify={"space-between"}>
                <Col md={8} xs={20}>
                  Không được chỉnh khi data rỗng
                </Col>
                <Col md={16} xs={4}>
                  <Switch
                    onChange={(value) =>
                      value
                        ? createToolSetting(
                            FeatureTypeEnum.MultipleEntitiesQuantityCapturing,
                            ToolSettingSettingTypeEnum.READONLY_IF_NULL,
                          )
                        : deleteToolSetting(
                            findSettingConfig(
                              FeatureTypeEnum.MultipleEntitiesQuantityCapturing,
                              ToolSettingSettingTypeEnum.READONLY_IF_NULL,
                            )?.id ?? 0,
                          )
                    }
                    loading={
                      createToolSettingMutation.isPending ||
                      deleteToolSettingMutation.isPending
                    }
                    defaultChecked={
                      findSettingConfig(
                        FeatureTypeEnum.MultipleEntitiesQuantityCapturing,
                        ToolSettingSettingTypeEnum.READONLY_IF_NULL,
                      )?.enabled ?? false
                    }
                  />
                </Col>
              </Row>
              <Row className="mt-2" justify={"space-between"}>
                <Col md={8} xs={20}>
                  Không được chỉnh khi data có giá trị = 0
                </Col>
                <Col md={16} xs={4}>
                  <Switch
                    onChange={(value) =>
                      value
                        ? createToolSetting(
                            FeatureTypeEnum.MultipleEntitiesQuantityCapturing,
                            ToolSettingSettingTypeEnum.READONLY_IF_ZERO,
                          )
                        : deleteToolSetting(
                            findSettingConfig(
                              FeatureTypeEnum.MultipleEntitiesQuantityCapturing,
                              ToolSettingSettingTypeEnum.READONLY_IF_ZERO,
                            )?.id ?? 0,
                          )
                    }
                    loading={
                      createToolSettingMutation.isPending ||
                      deleteToolSettingMutation.isPending
                    }
                    defaultChecked={
                      findSettingConfig(
                        FeatureTypeEnum.MultipleEntitiesQuantityCapturing,
                        ToolSettingSettingTypeEnum.READONLY_IF_ZERO,
                      )?.enabled ?? false
                    }
                  />
                </Col>
              </Row>
            </>
          )}
        </>
      )}

      <CustomModal
        title={"Thêm ngày ngoại lệ dùng tool"}
        isOpen={open}
        content={
          <Form layout="vertical" form={form}>
            <Form.Item
              label="Chọn ngày"
              rules={[{ required: true }]}
              name={"rangeDate"}
            >
              <DatePicker.RangePicker className="w-full" />
            </Form.Item>
          </Form>
        }
        confirmText="Thêm mới"
        onCancel={() => setOpen(false)}
        onConfirm={confirmAddPermittedDate}
        loading={createToolPermittedDateMutation.isPending}
      />
    </>
  );
};

export default ToolEditConfigTab;
