import { DEFAULT_CURRENT_PAGE, DEFAULT_PAGE_SIZE } from "@/common/constant";
import { useApp } from "@/UseApp";
import { useEffect, useMemo, useState } from "react";
import { useProjectReportOutletContext } from "../../../UseProjectReportOutletContext.tsx";
import { useReportOrdersQuery } from "../service";
import { useReportFilters } from "./useReportFilters";
import { useReportExport } from "./useReportExport";

/**
 * Main custom hook for Customer Information Capturing Report
 * Manages all the core logic and state for the report page
 */
export const useReportCustomerInformationCapturing = () => {
  const {
    componentFeatureQuery,
    projectId,
    componentFeatureId,
    advancedFilterValues,
    project,
  } = useProjectReportOutletContext();

  const { setLoading } = useApp();

  // Pagination state
  const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);

  // Filter management
  const { filterForm, filter, onFilterFormFinish } = useReportFilters(
    advancedFilterValues,
    () => {
      // Refetch data when filter changes but values are the same
      reportOrdersQuery.refetch();
    }
  );

  // Data fetching
  const reportOrdersQuery = useReportOrdersQuery(
    projectId,
    componentFeatureId,
    {
      ...filter,
      take: pageSize,
      skip: (currentPage - 1) * pageSize,
    },
  );

  // Export functionality
  const exportHooks = useReportExport({
    projectId,
    componentFeatureId,
    project,
    filter,
    paginationTotal: reportOrdersQuery.data?.count ?? 0,
    componentFeatureName: componentFeatureQuery.data?.name,
  });

  // Pagination configuration
  const pagination = useMemo(() => {
    return {
      current: currentPage,
      total: reportOrdersQuery.data?.count,
      pageSize: pageSize,
      onChange: (page: number) => {
        setCurrentPage(page);
      },
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: (_current: number, size: number) => {
        setPageSize(size);
        setCurrentPage(DEFAULT_CURRENT_PAGE);
      },
    };
  }, [currentPage, pageSize, reportOrdersQuery.data?.count]);

  // Loading state management
  useEffect(() => {
    setLoading(
      reportOrdersQuery.isLoading ||
        reportOrdersQuery.isFetching ||
        reportOrdersQuery.isPending,
    );
  }, [
    reportOrdersQuery.isFetching,
    reportOrdersQuery.isLoading,
    reportOrdersQuery.isPending,
    setLoading,
  ]);

  // Reset current page when filter changes
  useEffect(() => {
    setCurrentPage(DEFAULT_CURRENT_PAGE);
  }, [filter]);

  return {
    // Component data
    componentFeatureQuery,
    
    // Table data
    reportOrdersQuery,
    pagination,
    
    // Filter management
    filterForm,
    onFilterFormFinish,
    
    // Export functionality
    ...exportHooks,
    
    // Loading states
    isTableLoading: reportOrdersQuery.isLoading || reportOrdersQuery.isFetching,
    isFilterLoading: reportOrdersQuery.isFetching || reportOrdersQuery.isLoading || exportHooks.isExport,
  };
};
