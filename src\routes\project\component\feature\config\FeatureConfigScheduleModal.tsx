import CustomModal from "@/components/CustomModal";
import { useApp } from "@/UseApp";
import {
  DeleteOutlined,
  FolderOpenTwoTone,
  PlusOutlined,
} from "@ant-design/icons";
import { Button, Form, Space, TimePicker } from "antd";
import dayjs, { Dayjs } from "dayjs";
import { useCallback, useEffect } from "react";
import {
  useComponentFeatureSchedulesQuery,
  useUpdateComponentFeatureSchedulesMutation,
} from "../service";

interface FeatureConfigScheduleModalProps {
  componentFeatureId: number;
  projectId: number;
  projectComponentId: number;
  onCancelCb: () => void;
}

const FeatureConfigScheduleModal = ({
  componentFeatureId,
  projectId,
  projectComponentId,
  onCancelCb,
}: FeatureConfigScheduleModalProps) => {
  const { showNotification } = useApp();

  const [form] = Form.useForm();

  const componentFeatureSchedulesQuery = useComponentFeatureSchedulesQuery(
    projectId,
    projectComponentId,
    componentFeatureId,
  );
  const updateComponentFeatureSchedulesMutation =
    useUpdateComponentFeatureSchedulesMutation(
      projectId,
      projectComponentId,
      componentFeatureId,
    );

  const schedules = Form.useWatch("schedules", form);

  const content = (
    <>
      {(!schedules || schedules?.length === 0) && (
        <div className="p-10 text-center bg-input-disabled mt-5 rounded mb-5">
          <FolderOpenTwoTone className="text-5xl" twoToneColor={"#8C8C8D"} />
          <p> Chưa ràng buộc khung giờ nhập liệu</p>
        </div>
      )}

      <Form layout="vertical" form={form} className="mt-5">
        <Form.List name="schedules">
          {(fields, { add, remove }) => (
            <>
              {fields.map(({ key, name, ...restField }) => (
                <Space
                  key={key}
                  style={{ display: "flex", marginBottom: 8 }}
                  size={"large"}
                >
                  <Form.Item
                    {...restField}
                    name={[name, "startTime"]}
                    label={"Bắt đầu"}
                    rules={[{ required: true }]}
                  >
                    <TimePicker
                      format={"HH:mm"}
                      autoComplete="off"
                      needConfirm={false}
                    />
                  </Form.Item>
                  <Form.Item
                    {...restField}
                    name={[name, "endTime"]}
                    label={"Kết thúc"}
                    rules={[
                      { required: true },
                      ({ getFieldValue }) => ({
                        validator(_, value) {
                          if (
                            !value ||
                            getFieldValue(["schedules", name, "startTime"]) <
                              value
                          ) {
                            return Promise.resolve();
                          }
                          return Promise.reject(
                            new Error("Kết thúc phải lớn hơn bắt đầu"),
                          );
                        },
                      }),
                    ]}
                  >
                    <TimePicker
                      format={"HH:mm"}
                      autoComplete="off"
                      needConfirm={false}
                    />
                  </Form.Item>

                  <Form.Item label={" "}>
                    <DeleteOutlined onClick={() => remove(name)} />
                  </Form.Item>
                </Space>
              ))}
              <Button
                type="link"
                onClick={() => add()}
                icon={<PlusOutlined />}
                className="text-blue"
              >
                Thêm khung giờ nhập liệu
              </Button>
            </>
          )}
        </Form.List>
      </Form>
    </>
  );

  const confirm = useCallback(async () => {
    await form.validateFields();

    await updateComponentFeatureSchedulesMutation.mutateAsync(
      schedules.map(
        ({ startTime, endTime }: { startTime: Dayjs; endTime: Dayjs }) => ({
          startTime: startTime.format("HH:mm"),
          endTime: endTime.format("HH:mm"),
        }),
      ),
    );

    showNotification({
      type: "success",
      message: "Cập nhật thời gian nhập liệu thành công",
    });

    onCancelCb();
  }, [
    form,
    onCancelCb,
    schedules,
    showNotification,
    updateComponentFeatureSchedulesMutation,
  ]);

  useEffect(() => {
    form.setFieldsValue({
      schedules: componentFeatureSchedulesQuery?.data?.map((schedule) => ({
        startTime: dayjs(schedule.startTime, "HH:mm"),
        endTime: dayjs(schedule.endTime, "HH:mm"),
      })),
    });
  }, [form, componentFeatureSchedulesQuery?.data]);

  return (
    <>
      <CustomModal
        title={"Thời gian nhập liệu"}
        isOpen={true}
        content={content}
        width={600}
        onCancel={onCancelCb}
        onConfirm={confirm}
        confirmText="Lưu thông tin"
      />
    </>
  );
};

export default FeatureConfigScheduleModal;
