import { Col, Row } from "antd";
import _ from "lodash";
import { useMemo } from "react";
import Chart<PERSON>ontanier from "../../ChartContanier";
import TableChart from "../../chart-types/TableChart";
import { ChartTabProps, StatisticsTypeEnumToLabel } from "../../interface";
import {
  useChannelStatisticsQuery,
  useProvinceStatisticsQuery,
} from "../../service";

const TabTwoChartTable = ({
  projectId,
  filter,
  sort,
  type,
  activeKey,
}: ChartTabProps) => {
  const provinceStatisticsQuery = useProvinceStatisticsQuery(
    projectId,
    type,
    filter,
    activeKey === type,
  );
  const channelStatisticsQuery = useChannelStatisticsQuery(
    projectId,
    type,
    filter,
    activeKey === type,
  );

  const provinceStatistics = useMemo(() => {
    const data = _.map(
      _.groupBy(provinceStatisticsQuery.data ?? [], "provinceName"),
      (item, key) => ({
        label: key,
        items: item,
      }),
    );

    if (sort === "asc") {
      data.sort(
        (a, b) =>
          a.items.reduce((sum, item) => sum + item.totalValue, 0) -
          b.items.reduce((sum, item) => sum + item.totalValue, 0),
      );
    } else if (sort === "desc") {
      data.sort(
        (a, b) =>
          b.items.reduce((sum, item) => sum + item.totalValue, 0) -
          a.items.reduce((sum, item) => sum + item.totalValue, 0),
      );
    }

    return data;
  }, [provinceStatisticsQuery.data, sort]);

  const channelStatistics = useMemo(() => {
    const data = _.map(
      _.groupBy(channelStatisticsQuery.data ?? [], "channelName"),
      (item, key) => ({
        label: key,
        items: item,
      }),
    );

    if (sort === "asc") {
      data.sort(
        (a, b) =>
          a.items.reduce((sum, item) => sum + item.totalValue, 0) -
          b.items.reduce((sum, item) => sum + item.totalValue, 0),
      );
    } else if (sort === "desc") {
      data.sort(
        (a, b) =>
          b.items.reduce((sum, item) => sum + item.totalValue, 0) -
          a.items.reduce((sum, item) => sum + item.totalValue, 0),
      );
    }

    return data;
  }, [channelStatisticsQuery.data, sort]);

  return (
    <Row gutter={16} className="mt-0 pt-0 mb-10">
      <Col md={12}>
        <ChartContanier>
          <TableChart
            data={provinceStatistics}
            sort={sort}
            title={`${StatisticsTypeEnumToLabel[type]} theo tỉnh`}
            subtitle={"Subtitle"}
            loading={
              provinceStatisticsQuery.isLoading ||
              provinceStatisticsQuery.isFetching
            }
            firstColumnTitle="Tỉnh"
          />
        </ChartContanier>
      </Col>

      <Col md={12}>
        <ChartContanier>
          <TableChart
            data={channelStatistics}
            sort={sort}
            title={`${StatisticsTypeEnumToLabel[type]} theo kênh`}
            subtitle={"Subtitle"}
            loading={
              channelStatisticsQuery.isLoading ||
              channelStatisticsQuery.isFetching
            }
            firstColumnTitle="Kênh"
          />
        </ChartContanier>
      </Col>
    </Row>
  );
};

export default TabTwoChartTable;
