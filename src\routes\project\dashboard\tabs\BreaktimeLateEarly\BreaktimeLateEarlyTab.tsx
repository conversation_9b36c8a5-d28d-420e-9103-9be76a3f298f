import ChartContanier from "@/routes/project/chart/ChartContanier";
import { <PERSON>umn, Pie } from "@ant-design/charts";
import { Col, Row } from "antd";
import { useCallback } from "react";
import DashboardFilterZone from "../../DashboardFilterZone";

const BreaktimeLateEarlyTab = () => {
  const handleApply = useCallback(() => {}, []);

  const data = [
    { type: "Đi vệ sinh", value: 28.22 },
    { type: "Vào kho đếm hàng", value: 23.31 },
    { type: "Hỗ trợ khách hàng", value: 17.18 },
    { type: "Đi ăn", value: 15.95 },
    { type: "Khác", value: 15.34 },
  ];

  const config = {
    appendPadding: 10,
    data,
    angleField: "value",
    colorField: "type",
    radius: 0.8,
    interactions: [
      {
        type: "element-active",
      },
    ],
    color: ["#7986CB", "#FF8A65", "#4DD0E1", "#FFB74D", "#4FC3F7"],
    label: {
      text: (d: { type: string; value: number }) => `${d.type}\n ${d.value}`,
      position: "spider",
    },
    legend: false,
    title: {
      title: "Breaktime Overall",
      subtitle: "Tỷ lệ theo lý do (phút)",
    },
    tooltip: {
      field: "value",
      title: (d: { type: string }) => d.type,
      value: (d: { value: number }) => d.value,
    },
  };

  return (
    <>
      <DashboardFilterZone
        handleApply={handleApply}
        fields={["region", "province", "chain", "outlet", "date"]}
      />

      <Row gutter={[16, { xs: 16, sm: 16 }]}>
        <Col md={9} xs={24}>
          <ChartContanier>
            <Pie {...config} />
          </ChartContanier>
        </Col>

        <Col md={15} xs={24}>
          <ChartContanier>
            <Column
              title={{
                title: "Top Staff Frequent Breaktime",
                subtitle: "Thời lượng rời vị trí (phút)",
              }}
              data={[
                { name: "Nguyễn Thị Linh", time: 72 },
                { name: "Trần Văn An", time: 50 },
                { name: "Lê Thị Hồng", time: 44 },
                { name: "Phạm Minh Quân", time: 40 },
                { name: "Hoàng Thị Hoa", time: 34 },
                { name: "Vũ Văn Nam", time: 21 },
                { name: "Đỗ Thị Mai", time: 19 },
                { name: "Ngô Văn Tùng", time: 15 },
                { name: "Bùi Thị Lan", time: 10 },
                { name: "Nguyễn Văn Dũng", time: 3 },
              ]}
              xField={"name"}
              yField={"time"}
              style={{
                maxWidth: 50,
                fill: "#FD5401",
              }}
              label={{
                textBaseline: "bottom",
              }}
              axis={{
                x: {
                  labelSpacing: 4,
                  style: {
                    labelTransform: "rotate(-45)",
                  },
                },
              }}
            />
          </ChartContanier>
        </Col>
      </Row>
    </>
  );
};

export default BreaktimeLateEarlyTab;
