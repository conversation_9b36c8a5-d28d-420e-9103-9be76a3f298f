import { formatNumber } from "@/common/helper";
import { Col, Row } from "antd";

interface StatisticCellProps {
  topLabel: string;
  topValue?: number;
  bottomLabel: string;
  bottomValue?: number;
  className?: string;
}

const StatisticCell = ({
  topLabel,
  topValue,
  bottomLabel,
  bottomValue,
  className,
}: StatisticCellProps) => {
  return (
    <div className={`w-[386px] ${className}`}>
      <Row className="w-full border-t border-x border-b-0 border-solid border-[#DDE1EA] rounded-t-lg">
        <Col
          className="font-medium bg-[#F0F8FF] py-2 pl-[13px] border-r border-y-0 border-l-0 border-solid border-[#DDE1EA]"
          md={12}
        >
          {topLabel}
        </Col>
        <Col className="text-right pr-[13px] py-2" md={12}>
          {topValue !== undefined &&
            topValue !== null &&
            formatNumber(topValue)}
          {(topValue === undefined || topValue === null) && "_"}
        </Col>
      </Row>
      <Row className="w-full border border-solid border-[#DDE1EA] rounded-b-lg">
        <Col
          className="font-medium bg-[#F0F8FF] py-2 pl-[13px] border-r border-y-0 border-l-0 border-solid border-[#DDE1EA]"
          md={12}
        >
          {bottomLabel}
        </Col>
        <Col className="text-right pr-[13px] py-2" md={12}>
          {bottomValue !== undefined &&
            bottomValue !== null &&
            formatNumber(bottomValue)}
          {(bottomValue === undefined || bottomValue === null) && "_"}
        </Col>
      </Row>
    </div>
  );
};

export default StatisticCell;
