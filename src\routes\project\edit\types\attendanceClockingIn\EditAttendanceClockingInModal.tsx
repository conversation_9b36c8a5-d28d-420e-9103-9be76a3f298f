import { DATE_FORMAT } from "@/common/constant";
import { useUploadImageMutation } from "@/common/upload-image.helper";
import CustomModal from "@/components/CustomModal";
import FormImageUploadComponent from "@/components/formImageUploadComponent/FormImageUploadComponent";
import { useFormPhotosStore } from "@/components/formImageUploadComponent/state";
import { ComponentFeatureInterface } from "@/routes/project/component/feature/interface";
import {
  Col,
  Form,
  Input,
  Modal,
  Row,
  Skeleton,
  TimePicker,
  UploadFile,
} from "antd";
import { TimePickerProps } from "antd/lib";
import dayjs from "dayjs";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import { useAttendanceQuery, useUpdateAttendanceMutation } from "../../service";

interface EditAttendanceClockingInModalProps {
  setOpen: (isOpen: boolean) => void;
  attendanceId?: number | null;
  cb: () => void;
  componentFeature?: ComponentFeatureInterface;
  isAdd?: boolean;
}
const EditAttendanceClockingInModal = ({
  setOpen,
  attendanceId,
  cb,
  componentFeature,
  isAdd,
}: EditAttendanceClockingInModalProps) => {
  const projectId = parseInt(useParams().id ?? "0");

  const [form] = Form.useForm();
  const [timeIn, setTimeIn] = useState<string>("");
  const [timeOut, setTimeOut] = useState<string>("");
  const { formPhotos: photos, setFormPhotos: setPhotos } = useFormPhotosStore();
  const [loading, setLoading] = useState(false);

  const attendanceQuery = useAttendanceQuery(projectId, attendanceId ?? 0);

  const attendance = useMemo(() => attendanceQuery.data, [attendanceQuery]);

  const updateAttendanceMutation = useUpdateAttendanceMutation(
    projectId,
    attendanceId ?? 0,
  );
  const uploadImageMutation = useUploadImageMutation();

  const onChangeTimeIn: TimePickerProps["onChange"] = (_, timeString) => {
    setTimeIn(timeString as string);
  };

  const onChangeTimeOut: TimePickerProps["onChange"] = (_, timeString) => {
    setTimeOut(timeString as string);
  };

  const content = useMemo(
    () => (
      <Form layout="vertical" form={form}>
        {attendanceQuery.isLoading && <Skeleton loading active />}

        {attendanceQuery.isSuccess && (
          <>
            <h3 className="text-primary">Chấm công vào</h3>
            <Form.Item label="Thời gian vào" name={"timeIn"} rules={[]}>
              <Row gutter={0}>
                <Col className="bg-[#F5F5F5] flex items-center justify-center rounded border border-solid border-[#DDE1EA] px-2">
                  {dayjs(attendance?.in.deviceTime).format(DATE_FORMAT)}
                </Col>
                <Col className="ml-3">
                  <TimePicker
                    onChange={onChangeTimeIn}
                    defaultValue={dayjs(
                      dayjs(attendance?.in.deviceTime).format("HH:mm:ss"),
                      "HH:mm:ss",
                    )}
                    allowClear={false}
                  />
                </Col>
              </Row>
            </Form.Item>
            <Form.Item label="Vị trí" name={"locationIn"}>
              <Input />
            </Form.Item>
            <FormImageUploadComponent
              label="Hình chấm công vào"
              fieldName={["photoFiles", `in`]}
              max={1}
              required={false}
              imagesFile={attendance?.in?.image && [attendance?.in?.image]}
            />
            <h3 className="text-primary">Chấm công ra</h3>
            <Form.Item label="Thời gian ra" name={"timeOut"} rules={[]}>
              <Row gutter={0}>
                <Col className="bg-[#F5F5F5] flex items-center justify-center rounded border border-solid border-[#DDE1EA] px-2">
                  {dayjs(attendance?.in?.deviceTime).format(DATE_FORMAT)}
                </Col>
                <Col className="ml-3">
                  <TimePicker
                    onChange={onChangeTimeOut}
                    defaultValue={
                      attendance?.out?.deviceTime
                        ? dayjs(
                            dayjs(attendance?.out?.deviceTime).format(
                              "HH:mm:ss",
                            ),
                            "HH:mm:ss",
                          )
                        : null
                    }
                  />
                </Col>
              </Row>
            </Form.Item>
            <Form.Item label="Vị trí" name={"locationOut"}>
              <Input />
            </Form.Item>
            <FormImageUploadComponent
              label="Hình chấm công ra"
              fieldName={["photoFiles", `out`]}
              max={1}
              required={false}
              imagesFile={attendance?.out?.image && [attendance?.out?.image]}
            />
          </>
        )}
      </Form>
    ),
    [
      attendance?.in.deviceTime,
      attendance?.in?.image,
      attendance?.out?.deviceTime,
      attendance?.out?.image,
      attendanceQuery.isLoading,
      attendanceQuery.isSuccess,
      form,
    ],
  );

  const onConfirm = useCallback(async () => {
    setLoading(true);

    try {
      const data = await form.validateFields();
      const locationIn = form.getFieldValue("locationIn");
      const locationOut = form.getFieldValue("locationOut");

      const photoFiles: { fileList: UploadFile[] }[] = data.photoFiles ?? {};

      const photosData = [];
      for (const [type, photoFile] of Object.entries(photoFiles ?? {})) {
        if (photoFile) {
          const { fileList } = photoFile;
          for (const file of fileList) {
            if (file.originFileObj) {
              const result = await uploadImageMutation.mutateAsync(
                file.originFileObj,
              );
              if (result?.id)
                photosData.push({
                  type,
                  imageId: result.id,
                });
            }
          }
        }
      }

      for (const photo of photos) {
        const { image, type } = photo;

        photosData.push({
          type,
          imageId: image.id,
        });
      }

      const attendanceData: {
        in?: {
          latitude: number;
          longitude: number;
          imageId?: number | null;
          deviceId?: string | null;
          deviceTime: string;
        };
        out?: {
          latitude: number;
          longitude: number;
          imageId?: number;
          deviceId?: string | null;
          deviceTime: string;
        };
      } = {};

      const { isPhotoRequired, isLocationRequired } =
        componentFeature?.featureAttendance ?? {};

      if (timeIn) {
        const [latitudeIn, longitudeIn] = locationIn.split(",");
        const imageId = photosData.find((item) => item.type === "in")?.imageId;
        if (isPhotoRequired && !imageId) {
          throw new Error("Chưa chụp ảnh chấm công vào");
        }
        if (isLocationRequired && (!latitudeIn || !longitudeIn)) {
          throw new Error("Chưa chọn vị trí chấm công vào");
        }

        attendanceData["in"] = {
          deviceId: null,
          deviceTime: dayjs(
            `${dayjs(attendance?.in.deviceTime).format("YYYY-MM-DD")} ${timeIn}`,
          ).toISOString(),
          imageId,
          latitude: latitudeIn,
          longitude: longitudeIn,
        };
      }

      if (timeOut) {
        const [latitudeOut, longitudeOut] = locationOut.split(",");
        const imageId = photosData.find((item) => item.type === "out")?.imageId;
        if (isPhotoRequired && !imageId) {
          throw new Error("Chưa chụp ảnh chấm công ra");
        }
        if (isLocationRequired && (!latitudeOut || !longitudeOut)) {
          throw new Error("Chưa chọn vị trí chấm công ra");
        }

        attendanceData["out"] = {
          deviceId: null,
          deviceTime: dayjs(
            `${dayjs(attendance?.in.deviceTime).format("YYYY-MM-DD")} ${timeOut}`,
          ).toISOString(),
          imageId,
          latitude: latitudeOut,
          longitude: longitudeOut,
        };
      }

      await updateAttendanceMutation.mutateAsync(attendanceData);

      attendanceQuery.refetch();

      cb();
      setOpen(false);

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (e: any) {
      console.error(e);
      Modal.error({
        content: e.message,
        title: "Lỗi",
      });
    } finally {
      setLoading(false);
    }
  }, [
    attendance?.in.deviceTime,
    attendanceQuery,
    cb,
    componentFeature?.featureAttendance,
    form,
    photos,
    setOpen,
    timeIn,
    timeOut,
    updateAttendanceMutation,
    uploadImageMutation,
  ]);

  const onCancel = useCallback(() => {
    setOpen(false);
  }, [setOpen]);

  useEffect(() => {
    if (attendance) {
      form.setFieldsValue({
        locationIn: `${attendance?.in.latitude}, ${attendance?.in.longitude}`,
        locationOut: attendance?.out
          ? `${attendance?.out.latitude}, ${attendance?.out.longitude}`
          : "",
      });

      const photos = [
        attendance?.in.image
          ? {
              type: "in",
              image: attendance?.in.image,
            }
          : undefined,
        attendance?.out?.image
          ? {
              type: "out",
              image: attendance?.out.image,
            }
          : undefined,
      ].filter((item) => !!item);

      setPhotos(photos ?? []);

      setTimeIn(dayjs(attendance?.in.deviceTime).format("HH:mm:ss"));
      if (attendance?.out) {
        setTimeOut(dayjs(attendance?.out?.deviceTime).format("HH:mm:ss"));
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [attendance]);

  return (
    <CustomModal
      title={"Chỉnh sửa chấm công"}
      isOpen={true}
      content={content}
      onConfirm={onConfirm}
      onCancel={onCancel}
      confirmLoading={loading}
      confirmText={isAdd ? "" : "Cập nhật"}
      width={600}
    />
  );
};

export default EditAttendanceClockingInModal;
