import localforage from "localforage";
import { create } from "zustand";

type MainLayoutState = {
  collapsed: boolean;
};

type MainLayoutAction = {
  setCollapsed: (collapsed: boolean) => void;
};

export const useMainLayoutStore = create<MainLayoutState & MainLayoutAction>(
  (set) => ({
    collapsed: false,
    setCollapsed: (collapsed) => {
      set({ collapsed });
      localforage.setItem("collapsed", collapsed);
    },
  }),
);

localforage.getItem("collapsed").then((collapsed) => {
  useMainLayoutStore.setState({
    collapsed: (collapsed as boolean) || false,
  });
});
