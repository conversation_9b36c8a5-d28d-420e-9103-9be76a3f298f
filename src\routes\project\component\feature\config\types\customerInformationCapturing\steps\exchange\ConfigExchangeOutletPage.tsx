import { useApp } from "@/UseApp.tsx";
import { DEFAULT_CURRENT_PAGE, DEFAULT_PAGE_SIZE } from "@/common/constant.ts";
import { filterOption } from "@/common/helper.ts";
import { AbstractFilterInterface } from "@/common/interface.ts";
import TableActionCell from "@/components/TableActionCell.tsx";
import renderStatusOnTopCell from "@/components/renderStatusOnTopCell.tsx";
import { SubChannelInterface } from "@/routes/subChannel/interface.ts";
import {
  CloseOutlined,
  FileSearchOutlined,
  PlusOutlined,
  SearchOutlined,
  UnorderedListOutlined,
} from "@ant-design/icons";
import {
  useDistrictsQuery,
  useProvincesQuery,
  useWardsQuery,
} from "@location/service.ts";
import { useProjectAgenciesQuery } from "@project/general/services.ts";
import {
  ProjectAgencyChannelInterface,
  ProjectAgencyInterface,
} from "@project/interface.ts";
import { ProjectOutletInterface } from "@project/outlet/interface.ts";
import {
  useProjectChannelQuery,
  useProjectChannelsQuery,
} from "@project/outlet/service.ts";
import {
  Button,
  Form,
  Input,
  Modal,
  Select,
  Space,
  Table,
  Tabs,
  TabsProps,
} from "antd";
import _ from "lodash";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import {
  ConfigOutletTabEnum,
  FeatureSchemeInterface,
  FeatureSchemeOutletInterface,
} from "../../interface.ts";
import {
  useCreateSchemeOutletsMutation,
  useDeleteSchemeOutletsMutation,
  useSchemeOutletsAvailablesQuery,
  useSchemeOutletsQuery,
  useSchemesQuery,
} from "./service.ts";

const ConfigExchangeOutletTab = (props: {
  projectAgency: ProjectAgencyInterface;
  componentFeatureId: number;
  projectComponentId: number;
}) => {
  const { projectAgency, componentFeatureId, projectComponentId } = props;
  const { showNotification } = useApp();
  const navigate = useNavigate();

  const [isAvailableOpen, setIsAvailableOpen] = useState(false);
  const [isSchemeOutletOpen, setIsSchemeOutletOpen] = useState(false);
  const [searchForm] = Form.useForm();
  const [selectedProvinceId, setSelectedProvinceId] = useState<number | null>(
    null,
  );
  const [selectedDistrictId, setSelectedDistrictId] = useState<number | null>(
    null,
  );
  const [selectedScheme, setSelectedScheme] =
    useState<null | FeatureSchemeInterface>(null);
  const [filter, setFilter] = useState<
    (object & AbstractFilterInterface) | undefined
  >({});
  const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [selectedOutletKeys, setSelectedOutletKeys] = useState<React.Key[]>([]);
  const [channelIdSelected, setChannelIdSelected] = useState<number | null>(
    null,
  );

  const provincesQuery = useProvincesQuery();
  const districtsQuery = useDistrictsQuery(selectedProvinceId);
  const wardsQuery = useWardsQuery(selectedProvinceId, selectedDistrictId);
  const schemesQuery = useSchemesQuery(componentFeatureId, projectAgency.id);
  const schemeOutletsAvailablesQuery = useSchemeOutletsAvailablesQuery(
    componentFeatureId,
    selectedScheme?.id,
    { ...filter, take: pageSize, skip: (currentPage - 1) * pageSize },
    isAvailableOpen,
  );
  const schemeOutletsQuery = useSchemeOutletsQuery(
    componentFeatureId,
    selectedScheme?.id,
    { ...filter, take: pageSize, skip: (currentPage - 1) * pageSize },
    isSchemeOutletOpen,
  );
  const projectChannelsQuery = useProjectChannelsQuery(projectAgency.projectId);
  const projectChannelQuery = useProjectChannelQuery(
    projectAgency.projectId,
    channelIdSelected,
  );

  const createSchemeOutletsMutation = useCreateSchemeOutletsMutation(
    componentFeatureId,
    selectedScheme?.id,
  );
  const deleteSchemeOutletsMutation = useDeleteSchemeOutletsMutation(
    componentFeatureId,
    selectedScheme?.id,
  );

  const onSelectChange = useCallback((newSelectedRowKeys: React.Key[]) => {
    setSelectedOutletKeys(newSelectedRowKeys);
  }, []);

  const handleSelectProvinceChange = useCallback(
    (value: number) => {
      setSelectedProvinceId(value);
      searchForm.resetFields(["districtId", "wardId"]);
    },
    [searchForm],
  );

  const handleSelectDistrictChange = useCallback(
    (value: number) => {
      setSelectedDistrictId(value);
      searchForm.resetFields(["wardId"]);
    },
    [searchForm],
  );

  const onModalSearchFormFinish = useCallback(() => {
    setCurrentPage(DEFAULT_CURRENT_PAGE);
    if (_.isEqual(searchForm.getFieldsValue(), filter)) {
      if (isAvailableOpen) {
        schemeOutletsAvailablesQuery.refetch();
      }

      if (isSchemeOutletOpen) {
        schemeOutletsQuery.refetch();
      }
    }

    setFilter(searchForm.getFieldsValue());
  }, [
    filter,
    isAvailableOpen,
    isSchemeOutletOpen,
    schemeOutletsAvailablesQuery,
    schemeOutletsQuery,
    searchForm,
  ]);

  const handleActionAddClick = useCallback((record: FeatureSchemeInterface) => {
    setSelectedScheme(record);
    setIsAvailableOpen(true);
  }, []);

  const handleActionOutletListClick = useCallback(
    (record: FeatureSchemeInterface) => {
      setSelectedScheme(record);
      setIsSchemeOutletOpen(true);
    },
    [],
  );

  const handleActionDetailClick = useCallback(
    (record: FeatureSchemeInterface) => {
      navigate(
        `/project/${projectAgency.projectId}/component/${projectComponentId}/feature/${componentFeatureId}/customerInformationCapturing/exchange?tab=${projectAgency.id}#scheme${record.id}`,
      );
    },
    [componentFeatureId, navigate, projectAgency, projectComponentId],
  );

  const handlePaginationChange = useCallback(
    (page: number) => {
      setCurrentPage(page);
    },
    [setCurrentPage],
  );

  const handlePageSizeChange = useCallback((_current: number, size: number) => {
    setPageSize(size);
    setCurrentPage(DEFAULT_CURRENT_PAGE);
  }, []);

  const pagination = useMemo(() => {
    let total = 0;
    if (isAvailableOpen) total = schemeOutletsAvailablesQuery.data?.count ?? 0;
    if (isSchemeOutletOpen) total = schemeOutletsQuery.data?.count ?? 0;

    return {
      current: currentPage,
      total,
      pageSize: pageSize,
      onChange: handlePaginationChange,
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: handlePageSizeChange,
    };
  }, [
    isAvailableOpen,
    schemeOutletsAvailablesQuery.data?.count,
    isSchemeOutletOpen,
    schemeOutletsQuery.data?.count,
    currentPage,
    pageSize,
    handlePaginationChange,
    handlePageSizeChange,
  ]);

  const rowSelection = {
    selectedRowKeys: selectedOutletKeys,
    onChange: onSelectChange,
    getCheckboxProps: (record: ProjectOutletInterface) => {
      return {
        disabled: (!record.isAvailable && isAvailableOpen) || !record.isActive,
      };
    },
  };

  const newAvailableSelectedOutletKeys = useMemo(() => {
    return (
      schemeOutletsAvailablesQuery.data?.entities
        .filter(
          (projectOutlet) =>
            selectedOutletKeys.includes(projectOutlet.id) &&
            projectOutlet.isAvailable,
        )
        .map((item) => item.id) ?? []
    );
  }, [selectedOutletKeys, schemeOutletsAvailablesQuery.data?.entities]);

  const onModalSubmitClick = useCallback(async () => {
    if (isAvailableOpen && newAvailableSelectedOutletKeys?.length > 0) {
      await createSchemeOutletsMutation.mutateAsync({
        projectOutletIds: newAvailableSelectedOutletKeys.map((id) =>
          Number(id),
        ),
      });

      showNotification({
        type: "success",
        message: `Thêm ${newAvailableSelectedOutletKeys.length} outlets vào scheme #${selectedScheme?.id} - ${selectedScheme?.name} thành công`,
      });
      setIsAvailableOpen(false);

      setSelectedScheme(null);
      setSelectedOutletKeys([]);
      schemesQuery.refetch();
      schemeOutletsAvailablesQuery.refetch();
    }

    if (isSchemeOutletOpen && selectedOutletKeys.length > 0) {
      await deleteSchemeOutletsMutation.mutateAsync({
        featureSchemeOutletIds: selectedOutletKeys.map((id) => Number(id)),
      });

      showNotification({
        type: "success",
        message: `Xóa ${selectedOutletKeys.length} outlets khỏi scheme #${selectedScheme?.id} - ${selectedScheme?.name} thành công`,
      });
      setIsSchemeOutletOpen(false);

      setSelectedScheme(null);
      setSelectedOutletKeys([]);
      schemesQuery.refetch();
      schemeOutletsAvailablesQuery.refetch();
    }
  }, [
    createSchemeOutletsMutation,
    deleteSchemeOutletsMutation,
    isAvailableOpen,
    isSchemeOutletOpen,
    newAvailableSelectedOutletKeys,
    schemeOutletsAvailablesQuery,
    schemesQuery,
    selectedOutletKeys,
    selectedScheme?.id,
    selectedScheme?.name,
    showNotification,
  ]);

  useEffect(() => {
    if (!selectedScheme?.id) {
      return;
    }

    if (isAvailableOpen) {
      setSelectedOutletKeys(
        schemeOutletsAvailablesQuery.data?.entities
          .filter(
            (projectOutlet) =>
              projectOutlet.featureSchemeOutlets?.[0]?.featureScheme?.id ===
              selectedScheme?.id,
          )
          .map((item) => item.id) ?? [],
      );
    }

    if (isSchemeOutletOpen) {
      setSelectedOutletKeys([]);
    }
  }, [
    isAvailableOpen,
    isSchemeOutletOpen,
    schemeOutletsAvailablesQuery.data?.entities,
    selectedScheme?.id,
  ]);

  const onModalClose = useCallback(() => {
    setIsSchemeOutletOpen(false);
    setIsAvailableOpen(false);
    setSelectedScheme(null);
    setSelectedDistrictId(null);
    setSelectedProvinceId(null);
    searchForm.resetFields();
    setFilter({});
    setCurrentPage(DEFAULT_CURRENT_PAGE);
    setPageSize(DEFAULT_PAGE_SIZE);
  }, [searchForm]);

  const dataSource = useMemo(() => {
    let data: ProjectOutletInterface[] = [];
    if (isAvailableOpen) {
      data = schemeOutletsAvailablesQuery.data?.entities ?? [];
    }

    if (isSchemeOutletOpen) {
      data =
        schemeOutletsQuery.data?.entities.map((featureSchemeOutlet) => ({
          ...featureSchemeOutlet.projectOutlet,
          id: featureSchemeOutlet.id,
        })) ?? [];
    }

    return data;
  }, [
    isAvailableOpen,
    isSchemeOutletOpen,
    schemeOutletsAvailablesQuery.data?.entities,
    schemeOutletsQuery.data?.entities,
  ]);

  return (
    <>
      <Table
        dataSource={schemesQuery.data?.entities}
        pagination={false}
        rowKey={"id"}
        columns={[
          {
            title: "Mã scheme",
            dataIndex: "id",
          },
          {
            title: "Tên scheme",
            dataIndex: "name",
          },
          {
            title: "Outlet đã phân bổ",
            dataIndex: "featureSchemeOutletsCount",
            align: "right",
          },
          {
            render: (_, record) => (
              <TableActionCell
                actions={[
                  {
                    key: ConfigOutletTabEnum.ADD,
                    action: handleActionAddClick,
                  },
                  {
                    key: ConfigOutletTabEnum.OUTLET_LIST,
                    action: handleActionOutletListClick,
                  },
                  {
                    key: ConfigOutletTabEnum.DETAIL,
                    action: handleActionDetailClick,
                  },
                ]}
                items={[
                  {
                    key: ConfigOutletTabEnum.ADD,
                    label: "Thêm outlet vào scheme",
                    icon: <PlusOutlined />,
                  },
                  {
                    key: ConfigOutletTabEnum.OUTLET_LIST,
                    label: "Danh sách outlet trong scheme",
                    icon: <UnorderedListOutlined />,
                  },
                  {
                    key: ConfigOutletTabEnum.DETAIL,
                    label: "Chi tiết",
                    icon: <FileSearchOutlined />,
                  },
                ]}
                record={record}
              />
            ),
          },
        ]}
      />

      <Modal
        open={isAvailableOpen || isSchemeOutletOpen}
        footer={null}
        closeIcon={null}
        width={1450}
        styles={{ content: { padding: 0 } }}
      >
        <div className="pl-10 pr-10 pt-3 pb-5">
          <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
            <h2 className="text-neutral-700 text-2xl font-semibold ">
              {isAvailableOpen && "Thêm outlet vào scheme"}
              {isSchemeOutletOpen && "Danh sách outlet áp dụng scheme quà"}
            </h2>
            <div className="pt-5">
              <Button
                type="link"
                onClick={onModalClose}
                size="large"
                icon={<CloseOutlined />}
              />
            </div>
          </div>

          <p>
            <span className="text-hint">
              Scheme quà đang phân bổ outlet: &nbsp;
            </span>
            <span className="text-text-color font-semibold">
              {" "}
              #{selectedScheme?.id} -{" "}
            </span>
            <span className="text-primary font-medium">
              {selectedScheme?.name}
            </span>
          </p>

          <Form
            layout="inline"
            onFinish={onModalSearchFormFinish}
            form={searchForm}
          >
            <Space>
              <Form.Item name="keyword">
                <Input
                  prefix={<SearchOutlined />}
                  placeholder="Tìm theo mã, tên outlet, số nhà, tên đường"
                  allowClear
                  className={"min-w-[322px]"}
                />
              </Form.Item>

              <Form.Item name="provinceId">
                <Select
                  allowClear
                  placeholder="Tỉnh/ TP"
                  showSearch
                  optionFilterProp="children"
                  filterOption={filterOption}
                  options={provincesQuery.data?.map((province) => ({
                    label: province.name,
                    value: province.id,
                  }))}
                  popupMatchSelectWidth={false}
                  onChange={handleSelectProvinceChange}
                />
              </Form.Item>

              <Form.Item name="districtId">
                <Select
                  allowClear
                  placeholder="Quận/ Huyện"
                  showSearch
                  optionFilterProp="children"
                  filterOption={filterOption}
                  options={districtsQuery.data?.map((district) => ({
                    label: district.name,
                    value: district.id,
                  }))}
                  popupMatchSelectWidth={false}
                  onChange={handleSelectDistrictChange}
                />
              </Form.Item>

              <Form.Item name="wardId">
                <Select
                  allowClear
                  placeholder="Phường/ Xã"
                  showSearch
                  optionFilterProp="children"
                  filterOption={filterOption}
                  options={wardsQuery.data?.map((ward) => ({
                    label: ward.name,
                    value: ward.id,
                  }))}
                  popupMatchSelectWidth={false}
                />
              </Form.Item>

              <Form.Item name={"channelId"}>
                <Select
                  allowClear
                  placeholder="Kênh"
                  showSearch
                  optionFilterProp="children"
                  filterOption={filterOption}
                  options={projectChannelsQuery.data?.map((channel) => ({
                    label: channel.name,
                    value: channel.id,
                  }))}
                  popupMatchSelectWidth={false}
                  onSelect={(id) => setChannelIdSelected(id)}
                  onChange={() => {
                    searchForm.resetFields(["subChannelId"]);
                    setChannelIdSelected(null);
                  }}
                />
              </Form.Item>

              <Form.Item name={"subChannelId"}>
                <Select
                  allowClear
                  placeholder="Nhóm"
                  showSearch
                  optionFilterProp="children"
                  filterOption={filterOption}
                  options={projectChannelQuery?.data?.subChannels?.map(
                    (subChannel) => ({
                      label: subChannel.name,
                      value: subChannel.id,
                    }),
                  )}
                  popupMatchSelectWidth={false}
                />
              </Form.Item>

              <Form.Item>
                <Button
                  htmlType="submit"
                  loading={
                    schemeOutletsQuery.isFetching ||
                    schemeOutletsAvailablesQuery.isFetching
                  }
                >
                  Tìm kiếm
                </Button>
              </Form.Item>
            </Space>
          </Form>

          <Table
            className="mt-3"
            rowKey={"id"}
            dataSource={dataSource}
            pagination={pagination}
            rowSelection={rowSelection}
            scroll={{
              x: "max-content",
              y: pagination.total ? "80vh" : undefined,
            }}
            loading={
              schemeOutletsQuery.isFetching ||
              schemeOutletsAvailablesQuery.isFetching
            }
            columns={[
              {
                title: "Mã outlet",
                dataIndex: "code",
                className: "min-w-[150px]",
              },
              {
                title: "Tên outlet",
                dataIndex: "name",
                className: "min-w-[150px]",
                render: (name: string, record: ProjectOutletInterface) => {
                  const recordSchemeId =
                    record.featureSchemeOutlets?.[0]?.featureScheme?.id;

                  return (
                    <>
                      {isAvailableOpen &&
                        recordSchemeId === selectedScheme?.id &&
                        renderStatusOnTopCell(
                          "Đã thêm vào scheme hiện tại",
                          "#008916",
                          "#E5F5E7",
                        )}

                      {isAvailableOpen &&
                        recordSchemeId !== selectedScheme?.id &&
                        !record.isAvailable &&
                        renderStatusOnTopCell(
                          "Đã thêm vào scheme khác",
                          "#393939",
                          "#F5F5F5",
                        )}

                      {!record.isActive &&
                        renderStatusOnTopCell(
                          "Ngừng hoạt động",
                          "#DF3C3C",
                          "#FFEEEE",
                        )}
                      <p>{name}</p>
                    </>
                  );
                },
              },
              {
                title: "Số nhà",
                className: "min-w-[150px]",
                dataIndex: "houseNumber",
              },
              {
                title: "Tên đường",
                className: "min-w-[150px]",
                dataIndex: "streetName",
              },
              {
                title: "Tỉnh/ TP",
                className: "min-w-[150px]",
                dataIndex: "province",
                render: (province) => province?.name,
              },
              {
                title: "Quận/ Huyện",
                className: "min-w-[150px]",
                dataIndex: "district",
                render: (district) => district?.name,
              },
              {
                title: "Phường/ Xã",
                className: "min-w-[150px]",
                dataIndex: "ward",
                render: (ward) => ward?.name,
              },
              {
                title: "Kênh",
                className: "min-w-[150px]",
                dataIndex: "projectAgencyChannel",
                render: (projectAgencyChannel: ProjectAgencyChannelInterface) =>
                  projectAgencyChannel?.channel.name,
              },
              {
                title: "Nhóm",
                className: "min-w-[150px]",
                dataIndex: "subChannel",
                render: (subChannel: SubChannelInterface) => subChannel?.name,
              },
              {
                title: "Mã scheme",
                className: "min-w-[150px]",
                dataIndex: "featureSchemeOutlets",
                render: (
                  featureSchemeOutlets: FeatureSchemeOutletInterface[],
                ) => featureSchemeOutlets?.[0]?.featureScheme?.id,
                hidden: isSchemeOutletOpen,
              },
              {
                title: "Tên scheme",
                className: "min-w-[150px]",
                dataIndex: "featureSchemeOutlets",
                render: (
                  featureSchemeOutlets: FeatureSchemeOutletInterface[],
                ) => featureSchemeOutlets?.[0]?.featureScheme?.name,
                hidden: isSchemeOutletOpen,
              },
            ]}
          />
        </div>

        <div className="flex justify-end pb-4 pt-4 bg-[#F7F8FA]">
          <Space className="pr-10">
            <Button onClick={onModalClose}>Đóng</Button>
            <Button
              type={"primary"}
              onClick={onModalSubmitClick}
              disabled={
                (isAvailableOpen &&
                  newAvailableSelectedOutletKeys.length === 0) ||
                (isSchemeOutletOpen && selectedOutletKeys.length === 0)
              }
            >
              {isAvailableOpen &&
                `Thêm ${newAvailableSelectedOutletKeys.length} outlet vào scheme`}

              {isSchemeOutletOpen &&
                `Bỏ ${selectedOutletKeys.length} outlet khỏi scheme`}
            </Button>
          </Space>
        </div>
      </Modal>
    </>
  );
};

/**
 * Cấu hình thêm outlet vào scheme
 * @constructor
 */
export default function ConfigExchangeOutletPage() {
  const projectId = parseInt(useParams().id ?? "0");
  const projectComponentId = parseInt(useParams().projectComponentId ?? "0");
  const componentFeatureId = parseInt(useParams().componentFeatureId ?? "0");

  const projectAgenciesQuery = useProjectAgenciesQuery(projectId);

  const items: TabsProps["items"] = projectAgenciesQuery.data?.map(
    (projectAgency) => ({
      key: projectAgency.id.toString(),
      label: projectAgency.agency.name,
      children: (
        <ConfigExchangeOutletTab
          projectAgency={projectAgency}
          projectComponentId={projectComponentId}
          componentFeatureId={componentFeatureId}
        />
      ),
    }),
  );

  return <Tabs items={items} type={"card"} />;
}
