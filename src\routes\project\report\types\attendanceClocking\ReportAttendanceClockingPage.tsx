import InnerContainer from "@/components/InnerContainer/InnerContainer.tsx";
import { Tabs, TabsProps } from "antd";
import { useState } from "react";
import { useProjectReportOutletContext } from "../../UseProjectReportOutletContext.tsx";
import BoothTab from "./tabs/BoothTab";
import EmployeeTab from "./tabs/EmployeeTab";

export default function ReportAttendanceClockingPage() {
  const { projectId, componentFeatureId, advancedFilterValues } =
    useProjectReportOutletContext();
  const [activeItem, setActiveItem] = useState("booth");

  const items: TabsProps["items"] = [
    {
      key: "booth",
      label: "Theo booth",
      children: (
        <BoothTab
          projectId={projectId}
          componentFeatureId={componentFeatureId}
          advancedFilterValues={advancedFilterValues}
          isActive={activeItem === "booth"}
        />
      ),
    },
    {
      key: "employee",
      label: "<PERSON> nhân viên",
      children: (
        <EmployeeTab
          projectId={projectId}
          componentFeatureId={componentFeatureId}
          advancedFilterValues={advancedFilterValues}
          isActive={activeItem === "employee"}
        />
      ),
    },
  ];
  return (
    <div>
      <h2>Chấm công</h2>
      <InnerContainer>
        <Tabs
          items={items}
          onChange={(key: string) => {
            setActiveItem(key);
          }}
          type={"card"}
        />
      </InnerContainer>
    </div>
  );
}
