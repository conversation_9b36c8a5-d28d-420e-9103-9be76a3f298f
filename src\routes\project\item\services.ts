import { useApp } from "@/UseApp";
import { AbstractFilterInterface } from "@/common/interface";
import { AppContextInterface } from "@/interface.ts";
import { useQuery } from "@tanstack/react-query";
import { ItemTypeInterface } from "../../item-type/interface.ts";
import { ItemInterface } from "../../item/interface.ts";
import { ApiProjectItemResponseInterface } from "./interface";

export const getProjectItems = async (
  axiosGet: AppContextInterface["axiosGet"],
  projectId: number,
  filter: unknown,
) => {
  return await axiosGet<ApiProjectItemResponseInterface, unknown>(
    `/projects/${projectId}/items`,
    filter,
  );
};

export const useProjectItemsQuery = (
  projectId: number,
  filter?: { getInActive?: boolean } & AbstractFilterInterface,
  enabled?: boolean,
) => {
  const { axiosGet } = useApp();

  const { getInActive, ...restFilter } = filter ?? {};

  const queryFilter = {
    ...(getInActive ? {} : { isActive: true }),
    ...restFilter,
  };

  return useQuery({
    queryKey: ["projectItems", projectId, queryFilter],
    queryFn: () =>
      axiosGet<ApiProjectItemResponseInterface, unknown>(
        `/projects/${projectId}/items`,
        queryFilter,
      ),
    enabled,
  });
};

export const getProjectItemTypes = async (
  axiosGet: AppContextInterface["axiosGet"],
  projectId: number,
) => {
  return await axiosGet<ItemTypeInterface[], unknown>(
    `/projects/${projectId}/items/types`,
  );
};

export const useProjectItemsTypesQuery = (
  projectId: number,
  enabled: boolean,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["projectItemsTypes", projectId],
    queryFn: () => getProjectItemTypes(axiosGet, projectId),
    enabled,
  });
};

export const useProjectItemsAvailableQuery = (
  projectId: number,
  filter?: object & AbstractFilterInterface,
  enabled?: boolean,
) => {
  const { axiosGet } = useApp();
  return useQuery({
    queryKey: ["projectItemsAvailable", projectId, filter],
    queryFn: () =>
      axiosGet<{ entities: ItemInterface[]; count: number }, unknown>(
        `/projects/${projectId}/items/availables`,
        filter,
      ),
    enabled,
  });
};
