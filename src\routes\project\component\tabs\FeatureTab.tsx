import { useApp } from "@/UseApp";
import { CURD } from "@/common/constant";
import { filterOption, formErrorResponse<PERSON>and<PERSON> } from "@/common/helper";
import CustomTable from "@/components/CustomTable/CustomTable";
import FilterComponent from "@/components/FilterComponent";
import ModalCURD from "@/components/ModalCURD";
import TableActionCell from "@/components/TableActionCell";
import { renderTableCell } from "@/components/table-cell.tsx";
import { useProjectBoothsQuery } from "@/routes/project/configOutlet/service";
import {
  DeleteOutlined,
  EditOutlined,
  FileSearchOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined,
} from "@ant-design/icons";
import { Form, Input, Modal, Select } from "antd";
import { ColumnsType } from "antd/es/table";
import _ from "lodash";
import { useCallback, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useProjectReportComponentsQuery } from "../../report/service";
import {
  ProjectComponentActionEnum,
  ProjectComponentBoothInterface,
  ProjectComponentInterface,
} from "../interface";
import {
  useCreateProjectComponentMutation,
  useCreateProjectComponentsPublicationsMutation,
  useDeleteProjectComponentMutation,
  useProjectComponentsQuery,
  useUpdateProjectComponentMutation,
} from "../service";

export default function FeatureTab() {
  const projectId = parseInt(useParams().id ?? "0");

  const { showNotification, openDeleteModal } = useApp();
  const navigate = useNavigate();

  const [searchForm] = Form.useForm();
  const [form] = Form.useForm();
  const [isOpen, setIsOpen] = useState(false);
  const [formAction, setFormAction] = useState<CURD | null>(null);
  const [modal, contextHolder] = Modal.useModal();
  const [filter, setFilter] = useState({});

  const projectBoothsQuery = useProjectBoothsQuery(projectId, {
    take: 50,
    skip: 0,
  });
  const projectComponentsQuery = useProjectComponentsQuery(projectId, {
    take: 0,
    skip: 0,
    ...filter,
  });
  const projectReportComponentsQuery =
    useProjectReportComponentsQuery(projectId);

  const createProjectComponentMutation =
    useCreateProjectComponentMutation(projectId);
  const updateProjectComponentMutation =
    useUpdateProjectComponentMutation(projectId);
  const deleteProjectComponentMutation =
    useDeleteProjectComponentMutation(projectId);
  const createProjectComponentsPublicationsMutation =
    useCreateProjectComponentsPublicationsMutation(projectId);

  const onFinish = useCallback(async () => {
    if (formAction === CURD.CREATE) {
      try {
        await createProjectComponentMutation.mutateAsync(form.getFieldsValue());
        showNotification({
          type: "success",
          message: "Thêm nhóm chức năng thành công",
        });
        form.resetFields();
        setFormAction(null);
        setIsOpen(false);
        projectComponentsQuery.refetch();
        projectBoothsQuery.refetch();
      } catch (e) {
        formErrorResponseHandler(form, e);
      }
    }

    if (formAction === CURD.UPDATE) {
      try {
        await updateProjectComponentMutation.mutateAsync({
          id: form.getFieldValue("id"),
          data: form.getFieldsValue(),
        });
        showNotification({
          type: "success",
          message: "Cập nhật chức năng thành công",
        });
        form.resetFields();
        setFormAction(null);
        setIsOpen(false);
        projectComponentsQuery.refetch();
        projectBoothsQuery.refetch();
      } catch (e) {
        formErrorResponseHandler(form, e);
      }
    }
  }, [
    createProjectComponentMutation,
    form,
    formAction,
    projectBoothsQuery,
    projectComponentsQuery,
    showNotification,
    updateProjectComponentMutation,
  ]);

  const handleActionEditClick = useCallback(
    async (record: ProjectComponentInterface) => {
      form.setFieldsValue({
        ...record,
      });
      form.setFieldValue(
        "projectBoothIds",
        record.projectComponentBooths.map((item) => item.projectBooth.id),
      );
      setIsOpen(true);
      setFormAction(CURD.UPDATE);
    },
    [form],
  );

  const handleActionInActiveClick = useCallback(
    (record: ProjectComponentInterface) => {
      modal.confirm({
        title: `Ngừng hoạt động nhóm chức năng: ${record.name}`,
        content: `Bạn có chắc chắn muốn ngừng hoạt động nhóm chức năng (${record.name}) này?`,
        okText: "Ngừng hoạt động",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await updateProjectComponentMutation.mutateAsync({
              id: record.id,
              data: { isActive: false },
            });

            showNotification({
              type: "success",
              message: `Ngừng hoạt động ${record.name} thành công`,
            });

            projectComponentsQuery.refetch();
          } catch (error) {
            console.error(error);
            showNotification({
              type: "error",
              message: `Ngừng hoạt động ${record.name} thất bại`,
            });
          }
        },
      });
    },
    [
      modal,
      projectComponentsQuery,
      showNotification,
      updateProjectComponentMutation,
    ],
  );

  const handleActionActiveClick = useCallback(
    (record: ProjectComponentInterface) => {
      modal.confirm({
        title: `Kích hoạt nhóm chức năng: ${record.name}`,
        content: `Bạn có chắc chắn muốn kích hoạt nhóm chức năng (${record.name}) này?`,
        okText: "Kích hoạt",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await updateProjectComponentMutation.mutateAsync({
              id: record.id,
              data: { isActive: true },
            });

            showNotification({
              type: "success",
              message: `Kích hoạt ${record.name} thành công`,
            });

            projectComponentsQuery.refetch();
          } catch (error) {
            console.error(error);
            showNotification({
              type: "error",
              message: `Kích hoạt ${record.name} thất bại`,
            });
          }
        },
      });
    },
    [
      modal,
      projectComponentsQuery,
      showNotification,
      updateProjectComponentMutation,
    ],
  );

  const handleActionDeleteClick = useCallback(
    (record: ProjectComponentInterface) => {
      openDeleteModal({
        content: (
          <p>
            Bạn muốn xóa nhóm chức năng{" "}
            <span className={"font-semibold"}>{record.name}</span> khỏi dự án?
          </p>
        ),
        deleteText: "Xác nhận xóa",
        loading: false,
        onCancel(): void {},
        onDelete: async () => {
          await deleteProjectComponentMutation.mutateAsync(record.id);
          showNotification({
            type: "success",
            message: "Xóa nhóm chức năng thành công",
          });
          projectComponentsQuery.refetch();
          projectBoothsQuery.refetch();
        },
        title: `Xóa nhóm chức năng`,
        titleError: "Không thể xóa nhóm chức năng",
        contentHeader: (
          <>
            Không thể xóa nhóm chức năng{" "}
            <span className="font-semibold">{record.name}</span> khỏi dự án bởi
            vì:
          </>
        ),
      });
    },
    [
      deleteProjectComponentMutation,
      openDeleteModal,
      projectBoothsQuery,
      projectComponentsQuery,
      showNotification,
    ],
  );

  const handleActionViewClick = useCallback(
    (record: ProjectComponentInterface) => {
      navigate(`/project/${projectId}/component/${record.id}`, {
        state: record,
      });
    },
    [navigate, projectId],
  );

  const handleActionPublicationClick = useCallback(
    async (record: ProjectComponentInterface) => {
      modal.confirm({
        title: `Phát hành nhóm chức năng: ${record.name}`,
        content: `Bạn có chắc chắn muốn phát hành nhóm chức năng (${record.name}) này?`,
        okText: "Phát hành",
        cancelText: "Hủy",
        onOk: async () => {
          await createProjectComponentsPublicationsMutation.mutateAsync({
            projectComponentIds: [record.id],
          });

          projectReportComponentsQuery.refetch();

          showNotification({
            type: "success",
            message: `Phát hành chức năng ${record.name} thành công`,
          });
        },
      });
    },
    [
      createProjectComponentsPublicationsMutation,
      modal,
      projectReportComponentsQuery,
      showNotification,
    ],
  );

  const actionItems = [
    {
      key: ProjectComponentActionEnum.EDIT,
      label: "Chỉnh sửa",
      icon: <EditOutlined />,
    },
    {
      key: ProjectComponentActionEnum.VIEW,
      label: "Chức năng tại booth",
      icon: <FileSearchOutlined />,
    },
    {
      key: ProjectComponentActionEnum.PUBLICATION,
      label: "Phát hành",
      icon: <PlayCircleOutlined />,
    },
    {
      key: ProjectComponentActionEnum.INACTIVE,
      label: "Ngừng hoạt động",
      icon: <PauseCircleOutlined />,
    },
    {
      key: ProjectComponentActionEnum.ACTIVE,
      label: "Kích hoạt",
      icon: <PlayCircleOutlined />,
    },
    {
      key: ProjectComponentActionEnum.DELETE,
      label: "Xóa khỏi dự án",
      icon: <DeleteOutlined />,
    },
  ];

  const actionActions = [
    {
      key: ProjectComponentActionEnum.EDIT,
      action: handleActionEditClick,
    },
    {
      key: ProjectComponentActionEnum.INACTIVE,
      action: handleActionInActiveClick,
    },
    {
      key: ProjectComponentActionEnum.ACTIVE,
      action: handleActionActiveClick,
    },
    {
      key: ProjectComponentActionEnum.DELETE,
      action: handleActionDeleteClick,
    },
    {
      key: ProjectComponentActionEnum.VIEW,
      action: handleActionViewClick,
    },
    {
      key: ProjectComponentActionEnum.PUBLICATION,
      action: handleActionPublicationClick,
    },
  ];

  const ACTION_ACTIVE = [
    ProjectComponentActionEnum.EDIT,
    ProjectComponentActionEnum.VIEW,
    ProjectComponentActionEnum.PUBLICATION,
    ProjectComponentActionEnum.INACTIVE,
    ProjectComponentActionEnum.DELETE,
  ];
  const ACTION_INACTIVE = [
    ProjectComponentActionEnum.EDIT,
    ProjectComponentActionEnum.VIEW,
    ProjectComponentActionEnum.PUBLICATION,
    ProjectComponentActionEnum.ACTIVE,
    ProjectComponentActionEnum.DELETE,
  ];

  const columns: ColumnsType<ProjectComponentInterface> = [
    {
      title: "Tên nhóm chức năng",
      key: "name",
      dataIndex: "name",
      className: "cursor-pointer",
      onCell: (record) => {
        return {
          onClick: () => {
            handleActionViewClick(record);
          },
        };
      },
    },
    {
      title: "Loại booth áp dụng",
      dataIndex: "projectComponentBooths",
      render: (projectComponentBooths: ProjectComponentBoothInterface[]) => {
        return (
          <ul>
            {projectComponentBooths.map((projectComponentBooth) => (
              <li key={projectComponentBooth.id}>
                {projectComponentBooth.projectBooth.name}
              </li>
            ))}
          </ul>
        );
      },
    },
    {
      title: "Số chức năng",
      key: "booths.name",
      dataIndex: "projectFeaturesCount",
      render: (value: number) => {
        return value;
      },
    },
    {
      key: "isActive",
      title: "Tình trạng",
      dataIndex: "isActive",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "isActive");
      },
    },
    {
      key: "actions",
      render: (_, record) => {
        const actionKeys = record.isActive ? ACTION_ACTIVE : ACTION_INACTIVE;
        const items = actionItems.filter((item) =>
          actionKeys.includes(item.key),
        );
        return (
          <TableActionCell
            actions={actionActions}
            items={items}
            record={record}
          />
        );
      },
      fixed: "right",
      width: 100,
    },
  ];

  const formContent = (
    <>
      <Form.Item name={"id"} hidden={true}></Form.Item>

      <Form.Item
        name={"name"}
        label={"Tên nhóm chức năng"}
        rules={[
          {
            required: true,
            message: "Tên nhóm chức năng không được để trống",
          },
        ]}
      >
        <Input />
      </Form.Item>
      <Form.Item
        name={"projectBoothIds"}
        label={"Booth áp dụng"}
        rules={[
          {
            required: true,
            message: "Booth áp dụng không được để trống",
          },
        ]}
      >
        {(() => {
          const id = form.getFieldValue("id");
          const projectComponent = projectComponentsQuery.data?.entities.find(
            (projectComponent) => projectComponent.id === id,
          );

          const projectBoothIdsInComponents =
            projectComponentsQuery.data?.entities.flatMap((projectComponent) =>
              projectComponent.projectComponentBooths.map(
                (projectComponentBooth) =>
                  projectComponentBooth.projectBooth.id,
              ),
            );

          const projectBoothIdExistInOtherComponents = _.difference(
            projectBoothIdsInComponents,
            projectComponent?.projectComponentBooths.map(
              (projectBoothComponent) => projectBoothComponent.projectBooth.id,
            ) ?? [],
          );

          const activeOptions =
            projectBoothsQuery.data?.entities.map((projectBooth) => {
              return {
                label: projectBooth.name,
                value: projectBooth.id,
                booth: projectBooth,
                disabled:
                  formAction === CURD.CREATE
                    ? projectBoothIdsInComponents?.includes(projectBooth.id)
                    : projectBoothIdExistInOtherComponents.includes(
                        projectBooth.id,
                      ),
              };
            }) ?? [];

          const selectedOptions =
            projectComponent?.projectComponentBooths.map(
              (projectComponentBooth) => ({
                label: projectComponentBooth.projectBooth.name,
                value: projectComponentBooth.projectBooth.id,
                booth: projectComponentBooth.projectBooth,
                disabled: false,
              }),
            ) ?? [];

          const options = _.uniqBy(
            _.concat(activeOptions, selectedOptions),
            (o) => o.value,
          );

          return (
            <Select
              mode="multiple"
              showSearch
              optionFilterProp="children"
              filterOption={filterOption}
              options={options}
              optionRender={(option) => {
                console.log(option);
                return (
                  <>
                    <span className="text-[#393939] pr-3">{option.label}</span>
                    {option.data.disabled && (
                      <span className="text-[#8C8C8D] italic">
                        Đã tồn tại trong nhóm chức năng khác
                      </span>
                    )}
                  </>
                );
              }}
            />
          );
        })()}
      </Form.Item>
    </>
  );

  return (
    <>
      <FilterComponent
        searchHandler={function (): void {
          const values = searchForm.getFieldsValue();
          if (_.isEqual(filter, values)) {
            projectComponentsQuery.refetch();
          }
          setFilter(values);
        }}
        searchForm={searchForm}
        filterOptions={[
          {
            label: "Tất cả",
            value: "all",
          },
          {
            label: "Tên nhóm chức năng",
            value: "name",
          },
          {
            label: "Loại booth áp dụng",
            value: "boothName",
          },
        ]}
        handleAddButtonClick={function (): void {
          setFormAction(CURD.CREATE);
          setIsOpen(true);
        }}
        className={"mb-6"}
      />

      <ModalCURD
        title={
          formAction === CURD.CREATE
            ? "Thêm nhóm chức năng"
            : "Thông tin nhóm chức năng"
        }
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        formContent={formContent}
        form={form}
        onFinish={onFinish}
        action={formAction}
        btnConfirmLoading={createProjectComponentMutation.isPending}
      />

      <CustomTable
        dataSource={projectComponentsQuery.data?.entities ?? []}
        columns={columns}
        pagination={false}
        loading={
          projectComponentsQuery.isPending || projectComponentsQuery.isFetching
        }
      />
      <p className={"pb-0 mb-0"}>
        Số kết quả trả về: {projectComponentsQuery.data?.count ?? 0}
      </p>

      {contextHolder}
    </>
  );
}
