import { useApp } from "@/UseApp.tsx";
import { DEFAULT_CURRENT_PAGE, DEFAULT_PAGE_SIZE } from "@/common/constant.ts";
import { filterOption } from "@/common/helper";
import ProductItemCell from "@/components/ProductItemCell.tsx";
import TableActionCell from "@/components/TableActionCell.tsx";
import { renderTableCell } from "@/components/table-cell.tsx";
import {
  ProductInterface,
  ProductPackagingInterface,
} from "@/routes/product/interface.ts";
import {
  CloseOutlined,
  DeleteOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { useProjectBrandsQuery } from "@project/general/services.ts";
import { ProjectProductInterface } from "@project/product/interface.ts";
import { UseQueryResult } from "@tanstack/react-query";
import {
  But<PERSON>,
  Col,
  Form,
  Input,
  Modal,
  Row,
  Select,
  Space,
  Table,
} from "antd";
import _ from "lodash";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useOutletContext, useParams } from "react-router-dom";
import StepLockPage from "../../StepLockPage.tsx";
import {
  ConfigSamplingEnum,
  CustomerFeatureSamplingInterface,
  OrderInterface,
  StepLockEnum,
} from "../../interface.ts";
import {
  useCreateSamplingMutation,
  useDeleteSamplingMutation,
  useSamplingsAvailablesQuery,
  useSamplingsQuery,
  useUpdateSamplingMutation,
} from "./service.ts";

export default function ConfigCustomerSamplingPage() {
  const [orderLatestQuery]: [UseQueryResult<OrderInterface, unknown>] =
    useOutletContext();

  const componentFeatureId = parseInt(useParams().componentFeatureId ?? "0");
  const projectId = parseInt(useParams().id ?? "0");
  const { showNotification } = useApp();

  const [isOpen, setIsOpen] = useState(false);
  const [searchForm] = Form.useForm();
  const [selectedProductKeys, setSelectedProductKeys] = useState<React.Key[]>(
    [],
  );
  const [modal, contextHolder] = Modal.useModal();
  const [filter, setFilter] = useState<{
    keyword?: string;
  }>({});
  const [filterAvailables, setFilterAvailables] = useState<{
    keyword?: string;
    projectBrandId?: number;
  }>({});
  const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [currentPageAvailables, setCurrentPageAvailables] =
    useState(DEFAULT_CURRENT_PAGE);
  const [pageSizeAvailables, setPageSizeAvailables] =
    useState(DEFAULT_PAGE_SIZE);

  const samplingsAvailablesQuery = useSamplingsAvailablesQuery(
    componentFeatureId,
    {
      ...filterAvailables,
      take: pageSizeAvailables,
      skip: (currentPageAvailables - 1) * pageSizeAvailables,
    },
  );
  const samplingsQuery = useSamplingsQuery(componentFeatureId, {
    ...filter,
    take: pageSize,
    skip: (currentPage - 1) * pageSize,
  });
  const projectBrandsQuery = useProjectBrandsQuery(projectId);

  const createSamplingMutation = useCreateSamplingMutation(componentFeatureId);
  const updateSamplingMutation = useUpdateSamplingMutation(componentFeatureId);
  const deleteSamplingMutation = useDeleteSamplingMutation(componentFeatureId);

  const onModalFormFinish = useCallback(
    (values: { projectBrandId?: number; keyword?: string }) => {
      if (_.isEqual(values, filterAvailables)) {
        samplingsAvailablesQuery.refetch();
      }
      setFilterAvailables(values);
    },
    [filterAvailables, samplingsAvailablesQuery],
  );

  useEffect(() => {
    setSelectedProductKeys(
      samplingsAvailablesQuery.data?.entities
        ?.filter((item) => !item.isAvailable)
        .map((item) => item.id) ?? [],
    );
  }, [samplingsAvailablesQuery.data?.entities]);

  const newSelectedProductKeys = useMemo(() => {
    return selectedProductKeys
      .filter((key) =>
        samplingsAvailablesQuery.data?.entities?.find(
          (x) => x.id === key && x.isAvailable,
        ),
      )
      .map((item) => Number(item));
  }, [samplingsAvailablesQuery.data?.entities, selectedProductKeys]);

  const onModalSubmitClick = useCallback(async () => {
    if (newSelectedProductKeys.length > 0 && isOpen) {
      await createSamplingMutation.mutateAsync({
        projectProductIds: newSelectedProductKeys,
      });

      showNotification({
        type: "success",
        message: `Thêm ${newSelectedProductKeys.length} sản phẩm thành công`,
      });
      setIsOpen(false);
      setSelectedProductKeys([]);
      await samplingsAvailablesQuery.refetch();
      await samplingsQuery.refetch();
    }
  }, [
    createSamplingMutation,
    isOpen,
    newSelectedProductKeys,
    samplingsAvailablesQuery,
    samplingsQuery,
    showNotification,
  ]);

  const onSelectChange = useCallback((newSelectedRowKeys: React.Key[]) => {
    setSelectedProductKeys(newSelectedRowKeys);
  }, []);

  const rowSelection = {
    selectedRowKeys: selectedProductKeys,
    onChange: onSelectChange,
    getCheckboxProps: (record: ProjectProductInterface) => {
      return {
        disabled: !record.isAvailable,
      };
    },
  };

  const handleActionInActiveClick = useCallback(
    (record: CustomerFeatureSamplingInterface) => {
      modal.confirm({
        title: `Ngừng hoạt động dry sampling: ${record.projectProduct.product.name}`,
        content: `Bạn có chắc chắn muốn ngừng hoạt động dry sampling ${record.projectProduct.product.name} này?`,
        okText: "Ngừng hoạt động",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await updateSamplingMutation.mutateAsync({
              id: record.id,
              isActive: false,
            });

            showNotification({
              type: "success",
              message: `Ngừng hoạt động dry sampling ${record.projectProduct.product.name} thành công`,
            });

            await samplingsQuery.refetch();
          } catch (error) {
            console.error(error);

            showNotification({
              type: "error",
              message: `Ngừng hoạt động dry sampling ${record.projectProduct.product.name} thất bại`,
            });
          }
        },
      });
    },
    [modal, samplingsQuery, showNotification, updateSamplingMutation],
  );

  const handleActionActiveClick = useCallback(
    (record: CustomerFeatureSamplingInterface) => {
      modal.confirm({
        title: `Kích hoạt dry sampling: ${record.projectProduct.product.name}`,
        content: `Bạn có chắc chắn muốn kích hoạt dry sampling ${record.projectProduct.product.name} này?`,
        okText: "Kích hoạt",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await updateSamplingMutation.mutateAsync({
              id: record.id,
              isActive: true,
            });

            showNotification({
              type: "success",
              message: `Kích hoạt dry sampling ${record.projectProduct.product.name} thành công`,
            });

            await samplingsQuery.refetch();
          } catch (error) {
            console.error(error);

            showNotification({
              type: "error",
              message: `Kích hoạt dry sampling ${record.projectProduct.product.name} thất bại`,
            });
          }
        },
      });
    },
    [modal, samplingsQuery, showNotification, updateSamplingMutation],
  );

  const handleActionDeleteClick = useCallback(
    (record: CustomerFeatureSamplingInterface) => {
      modal.confirm({
        title: `Xóa dry sampling: ${record.projectProduct.product.name}`,
        content: `Bạn có chắc chắn muốn xóa dry sampling ${record.projectProduct.product.name} này?`,
        okText: "Xóa",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await deleteSamplingMutation.mutateAsync(record.id);

            showNotification({
              type: "success",
              message: `Xóa dry sampling ${record.projectProduct.product.name} thành công`,
            });

            await samplingsQuery.refetch();
            await samplingsAvailablesQuery.refetch();
          } catch (error) {
            console.error(error);

            showNotification({
              type: "error",
              message: `Xóa dry sampling ${record.projectProduct.product.name} thất bại`,
            });
          }
        },
      });
    },
    [
      deleteSamplingMutation,
      modal,
      samplingsAvailablesQuery,
      samplingsQuery,
      showNotification,
    ],
  );

  const actions = [
    {
      key: ConfigSamplingEnum.INACTIVE,
      action: handleActionInActiveClick,
    },
    {
      key: ConfigSamplingEnum.ACTIVE,
      action: handleActionActiveClick,
    },
    {
      key: ConfigSamplingEnum.DELETE,
      action: handleActionDeleteClick,
    },
  ];

  const onSearchSubmit = useCallback(({ keyword }: { keyword: string }) => {
    setFilter((value) => ({ ...value, keyword }));
  }, []);

  const pagination = useMemo(() => {
    return {
      current: currentPage,
      total: samplingsQuery.data?.count ?? 0,
      pageSize: pageSize,
      onChange: (page: number) => {
        setCurrentPage(page);
      },
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: (_current: number, size: number) => {
        setPageSize(size);
        setCurrentPage(DEFAULT_CURRENT_PAGE);
      },
    };
  }, [currentPage, pageSize, samplingsQuery.data?.count]);

  const paginationAvailables = useMemo(() => {
    return {
      current: currentPageAvailables,
      total: samplingsAvailablesQuery.data?.count ?? 0,
      pageSize: pageSizeAvailables,
      onChange: (page: number) => {
        setCurrentPageAvailables(page);
      },
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: (_current: number, size: number) => {
        setPageSizeAvailables(size);
        setCurrentPageAvailables(DEFAULT_CURRENT_PAGE);
      },
    };
  }, [
    currentPageAvailables,
    pageSizeAvailables,
    samplingsAvailablesQuery.data?.count,
  ]);

  const onModalClose = useCallback(() => {
    setIsOpen(false);
    searchForm.resetFields();
    setCurrentPageAvailables(DEFAULT_CURRENT_PAGE);
  }, [searchForm]);

  if (!orderLatestQuery.data?.hasSampling) {
    return (
      <StepLockPage
        title="Chức năng ghi nhận dry sampling phát cho khách"
        description="Chức năng cho phép cấu hình các sản phẩm dry sampling để phát cho khách hàng"
        type={StepLockEnum.Sampling}
        orderLatestQuery={orderLatestQuery}
        locked={!orderLatestQuery.data?.hasSampling}
      />
    );
  }

  return (
    <>
      <Row justify={"space-between"} className={"mb-6"}>
        <Col>
          <Form layout="inline" onFinish={onSearchSubmit}>
            <Form.Item name={"keyword"}>
              <Input
                placeholder="Tìm theo tên, mã hoặc nhãn hàng"
                prefix={<SearchOutlined />}
                style={{ width: "300px" }}
                allowClear
              />
            </Form.Item>

            <Form.Item>
              <Button htmlType="submit">Tìm kiếm</Button>
            </Form.Item>
          </Form>
        </Col>
        <Col>
          <Button
            icon={<PlusOutlined />}
            type="primary"
            onClick={() => setIsOpen(true)}
          >
            Thêm mới
          </Button>
        </Col>
      </Row>

      <Table
        dataSource={samplingsQuery.data?.entities}
        pagination={pagination}
        columns={[
          {
            title: "Tên",
            dataIndex: "projectProduct",
            render: (projectProduct: ProjectProductInterface) => {
              return (
                <ProductItemCell
                  variants={projectProduct.product?.image?.variants ?? []}
                  name={projectProduct.product.name}
                />
              );
            },
          },
          {
            title: "Mã",
            dataIndex: "projectProduct",
            render: (projectProduct: ProjectProductInterface) =>
              projectProduct.product.code,
          },
          {
            title: "Nhãn hàng",
            dataIndex: "projectProduct",
            render: (projectProduct: ProjectProductInterface) =>
              projectProduct.product.brand.name,
          },
          {
            title: "Quy cách ",
            dataIndex: "projectProduct",
            render: (projectProduct: ProjectProductInterface) =>
              projectProduct.productPackaging?.unit.name,
          },
          {
            title: "Tình trạng",
            dataIndex: "isActive",
            key: "isActive",
            render: (value, record, index) => {
              return renderTableCell(value, record, index, "isActive");
            },
          },
          {
            render: (_, record) => (
              <TableActionCell
                actions={actions}
                items={[
                  record.isActive
                    ? {
                        key: ConfigSamplingEnum.INACTIVE,
                        label: (
                          <>
                            <PauseCircleOutlined /> Ngừng hoạt động
                          </>
                        ),
                      }
                    : {
                        key: ConfigSamplingEnum.ACTIVE,
                        label: (
                          <>
                            <PlayCircleOutlined /> Kích hoạt
                          </>
                        ),
                      },
                  {
                    key: ConfigSamplingEnum.DELETE,
                    label: (
                      <>
                        <DeleteOutlined /> Xóa
                      </>
                    ),
                  },
                ]}
                record={record}
              />
            ),
          },
        ]}
      />
      <Modal
        open={isOpen}
        footer={null}
        closeIcon={null}
        width={900}
        styles={{ content: { padding: 0 } }}
      >
        <div className="pl-10 pr-10 pt-3 pb-5">
          <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
            <h2 className="text-neutral-700 text-2xl font-semibold ">
              Thêm dry sampling
            </h2>
            <div className="pt-5">
              <Button
                type="link"
                onClick={onModalClose}
                size="large"
                icon={<CloseOutlined />}
              />
            </div>
          </div>

          <Form
            layout="vertical"
            onFinish={onModalFormFinish}
            form={searchForm}
          >
            <Space>
              <Form.Item name="projectBrandId" label="Nhãn hàng">
                <Select
                  allowClear
                  placeholder="Tất cả"
                  showSearch
                  optionFilterProp="children"
                  filterOption={filterOption}
                  style={{ width: "200px" }}
                  options={projectBrandsQuery.data?.map((projectBrand) => ({
                    label: projectBrand.brand.name,
                    value: projectBrand.id,
                  }))}
                />
              </Form.Item>

              <Form.Item name="keyword" label="Tên sản phẩm">
                <Input placeholder="Nhập tên hoặc mã" allowClear />
              </Form.Item>

              <Form.Item label={" "}>
                <Button htmlType="submit">Tìm kiếm</Button>
              </Form.Item>
            </Space>
          </Form>

          <Table
            className="mt-3"
            rowKey={"id"}
            dataSource={samplingsAvailablesQuery.data?.entities}
            rowSelection={rowSelection}
            pagination={paginationAvailables}
            columns={[
              {
                title: "Tên",
                dataIndex: "product",
                render: (_, record) => {
                  return (
                    <ProductItemCell
                      variants={record?.product.image?.variants ?? []}
                      name={record.product.name}
                      isActive={record.isActive}
                      isAvailable={record.isAvailable}
                    />
                  );
                },
              },
              {
                title: "Mã",
                dataIndex: "product",
                render: (product: ProductInterface) => product.code,
              },
              {
                title: "Nhãn hàng",
                dataIndex: "product",
                render: (product: ProductInterface) => product.brand.name,
              },
              {
                title: "Đơn vị tính",
                dataIndex: "productPackaging",
                render: (productPackaging: ProductPackagingInterface) =>
                  productPackaging.unit.name,
              },
            ]}
          />
        </div>
        <div className="flex justify-end pb-4 pt-4 bg-[#F7F8FA]">
          <Space className="pr-10">
            <Button onClick={onModalClose}>Đóng</Button>
            <Button
              type={"primary"}
              onClick={onModalSubmitClick}
              disabled={newSelectedProductKeys.length === 0}
            >
              Thêm {newSelectedProductKeys.length} item vào chức năng
            </Button>
          </Space>
        </div>
      </Modal>

      {contextHolder}
    </>
  );
}
