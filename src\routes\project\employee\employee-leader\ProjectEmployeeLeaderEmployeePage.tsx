import { useApp } from "@/UseApp";
import { DEFAULT_CURRENT_PAGE, DEFAULT_PAGE_SIZE } from "@/common/constant";
import { filterOption } from "@/common/helper";
import FilterClassicComponent from "@/components/FilterClassicComponent";
import TableActionCell from "@/components/TableActionCell";
import UserOptionComponent from "@/components/UserOptionComponent";
import { renderTableCell } from "@/components/table-cell";
import { UserInterface } from "@/routes/user/interface";
import {
  ArrowLeftOutlined,
  DeleteOutlined,
  PauseCircleOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { Form, Input, Modal, Select, Table } from "antd";
import { ColumnsType } from "antd/es/table";
import _ from "lodash";
import { useCallback, useMemo, useState } from "react";
import { Link, useLocation, useParams } from "react-router-dom";
import { EmployeeActionEnum, ProjectEmployeeUserInterface } from "../interface";
import {
  useDeleteLeaderEmployeeMemberMutation,
  useEmployeeQuery,
  useLeaderEmployeeMembersQuery,
  useUpdateEmployeeMutation,
} from "../service";
import ProjectEmployeeLeaderEmployeeModal from "./modal/ProjectEmployeeLeaderEmployeeModal";

export default function ProjectEmployeeLeaderEmployeePage() {
  const projectId = parseInt(useParams().id ?? "0");
  const employeeId = parseInt(useParams().employeeId ?? "0");

  const location = useLocation();

  const { loading, showNotification } = useApp();

  const [searchForm] = Form.useForm();
  const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [isOpen, setIsOpen] = useState(false);
  const [modal, contextHolder] = Modal.useModal();
  const [filter, setFilter] = useState({});

  const employeeQuery = useEmployeeQuery(projectId, employeeId);
  const leaderEmployeeMembersQuery = useLeaderEmployeeMembersQuery(
    projectId,
    employeeId,
    {
      take: pageSize,
      skip: pageSize * (currentPage - 1),
      ...filter,
    },
  );

  const updateEmployeeMutation = useUpdateEmployeeMutation(projectId);
  const deleteLeaderEmployeeMemberMutation =
    useDeleteLeaderEmployeeMemberMutation(projectId, employeeId);

  const leader: ProjectEmployeeUserInterface =
    location.state || employeeQuery.data;

  const searchContent = (
    <>
      <Form.Item name="keyword">
        <Input
          placeholder="Tìm theo tên, sđt, email, username"
          allowClear
          prefix={<SearchOutlined />}
        />
      </Form.Item>
      <Form.Item name="isActive">
        <Select
          allowClear
          placeholder="Tình trạng"
          optionFilterProp="children"
          filterOption={filterOption}
          options={[
            {
              label: "Đang hoạt động",
              value: "true",
            },
            {
              label: "Ngừng hoạt động",
              value: "false",
            },
          ]}
        />
      </Form.Item>
    </>
  );

  const handlePaginationChange = useCallback(
    (page: number) => {
      setCurrentPage(page);
    },
    [setCurrentPage],
  );

  const handlePageSizeChange = useCallback(
    (_current: number, size: number) => {
      setPageSize(size);
      setCurrentPage(DEFAULT_CURRENT_PAGE);
    },
    [setPageSize, setCurrentPage],
  );

  const pagination = useMemo(() => {
    return {
      current: currentPage,
      total: leaderEmployeeMembersQuery.data?.count,
      pageSize: pageSize,
      onChange: handlePaginationChange,
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: handlePageSizeChange,
    };
  }, [
    currentPage,
    leaderEmployeeMembersQuery.data?.count,
    pageSize,
    handlePaginationChange,
    handlePageSizeChange,
  ]);

  const searchHandler = useCallback(() => {
    const values = searchForm.getFieldsValue();
    if (_.isEqual(filter, values)) {
      leaderEmployeeMembersQuery.refetch();
    }
    setFilter(values);
    setCurrentPage(DEFAULT_CURRENT_PAGE);
  }, [filter, leaderEmployeeMembersQuery, searchForm]);

  const ACTION_ACTIVE = [
    EmployeeActionEnum.INACTIVE,
    EmployeeActionEnum.DELETE,
  ];
  const ACTION_INACTIVE = [
    EmployeeActionEnum.ACTIVE,
    EmployeeActionEnum.DELETE,
  ];

  const actionItems = [
    {
      key: EmployeeActionEnum.INACTIVE,
      label: "Ngừng hoạt động",
      icon: <PauseCircleOutlined />,
    },
    {
      key: EmployeeActionEnum.ACTIVE,
      label: "Khôi phục",
      icon: <PauseCircleOutlined />,
    },
    {
      key: EmployeeActionEnum.DELETE,
      label: "Xóa khỏi quản lý của trưởng nhóm",
      icon: <DeleteOutlined />,
    },
  ];

  const handleActionInActiveClick = (record: ProjectEmployeeUserInterface) => {
    modal.confirm({
      title: `Ngừng hoạt động ${record.role.name}: ${record.user.name}`,
      content: `Bạn có chắc chắn muốn ngừng hoạt động ${record.role.name} này?`,
      okText: "Ngừng hoạt động",
      cancelText: "Hủy",
      onOk: async () => {
        try {
          await updateEmployeeMutation.mutateAsync({
            id: record.id,
            isActive: false,
          });

          showNotification({
            type: "success",
            message: `Ngừng hoạt động (${record.role.name}) ${record.user.name} thành công`,
          });

          leaderEmployeeMembersQuery.refetch();
        } catch (error) {
          console.error(error);

          showNotification({
            type: "error",
            message: `Ngừng hoạt động (${record.role.name}) ${record.user.name} thất bại`,
          });
        }
      },
    });
  };

  const handleActionActiveClick = (record: ProjectEmployeeUserInterface) => {
    modal.confirm({
      title: `Kích hoạt ${record.role.name}: ${record.user.name}`,
      content: `Bạn có chắc chắn muốn kích hoạt ${record.role.name} này?`,
      okText: "Kích hoạt",
      cancelText: "Hủy",
      onOk: async () => {
        try {
          await updateEmployeeMutation.mutateAsync({
            id: record.id,
            isActive: true,
          });

          showNotification({
            type: "success",
            message: `Kích hoạt (${record.role.name}) ${record.user.name} thành công`,
          });

          leaderEmployeeMembersQuery.refetch();
        } catch (error) {
          console.error(error);

          showNotification({
            type: "error",
            message: `Kích hoạt (${record.role.name}) ${record.user.name} thất bại`,
          });
        }
      },
    });
  };

  const handleActionDeleteClick = (record: ProjectEmployeeUserInterface) => {
    modal.confirm({
      title: `Xóa ${record.role.name}: ${record.user.name}`,
      content: `Bạn có chắc chắn muốn xóa ${record.role.name} này?`,
      okText: "Xóa",
      cancelText: "Hủy",
      onOk: async () => {
        try {
          await deleteLeaderEmployeeMemberMutation.mutateAsync(record.id);

          showNotification({
            type: "success",
            message: `Xóa (${record.role.name}) ${record.user.name} thành công`,
          });

          leaderEmployeeMembersQuery.refetch();
        } catch (error) {
          console.error(error);

          showNotification({
            type: "error",
            message: `Xóa (${record.role.name}) ${record.user.name} thất bại`,
          });
        }
      },
    });
  };

  const actionActions = [
    {
      key: EmployeeActionEnum.DELETE,
      action: handleActionDeleteClick,
    },
    {
      key: EmployeeActionEnum.INACTIVE,
      action: handleActionInActiveClick,
    },
    {
      key: EmployeeActionEnum.ACTIVE,
      action: handleActionActiveClick,
    },
  ];

  const columns: ColumnsType<ProjectEmployeeUserInterface> = [
    {
      title: "Nhân viên",
      dataIndex: "user",
      key: "user.name",
      className: "min-w-[100px]",
      render: (value: UserInterface) => {
        return (
          <UserOptionComponent
            avatarUrl={value.picture}
            name={value.name}
            phone={value.phone}
            email={value.email}
            isAvailable={true}
          />
        );
      },
    },
    {
      title: "Username",
      dataIndex: "user",
      key: "user.username",
      className: "min-w-[100px]",
      render: (value: UserInterface) => value?.username,
    },
    {
      title: "Vị trí",
      dataIndex: "role",
      key: "role",
      className: "min-w-[100px]",
      render: renderTableCell,
    },
    {
      title: "Tình trạng",
      className: "min-w-[100px]",
      dataIndex: "isActive",
      key: "isActive",
      render: (
        value: boolean,
        record: ProjectEmployeeUserInterface,
        index: number,
      ) => {
        return renderTableCell(value, record, index, "isActive");
      },
    },
    {
      key: "actions",
      render: (_, record: ProjectEmployeeUserInterface) => {
        const actionKeys = record.isActive ? ACTION_ACTIVE : ACTION_INACTIVE;
        const items = actionItems.filter((item) =>
          actionKeys.includes(item.key),
        );
        return (
          <TableActionCell
            actions={actionActions}
            items={items}
            record={record}
          />
        );
      },
      fixed: "right",
      width: 100,
    },
  ];

  return (
    <>
      <Link to={`/project/${projectId}/employee?tab=${leader.role.name}`}>
        <p className={"text-hint mt-[34px]"}>
          <ArrowLeftOutlined /> Quay lại
        </p>
      </Link>
      <h2>Nhân viên được trưởng nhóm quản lý</h2>
      <div className="bg-white pt-3 pl-10 pr-10 rounded pb-5">
        <h3>Trưởng nhóm</h3>
        <div className="table">
          <div className="border-solid border-2 border-[#C4D6FF] p-2 table-cell bg-[#F0F8FF] rounded">
            <UserOptionComponent
              avatarUrl={leader.user.picture}
              name={leader.user.name}
              phone={leader.user.phone}
              email={leader.user.email}
              roleName={leader.role.name}
            />
          </div>
          &nbsp; &nbsp; &nbsp;
          <div className="border-solid border-2 border-[#C4D6FF] p-2 table-cell bg-[#F0F8FF] rounded pl-5 pr-10">
            <p className="text-[#8C8C8D] m-0">Trực thuộc Agency</p>
            <p className="text-[#393939] m-0">
              {leader.projectAgency?.agency.name}
            </p>
          </div>
        </div>
        <h3>Thành viên trong nhóm</h3>
        <FilterClassicComponent
          showAddButton={true}
          searchHandler={searchHandler}
          searchForm={searchForm}
          content={searchContent}
          className="mb-6"
          handleAddButtonClick={() => {
            setIsOpen(true);
          }}
        />

        <Table
          dataSource={leaderEmployeeMembersQuery.data?.entities}
          columns={columns}
          rowKey={(record) => record.id}
          scroll={{
            x: "max-content",
            y: pagination.total ? "80vh" : undefined,
          }}
          pagination={pagination}
          loading={loading}
        />

        <ProjectEmployeeLeaderEmployeeModal
          isOpen={isOpen}
          setIsOpen={setIsOpen}
          leader={leader}
          projectId={projectId}
          cb={() => {
            leaderEmployeeMembersQuery.refetch();
          }}
        />
      </div>
      {contextHolder}
    </>
  );
}
