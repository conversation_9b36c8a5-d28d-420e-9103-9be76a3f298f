import { ImageInterface } from "@/common/interface";
import { ItemTypeInterface } from "../item-type/interface";
import { UnitInterface } from "../unit/UnitPage";

export interface ItemInterface {
  name: string;
  code: string;
  isActive: boolean;
  id: number;
  image?: ImageInterface;
  itemType?: ItemTypeInterface;
  unit?: UnitInterface;
  itemTypeId: number;
  isAvailable?: boolean;
}

export interface ApiItemResponseInterface {
  entities: ItemInterface[];
  count: number;
}
