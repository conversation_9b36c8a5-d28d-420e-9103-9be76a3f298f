import react from "@vitejs/plugin-react-swc";
import * as path from "path";
import { defineConfig } from "vite";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
  },
  preview: {
    port: 3000,
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src/"),
      "@project": `${path.resolve(__dirname, "./src/routes/project")}`,
      "@location": `${path.resolve(__dirname, "./src/routes/location")}`,
    },
  },
});
