import sortDownBlack from "@/assets/sort-down-black.svg";
import sortDownBlue from "@/assets/sort-down-blue.svg";
import sortUpBlack from "@/assets/sort-up-black.svg";
import sortUpBlue from "@/assets/sort-up-blue.svg";
import { useProvincesQuery } from "@/routes/location/service";
import { Button, Col, Row, Space } from "antd";
import { useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import ChartContanier from "../chart/ChartContanier";
import PopoverCheckbox from "../chart/PopoverCheckbox";
import BarChart from "../chart/chart-types/BarChart";
import { useSitecheckChartByProvinceQuery } from "./service";

const SitecheckPage = () => {
  const [filter, setFilter] = useState<{
    provinceIds?: number[];
    channelIds?: number[];
  }>({ provinceIds: [] });
  const projectId = parseInt(useParams().id ?? "0");

  const [sort, setSort] = useState<"asc" | "desc" | undefined>("desc");

  const provincesQuery = useProvincesQuery();
  const sitecheckChartByProvinceQuery =
    useSitecheckChartByProvinceQuery(projectId);

  const dataChart = useMemo(() => {
    const { datasets, labels } = sitecheckChartByProvinceQuery.data?.data ?? {};
    return (
      datasets?.flatMap((item) => {
        return item.data.map((itemData, index) => ({
          label: labels?.[index] ?? "",
          value: itemData,
          color: item.color,
          type: item.label,
        }));
      }) ?? []
    );
  }, [sitecheckChartByProvinceQuery.data?.data]);

  const filteredDataChart = useMemo(() => {
    if (!filter) return dataChart;
    if (filter.provinceIds?.length === 0) return dataChart;
    return dataChart?.filter((item) => {
      if (
        filter.provinceIds
          ?.map((id) => provincesQuery.data?.find((p) => p.id === id)?.name)
          .includes(item.label)
      )
        return true;

      return false;
    });
  }, [dataChart, filter, provincesQuery.data]);

  const sortedDataChart = useMemo(() => {
    if (!sort) return filteredDataChart;
    if (sort === "asc") {
      filteredDataChart?.sort((a, b) => a["value"] - b["value"]);
      return filteredDataChart;
    } else if (sort === "desc") {
      filteredDataChart?.sort((a, b) => b["value"] - a["value"]);
      return filteredDataChart;
    } else {
      return filteredDataChart;
    }
  }, [filteredDataChart, sort]);

  return (
    <>
      <h2>Dashboard</h2>
      <div className="bg-white px-10 pt-[30px] pb-5 rounded">
        <div className="flex justify-between">
          <p className="text-[18px] font-semibold"></p>

          <Space className="mt-0 pt-0">
            <PopoverCheckbox
              options={
                provincesQuery.data?.map(({ id, name }) => ({
                  label: name,
                  value: id,
                })) ?? []
              }
              buttonTitle="Tỉnh/ TP"
              title="Tìm theo tỉnh"
              placeholder="Nhập tỉnh cần tìm"
              onOkeCb={(values) => {
                setFilter({ ...filter, provinceIds: values });
              }}
            />

            <div className="w-[1px] h-[33px] bg-[#DDE1EA]"></div>

            <Button
              className="px-2"
              onClick={() => {
                if (sort !== "desc") {
                  setSort("desc");
                } else {
                  setSort(undefined);
                }
              }}
            >
              {sort === "desc" ? (
                <img src={sortDownBlue} alt="sort-down-blue" />
              ) : (
                <img src={sortDownBlack} alt="sort-down-black" />
              )}
            </Button>

            <Button
              className="px-2"
              onClick={() => {
                if (sort !== "asc") {
                  setSort("asc");
                } else {
                  setSort(undefined);
                }
              }}
            >
              {sort === "asc" ? (
                <img src={sortUpBlue} alt="sort-down" />
              ) : (
                <img
                  src={sortUpBlack}
                  alt="sort-down"
                  style={{ color: "red" }}
                />
              )}
            </Button>
          </Space>
        </div>

        <Row gutter={16} className="mt-0 pt-0 mb-10">
          <Col md={18}>
            <ChartContanier height="800px">
              <BarChart
                title={`Kết quả sitecheck theo tỉnh`}
                subtitle="Subtitle"
                data={sortedDataChart}
                xField="label"
                yField="value"
                loading={
                  sitecheckChartByProvinceQuery.isLoading ||
                  sitecheckChartByProvinceQuery.isFetching
                }
                colorField="type"
                height={600}
                ratio={sortedDataChart.length > 50 ? 0.1 : 0.5}
              />
            </ChartContanier>
          </Col>
        </Row>
      </div>
    </>
  );
};

export default SitecheckPage;
