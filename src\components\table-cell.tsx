import { isEmpty } from "class-validator";
import dayjs from "dayjs";
import {
  ACTIVE_LABEL,
  DATETIME_FORMAT,
  DATE_FORMAT,
  INACTIVE_LABEL,
} from "../common/constant";

const activeDot = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="10"
    height="10"
    viewBox="0 0 10 10"
    fill="none"
  >
    <circle cx="5" cy="5" r="5" fill="#00CD21" />
  </svg>
);

const inactiveDot = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="10"
    height="10"
    viewBox="0 0 10 10"
    fill="none"
  >
    <circle cx="5" cy="5" r="5" fill="#F92525" />
  </svg>
);

export const renderTableCell = (
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  value: any,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  _record: any,
  _index: number,
  valueType?: "object" | "isActive" | "datetime" | "array" | "date",
  numberArrayItemShow?: number,
  arrayTextMore?: string,
) => {
  if (isEmpty(value)) {
    return "-";
  }

  const type = valueType ?? typeof value;
  switch (type) {
    case "string":
      return value;

    case "number":
      return value.toString();

    case "isActive":
      if (value) {
        return (
          <>
            {activeDot} <span className={"ml-2"}>{ACTIVE_LABEL}</span>
          </>
        );
      } else {
        return (
          <>
            {inactiveDot} <span className={"ml-2"}> {INACTIVE_LABEL}</span>
          </>
        );
      }

    case "object":
      return value.name;

    case "datetime":
      return dayjs(value).format(DATETIME_FORMAT);

    case "array": {
      let rest = 0;

      if (numberArrayItemShow && value.length > numberArrayItemShow) {
        rest = value.length - numberArrayItemShow;
      }

      const arrayItemShow = value
        .slice(0, numberArrayItemShow)
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        .map((item: any) => item.name);

      if (arrayItemShow.length === 0) return "-";

      return (
        <p>
          {arrayItemShow.join(", ")}{" "}
          {rest > 0 ? (
            <span className="text-rest">
              (+{rest} {arrayTextMore})
            </span>
          ) : (
            <></>
          )}
        </p>
      );
    }

    case "date":
      return dayjs(value).format(DATE_FORMAT);

    default:
      return "-";
  }
};
