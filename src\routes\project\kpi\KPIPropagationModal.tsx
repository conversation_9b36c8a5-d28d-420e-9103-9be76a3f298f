import { filterOption } from "@/common/helper";
import CustomModal from "@/components/CustomModal";
import { useApp } from "@/UseApp";
import { ProvinceInterface } from "@location/interface";
import { Checkbox, Col, Radio, Row, Select } from "antd";
import { useCallback, useMemo, useState } from "react";
import { ChannelInterface } from "../../channel/interface";
import { ProjectOutletInterface } from "../outlet/interface";
import {
  ItemKpiTypeEnum,
  PeriodTypeEnum,
  PROPAGATION_MODAL_TYPE_LABELS,
  PropagationModalType,
} from "./interface";
import {
  PropagationKpiMutationDataType,
  usePropagationKpiMutation,
} from "./service";

const KPI_S = [
  {
    type: ItemKpiTypeEnum.SALES_REVENUE,
    label: "KPI doanh số",
  },
  {
    type: ItemKpiTypeEnum.ORDER,
    label: "KPI đơn hàng",
  },
  {
    type: ItemKpiTypeEnum.HIT,
    label: "KPI sampling (hit)",
  },
  {
    type: ItemKpiTypeEnum.SESSION,
    label: "Session",
  },
  {
    type: ItemKpiTypeEnum.SAMPLING,
    label: "Sampling (dry)",
  },
  {
    type: ItemKpiTypeEnum.GIFT,
    label: "Quà thường",
  },
  {
    type: ItemKpiTypeEnum.GAME,
    label: "Quà game",
  },
];

interface KPIPropagationModalProps {
  options: { value: number; label: string }[];
  cancel: () => void;
  isOpen: boolean;
  projectId: number;
  channel?: ChannelInterface;
  province?: ProvinceInterface;
  projectOutlet?: ProjectOutletInterface;
  cb: () => void;
  contentTitle: JSX.Element;
  type: PropagationModalType;
  periodType?: PeriodTypeEnum;
}

const KPIPropagationModal = ({
  options,
  cancel,
  isOpen,
  projectId,
  channel,
  province,
  cb,
  contentTitle,
  type,
  projectOutlet,
  periodType,
}: KPIPropagationModalProps) => {
  const { showNotification } = useApp();

  const [target, setTarget] = useState<"all" | "selected">("all");
  const [kpiSValue, setKpiSValue] = useState<ItemKpiTypeEnum[]>([]);
  const [selectedOptions, setSelectedOptions] = useState<number[]>([]);

  const propagationKpiMutation = usePropagationKpiMutation(projectId);

  const onCancel = useCallback(() => {
    setTarget("all");
    cancel();
  }, [cancel]);

  const isSubmitDisabled = useMemo(() => {
    if (kpiSValue.length === 0) {
      return true;
    }

    if (target === "selected" && selectedOptions.length === 0) {
      return true;
    }

    return false;
  }, [kpiSValue.length, selectedOptions?.length, target]);

  const onConfirm = useCallback(async () => {
    const data: PropagationKpiMutationDataType = {
      sourceKpi: {},
      targetKpis: [],
      targetKpiTypes: kpiSValue,
    };

    if (channel) {
      data.sourceKpi = {
        channelId: channel.id,
      };

      if (target === "all") {
        data.targetKpis = options.map((item) => ({
          channelId: item.value,
        }));
      } else if (target === "selected") {
        data.targetKpis = selectedOptions.map((item) => ({
          channelId: item,
        }));
      }
    }

    if (province) {
      data.sourceKpi = {
        provinceId: province.id,
      };

      if (target === "all") {
        data.targetKpis = options.map((item) => ({
          provinceId: item.value,
        }));
      } else if (target === "selected") {
        data.targetKpis = selectedOptions.map((item) => ({
          provinceId: item,
        }));
      }
    }

    if (projectOutlet) {
      data.sourceKpi = {
        projectOutletId: projectOutlet.id,
        periodType,
      };

      if (target === "all") {
        data.targetKpis = options.map((item) => ({
          projectOutletId: item.value,
          periodType,
        }));
      } else if (target === "selected") {
        data.targetKpis = selectedOptions.map((item) => ({
          projectOutletId: item,
          periodType,
        }));
      }
    }

    await propagationKpiMutation.mutateAsync(data);

    showNotification({
      type: "success",
      message: "Áp dụng KPI thành công",
    });

    cb();
  }, [
    kpiSValue,
    channel,
    province,
    projectOutlet,
    propagationKpiMutation,
    showNotification,
    cb,
    target,
    options,
    selectedOptions,
    periodType,
  ]);

  return (
    <CustomModal
      title={"Áp dụng KPI hàng loạt"}
      isOpen={isOpen}
      confirmText="Áp dụng"
      onCancel={onCancel}
      onConfirm={onConfirm}
      confirmDisable={isSubmitDisabled}
      confirmLoading={propagationKpiMutation.isPending}
      content={
        <>
          {contentTitle}

          <Checkbox
            className="mb-4"
            indeterminate={
              kpiSValue.length > 0 && kpiSValue.length < KPI_S.length
            }
            onChange={(e) => {
              const checked = e.target.checked;
              if (checked) {
                setKpiSValue(KPI_S.map((item) => item.type));
              } else {
                setKpiSValue([]);
              }
            }}
          >
            Tất cả
          </Checkbox>
          <br />
          <Checkbox.Group
            onChange={setKpiSValue}
            value={kpiSValue}
            style={{ width: "100%", display: "flex", flexDirection: "column" }}
          >
            {KPI_S.map(({ type, label }) => (
              <Row key={type} className="w-full mb-4">
                <Col span={24}>
                  <Checkbox key={type} value={type}>
                    {label}
                  </Checkbox>
                </Col>
              </Row>
            ))}
          </Checkbox.Group>

          <p>{PROPAGATION_MODAL_TYPE_LABELS[type]} áp dụng hàng loạt</p>

          <Radio.Group
            onChange={(e) => setTarget(e.target.value)}
            value={target}
          >
            <Radio value="all">Tất cả</Radio>
            <Radio value="selected">
              {PROPAGATION_MODAL_TYPE_LABELS[type]} được chọn
            </Radio>
          </Radio.Group>
          <br />

          {target === "selected" && (
            <Select
              options={options}
              className="w-full mt-4"
              mode="multiple"
              filterOption={filterOption}
              showSearch
              onChange={setSelectedOptions}
            />
          )}
          <div className="mb-8"></div>
        </>
      }
    />
  );
};

export default KPIPropagationModal;
