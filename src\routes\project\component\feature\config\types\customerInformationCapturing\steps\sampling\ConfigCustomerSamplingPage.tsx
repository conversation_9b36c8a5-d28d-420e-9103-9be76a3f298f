import { UseQueryResult } from "@tanstack/react-query";
import { useOutletContext } from "react-router-dom";
import ConfigSamplingPage from "../../../sampling/steps/sampling/ConfigSamplingPage";
import { OrderInterface, StepLockEnum } from "../../interface";
import StepLockPage from "../../StepLockPage";

const ConfigCustomerSamplingPage = () => {
  const [orderLatestQuery]: [UseQueryResult<OrderInterface, unknown>] =
    useOutletContext();

  if (!orderLatestQuery.data?.hasSampling) {
    return (
      <StepLockPage
        title="Chức năng ghi nhận sampling phát cho khách"
        description="Chức năng cho phép cấu hình các sản phẩm sampling để phát cho khách hàng"
        type={StepLockEnum.Sampling}
        orderLatestQuery={orderLatestQuery}
        locked={!orderLatestQuery.data?.hasSampling}
      />
    );
  }

  return <ConfigSamplingPage />;
};

export default ConfigCustomerSamplingPage;
