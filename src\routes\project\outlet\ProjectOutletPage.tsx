import { useApp } from "@/UseApp";
import {
  ACTIVE_LABEL,
  CHUNK_SIZE,
  CURD,
  INACTIVE_LABEL,
} from "@/common/constant";
import { createFileAndDownLoad } from "@/common/export-excel.helper";
import { filterOption } from "@/common/helper";
import CustomTable from "@/components/CustomTable/CustomTable";
import DebounceSelect from "@/components/DebounceSelectComponent";
import FilterClassicComponent from "@/components/FilterClassicComponent";
import InnerContainer from "@/components/InnerContainer/InnerContainer";
import TableActionCell from "@/components/TableActionCell";
import UserOptionComponent from "@/components/UserOptionComponent";
import { renderTableCell } from "@/components/table-cell";
import { useUrlFiltersWithQuery } from "@/hooks/useUrlFiltersWithQuery";
import {
  DeleteOutlined,
  EditOutlined,
  PauseCircleOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import {
  useDistrictsQuery,
  useProvincesQuery,
  useWardsQuery,
} from "@location/service";
import ProjectOutletModal from "@project/outlet/ProjectOutletModal.tsx";
import { useLeaderRoleQuery } from "@project/role/service.ts";
import { Form, Input, Modal, Select, Space, TreeSelect } from "antd";
import { ColumnsType } from "antd/es/table";
import { useCallback, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import { ProjectEmployeeUserInterface } from "../employee/interface";
import { useGetAgencyEmployeeLeadersMutation } from "../employee/service";
import { useProjectAgenciesQuery } from "../general/services";
import {
  ProjectAgencyChannelInterface,
  ProjectAgencyInterface,
} from "../interface";
import { RegionInterface, RegionOption } from "../region/interface";
import { useProjectRegionTreesQuery } from "../region/service";
import ImportOutletModal from "./ImportOutletModal";
import {
  HAS_OVERNIGHT_SHIFT_FALSE_LABEL,
  HAS_OVERNIGHT_SHIFT_TRUE_LABEL,
  ProjectOutletActionEnum,
  ProjectOutletInterface,
} from "./interface";
import {
  useDeleteOutletMutation,
  useGetProjectOutletsMutation,
  useProjectOutletsQuery,
  useUpdateOutletMutation,
} from "./service";

export default function ProjectOutletPage() {
  const projectId = parseInt(useParams().id ?? "0");

  const { showNotification, openDeleteModal } = useApp();

  const [modal, contextHolder] = Modal.useModal();
  const [searchForm] = Form.useForm();
  const [form] = Form.useForm();
  const [action, setAction] = useState<CURD | null>(null);
  const [isShowImport, setIsShowImport] = useState(false);
  const [selectedProvinceId, setSelectedProvinceId] = useState<number | null>(
    null,
  );
  const [selectedDistrictId, setSelectedDistrictId] = useState<number | null>(
    null,
  );
  const [selectedOutlet, setSelectedOutlet] = useState<
    ProjectOutletInterface | undefined
  >(undefined);
  const [exportLoading, setExportLoading] = useState(false);
  const {
    query: { data, refetch, isFetching, isRefetching },
    handleSearch,
    getPaginationProps,
    filter,
  } = useUrlFiltersWithQuery<ProjectOutletInterface>({
    formInstance: searchForm,
    useQueryHook: useProjectOutletsQuery,
    queryParams: [projectId],
    options: {
      urlSync: {
        enabled: false,
      },
      transformations: {
        toFilterValues: {
          leader: (leader) => ({
            leaderId: leader["value"],
          }),
        },
      },
    },
  });

  const provincesQuery = useProvincesQuery();
  const districtsQuery = useDistrictsQuery(selectedProvinceId);
  const wardsQuery = useWardsQuery(selectedProvinceId, selectedDistrictId);
  const projectAgenciesQuery = useProjectAgenciesQuery(projectId);
  const leaderRoleQuery = useLeaderRoleQuery(projectId);
  const projectRegionTreesQuery = useProjectRegionTreesQuery(projectId);

  const updateOutletMutation = useUpdateOutletMutation(projectId, form);
  const getAgencyEmployeeLeadersMutation =
    useGetAgencyEmployeeLeadersMutation(projectId);
  const deleteOutletMutation = useDeleteOutletMutation(projectId);
  const getProjectOutletsMutation = useGetProjectOutletsMutation(projectId);

  const fetchLeaderOptions = useCallback(
    async (keyword?: string) => {
      if (keyword?.length === 0) {
        return [];
      }

      const projectAgencyId = searchForm.getFieldValue("projectAgencyId");

      if (!projectAgencyId) {
        return [];
      }

      const response = await getAgencyEmployeeLeadersMutation.mutateAsync({
        keyword,
        take: 10,
        projectAgencyId,
      });

      if (!Array.isArray(response)) {
        return response.entities.map((item) => ({
          value: item.id,
          label: item.user.name,
          user: item.user,
          isAvailable: true,
        }));
      }
      return [];
    },
    [getAgencyEmployeeLeadersMutation, searchForm],
  );

  const handleSelectProvinceChange = useCallback(
    (value: number) => {
      setSelectedProvinceId(value);
      searchForm.resetFields(["districtId", "wardId"]);
    },
    [searchForm],
  );

  const handleSelectDistrictChange = useCallback(
    (value: number) => {
      setSelectedDistrictId(value);
      searchForm.resetFields(["wardId"]);
    },
    [searchForm],
  );

  const regionOptions = useMemo(() => {
    const getRegions = (regions: RegionInterface[]): RegionOption[] => {
      return regions.map((region: RegionInterface) => ({
        value: region.id,
        label: region.name,
        children: getRegions(region.children || []),
      }));
    };

    return getRegions(projectRegionTreesQuery.data || []);
  }, [projectRegionTreesQuery.data]);

  const ACTION_ACTIVE = [
    ProjectOutletActionEnum.INACTIVE,
    ProjectOutletActionEnum.DELETE,
    ProjectOutletActionEnum.EDIT,
  ];

  const ACTION_INACTIVE = [
    ProjectOutletActionEnum.ACTIVE,
    ProjectOutletActionEnum.DELETE,
    ProjectOutletActionEnum.EDIT,
  ];

  const actionItems = [
    {
      key: ProjectOutletActionEnum.EDIT,
      label: (
        <Space>
          <EditOutlined /> Chỉnh sửa
        </Space>
      ),
    },
    {
      key: ProjectOutletActionEnum.INACTIVE,
      label: (
        <Space>
          <PauseCircleOutlined /> Ngừng hoạt động
        </Space>
      ),
    },
    {
      key: ProjectOutletActionEnum.ACTIVE,
      label: (
        <Space>
          <PauseCircleOutlined /> Khôi phục
        </Space>
      ),
    },
    {
      key: ProjectOutletActionEnum.DELETE,
      label: (
        <Space>
          <DeleteOutlined /> Xóa khỏi dự án
        </Space>
      ),
    },
  ];

  const handleActionEditClick = useCallback(
    (record: ProjectOutletInterface) => {
      setAction(CURD.UPDATE);
      setSelectedOutlet(record);
    },
    [],
  );

  const handleActionInActiveClick = (record: ProjectOutletInterface) => {
    modal.confirm({
      title: `Ngừng hoạt động outlet: ${record.name}`,
      content: `Bạn có chắc chắn muốn ngừng hoạt động outlet ${record.name} này?`,
      okText: "Ngừng hoạt động",
      cancelText: "Hủy",
      onOk: async () => {
        try {
          await updateOutletMutation.mutateAsync({
            id: record.id,
            isActive: false,
          });

          showNotification({
            type: "success",
            message: `Ngừng hoạt động outlet ${record.name} thành công`,
          });

          refetch();
        } catch (error) {
          console.error(error);

          showNotification({
            type: "error",
            message: `Ngừng hoạt động outlet ${record.name} thất bại`,
          });
        }
      },
    });
  };

  const handleActionActiveClick = (record: ProjectOutletInterface) => {
    modal.confirm({
      title: `Kích hoạt outlet: ${record.name}`,
      content: `Bạn có chắc chắn muốn kích hoạt outlet ${record.name} này?`,
      okText: "Kích hoạt",
      cancelText: "Hủy",
      onOk: async () => {
        try {
          await updateOutletMutation.mutateAsync({
            id: record.id,
            isActive: true,
          });

          showNotification({
            type: "success",
            message: `Kích hoạt outlet ${record.name} thành công`,
          });

          refetch();
        } catch (error) {
          console.error(error);

          showNotification({
            type: "error",
            message: `Kích hoạt outlet ${record.name} thất bại`,
          });
        }
      },
    });
  };

  const handleActionDeleteClick = (record: ProjectOutletInterface) => {
    openDeleteModal({
      content: (
        <p>
          Bạn muốn xóa outlet{" "}
          <span className={"font-semibold"}>{record.name}</span> khỏi dự án?
        </p>
      ),
      deleteText: "Xác nhận xóa",
      loading: false,
      onCancel(): void {},
      onDelete: async () => {
        await deleteOutletMutation.mutateAsync(record.id);
        showNotification({
          type: "success",
          message: "Xóa outlet thành công",
        });
        await refetch();
      },
      title: `Xóa outlet`,
      titleError: "Không thể xóa outlet",
      contentHeader: (
        <>
          Không thể xóa outlet{" "}
          <span className="font-semibold">{record.name}</span> khỏi dự án bởi
          vì:
        </>
      ),
    });
  };

  const actionActions = [
    {
      key: ProjectOutletActionEnum.EDIT,
      action: handleActionEditClick,
    },
    {
      key: ProjectOutletActionEnum.INACTIVE,
      action: handleActionInActiveClick,
    },
    {
      key: ProjectOutletActionEnum.ACTIVE,
      action: handleActionActiveClick,
    },
    {
      key: ProjectOutletActionEnum.DELETE,
      action: handleActionDeleteClick,
    },
  ];

  const columns: ColumnsType<ProjectOutletInterface> = [
    {
      title: "Mã outlet",
      dataIndex: "code",
      key: "code",
      className: "min-w-[100px]",
    },
    {
      title: "Tên outlet",
      dataIndex: "name",
      key: "name",
      className: "min-w-[100px]",
    },
    {
      title: "Khu vực",
      dataIndex: "projectRegion",
      key: "projectRegion",
      className: "min-w-[100px]",
      render: renderTableCell,
    },
    {
      title: "Số nhà",
      dataIndex: "houseNumber",
      key: "houseNumber",
      render: renderTableCell,
      className: "min-w-[100px]",
    },
    {
      title: "Tên đường",
      dataIndex: "streetName",
      key: "streetName",
      render: renderTableCell,
      className: "min-w-[100px]",
    },
    {
      title: "Tỉnh/ TP",
      dataIndex: "province",
      key: "province",
      render: renderTableCell,
      className: "min-w-[100px]",
    },
    {
      title: "Quận/ Huyện",
      dataIndex: "district",
      key: "district",
      render: renderTableCell,
      className: "min-w-[100px]",
    },
    {
      title: "Phường/ Xã",
      dataIndex: "ward",
      key: "ward",
      render: renderTableCell,
      className: "min-w-[100px]",
    },
    {
      title: "Loại ca làm việc",
      dataIndex: "hasOvernightShift",
      key: "hasOvernightShift",
      render: (value?: boolean) => {
        return value
          ? HAS_OVERNIGHT_SHIFT_TRUE_LABEL
          : HAS_OVERNIGHT_SHIFT_FALSE_LABEL;
      },
      className: "min-w-[100px]",
    },
    {
      title: "Kênh",
      dataIndex: "projectAgencyChannel",
      key: "projectAgencyChannel",
      render: (value: ProjectAgencyChannelInterface) => value?.channel.name,
      className: "min-w-[100px]",
    },
    {
      title: "Nhóm",
      dataIndex: "subChannel",
      key: "subChannel",
      render: renderTableCell,
      className: "min-w-[100px]",
    },
    {
      title: "Agency phụ trách",
      dataIndex: "projectAgency",
      key: "projectAgency",
      render: (value: ProjectAgencyInterface) => value.agency.name,
      className: "min-w-[100px]",
    },
    {
      title: "Trưởng nhóm quản lý",
      dataIndex: "projectEmployeeUser",
      key: "projectEmployeeUser",
      render: (value?: ProjectEmployeeUserInterface) => {
        const user = value?.user;
        if (!user) return "-";
        return (
          <UserOptionComponent
            avatarUrl={user.picture}
            name={user.name}
            phone={user.phone}
            email={user.email}
            isAvailable={true}
          />
        );
      },
      className: "min-w-[100px]",
    },
    {
      title: "Tọa độ",
      render: (value: ProjectOutletInterface) => {
        if (!value.latitude || !value.longitude) return "-";
        return (
          <a
            href={`https://www.google.com/maps/search/?api=1&query=${value.latitude},${value.longitude}`}
            target="_blank"
            rel="noopener noreferrer"
          >
            (
            {`${Number(value.latitude).toFixed(2)}, ${Number(value.longitude).toFixed(2)}`}
            )
          </a>
        );
      },
      className: "min-w-[100px]",
    },
    {
      title: "Tình trạng",
      dataIndex: "isActive",
      key: "isActive",
      className: "min-w-[100px]",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "isActive");
      },
    },
    {
      key: "actions",
      render: (_, record) => {
        const actionKeys = record.isActive ? ACTION_ACTIVE : ACTION_INACTIVE;
        const items = actionItems.filter((item) =>
          actionKeys.includes(item.key),
        );
        return (
          <TableActionCell
            actions={actionActions}
            items={items}
            record={record}
          />
        );
      },
      fixed: "right",
      width: 100,
    },
  ];

  const searchFormContent = (
    <>
      <Form.Item name="keyword">
        <Input
          prefix={<SearchOutlined />}
          placeholder="Tìm theo mã, tên outlet, số nhà, tên đường"
          allowClear
        />
      </Form.Item>

      <Form.Item name={"projectRegionId"}>
        <TreeSelect
          treeData={regionOptions}
          treeDefaultExpandAll
          placeholder="Phân vùng"
          popupMatchSelectWidth={false}
        />
      </Form.Item>

      <Form.Item name="provinceId">
        <Select
          allowClear
          placeholder="Tỉnh/ TP"
          showSearch
          optionFilterProp="children"
          filterOption={filterOption}
          options={provincesQuery.data?.map((province) => ({
            label: province.name,
            value: province.id,
          }))}
          popupMatchSelectWidth={false}
          onChange={handleSelectProvinceChange}
        />
      </Form.Item>

      <Form.Item name="districtId">
        <Select
          allowClear
          placeholder="Quận/ Huyện"
          showSearch
          optionFilterProp="children"
          filterOption={filterOption}
          options={districtsQuery.data?.map((district) => ({
            label: district.name,
            value: district.id,
          }))}
          popupMatchSelectWidth={false}
          onChange={handleSelectDistrictChange}
        />
      </Form.Item>

      <Form.Item name="wardId">
        <Select
          showSearch
          allowClear
          placeholder="Chọn Phường/Xã"
          optionFilterProp="children"
          filterOption={filterOption}
          popupMatchSelectWidth={false}
          options={wardsQuery.data?.map((ward) => ({
            label: ward.name,
            value: ward.id,
          }))}
        />
      </Form.Item>

      <Form.Item name="projectAgencyId">
        <Select
          placeholder="Agency phụ trách"
          showSearch
          optionFilterProp="children"
          filterOption={filterOption}
          options={projectAgenciesQuery.data?.map((projectAgency) => ({
            label: projectAgency.agency.name,
            value: projectAgency.id,
          }))}
          allowClear
          popupMatchSelectWidth={false}
        />
      </Form.Item>

      <Form.Item name="leader">
        <DebounceSelect
          popupMatchSelectWidth={false}
          placeholder="Trưởng nhóm quản lý"
          allowClear
          showSearch
          fetchOptions={fetchLeaderOptions}
          optionRender={(option) => {
            if (option.data.user) {
              return (
                <UserOptionComponent
                  avatarUrl={option.data.user?.imageUrl}
                  name={option.data.user?.name}
                  phone={option.data.user?.phone}
                  email={option.data.user?.email}
                  key={option.data.user.id}
                />
              );
            }
            return option.label;
          }}
        />
      </Form.Item>
    </>
  );

  const pagination = useMemo(() => getPaginationProps(), [getPaginationProps]);

  const handleExcelButtonClick = useCallback(async () => {
    const total = pagination.total;

    if (!total || total === 0) {
      return;
    }

    const fetchAllData = async () => {
      const requests = [];

      for (let skip = 0; skip < total; skip += CHUNK_SIZE) {
        requests.push(
          getProjectOutletsMutation.mutateAsync({
            take: CHUNK_SIZE,
            skip: skip,
            ...filter,
          }),
        );
      }

      const results = await Promise.all(requests);
      const allEntities = results.flatMap((result) => result.entities);

      return allEntities;
    };

    setExportLoading(true);

    try {
      const allData = await fetchAllData();

      const data = allData.map((projectOutlet) => {
        const {
          code,
          name,
          houseNumber,
          streetName,
          province,
          district,
          ward,
          subChannel,
          projectAgencyChannel,
          projectAgency,
          projectEmployeeUser,
          isActive,
          hasOvernightShift,
          latitude,
          longitude,
        } = projectOutlet;

        return [
          code,
          name,
          houseNumber,
          streetName,
          province.name,
          district.name,
          ward?.name,
          hasOvernightShift
            ? HAS_OVERNIGHT_SHIFT_TRUE_LABEL
            : HAS_OVERNIGHT_SHIFT_FALSE_LABEL,
          projectAgencyChannel.channel.name,
          subChannel?.name,
          projectAgency.agency.name,
          projectEmployeeUser?.user?.phone,
          latitude,
          longitude,
          isActive ? ACTIVE_LABEL : INACTIVE_LABEL,
        ];
      });

      const headers = [
        "Mã outlet",
        "Tên outlet",
        "Số nhà",
        "Tên đường",
        "Tỉnh/ TP",
        "Quận/ Huyện",
        "Phường/ Xã",
        "Loại ca làm việc",
        "Kênh",
        "Nhóm",
        "Agency phụ trách",
        "SĐT trưởng nhóm quản lý outlet này",
        "Latitude",
        "Longitude",
        "Tình trạng",
      ];

      const fileName = `Danh sach outlet trong du an ID ${projectId}`;

      await createFileAndDownLoad({ data, headers, fileName });
    } catch (e) {
      console.error(e);
    } finally {
      setExportLoading(false);
    }
  }, [filter, getProjectOutletsMutation, pagination.total, projectId]);

  const loading = useMemo(
    () => isFetching || isRefetching || exportLoading,
    [exportLoading, isFetching, isRefetching],
  );

  return (
    <div>
      <h2>Outlet trong dự án</h2>
      <InnerContainer>
        <FilterClassicComponent
          searchHandler={handleSearch}
          searchForm={searchForm}
          handleAddButtonClick={function (): void {
            setAction(CURD.CREATE);
          }}
          content={searchFormContent}
          className="mb-6"
          showAddButton
          btnLoading={loading}
          onImportClick={() => {
            setIsShowImport(true);
          }}
          showExportButton
          handleExcelButtonClick={handleExcelButtonClick}
        />

        <CustomTable
          columns={columns}
          dataSource={data?.entities}
          scroll={{
            x: "max-content",
            y: pagination.total ? "80vh" : undefined,
          }}
          loading={loading}
          pagination={pagination}
        />

        <ImportOutletModal
          isOpen={isShowImport}
          setIsOpen={setIsShowImport}
          projectId={projectId}
          cb={() => {
            refetch();
          }}
        />

        {!!action && leaderRoleQuery.data?.id && (
          <ProjectOutletModal
            action={action}
            projectId={projectId}
            cb={() => {
              refetch();
              setAction(null);
              setSelectedOutlet(undefined);
            }}
            leaderRole={leaderRoleQuery.data}
            cancelCb={() => {
              setAction(null);
              setSelectedOutlet(undefined);
            }}
            selectedOutlet={selectedOutlet}
          />
        )}
      </InnerContainer>

      {contextHolder}
    </div>
  );
}
