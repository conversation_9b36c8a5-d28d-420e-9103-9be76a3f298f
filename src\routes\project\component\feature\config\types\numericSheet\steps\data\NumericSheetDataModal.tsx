import { CURD } from "@/common/constant";
import FormNumberInput from "@/components/FormNumberInput";
import ModalCURD from "@/components/ModalCURD";
import { useApp } from "@/UseApp";
import { Col, ColorPicker, Form, Input, Radio, Row } from "antd";
import { useCallback, useEffect, useState } from "react";
import {
  FeatureNumericAttributeInterface,
  FeatureNumericAttributeTypeEnum,
} from "../../interface";
import {
  useCreateNumericAttributeMutation,
  useUpdateNumericAttributeMutation,
} from "../../service";

interface NumericSheetDataModalProps {
  onCancelCb: () => void;
  cb: () => void;
  componentFeatureId: number;
  action: CURD | null;
  selectedNumericAttribute: FeatureNumericAttributeInterface | null;
}

const NumericSheetDataModal = ({
  onCancelCb,
  cb,
  action,
  componentFeatureId,
  selectedNumericAttribute,
}: NumericSheetDataModalProps) => {
  const { showNotification } = useApp();

  const [form] = Form.useForm();
  const [type, setType] = useState<FeatureNumericAttributeTypeEnum | undefined>(
    undefined,
  );

  const createNumericAttributeMutation =
    useCreateNumericAttributeMutation(componentFeatureId);
  const updateNumericAttributeMutation =
    useUpdateNumericAttributeMutation(componentFeatureId);

  const onFinish = useCallback(async () => {
    await form.validateFields();
    const data = form.getFieldsValue();

    const { backgroundColor, foregroundColor } = data;

    data.backgroundColor =
      typeof backgroundColor === "string"
        ? backgroundColor
        : backgroundColor.toHexString();

    data.foregroundColor =
      typeof foregroundColor === "string"
        ? foregroundColor
        : foregroundColor.toHexString();

    if (action === CURD.CREATE) {
      await createNumericAttributeMutation.mutateAsync(data);

      showNotification({
        type: "success",
        message: "Thêm dữ liệu cần ghi nhận thành công",
      });
    }

    if (action === CURD.UPDATE && selectedNumericAttribute) {
      await updateNumericAttributeMutation.mutateAsync({
        id: selectedNumericAttribute.id,
        ...data,
      });

      showNotification({
        type: "success",
        message: "Cập nhật dữ liệu cần ghi nhận thành công",
      });
    }

    cb();
  }, [
    action,
    cb,
    createNumericAttributeMutation,
    form,
    selectedNumericAttribute,
    showNotification,
    updateNumericAttributeMutation,
  ]);

  const formContent = (
    <>
      <Form.Item
        label="Tên dữ liệu cần ghi nhận"
        name={"name"}
        rules={[{ required: true }]}
      >
        <Input />
      </Form.Item>

      <Form.Item
        label={"Loại data nhập liệu"}
        name={"type"}
        rules={[{ required: true }]}
      >
        <Radio.Group
          onChange={(e) => {
            setType(e.target.value);
            form.resetFields(["minimum", "maximum"]);
          }}
        >
          <Radio value={FeatureNumericAttributeTypeEnum.INTEGER}>
            Số nguyên
          </Radio>
          <Radio value={FeatureNumericAttributeTypeEnum.DECIMAL}>
            Số thập phân
          </Radio>
        </Radio.Group>
      </Form.Item>

      <Row gutter={8}>
        <Col md={12}>
          <Form.Item
            label={"Giá trị tối thiểu"}
            name={"minimum"}
            rules={[{ required: true }]}
          >
            <FormNumberInput
              disabled={type === undefined}
              className="w-full"
              hasDot={type === FeatureNumericAttributeTypeEnum.DECIMAL}
            />
          </Form.Item>
        </Col>

        <Col md={12}>
          <Form.Item
            label={"Giá trị tối đa"}
            name={"maximum"}
            rules={[
              { required: true },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue("minimum") <= value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(
                    new Error(
                      "Giá trị tối đa phải lớn hơn hoặc bằng giá trị tối thiểu",
                    ),
                  );
                },
              }),
            ]}
          >
            <FormNumberInput
              className="w-full"
              hasDot={type === FeatureNumericAttributeTypeEnum.DECIMAL}
              disabled={type === undefined}
            />
          </Form.Item>
        </Col>
      </Row>

      <Form.Item
        name={"backgroundColor"}
        label={"Màu nền"}
        layout={"horizontal"}
      >
        <ColorPicker
          showText
          defaultValue={"#FFFFFF"}
          mode={"single"}
          disabledAlpha
          format="hex"
        />
      </Form.Item>

      <Form.Item
        name={"foregroundColor"}
        label={"Màu chữ"}
        layout={"horizontal"}
      >
        <ColorPicker
          showText
          defaultValue={"#000000"}
          disabledAlpha
          format="hex"
        />
      </Form.Item>
    </>
  );

  useEffect(() => {
    if (action === CURD.UPDATE && selectedNumericAttribute) {
      form.setFieldsValue({
        ...selectedNumericAttribute,
      });
    }
  }, [action, form, selectedNumericAttribute]);

  useEffect(() => {
    if (action === CURD.CREATE) {
      form.setFieldsValue({
        backgroundColor: "#FFFFFF",
        foregroundColor: "#000000",
      });
    }
  }, [form, action]);

  return (
    <ModalCURD
      title={"Thêm dữ liệu cần ghi nhận"}
      isOpen={true}
      formContent={formContent}
      form={form}
      onFinish={onFinish}
      onCancelCb={() => {
        form.resetFields();
        onCancelCb();
      }}
      action={action}
    />
  );
};

export default NumericSheetDataModal;
