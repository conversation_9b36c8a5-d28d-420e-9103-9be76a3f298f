import CustomModal from "@/components/CustomModal.tsx";
import { useCanPermission } from "@/layouts/MainLayout/hook.ts";
import { LoadingOutlined, RightOutlined } from "@ant-design/icons";
import { useMemo, useState } from "react";
import { Link } from "react-router-dom";
import {
  ComponentFeatureInterface,
  FeatureTypeEnum,
} from "../component/feature/interface.ts";
import { PermissionEnum } from "../interface.ts";
import { RecordAttendanceInterface } from "../report/types/attendanceClocking/interface.ts";
import { useFeaturesByAttendanceAQuery } from "./service.ts";
import EditAttendanceClockingInModal from "./types/attendanceClockingIn/EditAttendanceClockingInModal.tsx";

interface EditFeatureItemProps {
  projectId: number;
  componentFeature: ComponentFeatureInterface;
  attendance?: RecordAttendanceInterface | null;
  cb?: () => void;
}

const EditFeatureItem = ({
  projectId,
  componentFeature,
  attendance,
  cb,
}: EditFeatureItemProps) => {
  const { name, id, type } = componentFeature;

  const [open, setOpen] = useState(false);

  if (type === FeatureTypeEnum.AttendanceClockingIn) {
    return (
      <>
        <header
          role="banner"
          className="flex items-center border-[1.5px] border-solid border-[#DDE1EA] rounded-lg p-2 cursor-pointer mt-3"
          onClick={() => {
            setOpen(true);
          }}
        >
          <div className="flex items-center justify-between w-full">
            {name}
            <RightOutlined />
          </div>
        </header>

        {open && (
          <EditAttendanceClockingInModal
            setOpen={setOpen}
            attendanceId={attendance?.id}
            cb={() => {
              cb?.();
            }}
            componentFeature={componentFeature}
          />
        )}
      </>
    );
  }

  return (
    <header
      role="banner"
      className="flex items-center border-[1.5px] border-solid border-[#DDE1EA] rounded-lg p-2 cursor-pointer mt-3"
    >
      <Link
        to={`/project/${projectId}/edit/attendance/${attendance?.id}/feature/${id}/${type}`}
        className="w-full text-black hover:text-primary"
      >
        <div className="flex items-center justify-between w-full">
          {name}
          <RightOutlined />
        </div>
      </Link>
    </header>
  );
};

interface ModalEditProps {
  isOpen: boolean;
  projectId: number;
  attendance?: RecordAttendanceInterface | null;
  onCancel: () => void;
  setOpen: (isOpen: boolean) => void;
  cb?: () => void;
}

const ModalEdit = ({
  isOpen,
  projectId,
  attendance,
  onCancel,
  setOpen,
  cb,
}: ModalEditProps) => {
  const { canPermissionFunction } = useCanPermission(projectId);

  const attendanceFeaturesQuery = useFeaturesByAttendanceAQuery(
    projectId,
    attendance?.id,
  );

  const ALLOWED_FEATURES = useMemo(
    () =>
      [
        canPermissionFunction(PermissionEnum.NONE_ACCESS)
          ? FeatureTypeEnum.CustomerInformationCapturing
          : null,
        FeatureTypeEnum.CustomerInformationCapturing,
        FeatureTypeEnum.Sampling,
        FeatureTypeEnum.MultipleEntitiesQuantityCapturing,
        FeatureTypeEnum.AttendanceClockingIn,
        FeatureTypeEnum.NumericSheet,
      ].filter((item) => item !== null),
    [canPermissionFunction],
  );

  if (attendanceFeaturesQuery.isLoading) {
    return <LoadingOutlined />;
  }

  return (
    <CustomModal
      onCancel={onCancel}
      content={
        <div className="mb-5">
          {attendanceFeaturesQuery.data
            ?.filter((componentFeature) =>
              ALLOWED_FEATURES.includes(componentFeature.type),
            )
            .map((componentFeature) => {
              return (
                <EditFeatureItem
                  attendance={attendance}
                  key={componentFeature.id}
                  projectId={projectId}
                  componentFeature={componentFeature}
                  cb={() => {
                    setOpen(false);
                    cb?.();
                  }}
                />
              );
            })}
        </div>
      }
      isOpen={isOpen}
      title={`Chọn dữ liệu cần chỉnh sửa`}
      hideConfirm={true}
    />
  );
};

export default ModalEdit;
