import { useApp } from "@/UseApp";
import { AbstractFilterInterface } from "@/common/interface";
import { useMutation, useQuery } from "@tanstack/react-query";
import { PhotographyInterface } from "./interface";

export const useCreatePhotographyMutation = (componentFeatureId: number) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createPhotography", componentFeatureId],
    mutationFn: (data: {
      name: string;
      description: string;
      minimum: number;
      maximum: number;
      isWatermarkRequired: boolean;
    }) => axiosPost(`/features/${componentFeatureId}/photos`, data),
  });
};

export const useUpdatePhotographyMutation = (componentFeatureId: number) => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["updatePhotography", componentFeatureId],
    mutationFn: (data: {
      id: number;
      name: string;
      description: string;
      minimum: number;
      maximum: number;
      isWatermarkRequired: boolean;
    }) => axiosPatch(`/features/${componentFeatureId}/photos/${data.id}`, data),
  });
};

export const useUpdateIsActivePhotographyMutation = (
  componentFeatureId: number,
) => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["updateIsActivePhotography", componentFeatureId],
    mutationFn: (data: { isActive: boolean; id: number }) =>
      axiosPatch(`/features/${componentFeatureId}/photos/${data.id}`, data),
  });
};

export const useDeletePhotographyMutation = (componentFeatureId: number) => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deletePhotography", componentFeatureId],
    mutationFn: (id: number) =>
      axiosDelete(`/features/${componentFeatureId}/photos/${id}`),
  });
};

export const usePhotographiesQuery = (
  componentFeatureId: number,
  filter?: object & AbstractFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["photographies", componentFeatureId, filter],
    queryFn: () =>
      axiosGet<{ entities: PhotographyInterface[]; count: number }, unknown>(
        `/features/${componentFeatureId}/photos`,
        filter,
      ),
  });
};

export const useArrangementPhotographyMutation = (
  componentFeatureId: number,
) => {
  const { axiosPut } = useApp();

  return useMutation({
    mutationKey: ["arrangementPhotography", componentFeatureId],
    mutationFn: (data: { activeId: number; overId: number }) =>
      axiosPut(
        `/features/${componentFeatureId}/photos/${data.activeId}/arrangement`,
        {
          overFeaturePhotoId: data.overId,
        },
      ),
  });
};
