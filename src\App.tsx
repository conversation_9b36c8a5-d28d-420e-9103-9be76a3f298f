import { useAuth0 } from "@auth0/auth0-react";
import React, { useCallback, useLayoutEffect } from "react";
import "./App.css";
import AppRouter from "./AppRouter";
import LoadingPage from "./LoadingPage";
import { UserInterface, UserTypeEnum } from "./routes/user/interface";
import { useApp } from "./UseApp";

function App() {
  const { isLoading, user } = useAuth0();
  const { axiosGet, setUserLogin } = useApp();

  const checkUserType = useCallback(async () => {
    const basename = import.meta.env.VITE_BASENAME as string;
    let forbiddenPath = "";
    if (basename.endsWith("/")) {
      forbiddenPath = basename + "forbidden";
    } else {
      forbiddenPath = basename + "/forbidden";
    }

    if (window.location.pathname === forbiddenPath) return;

    try {
      const userMe = await axiosGet<UserInterface, unknown>("/users/me");

      if (!userMe) {
        window.location.replace(forbiddenPath);
      }
      setUserLogin(userMe);

      switch (userMe.type) {
        case UserTypeEnum.ADMIN:
          return;
        case UserTypeEnum.AGENCY:
          return;
        case UserTypeEnum.CLIENT:
          return;
        case UserTypeEnum.MANAGER:
          return;
        case UserTypeEnum.EMPLOYEE:
          window.location.replace(forbiddenPath);
          return;
        default:
          window.location.replace(forbiddenPath);
      }
    } catch (e) {
      console.error(e);
    }
  }, [axiosGet, setUserLogin]);

  useLayoutEffect(() => {
    if (user) {
      checkUserType();
    }
  }, [user, checkUserType]);

  if (isLoading) {
    return <LoadingPage />;
  }

  return (
    <React.Suspense>
      <AppRouter />
    </React.Suspense>
  );
}

export default App;
