import { CURD } from "@/common/constant.ts";
import { filterOption } from "@/common/helper.ts";
import { getImageVariants } from "@/common/image.helper.ts";
import { uploadImage } from "@/common/upload-image.helper.ts";
import CreateUnitInSelectComponent from "@/components/CreateUnitInSelectComponent.tsx";
import UploadButton from "@/components/UploadButton.tsx";
import { useApp } from "@/UseApp.tsx";
import { CloseOutlined, LoadingOutlined } from "@ant-design/icons";
import {
  Button,
  ColorPicker,
  Form,
  FormInstance,
  Input,
  Modal,
  Select,
  Upload,
} from "antd";
import React, { useCallback, useEffect, useState } from "react";
import { useClientsQuery } from "../client/service.ts";
import { useItemTypesQuery } from "../item-type/services.ts";
import { useUnitsQuery } from "../unit/service.ts";
import { ItemInterface } from "./interface.ts";

interface ItemModalInterface {
  isOpen: boolean;
  modalTitle: string;
  setIsOpen: (isOpen: boolean) => void;
  form: FormInstance;
  disabledSelectItemType?: boolean;
  formAction: CURD | null;
  callback?: () => void;
  setModalTitle?: (modalTitle: string) => void;
  item?: ItemInterface;
}

export const ItemModal = (props: ItemModalInterface) => {
  const {
    isOpen,
    modalTitle,
    setIsOpen,
    form,
    disabledSelectItemType,
    formAction,
    callback,
    setModalTitle,
    item,
  } = props;

  const { axiosPost, axiosPatch, showNotification, loading, setLoading } =
    useApp();

  const [imageUrl, setImageUrl] = useState<string>();
  const [isLoadingUpload, setIsLoadingUpload] = useState(false);
  const [selectedClientId, setSelectedClientId] = useState<number | undefined>(
    undefined,
  );

  const clientsQuery = useClientsQuery(
    {
      take: 0,
      skip: 0,
      getInActive: true,
    },
    isOpen,
  );
  const unitsQuery = useUnitsQuery(
    {
      take: 0,
      skip: 0,
      clientId: selectedClientId ?? form.getFieldValue("clientId"),
      getInActive: true,
    },
    (!!selectedClientId || !!form.getFieldValue("clientId")) && isOpen,
  );
  const itemTypesQuery = useItemTypesQuery(
    {
      take: 0,
      skip: 0,
      clientId: selectedClientId,
      getInActive: true,
    },
    (!!selectedClientId || !!form.getFieldValue("clientId")) && isOpen,
  );

  useEffect(() => {
    if (item?.image?.variants)
      setImageUrl(getImageVariants(item?.image?.variants ?? [], "public"));
  }, [item]);

  const onCancel = useCallback(() => {
    setIsOpen(false);
    form.resetFields();
    setImageUrl("");
    setSelectedClientId(undefined);
  }, [form, setIsOpen]);

  const handleFormSubmit = useCallback(async () => {
    setLoading(true);
    try {
      await form.validateFields();
      const data = form.getFieldsValue();
      const id = form.getFieldValue("id");
      if (data.imageFile) {
        data.imageId = (await uploadImage(axiosPost, data.imageFile.file))?.id;
        delete data.imageFile;
      }

      const { backgroundColor, foregroundColor } = data;

      data.backgroundColor =
        typeof backgroundColor === "string"
          ? backgroundColor
          : backgroundColor?.toHexString();

      data.foregroundColor =
        typeof foregroundColor === "string"
          ? foregroundColor
          : foregroundColor?.toHexString();

      switch (formAction) {
        case CURD.CREATE:
          await axiosPost(`/items`, data);
          showNotification({
            type: "success",
            message: "Thêm vật phẩm thành công",
          });
          break;
        case CURD.UPDATE:
          await axiosPatch(`/items/${id}`, data);
          showNotification({
            type: "success",
            message: "Cập nhật vật phẩm thành công",
          });
          break;
        default:
          return;
      }

      form.resetFields();
      setSelectedClientId(undefined);
      setIsOpen(false);
      if (setModalTitle) setModalTitle("");
      if (callback) callback();
      setImageUrl("");

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      const data: { message: { field: string; message: string }[] } =
        error?.response?.data;
      const { message } = data;
      form.setFields([
        { name: message[0].field, errors: [message[0].message] },
      ]);
    } finally {
      setLoading(false);
    }
  }, [
    axiosPatch,
    axiosPost,
    callback,
    form,
    formAction,
    setIsOpen,
    setLoading,
    setModalTitle,
    showNotification,
  ]);

  const beforeUpload = useCallback(
    async (file: File) => {
      setIsLoadingUpload(true);
      const imageUrl = URL.createObjectURL(file);
      if (imageUrl) {
        setImageUrl(imageUrl);
      }
      setIsLoadingUpload(false);
      return false;
    },
    [setIsLoadingUpload],
  );

  const selectUnitDropdownRender = (menu: React.ReactElement) => {
    return (
      <CreateUnitInSelectComponent
        menu={menu}
        cb={() => {
          unitsQuery.refetch();
        }}
        clientId={selectedClientId ?? form.getFieldValue("clientId")}
      />
    );
  };

  useEffect(() => {
    if (formAction === CURD.CREATE) {
      form.setFieldsValue({
        backgroundColor: "#FFFFFF",
        foregroundColor: "#000000",
      });
    }
  }, [form, formAction]);

  return (
    <Modal
      open={isOpen}
      footer={null}
      closeIcon={null}
      styles={{ content: { padding: 0 } }}
    >
      <div className={"pl-10 pr-10"}>
        <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
          <h2 className="text-neutral-700 text-2xl font-semibold ">
            {modalTitle}
          </h2>
          <div className="pt-5">
            <Button
              type="link"
              onClick={onCancel}
              size="large"
              icon={<CloseOutlined />}
            />
          </div>
        </div>
      </div>

      <Form
        name="itemForm"
        onFinish={handleFormSubmit}
        layout={"vertical"}
        form={form}
      >
        <div className={"pl-10 pr-10"}>
          <Form.Item name={"id"} hidden={true}></Form.Item>
          <Form.Item name={"imageUrl"} hidden={true}></Form.Item>

          <Form.Item
            name="name"
            label={"Tên vật phẩm"}
            rules={[
              {
                required: true,
                message: "Tên vật phẩm không được để trống",
              },
            ]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="code"
            label={"Mã vật phẩm"}
            rules={[
              {
                required: true,
                message: "Mã vật phẩm không được để trống",
              },
            ]}
          >
            <Input />
          </Form.Item>

          {(() => {
            const clientOptions = clientsQuery.data?.entities
              .filter(
                (client) =>
                  client.isActive ||
                  client.id === selectedClientId ||
                  client.id === form.getFieldValue("clientId"),
              )
              .map((client) => ({
                label: client.name,
                value: client.id,
              }));
            return (
              <Form.Item
                name="clientId"
                label={"Client"}
                rules={[{ required: true }]}
              >
                <Select
                  options={clientOptions}
                  showSearch
                  optionFilterProp="children"
                  filterOption={filterOption}
                  disabled={
                    formAction === CURD.UPDATE || disabledSelectItemType
                  }
                  onSelect={(value) => {
                    setSelectedClientId(value);
                    form.resetFields(["unitId", "itemTypeId"]);
                  }}
                />
              </Form.Item>
            );
          })()}

          <Form.Item noStyle dependencies={["clientId"]}>
            {() => {
              const unitOptions = unitsQuery.data?.entities
                .filter(
                  (unit) =>
                    unit.isActive ||
                    unit.id === selectedClientId ||
                    unit.id === form.getFieldValue("unitId"),
                )
                .map((unit) => ({
                  label: unit.name,
                  value: unit.id,
                }));

              return (
                <Form.Item
                  name="unitId"
                  label={"Đơn vị tính"}
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <Select
                    disabled={formAction === CURD.UPDATE}
                    showSearch
                    optionFilterProp="children"
                    filterOption={filterOption}
                    options={unitOptions}
                    dropdownRender={selectUnitDropdownRender}
                  />
                </Form.Item>
              );
            }}
          </Form.Item>

          <Form.Item noStyle dependencies={["clientId"]}>
            {() => {
              const itemTypeOptions = !form.getFieldValue("clientId")
                ? []
                : (itemTypesQuery.data?.entities
                    .filter(
                      (itemType) =>
                        itemType.isActive ||
                        itemType.id === form.getFieldValue("itemTypeId"),
                    )
                    .map((client) => ({
                      label: client.name,
                      value: client.id,
                    })) ?? []);

              return (
                <Form.Item
                  name="itemTypeId"
                  label={"Loại vật phẩm"}
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <Select
                    showSearch
                    disabled={disabledSelectItemType}
                    optionFilterProp="children"
                    filterOption={filterOption}
                    options={itemTypeOptions}
                  />
                </Form.Item>
              );
            }}
          </Form.Item>

          <Form.Item name="imageFile" label={"Ảnh sản phẩm"}>
            <Upload
              name="avatar"
              listType="picture-card"
              showUploadList={false}
              beforeUpload={beforeUpload}
              accept="image/*"
            >
              {(() => {
                if (isLoadingUpload) {
                  return <LoadingOutlined />;
                }
                if (imageUrl) {
                  return (
                    <img
                      src={imageUrl}
                      alt="avatar"
                      style={{ width: "100%", height: "100%" }}
                    />
                  );
                } else {
                  return <UploadButton loading={isLoadingUpload} />;
                }
              })()}
            </Upload>
          </Form.Item>

          <Form.Item
            name={"backgroundColor"}
            label={"Màu nền"}
            layout={"horizontal"}
          >
            <ColorPicker
              showText
              defaultValue={"#FFFFFF"}
              mode={"single"}
              disabledAlpha
              format="hex"
            />
          </Form.Item>

          <Form.Item
            name={"foregroundColor"}
            label={"Màu chữ"}
            layout={"horizontal"}
          >
            <ColorPicker
              showText
              defaultValue={"#000000"}
              disabledAlpha
              format="hex"
            />
          </Form.Item>
        </div>
        <div className="flex justify-end gap-4 py-4 rounded max-md:max-w-full max-md:flex-wrap pb-4 pl-10 pr-10 bg-[#F7F8FA]">
          <Button htmlType="button" onClick={onCancel}>
            Đóng
          </Button>
          <Button htmlType="submit" type={"primary"} loading={loading}>
            {formAction === CURD.CREATE ? "Thêm mới" : "Cập nhật"}
          </Button>
        </div>
      </Form>
    </Modal>
  );
};
