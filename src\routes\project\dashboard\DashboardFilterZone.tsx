import { DATE_FORMAT } from "@/common/constant";
import { filterOption } from "@/common/helper";
import DebounceSelect from "@/components/DebounceSelectComponent";
import UserOptionComponent from "@/components/UserOptionComponent";
import useDeviceType from "@/hooks/useDeviceType";
import { useProvincesQuery } from "@/routes/location/service";
import { SearchOutlined } from "@ant-design/icons";
import {
  Button,
  Col,
  DatePicker,
  Form,
  FormInstance,
  Modal,
  Row,
  Select,
  TreeSelect,
} from "antd";
import dayjs from "dayjs";
import { Fragment, useCallback, useEffect, useMemo, useState } from "react";
import { useGetEmployeeLeaderMutation } from "../employee/service";
import {
  useGetProjectOutletsMutation,
  useProjectChannelsQuery,
} from "../outlet/service";
import { RegionInterface } from "../region/interface";
import { useProjectRegions } from "../region/service";
import { useProjectQuery } from "../services";
import { DashboardFilterInterface } from "./interface";

const { RangePicker } = DatePicker;

type Field =
  | "region"
  | "chain"
  | "outlet"
  | "booth"
  | "date"
  | "date.single"
  | "province"
  | "leader"
  | "oos.productType"
  | "brand";

type RegionOption = {
  value: number;
  label: string;
  children: RegionOption[];
};

interface DashboardFilterZoneProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  handleApply: (values: any) => void;
  readonly fields?: (Field | undefined)[];
  form?: FormInstance;
  projectId?: number;
  setFilter?: React.Dispatch<React.SetStateAction<DashboardFilterInterface>>;
}
const DashboardFilterZone = ({
  handleApply,
  fields,
  form,
  projectId,
  setFilter,
}: DashboardFilterZoneProps) => {
  const isMobile = useDeviceType();

  const [open, setOpen] = useState(false);

  const provincesQuery = useProvincesQuery();
  const projectChannelsQuery = useProjectChannelsQuery(projectId ?? 0);
  const projectQuery = useProjectQuery(projectId);
  const projectRegions = useProjectRegions(projectId ?? 0);

  const getProjectOutletsMutation = useGetProjectOutletsMutation(
    projectId ?? 0,
  );
  const getEmployeeLeaderMutation = useGetEmployeeLeaderMutation(
    projectId ?? 0,
  );

  useEffect(() => {
    if (setFilter) {
      const filterValue: DashboardFilterInterface = {};

      if (fields?.includes("date")) {
        if (isMobile) {
          const endDate = dayjs();
          const startDate = dayjs(projectQuery.data?.startDate).isAfter(
            endDate.subtract(2, "day"),
          )
            ? dayjs(projectQuery.data?.startDate)
            : endDate.subtract(2, "day");

          filterValue.startDate = startDate.format("YYYY-MM-DD");

          filterValue.endDate = endDate.format("YYYY-MM-DD");

          form?.setFieldsValue({
            date: [startDate, endDate],
          });
        } else {
          filterValue.startDate = dayjs(projectQuery.data?.startDate).format(
            "YYYY-MM-DD",
          );
          filterValue.endDate = dayjs().format("YYYY-MM-DD");

          form?.setFieldsValue({
            date: [dayjs(projectQuery.data?.startDate), dayjs()],
          });
        }
      }

      if (fields?.includes("date.single")) {
        filterValue.startDate = dayjs().format("YYYY-MM-DD");
        filterValue.endDate = dayjs().format("YYYY-MM-DD");

        form?.setFieldsValue({
          dateSingle: dayjs(),
        });
      }

      if (fields?.includes("oos.productType")) {
        filterValue.isMergedProduct = true;
        form?.setFieldsValue({
          isMergedProduct: true,
        });
      }

      setFilter(filterValue);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const regionOptions = useMemo(() => {
    const getRegions = (regions: RegionInterface[]): RegionOption[] => {
      return regions.map((region: RegionInterface) => ({
        value: region.id,
        label: region.name,
        children: getRegions(region.children || []),
      }));
    };

    return getRegions(projectRegions || []);
  }, [projectRegions]);

  const fetchProjectOutletOptions = useCallback(
    async (keyword?: string) => {
      if (keyword?.length === 0) {
        return [];
      }

      const { entities: projectOutlets } =
        await getProjectOutletsMutation.mutateAsync({
          keyword,
          take: 10,
          skip: 0,
        });
      return projectOutlets.map((projectOutlet) => ({
        label: projectOutlet.name,
        value: projectOutlet.id,
      }));
    },
    [getProjectOutletsMutation],
  );

  const fetchLeaderOptions = useCallback(
    async (keyword?: string) => {
      if (keyword?.length === 0) {
        return [];
      }

      const response = await getEmployeeLeaderMutation.mutateAsync({
        keyword,
        take: 10,
      });

      if (!Array.isArray(response)) {
        return response.entities.map((item) => ({
          value: item.id,
          label: item.user.name,
          user: item.user,
          isAvailable: true,
        }));
      }
      return [];
    },
    [getEmployeeLeaderMutation],
  );

  const formFields = useMemo(
    () => [
      {
        key: "region",
        field: (
          <Form.Item name="projectRegionIds">
            <TreeSelect
              treeData={regionOptions}
              style={{ minWidth: 150 }}
              placeholder="Khu vực"
              multiple
              popupMatchSelectWidth={false}
              allowClear
            />
          </Form.Item>
        ),
      },
      {
        key: "province",
        field: (
          <Form.Item name="provinceIds">
            <Select
              allowClear
              placeholder="Tỉnh/ TP"
              showSearch
              optionFilterProp="children"
              filterOption={filterOption}
              options={provincesQuery.data?.map((province) => ({
                label: province.name,
                value: province.id,
              }))}
              popupMatchSelectWidth={false}
              style={{ minWidth: 150 }}
              mode="multiple"
            />
          </Form.Item>
        ),
      },
      {
        key: "chain",
        field: (
          <Form.Item name="channelIds">
            <Select
              allowClear
              placeholder="Chain"
              showSearch
              optionFilterProp="children"
              filterOption={filterOption}
              options={projectChannelsQuery.data?.map((channel) => ({
                label: channel.name,
                value: channel.id,
              }))}
              popupMatchSelectWidth={false}
              mode="multiple"
              style={{ minWidth: 150 }}
            />
          </Form.Item>
        ),
      },
      {
        key: "outlet",
        field: (
          <Form.Item name="projectOutletIds">
            <DebounceSelect
              fetchOptions={fetchProjectOutletOptions}
              showSearch
              labelInValue={false}
              allowClear
              placeholder={"Mã/ Tên outlet"}
              style={{ minWidth: 150 }}
              mode="multiple"
            />
          </Form.Item>
        ),
      },
      {
        key: "leader",
        field: (
          <Form.Item name="leaderIds">
            <DebounceSelect
              popupMatchSelectWidth={false}
              placeholder="Trưởng nhóm quản lý"
              allowClear
              showSearch
              mode="multiple"
              fetchOptions={fetchLeaderOptions}
              optionRender={(option) => {
                if (option.data.user) {
                  return (
                    <UserOptionComponent
                      avatarUrl={option.data.user?.imageUrl}
                      name={option.data.user?.name}
                      phone={option.data.user?.phone}
                      email={option.data.user?.email}
                      key={option.data.user.id}
                    />
                  );
                }
                return option.label;
              }}
              className="min-w-[150px]"
            />
          </Form.Item>
        ),
      },
      {
        key: "booth",
        field: (
          <Form.Item name="booth">
            <Select placeholder="Booth" style={{ minWidth: 150 }} />
          </Form.Item>
        ),
      },
      {
        key: "brand",
        field: (
          <Form.Item name="brand">
            <Select placeholder="Brand" style={{ minWidth: 150 }} />
          </Form.Item>
        ),
      },
      {
        key: "date",
        field: (
          <Form.Item name="date">
            <RangePicker
              style={{ width: "100%" }}
              minDate={dayjs(projectQuery.data?.startDate)}
              maxDate={dayjs()}
              format={DATE_FORMAT}
            />
          </Form.Item>
        ),
      },
      {
        key: "date.single",
        field: (
          <Form.Item name="dateSingle">
            <DatePicker
              style={{ width: "100%" }}
              minDate={dayjs(projectQuery.data?.startDate)}
              maxDate={dayjs()}
              placeholder="Ngày"
              format={DATE_FORMAT}
            />
          </Form.Item>
        ),
      },
      {
        key: "oos.productType",
        field: (
          <Form.Item name={"isMergedProduct"}>
            <Select
              placeholder="Loại sản phẩm"
              allowClear={false}
              options={[
                {
                  label: "Sản phẩm gộp",
                  value: true,
                },
                {
                  label: "Sản phẩm riêng lẻ",
                  value: false,
                },
              ]}
              popupMatchSelectWidth={false}
            />
          </Form.Item>
        ),
      },
    ],
    [
      fetchLeaderOptions,
      fetchProjectOutletOptions,
      projectChannelsQuery.data,
      projectQuery.data?.startDate,
      provincesQuery.data,
      regionOptions,
    ],
  );

  if (isMobile) {
    return (
      <div className="w-full mb-5">
        <Button
          className="w-full bg-input-disabled"
          icon={<SearchOutlined />}
          type="text"
          onClick={() => setOpen(true)}
        >
          Filter
        </Button>
        <Modal
          open={open}
          footer={null}
          onCancel={() => setOpen(false)}
          title={"Filter"}
        >
          <div className="mt-5">
            <Form
              form={form}
              onFinish={(values) => {
                handleApply(values);
                setOpen(false);
              }}
              initialValues={{
                filterField: "all",
              }}
            >
              {fields
                ?.filter((item) => item)
                .map((item) => (
                  <Fragment key={item}>
                    {
                      formFields.find((formFiled) => formFiled.key === item)
                        ?.field
                    }
                  </Fragment>
                ))}

              <Form.Item>
                <Button
                  htmlType="submit"
                  type="default"
                  className="w-full bg-[#F0F1F5]"
                >
                  Tìm kiếm
                </Button>
              </Form.Item>
            </Form>
          </div>
        </Modal>
      </div>
    );
  }

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleApply}
      className="flex flex-wrap gap-4 items-end"
    >
      <Row gutter={16}>
        {fields
          ?.filter((item) => item)
          .map((item) => (
            <Col key={item}>
              {formFields.find((formFiled) => formFiled.key === item)?.field}
            </Col>
          ))}

        <Col>
          <Form.Item>
            <Button type="default" htmlType="submit">
              Apply
            </Button>
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
};

export default DashboardFilterZone;
