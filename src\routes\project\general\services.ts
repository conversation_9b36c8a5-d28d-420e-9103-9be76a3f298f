import { ItemTypeInterface } from "@/routes/item-type/interface.ts";
import { useApp } from "@/UseApp";
import { AbstractFilterInterface } from "@/common/interface";
import { AppContextInterface } from "@/interface.ts";
import { ChannelInterface } from "@/routes/channel/interface.ts";
import { useQuery } from "@tanstack/react-query";
import { UnitInterface } from "../../unit/UnitPage.tsx";
import { ApiProjectEmployeeUserResponseInterface } from "../employee/interface.ts";
import {
  ProjectAgencyChannelInterface,
  ProjectAgencyInterface,
  ProjectBrandInterface,
} from "../interface";

export const getProjectAgencies = async (
  axiosGet: AppContextInterface["axiosGet"],
  projectId: number,
  filter: object & AbstractFilterInterface,
) => {
  return await axiosGet<ProjectAgencyInterface[], unknown>(
    `/projects/${projectId}/agencies`,
    filter,
  );
};

export const getProjectChannels = async (
  axiosGet: AppContextInterface["axiosGet"],
  projectId: number,
) => {
  return await axiosGet<ChannelInterface[], unknown>(
    `projects/${projectId}/channels`,
  );
};

export const getProjectAgenciesByChannel = async (
  axiosGet: AppContextInterface["axiosGet"],
  projectId: number,
  channelId: number,
) => {
  const response = await axiosGet<ProjectAgencyChannelInterface, unknown>(
    `projects/${projectId}/agency-channels/${channelId}/agencies`,
  );

  if (Array.isArray(response)) {
    return response;
  }
  return [];
};

/**
 * Danh sách agencies có trong dự án
 */
export const useProjectAgenciesQuery = (
  projectId: number,
  enabled?: boolean,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["projectAgencies", projectId],
    queryFn: async () =>
      axiosGet<ProjectAgencyInterface[], unknown>(
        `projects/${projectId}/agencies`,
      ),
    enabled,
  });
};

export const useProjectBrandsQuery = (projectId: number, enabled = true) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["projectBrands", projectId],
    queryFn: async () =>
      axiosGet<ProjectBrandInterface[], unknown>(
        `projects/${projectId}/brands`,
      ),
    enabled,
  });
};

export const useProjectChannelQuery = (projectId: number) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["projectChannel", axiosGet, projectId],
    queryFn: () => getProjectChannels(axiosGet, projectId),
  });
};

export const useProjectAgencyChannelQuery = (
  projectId: number,
  channelIdSelected?: number | null,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["projectAgencyChannel", axiosGet, projectId, channelIdSelected],
    queryFn: () =>
      getProjectAgenciesByChannel(axiosGet, projectId, channelIdSelected ?? 0),
    enabled: !!channelIdSelected,
  });
};

export const getAgencyEmployeeLeaders = async (
  axiosGet: AppContextInterface["axiosGet"],
  projectId: number,
  projectAgencyId: number,
  filter: {
    roleId?: number;
    keyword?: string;
  } & AbstractFilterInterface,
) => {
  return await axiosGet<ApiProjectEmployeeUserResponseInterface, unknown>(
    `/projects/${projectId}/agencies/${projectAgencyId}/leaders`,
    filter,
  );
};

export const useProjectUnitsQuery = (
  projectId: number,
  filter?: AbstractFilterInterface,
  enabled?: boolean,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["projectUnits", projectId, filter],
    queryFn: async () =>
      axiosGet<{ entities: UnitInterface[]; count: number }, unknown>(
        `projects/${projectId}/units`,
        filter,
      ),
    enabled,
  });
};

export const useProjectItemTypesQuery = (projectId: number) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["projectItemTypes", projectId],
    queryFn: async () =>
      axiosGet<{ entities: ItemTypeInterface[]; count: number }, unknown>(
        `projects/${projectId}/item-types`,
      ),
  });
};
