import { DEFAULT_PAGE_SIZE } from "@/common/constant";
import { filterOption } from "@/common/helper";
import TableActionCell from "@/components/TableActionCell";
import { CheckSquareOutlined } from "@ant-design/icons";
import { ProvinceInterface } from "@location/interface";
import { Button, Select, Table } from "antd";
import { useCallback, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import {
  ItemKpiTypeEnum,
  KPIScopeEnum,
  ProjectKPIItemInterface,
  PropagationModalType,
} from "../../../interface";
import KPIEditInput from "../../../KPIEditInput";
import KPIModal from "../../../KPIModal";
import KPIPropagationModal from "../../../KPIPropagationModal";
import { useProjectKPIQuery } from "../../../service";

const ProjectProvinceKPIPage = () => {
  const projectId = parseInt(useParams().id ?? "0");

  const [itemKpiType, setItemKpiType] = useState<ItemKpiTypeEnum | undefined>();
  const [modalTitle, setModalTitle] = useState<string | undefined>(undefined);
  const [openedProvince, setOpenedProvince] = useState<
    ProvinceInterface | undefined
  >(undefined);
  const [innerModalTitle, setInnerModalTitle] = useState<JSX.Element>(<></>);
  const [itemsKpiModal, setItemsKpiModal] = useState<ProjectKPIItemInterface[]>(
    [],
  );
  const [isPropagationModalOpen, setIsPropagationModalOpen] =
    useState<boolean>(false);
  /**
   * Dùng làm filter
   */
  const [selectedProvinceId, setSelectedProvinceId] = useState<number | null>(
    null,
  );

  const projectKPIQuery = useProjectKPIQuery(projectId, KPIScopeEnum.PROVINCE);

  const cb = useCallback(() => projectKPIQuery.refetch(), [projectKPIQuery]);

  const dataSource = useMemo(() => {
    if (selectedProvinceId) {
      return (
        projectKPIQuery.data?.filter(
          (item) => item.province?.id === selectedProvinceId,
        ) ?? []
      );
    }
    return projectKPIQuery.data ?? [];
  }, [projectKPIQuery.data, selectedProvinceId]);

  const kpiModalcancel = useCallback(() => {
    setItemKpiType(undefined);
    setModalTitle(undefined);
    setInnerModalTitle(<></>);
    setOpenedProvince(undefined);
    setItemsKpiModal([]);
    projectKPIQuery.refetch();
  }, [projectKPIQuery]);

  const propagationCb = useCallback(() => {
    projectKPIQuery.refetch();
    setIsPropagationModalOpen(false);
    setOpenedProvince(undefined);
  }, [projectKPIQuery]);

  const kpiPropagationModalCancel = useCallback(() => {
    projectKPIQuery.refetch();
    setIsPropagationModalOpen(false);
    setOpenedProvince(undefined);
  }, [projectKPIQuery]);

  return (
    <>
      <Select
        placeholder="Chọn Tỉnh/ TP cần tìm"
        options={
          projectKPIQuery.data?.map((item) => ({
            value: item.province?.id ?? 0,
            label: item.province?.name ?? "",
          })) ?? []
        }
        filterOption={filterOption}
        allowClear
        showSearch
        popupMatchSelectWidth={false}
        onChange={setSelectedProvinceId}
      />
      <Table
        className="mt-6"
        dataSource={dataSource}
        rowKey={(o) => o.province?.id ?? 0}
        columns={[
          {
            title: "Tỉnh/ TP",
            dataIndex: "province",
            render: ({ name }: { name: string }) => name,
          },
          {
            title: "KPI doanh số",
            dataIndex: "salesRevenue",
            align: "right",
            render: (
              salesRevenue: string,
              { province }: { province?: ProvinceInterface; id: number },
            ) => (
              <KPIEditInput
                kpi={salesRevenue ? Number(salesRevenue) : null}
                cb={cb}
                projectId={projectId}
                type={"salesRevenue"}
                provinceId={province?.id}
              />
            ),
          },
          {
            title: "KPI đơn hàng",
            dataIndex: "order",
            align: "right",
            render: (
              order: string,
              { province }: { province?: ProvinceInterface; id: number },
            ) => (
              <KPIEditInput
                kpi={order ? Number(order) : null}
                cb={cb}
                provinceId={province?.id}
                type={"order"}
                projectId={projectId}
              />
            ),
          },
          {
            title: "KPI sampling (hit)",
            dataIndex: "hit",
            align: "right",
            render: (
              hit: string,
              { province }: { province?: ProvinceInterface; id: number },
            ) => (
              <KPIEditInput
                kpi={hit ? Number(hit) : null}
                cb={cb}
                projectId={projectId}
                type={"hit"}
                provinceId={province?.id}
              />
            ),
          },
          {
            title: "KPI session",
            dataIndex: "session",
            align: "right",
            render: (
              session: string,
              { province }: { province?: ProvinceInterface; id: number },
            ) => (
              <KPIEditInput
                kpi={session ? Number(session) : null}
                cb={cb}
                projectId={projectId}
                type={"session"}
                provinceId={province?.id}
              />
            ),
          },
          {
            title: "KPI sampling (dry)",
            align: "right",
            render: (record: {
              id: number;
              province?: ProvinceInterface;
              projectKpiItems: ProjectKPIItemInterface[];
            }) => (
              <Button
                className="text-blue"
                type="link"
                onClick={() => {
                  setItemKpiType(ItemKpiTypeEnum.SAMPLING);
                  setModalTitle("Cài đặt KPI sampling (dry)");
                  setOpenedProvince(record.province);
                  setInnerModalTitle(
                    <>
                      Tỉnh đang chọn:{" "}
                      <span className="text-blue">{record.province?.name}</span>
                    </>,
                  );
                  setItemsKpiModal(record.projectKpiItems);
                }}
              >
                Cài đặt
              </Button>
            ),
          },
          {
            title: "KPI quà thường",
            align: "right",
            render: (record: {
              id: number;
              province?: ProvinceInterface;
              projectKpiItems: ProjectKPIItemInterface[];
            }) => (
              <Button
                className="text-blue"
                type="link"
                onClick={() => {
                  setItemKpiType(ItemKpiTypeEnum.GIFT);
                  setModalTitle("Cài đặt KPI quà thường");
                  setOpenedProvince(record.province);
                  setInnerModalTitle(
                    <>
                      Tỉnh đang chọn:{" "}
                      <span className="text-blue">{record.province?.name}</span>
                    </>,
                  );
                  setItemsKpiModal(record.projectKpiItems);
                }}
              >
                Cài đặt
              </Button>
            ),
          },
          {
            title: "KPI quà game",
            align: "right",
            render: (record: {
              id: number;
              province?: ProvinceInterface;
              projectKpiItems: ProjectKPIItemInterface[];
            }) => (
              <Button
                className="text-blue"
                type="link"
                onClick={() => {
                  setItemKpiType(ItemKpiTypeEnum.GAME);
                  setModalTitle("Cài đặt KPI quà game");
                  setOpenedProvince(record.province);
                  setInnerModalTitle(
                    <>
                      Tỉnh đang chọn:{" "}
                      <span className="text-blue">{record.province?.name}</span>
                    </>,
                  );
                  setItemsKpiModal(record.projectKpiItems);
                }}
              >
                Cài đặt
              </Button>
            ),
          },
          {
            render: (
              _,
              record: { id: number; province?: ProvinceInterface },
            ) => (
              <TableActionCell
                actions={[
                  {
                    key: "apply",
                    action: (record: {
                      province?: ProvinceInterface;
                      id: number;
                    }) => {
                      setIsPropagationModalOpen(true);
                      setOpenedProvince(record.province);
                    },
                  },
                ]}
                items={[
                  {
                    label: "Áp dụng KPI này cho các tỉnh/TP còn lại",
                    icon: <CheckSquareOutlined />,
                    key: "apply",
                  },
                ]}
                record={record}
              />
            ),
          },
        ]}
        pagination={{ defaultPageSize: DEFAULT_PAGE_SIZE }}
      />

      {itemKpiType && openedProvince && modalTitle && (
        <KPIModal
          isOpen={true}
          title={innerModalTitle}
          itemKpiType={itemKpiType}
          cancel={kpiModalcancel}
          projectId={projectId}
          modalTitle={modalTitle}
          items={itemsKpiModal}
          cb={cb}
          provinceId={openedProvince?.id}
        />
      )}

      {isPropagationModalOpen && openedProvince && (
        <KPIPropagationModal
          options={
            projectKPIQuery.data
              ?.filter((item) => item?.province?.id !== openedProvince?.id)
              .map((item) => ({
                value: item.province?.id ?? 0,
                label: item.province?.name ?? "",
              })) ?? []
          }
          isOpen={isPropagationModalOpen}
          cancel={kpiPropagationModalCancel}
          projectId={projectId}
          cb={propagationCb}
          province={openedProvince}
          contentTitle={
            <>
              <p>
                Tỉnh đang chọn:{" "}
                <span className="text-blue font-semibold">
                  {openedProvince.name}
                </span>
              </p>
              <p>Chọn loại KPI cần áp dụng hàng loạt</p>
            </>
          }
          type={PropagationModalType.PROVINCE}
        />
      )}
    </>
  );
};

export default ProjectProvinceKPIPage;
