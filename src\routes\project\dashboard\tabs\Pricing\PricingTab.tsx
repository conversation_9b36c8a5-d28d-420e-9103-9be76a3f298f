import useDeviceType from "@/hooks/useDeviceType";
import ChartContanier from "@/routes/project/chart/ChartContanier";
import { Line } from "@ant-design/charts";
import { useCallback } from "react";
import DashboardFilterZone from "../../DashboardFilterZone";

const PricingTab = () => {
  const isMobile = useDeviceType();

  const handleApply = useCallback(() => {}, []);

  const data = [
    // Tiger Regular
    { week: "W51", product: "Tiger Regular", price: 385 },
    { week: "W52", product: "Tiger Regular", price: 390 },
    { week: "W1", product: "Tiger Regular", price: 389 },
    { week: "W2", product: "Tiger Regular", price: 390 },
    { week: "W3", product: "Tiger Regular", price: 387 },
    { week: "W4", product: "Tiger Regular", price: 395 },
    { week: "W5", product: "Tiger Regular", price: 387 },

    // Tiger Crystal
    { week: "W51", product: "Tiger Crystal", price: 385 },
    { week: "W52", product: "Tiger Crystal", price: 388 },
    { week: "W1", product: "Tiger Crystal", price: 387 },
    { week: "W2", product: "Tiger Crystal", price: 386 },
    { week: "W3", product: "Tiger Crystal", price: 382 },
    { week: "W4", product: "Tiger Crystal", price: 385 },
    { week: "W5", product: "Tiger Crystal", price: 390 },

    // Larue Blue
    { week: "W51", product: "Larue Blue", price: 350 },
    { week: "W52", product: "Larue Blue", price: 355 },
    { week: "W1", product: "Larue Blue", price: 355 },
    { week: "W2", product: "Larue Blue", price: 356 },
    { week: "W3", product: "Larue Blue", price: 354 },
    { week: "W4", product: "Larue Blue", price: 352 },
    { week: "W5", product: "Larue Blue", price: 359 },

    // Larue Special
    { week: "W51", product: "Larue Special", price: 335 },
    { week: "W52", product: "Larue Special", price: 337 },
    { week: "W1", product: "Larue Special", price: 336 },
    { week: "W2", product: "Larue Special", price: 334 },
    { week: "W3", product: "Larue Special", price: 338 },
    { week: "W4", product: "Larue Special", price: 340 },
    { week: "W5", product: "Larue Special", price: 343 },

    // Sài gòn chill 24 lon
    { week: "W51", product: "Sài gòn chill 24 lon", price: 288 },
    { week: "W52", product: "Sài gòn chill 24 lon", price: 290 },
    { week: "W1", product: "Sài gòn chill 24 lon", price: 289 },
    { week: "W2", product: "Sài gòn chill 24 lon", price: 285 },
    { week: "W3", product: "Sài gòn chill 24 lon", price: 290 },
    { week: "W4", product: "Sài gòn chill 24 lon", price: 292 },
    { week: "W5", product: "Sài gòn chill 24 lon", price: 295 },

    // Sài gòn larger 24 lon
    { week: "W51", product: "Sài gòn larger 24 lon", price: 266 },
    { week: "W52", product: "Sài gòn larger 24 lon", price: 268 },
    { week: "W1", product: "Sài gòn larger 24 lon", price: 270 },
    { week: "W2", product: "Sài gòn larger 24 lon", price: 265 },
    { week: "W3", product: "Sài gòn larger 24 lon", price: 272 },
    { week: "W4", product: "Sài gòn larger 24 lon", price: 278 },
    { week: "W5", product: "Sài gòn larger 24 lon", price: 275 },

    // Saigon Special
    { week: "W51", product: "Saigon Special", price: 254 },
    { week: "W52", product: "Saigon Special", price: 254 },
    { week: "W1", product: "Saigon Special", price: 251 },
    { week: "W2", product: "Saigon Special", price: 256 },
    { week: "W3", product: "Saigon Special", price: 254 },
    { week: "W4", product: "Saigon Special", price: 255 },
    { week: "W5", product: "Saigon Special", price: 260 },
  ];

  const config = {
    data,
    xField: "week",
    yField: "price",
    colorField: "product",
    legend: {
      color: {
        position: isMobile ? "bottom" : "left",
        layout: {
          justifyContent: "center",
          alignItems: "center",
          flexDirection: "column",
        },
      },
    },
    point: {
      size: 5,
      shape: "circle",
      style: {
        fill: "white",
        stroke: "#5B8FF9",
        lineWidth: 2,
      },
    },
    title: {
      title: "Pricing",
      subtitle: "Ngàn VNĐ",
    },
    label: {
      textBaseline: "bottom",
      textAlign: "center",
      style: {
        dy: -5,
      },
    },
    tooltip: { channel: "y" },
  };

  return (
    <>
      <DashboardFilterZone
        handleApply={handleApply}
        fields={["region", "province", "chain", "brand", "date"]}
      />

      <ChartContanier>
        <Line {...config} height={600} />
      </ChartContanier>
    </>
  );
};

export default PricingTab;
