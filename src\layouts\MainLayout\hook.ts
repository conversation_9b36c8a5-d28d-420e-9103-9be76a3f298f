import { UserTypeEnum } from "@/routes/user/interface";
import { useApp } from "@/UseApp";
import { PermissionEnum } from "@project/interface";
import { useProjectQuery } from "@project/services";
import { useCallback, useMemo } from "react";

export const useCanPermission = (projectId: number) => {
  const { userLogin } = useApp();
  const { data, isLoading, isRefetching, isFetching } =
    useProjectQuery(projectId);

  const userPermissions = useMemo(() => {
    const clientPermissions =
      data?.projectClientUsers
        ?.filter((item) => item.userId === userLogin?.id)
        ?.flatMap((item) => item.permission) ?? [];

    const agencyPermissions =
      data?.projectAgencyUsers
        ?.filter((item) => item.userId === userLogin?.id)
        ?.flatMap((item) => item.permission) ?? [];

    return [...clientPermissions, ...agencyPermissions];
  }, [data?.projectAgencyUsers, data?.projectClientUsers, userLogin?.id]);

  const canPermissionFunction = useCallback(
    (permission: PermissionEnum) => {
      if (userLogin?.type === UserTypeEnum.ADMIN) {
        return true;
      }

      return userPermissions?.includes(permission);
    },
    [userLogin?.type, userPermissions],
  );

  return {
    canPermissionFunction,
    isLoading: isLoading || isRefetching || isFetching,
  };
};
