import { useQuery } from "@tanstack/react-query";
import { useApp } from "@/UseApp";
import { AppContextInterface } from "@/interface.ts";
import { ApiRoleResponseInterface } from "./interface";

export const getProjectRoles = async (
  axiosGet: AppContextInterface["axiosGet"],
  projectId: number,
  filter: unknown,
) => {
  return await axiosGet<ApiRoleResponseInterface, unknown>(
    `/projects/${projectId}/roles`,
    filter,
  );
};

export const getLeaderRole = async (
  axiosGet: AppContextInterface["axiosGet"],
  projectId: number,
) => {
  const response = await axiosGet<ApiRoleResponseInterface, unknown>(
    `/projects/${projectId}/roles`,
    { type: "EMPLOYEE" },
  );

  if (!Array.isArray(response)) {
    const { entities } = response;
    return entities.find((item) => item.isLeader) ?? null;
  }
  return null;
};

export const useProjectRolesQuery = (
  projectId: number,
  filter: { type: "" | "EMPLOYEE" | "AGENCY" } = { type: "" },
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["projectRoles", projectId],
    queryFn: () =>
      axiosGet<ApiRoleResponseInterface, unknown>(
        `/projects/${projectId}/roles`,
        filter,
      ),
  });
};

export const useLeaderRoleQuery = (projectId: number) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["leaderRole", projectId],
    queryFn: () => getLeaderRole(axiosGet, projectId),
  });
};
