import TableActionCell from "@/components/TableActionCell";
import { FileSearchOutlined, PlusOutlined } from "@ant-design/icons";
import { Table } from "antd";
import { useCallback, useState } from "react";
import { useParams } from "react-router-dom";
import { FeatureNumericSheetInterface } from "../../interface";
import { useNumericSheetsQuery } from "../../service";
import NumericSheetOutletAddModal from "./NumericSheetOutletAddModal";
import NumericSheetOutletViewModal from "./NumericSheetOutletViewModal";

const NumericSheetOutletPage = () => {
  const projectId = parseInt(useParams().id ?? "0");
  const componentFeatureId = parseInt(useParams().componentFeatureId ?? "0");

  const [isAddOpen, setIsAddOpen] = useState(false);
  const [isViewOpen, setIsViewOpen] = useState(false);
  const [selectedFeatureNumericSheet, setSelectedFeatureNumericSheet] =
    useState<FeatureNumericSheetInterface | undefined>(undefined);

  const numericSheetsQuery = useNumericSheetsQuery(componentFeatureId, {
    take: 0,
  });

  const addOutlet = useCallback((record: FeatureNumericSheetInterface) => {
    setIsAddOpen(true);
    setSelectedFeatureNumericSheet(record);
  }, []);

  const viewOutlet = useCallback((record: FeatureNumericSheetInterface) => {
    setSelectedFeatureNumericSheet(record);
    setIsViewOpen(true);
  }, []);

  return (
    <>
      <Table
        dataSource={numericSheetsQuery.data?.entities}
        rowKey={"id"}
        columns={[
          {
            title: "Tên nhóm item",
            dataIndex: "name",
            className: "min-w-[100px]",
          },
          {
            title: "Outlet đã phân bổ",
            dataIndex: "featureNumericSheetOutletsCount",
            align: "right",
            className: "min-w-[100px]",
          },
          {
            render: (record) => {
              return (
                <TableActionCell
                  actions={[
                    {
                      key: "add",
                      action: addOutlet,
                    },
                    {
                      key: "view",
                      action: viewOutlet,
                    },
                  ]}
                  items={[
                    {
                      key: "add",
                      label: "Thêm outlet vào nhóm",
                      icon: <PlusOutlined />,
                    },
                    {
                      key: "view",
                      label: "Danh sách outlet trong nhóm",
                      icon: <FileSearchOutlined />,
                    },
                  ]}
                  record={record}
                />
              );
            },
          },
        ]}
        pagination={false}
      />

      {isAddOpen && selectedFeatureNumericSheet && (
        <NumericSheetOutletAddModal
          isOpen={isAddOpen}
          selectedFeatureNumericSheet={selectedFeatureNumericSheet}
          projectId={projectId}
          componentFeatureId={componentFeatureId}
          onCancelCb={() => {
            setIsAddOpen(false);
            setSelectedFeatureNumericSheet(undefined);
            numericSheetsQuery.refetch();
          }}
        />
      )}

      {isViewOpen && selectedFeatureNumericSheet && (
        <NumericSheetOutletViewModal
          isOpen={isViewOpen}
          projectId={projectId}
          componentFeatureId={componentFeatureId}
          selectedFeatureNumericSheet={selectedFeatureNumericSheet}
          onCancelCb={() => {
            setIsViewOpen(false);
            setSelectedFeatureNumericSheet(undefined);
            numericSheetsQuery.refetch();
          }}
        />
      )}
    </>
  );
};
export default NumericSheetOutletPage;
