import { CURD } from "@/common/constant.ts";
import { EditOutlined, LoadingOutlined, PlusOutlined } from "@ant-design/icons";
import { OosGroupInterface } from "@project/component/feature/config/types/outOfStockStatus/interface.ts";
import OutOfStockStatusGroupModal from "@project/component/feature/config/types/outOfStockStatus/steps/group/OutOfStockStatusGroupModal.tsx";
import { Button, Collapse } from "antd";
import { CollapseProps } from "antd/lib";
import { useCallback, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import OutOfStockStatusGroupCollapseChild from "./OutOfStockStatusGroupCollapseChild";
import { useOosGroupsQuery } from "./service";

const OutOfStockStatusGroupPage = () => {
  const componentFeatureId = parseInt(useParams().componentFeatureId ?? "0");

  const [action, setAction] = useState<CURD | null>(null);
  const [selectedOosGroup, setSelectedOosGroup] = useState<
    OosGroupInterface | undefined
  >(undefined);

  const oosGroupsQuery = useOosGroupsQuery(componentFeatureId, {
    take: 0,
  });

  const onAddGroup = useCallback(() => {
    setAction(CURD.CREATE);
  }, []);

  const items: CollapseProps["items"] = useMemo(() => {
    return oosGroupsQuery.data?.entities.map((oosGroup) => ({
      key: oosGroup.id,
      label: (
        <>
          <span className={"font-semibold text-primary mr-3"}>
            {oosGroup.name}
          </span>

          <EditOutlined
            className="cursor-pointer"
            onClick={() => {
              setAction(CURD.UPDATE);
              setSelectedOosGroup(oosGroup);
            }}
          />
        </>
      ),
      children: (
        <OutOfStockStatusGroupCollapseChild
          componentFeatureId={componentFeatureId}
          oosGroup={oosGroup}
        />
      ),
    }));
  }, [componentFeatureId, oosGroupsQuery.data?.entities]);

  return (
    <>
      <div className={"flex justify-end"}>
        <Button type={"primary"} icon={<PlusOutlined />} onClick={onAddGroup}>
          Thêm mới nhóm sản phẩm
        </Button>
      </div>

      {(() => {
        if (oosGroupsQuery.isLoading)
          return (
            <p>
              <LoadingOutlined />
            </p>
          );
        return (
          <Collapse
            defaultActiveKey={[oosGroupsQuery.data?.entities[0]?.id ?? ""]}
            ghost
            items={items}
            destroyInactivePanel
            collapsible="icon"
          />
        );
      })()}

      {action && (
        <OutOfStockStatusGroupModal
          cancelCb={() => setAction(null)}
          componentFeatureId={componentFeatureId}
          cb={() => {
            setAction(null);
            oosGroupsQuery.refetch();
          }}
          action={action}
          selectedFeatureOosGroup={selectedOosGroup}
        />
      )}
    </>
  );
};

export default OutOfStockStatusGroupPage;
