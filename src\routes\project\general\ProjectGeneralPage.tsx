import { CURD, DATE_FORMAT } from "@/common/constant";
import { filterOption, formErrorResponseHandler } from "@/common/helper";
import { allowUserType } from "@/common/user-type.helper.ts";
import InnerContainer from "@/components/InnerContainer/InnerContainer.tsx";
import ModalCURD from "@/components/ModalCURD";
import { renderTableCell } from "@/components/table-cell";
import TableActionCell from "@/components/TableActionCell";
import { useApp } from "@/UseApp";
import { endDateAntFormFieldValidate } from "@/validates/end-date-ant-form-field.validate.ts";
import {
  DeleteOutlined,
  EditOutlined,
  EllipsisOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import {
  Alert,
  Button,
  Col,
  DatePicker,
  Dropdown,
  Form,
  Input,
  MenuProps,
  Row,
  Select,
  Table,
} from "antd";
import { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";
import _ from "lodash";
import { useCallback, useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { useAgenciesQuery } from "../../agency/services";
import { useBrandsQuery } from "../../brand/services";
import { useChannelsQuery } from "../../channel/services";
import {
  ProjectAgencyChannelInterface,
  ProjectAgencyInterface,
  ProjectBrandInterface,
} from "../interface";
import { useProjectQuery } from "../services.ts";
import { getProjectAgencies } from "./services";

export default function ProjectGeneralPage() {
  const projectId = parseInt(useParams().id ?? "0");

  const {
    loading,
    setLoading,
    axiosGet,
    axiosPost,
    showNotification,
    axiosPatch,
    axiosDelete,
    userLogin,
    openDeleteModal,
  } = useApp();

  const [generalForm] = Form.useForm();
  const [projectAgencies, setProjectAgencies] = useState<
    ProjectAgencyInterface[]
  >([]);
  const [projectBrands, setProjectBrands] = useState<ProjectBrandInterface[]>(
    [],
  );
  const [isModalAgencyOpen, setIsModalAgencyOpen] = useState(false);
  const [agencyForm] = Form.useForm();
  const [formAction, setFormAction] = useState<CURD | null>(null);
  const [isModalBrandOpen, setIsModalBrandOpen] = useState(false);
  const [brandForm] = Form.useForm();
  const [selectedProjectAgency, setSelectedProjectAgency] = useState<
    ProjectAgencyInterface | undefined
  >(undefined);

  const projectQuery = useProjectQuery(projectId);
  const channelsQuery = useChannelsQuery({
    take: 0,
    skip: 0,
    clientId: projectQuery.data?.client?.id,
  });
  const agenciesQuery = useAgenciesQuery({
    take: 0,
    skip: 0,
    isActive: true,
  });
  const brandsQuery = useBrandsQuery({
    take: 0,
    skip: 0,
    clientId: projectQuery.data?.client?.id,
  });

  const fetchProjectAgencies = useCallback(async () => {
    setLoading(true);
    try {
      const response = await getProjectAgencies(axiosGet, projectId, {
        take: 0,
        skip: 0,
      });
      setProjectAgencies(response);
    } catch (e) {
      console.log("fetchAgencies: ", e);
    } finally {
      setLoading(false);
    }
  }, [setLoading, axiosGet, projectId]);

  const fetchProjectBrands = useCallback(async () => {
    setLoading(true);
    try {
      const response = await axiosGet<ProjectBrandInterface, unknown>(
        `/projects/${projectId}/brands`,
        { take: 0, skip: 0 },
      );
      if (Array.isArray(response)) {
        setProjectBrands(response);
      }
    } catch (e) {
      console.log("fetchBrands: ", e);
    } finally {
      setLoading(false);
    }
  }, [axiosGet, projectId, setLoading]);

  useEffect(() => {
    fetchProjectAgencies();
    fetchProjectBrands();
  }, [fetchProjectAgencies, fetchProjectBrands]);

  const agencyColumns: ColumnsType<ProjectAgencyInterface> = [
    {
      title: "Agency tham gia",
      key: "agency",
      dataIndex: "agency",
      render: renderTableCell,
    },
    {
      title: "Kênh thực hiện",
      key: "projectAgencyChannels",
      dataIndex: "projectAgencyChannels",
      render: (value: ProjectAgencyChannelInterface[]) => {
        const channels = value.map((item) => item.channel.name);
        return channels.join(", ");
      },
    },
    {
      key: "actions",
      render: (_, record) => {
        const handleMenuClick: MenuProps["onClick"] = (e) => {
          if (e.key === "EDIT") {
            setSelectedProjectAgency(record);
            setIsModalAgencyOpen(true);
            setFormAction(CURD.UPDATE);
            agencyForm.setFieldsValue({
              agencyId: record.agency.id,
              channelIds: record.projectAgencyChannels.map(
                (item) => item.channel.id,
              ),
              id: record.id,
            });
          }

          if (e.key === "DELETE") {
            openDeleteModal({
              content: (
                <p>
                  Bạn muốn xóa agency{" "}
                  <span className={"font-semibold"}>{record.agency.name}</span>{" "}
                  khỏi dự án?
                </p>
              ),
              deleteText: "Xác nhận xóa",
              loading: false,
              onCancel(): void {},
              onDelete: async () => {
                await axiosDelete(
                  `/projects/${projectId}/agencies/${record.id}`,
                );
                showNotification({
                  type: "success",
                  message: `Xóa agency ${record.agency.name} thành công.`,
                });

                await fetchProjectAgencies();
              },
              title: `Xóa agency`,
              titleError: "Không thể xóa agency",
              contentHeader: (
                <>
                  Không thể xóa agency{" "}
                  <span className="font-semibold">{record.agency.name}</span>{" "}
                  bởi vì:
                </>
              ),
            });
          }
        };

        const items: MenuProps["items"] = [
          {
            label: "Chỉnh sửa",
            key: "EDIT",
            icon: <EditOutlined />,
          },
          {
            label: "Xóa khỏi dự án",
            key: "DELETE",
            icon: <DeleteOutlined />,
          },
        ];

        const menuProps = {
          items,
          onClick: handleMenuClick,
        };
        return (
          <Dropdown menu={menuProps}>
            <Button type="link">
              <EllipsisOutlined />
            </Button>
          </Dropdown>
        );
      },
      fixed: "right",
      width: 100,
    },
  ];

  const handleActionDeleteClick = useCallback(
    async (record: ProjectBrandInterface) => {
      openDeleteModal({
        content: (
          <p>
            Bạn muốn xóa nhãn hàng{" "}
            <span className={"font-semibold"}>{record.brand.name}</span> khỏi dự
            án?
          </p>
        ),
        deleteText: "Xác nhận xóa",
        loading: false,
        onCancel(): void {},
        onDelete: async () => {
          await axiosDelete(`/projects/${projectId}/brands/${record.id}`);
          showNotification({
            type: "success",
            message: `Xóa nhãn hàng ${record.brand.name} thành công.`,
          });

          await fetchProjectBrands();
        },
        title: `Xóa nhãn hàng`,
        titleError: "Không thể xóa nhãn hàng",
        contentHeader: (
          <>
            Không thể xóa nhãn hàng{" "}
            <span className="font-semibold">{record.brand.name}</span> bởi vì:
          </>
        ),
      });
    },
    [
      axiosDelete,
      fetchProjectBrands,
      openDeleteModal,
      projectId,
      showNotification,
    ],
  );
  const actionActions = [
    {
      key: "DELETE",
      action: handleActionDeleteClick,
    },
  ];

  const brandColumns: ColumnsType<ProjectBrandInterface> = [
    {
      title: "Nhãn hàng tham gia",
      key: "brand",
      dataIndex: "brand",
      render: renderTableCell,
    },
    {
      title: "Sản phẩm thuộc nhãn hàng",
      key: "productsCount",
      dataIndex: "productsCount",
      render: renderTableCell,
    },
    {
      key: "actions",
      render: (_, record) => {
        return (
          <TableActionCell
            actions={actionActions}
            items={[
              {
                key: "DELETE",
                label: "Xóa khỏi dự án",
                icon: <DeleteOutlined />,
              },
            ]}
            record={record}
          />
        );
      },
    },
  ];

  const handleBtnAddAgencyClick = () => {
    setFormAction(CURD.CREATE);
    setIsModalAgencyOpen(true);
  };

  const onAgencyFormFinish = async () => {
    setLoading(true);

    await agencyForm.validateFields();
    const data = agencyForm.getFieldsValue();
    const id = agencyForm.getFieldValue("id");

    try {
      switch (formAction) {
        case CURD.CREATE:
          await axiosPost(`/projects/${projectId}/agencies`, data);
          showNotification({
            type: "success",
            message: "Thêm agency, kênh thực hiện thành công",
          });
          break;
        case CURD.UPDATE:
          await axiosPatch(`/projects/${projectId}/agencies/${id}`, data);
          showNotification({
            type: "success",
            message: "Cập nhật agency, kênh thực hiện thành công",
          });
          break;
        default:
          return;
      }

      fetchProjectAgencies();
      agencyForm.resetFields();
      setIsModalAgencyOpen(false);
      setSelectedProjectAgency(undefined);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      formErrorResponseHandler(agencyForm, error);
    } finally {
      setLoading(false);
    }
  };

  const agencyFormContent = (
    <>
      <Form.Item name={"id"} hidden={true}></Form.Item>

      <Form.Item
        name={"agencyId"}
        label={"Agency"}
        rules={[
          {
            required: true,
            message: "Agency không được bỏ trống.",
          },
        ]}
      >
        {(() => {
          const options =
            agenciesQuery.data?.entities
              .filter(
                (agency) =>
                  !projectAgencies
                    .flatMap((projectAgency) => projectAgency.agency.id)
                    .includes(agency.id),
              )
              .map((agency) => ({
                label: agency.name,
                value: agency.id,
              })) ?? [];

          if (formAction === CURD.UPDATE) {
            const agency = agenciesQuery.data?.entities.find(
              (item) => item.id === agencyForm.getFieldValue("agencyId"),
            );

            options.unshift({
              label: agency?.name ?? "",
              value: agency?.id ?? 0,
            });
          }

          return (
            <Select
              showSearch
              optionFilterProp="children"
              filterOption={filterOption}
              options={options}
              disabled={formAction === CURD.UPDATE}
            />
          );
        })()}
      </Form.Item>

      <Form.Item
        name={"channelIds"}
        label={"Kênh phụ trách"}
        rules={[
          {
            required: true,
            message: "Kênh phụ trách không được bỏ trống.",
          },
        ]}
      >
        {(() => {
          const channelActiveOptions =
            channelsQuery.data?.entities.map((channel) => ({
              label: channel.name,
              value: channel.id,
            })) ?? [];

          const selectedOptions =
            selectedProjectAgency?.projectAgencyChannels.map(
              (projectAgencyChannel) => ({
                value: projectAgencyChannel.channel.id,
                label: projectAgencyChannel.channel.name,
              }),
            ) ?? [];

          const channelOptions = _.uniqBy(
            _.concat(channelActiveOptions, selectedOptions),
            (o) => o.value,
          );

          return (
            <Select
              showSearch
              optionFilterProp="children"
              filterOption={filterOption}
              options={channelOptions}
              mode="multiple"
            />
          );
        })()}
      </Form.Item>
    </>
  );

  const handleBtnAddBrandClick = () => {
    setFormAction(CURD.UPDATE);
    setIsModalBrandOpen(true);
  };

  const onBrandFormFinish = async () => {
    setLoading(true);

    await brandForm.validateFields();
    const data = brandForm.getFieldsValue();
    try {
      await axiosPost(`/projects/${projectId}/brands`, data);
      showNotification({
        type: "success",
        message: "Thêm nhãn hàng trong dự án thành công",
      });

      fetchProjectBrands();
      brandForm.resetFields();
      setIsModalBrandOpen(false);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      formErrorResponseHandler(brandForm, error);
    } finally {
      setLoading(false);
    }
  };

  const brandFormContent = (
    <Form.Item
      name="brandIds"
      label="Nhãn hàng"
      rules={[
        {
          required: true,
          message: "Nhãn hàng không được bỏ trống.",
        },
      ]}
    >
      <Select
        showSearch
        optionFilterProp="children"
        filterOption={filterOption}
        options={brandsQuery.data?.entities
          .filter(
            (brand) =>
              !projectBrands
                .flatMap((projectBrand) => projectBrand.brand.id)
                .includes(brand.id),
          )
          .map((brand) => ({
            label: brand.name,
            value: brand.id,
          }))}
        mode="multiple"
      />
    </Form.Item>
  );

  const onGeneralFormFinish = async () => {
    setLoading(true);
    try {
      const data = generalForm.getFieldsValue();
      data.startDate = data.startDate?.format("YYYY-MM-DD");
      data.endDate = data.endDate?.format("YYYY-MM-DD");
      await axiosPatch(`/projects/${projectId}`, data);

      showNotification({
        type: "success",
        message: "Cập nhật thông tin dự án thành công",
      });
    } catch (e) {
      formErrorResponseHandler(generalForm, e);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    generalForm.setFieldsValue({
      ...projectQuery.data,
      startDate: dayjs(projectQuery.data?.startDate),
      endDate: dayjs(projectQuery.data?.endDate),
      clientId: projectQuery.data?.client?.id,
    });
  }, [generalForm, projectQuery.data]);

  if (!allowUserType(userLogin?.type)) {
    return (
      <div className={"inline-block mt-5"}>
        <Alert
          type={"info"}
          showIcon={true}
          message={"Vui lòng chọn loại báo cáo cần xem ở menu BÁO CÁO kế bên"}
        />
      </div>
    );
  }

  return (
    <div>
      <h2>Thông tin cơ bản</h2>
      <InnerContainer>
        <Row>
          <Col md={8}>
            <h4>Thông tin dự án</h4>
            <p className="text-gray-400 max-w-[350px]">
              Khai báo các thông tin của dự án như tên, thời gian diễn ra và
              người phụ trách dự án.
            </p>
          </Col>
          <Col md={8}>
            <Form
              form={generalForm}
              layout="vertical"
              onFinish={onGeneralFormFinish}
            >
              <Row>
                <Col md={11}>
                  <Form.Item label="ID dự án" name="id">
                    <Input disabled value={projectId} />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                label="Tên dự án"
                name="name"
                rules={[
                  {
                    required: true,
                    message: "Tên dự án không được để trống.",
                  },
                ]}
              >
                <Input />
              </Form.Item>

              <Row justify={"space-between"} gutter={16}>
                <Col md={12} xs={12}>
                  <Form.Item
                    label="Ngày bắt đầu"
                    name="startDate"
                    rules={[
                      {
                        required: true,
                        message: "Ngày bắt đầu không được để trống.",
                      },
                    ]}
                  >
                    <DatePicker
                      format={DATE_FORMAT}
                      style={{ width: "100%" }}
                      allowClear
                      placeholder=""
                    />
                  </Form.Item>
                </Col>

                <Col md={12} xs={12}>
                  <Form.Item noStyle dependencies={["startDate"]}>
                    {() => (
                      <Form.Item
                        label="Ngày kết thúc"
                        name="endDate"
                        rules={[
                          {
                            required: true,
                            message: "Ngày kết thúc không được để trống.",
                          },
                          endDateAntFormFieldValidate,
                        ]}
                      >
                        <DatePicker
                          format={DATE_FORMAT}
                          style={{ width: "100%" }}
                          allowClear
                          placeholder=""
                          minDate={generalForm.getFieldValue("startDate")}
                        />
                      </Form.Item>
                    )}
                  </Form.Item>
                </Col>
              </Row>

              {(() => {
                const client = projectQuery.data?.client;
                const clientOptions = [
                  { label: client?.name, value: client?.id },
                ];

                return (
                  <Form.Item
                    name="clientId"
                    label={"Client"}
                    rules={[{ required: true }]}
                  >
                    <Select disabled options={clientOptions} />
                  </Form.Item>
                );
              })()}

              <Row>
                <Col>
                  <Form.Item noStyle>
                    <Button
                      htmlType="submit"
                      type={"primary"}
                      loading={loading}
                    >
                      Cập nhật
                    </Button>
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </Col>
        </Row>
      </InnerContainer>

      <InnerContainer className="mt-5">
        <Row>
          <Col md={8} className="pr-16">
            <h4>Agency tham gia dự án và kênh phụ trách</h4>
            <p className="text-gray-400 max-w-[350px]">
              Khai báo Agency tham gia dự án và kênh thực hiện mà mỗi Agency
              phải phụ trách.
            </p>
          </Col>
          <Col md={16}>
            <Table
              dataSource={projectAgencies}
              columns={agencyColumns}
              loading={loading}
              pagination={false}
              rowKey={"id"}
            />
            <p
              onClick={handleBtnAddAgencyClick}
              className={
                "text-[#1D8EE6] cursor-pointer p-0 m-0 mt-4 font-semibold"
              }
            >
              <PlusOutlined color="#1D8EE6" /> Thêm mới
            </p>
          </Col>
        </Row>
      </InnerContainer>

      <InnerContainer className="mt-5">
        <Row>
          <Col md={8} className="pr-16">
            <h4>Nhãn hàng tham gia dự án</h4>
            <p className="text-gray-400 max-w-[350px]">
              Nhãn hàng được khai báo tham gia dự án sẽ áp dụng cho tất cả các
              Agency bên trong dự án.
            </p>
          </Col>
          <Col md={16}>
            <Table
              dataSource={projectBrands}
              columns={brandColumns}
              pagination={false}
              rowKey={"id"}
            />
            <p
              onClick={handleBtnAddBrandClick}
              className={
                "text-[#1D8EE6] cursor-pointer p-0 m-0 mt-4 font-semibold"
              }
            >
              <PlusOutlined color="#1D8EE6" /> Thêm mới
            </p>
          </Col>
        </Row>
      </InnerContainer>

      <ModalCURD
        title={
          formAction === CURD.CREATE
            ? "Thêm agency, kênh thực hiện"
            : "Agency, kênh thực hiện"
        }
        isOpen={isModalAgencyOpen}
        setIsOpen={setIsModalAgencyOpen}
        formContent={agencyFormContent}
        form={agencyForm}
        onFinish={onAgencyFormFinish}
        action={formAction}
        onCancelCb={() => {
          setSelectedProjectAgency(undefined);
        }}
      />

      <ModalCURD
        title="Thêm nhãn hàng trong dự án."
        isOpen={isModalBrandOpen}
        setIsOpen={setIsModalBrandOpen}
        formContent={brandFormContent}
        form={brandForm}
        onFinish={onBrandFormFinish}
        action={formAction}
        btnText="Thêm"
      />
    </div>
  );
}
