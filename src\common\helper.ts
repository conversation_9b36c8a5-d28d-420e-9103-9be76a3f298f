import { FormInstance } from "antd";
import { AxiosError } from "axios";
import dayjs from "dayjs";
import { DATE_FORMAT, DATETIME_FORMAT } from "./constant";

export const stringRemoveAccents = (str: string) =>
  str
    .normalize("NFD")
    .replace(/[\u0300-\u036f]/g, "")
    .replace(/đ/g, "d")
    .replace(/Đ/g, "D");

export const stringIncludes = (str: string, subStr: string) =>
  stringRemoveAccents(str)
    .toLocaleLowerCase()
    .includes(stringRemoveAccents(subStr).toLocaleLowerCase());
export const filterOption = (
  input: string,
  option?: { label: string; value: string | number },
) => stringIncludes(option?.label ?? "", input);

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const formErrorResponseHandler = (form: FormInstance, error: any) => {
  const data: { message: { field: string; message: string }[] } =
    error?.response?.data;
  const { message } = data;
  message.forEach((item: { field: string; message: string }) => {
    form.setFields([{ name: item.field, errors: [item.message] }]);
  });
};

export const formatMoney = (money?: number) => {
  return money?.toLocaleString("vi-VI");
};

export const compareObjectsByProperties = <T extends object>(
  obj1: T,
  obj2: T,
  properties: (keyof T)[],
): boolean => {
  if (!obj1 || !obj2) return false;
  return properties.every((property) => obj1[property] === obj2[property]);
};

export const formatDateTime = (dateTime: string) => {
  return dayjs(dateTime).format(DATETIME_FORMAT);
};

export const getAxiosErrorMessage: (error: AxiosError) => string = (
  error: AxiosError,
) => {
  const responseData = error.response?.data as { message: string[] | string };
  if (responseData.message) {
    if (typeof responseData.message === "string") {
      return responseData.message;
    } else if (Array.isArray(responseData.message)) {
      return responseData.message.join(", ");
    }
  }
  return error.message;
};

export const validateAndClearVietnamPhoneNumber = (phoneNumber: string) => {
  const cleanedPhoneNumber = phoneNumber.replace(/\D/g, "");
  const pattern = /^0\d{9}$/;

  const isValid = pattern.test(cleanedPhoneNumber);

  return {
    cleanedPhoneNumber,
    isValid,
  };
};

export const validateAndClearDate = (dateString: string) => {
  // Validate if the cleaned date string matches the format and is a valid date
  console.log(dateString);
  const isValid = dayjs(dateString, DATE_FORMAT, true).isValid();

  return {
    dateString,
    isValid,
  };
};

export const getErrorMessageFromAxiosError = (error: AxiosError) => {
  const data = error?.response?.data;
  if (!data || typeof data !== "object" || !("message" in data)) {
    return error.message;
  }
  const message = data.message;
  if (
    typeof message === "string" ||
    (Array.isArray(message) && typeof message[0] === "string")
  ) {
    return Array.isArray(message) ? message[0] : message;
  }
  return error.message;
};

interface ObjectComparisonResult {
  key: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  valueInObj1: any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  valueInObj2: any;
}

export const findObjectDifferences = (
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  obj1: any,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  obj2: any,
  keysToCompare: string[] | null = null,
  path: string = "",
): ObjectComparisonResult[] => {
  const differences: ObjectComparisonResult[] = [];

  if (
    typeof obj1 !== "object" ||
    typeof obj2 !== "object" ||
    obj1 === null ||
    obj2 === null
  ) {
    if (obj1 !== obj2) {
      differences.push({ key: path, valueInObj1: obj1, valueInObj2: obj2 });
    }
    return differences;
  }

  const keys =
    keysToCompare ??
    Array.from(
      new Set([...Object.keys(obj1 || {}), ...Object.keys(obj2 || {})]),
    );

  keys.forEach((key) => {
    const newPath = path ? `${path}.${key}` : key;
    if (!(key in obj1)) {
      differences.push({
        key: newPath,
        valueInObj1: undefined,
        valueInObj2: obj2[key],
      });
    } else if (!(key in obj2)) {
      differences.push({
        key: newPath,
        valueInObj1: obj1[key],
        valueInObj2: undefined,
      });
    } else {
      differences.push(
        ...findObjectDifferences(obj1[key], obj2[key], null, newPath),
      );
    }
  });

  return differences;
};

export const formatNumber = (value: number) =>
  new Intl.NumberFormat("vi-VN").format(value);

export const removeVietnameseTones = (str: string) =>
  str.normalize("NFD").replace(/[\u0300-\u036f]/g, "");

export const formatNumberOnChart = (value: number) => {
  let formattedValue: string;

  if (value >= 1_000_000_000) {
    formattedValue = (value / 1_000_000_000).toFixed(1);
    return parseFloat(formattedValue) + "B";
  } else if (value >= 1_000_000) {
    formattedValue = (value / 1_000_000).toFixed(1);
    return parseFloat(formattedValue) + "M";
  } else if (value >= 1_000) {
    formattedValue = (value / 1_000).toFixed(1);
    return parseFloat(formattedValue) + "K";
  } else {
    return value.toString();
  }
};

export const formatSecondsToHMS = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  // Pad the values to ensure they are always two digits
  const formattedHours = String(hours).padStart(2, "0");
  const formattedMinutes = String(minutes).padStart(2, "0");
  const formattedSeconds = String(remainingSeconds).padStart(2, "0");

  return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
};

export const cn = (...classNames: (string | boolean | undefined)[]): string =>
  classNames.filter(Boolean).join(" ");

export const getDatesInRange = (startDate: Date, endDate: Date): Date[] => {
  const dates: Date[] = [];
  const currentDate = new Date(startDate);

  while (currentDate <= endDate) {
    dates.push(new Date(currentDate));
    currentDate.setDate(currentDate.getDate() + 1); // Move to the next day
  }

  return dates;
};

export const randomColor = () => {
  const letters = "0123456789ABCDEF";
  let color = "#";
  for (let i = 0; i < 6; i++) {
    color += letters[Math.floor(Math.random() * 16)];
  }
  return color;
};

export function loadImageAsync(
  src: string,
  width?: number,
  height?: number,
): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const img = new Image(width, height);
    img.onerror = reject;
    img.onload = () => resolve(img);
    img.src = src;
  });
}

export function sleepAsync(ms: number) {
  return new Promise((resolve) => {
    setTimeout(resolve, ms);
  });
}
