import { CURD } from "@/common/constant.ts";
import ModalCURD from "@/components/ModalCURD.tsx";
import { useApp } from "@/UseApp.tsx";
import { OosLevelInterface } from "@project/component/feature/config/types/outOfStockStatus/interface.ts";
import { Checkbox, ColorPicker, Form, Input } from "antd";
import {
  useCreateOosLevelMutation,
  useUpdateOosLevelMutation,
} from "./service.ts";

import { useCallback, useEffect } from "react";

interface OutOfStockStatusStatusModalProps {
  action: CURD | null;
  onCancelCb: () => void;
  componentFeatureId: number;
  selectedFeatureOosLevel: OosLevelInterface | null;
  cb: () => void;
}

const OutOfStockStatusStatusModal = ({
  action,
  onCancelCb,
  componentFeatureId,
  selectedFeatureOosLevel,
  cb,
}: OutOfStockStatusStatusModalProps) => {
  const { showNotification } = useApp();

  const [form] = Form.useForm();

  const createOosLevelMutation = useCreateOosLevelMutation(componentFeatureId);
  const updateOosLevelMutation = useUpdateOosLevelMutation(componentFeatureId);

  const formContent = (
    <>
      <Form.Item
        label="Tên trạng thái"
        name={"name"}
        rules={[{ required: true }]}
      >
        <Input />
      </Form.Item>

      <Form.Item
        layout={"horizontal"}
        name={"isStandard"}
        valuePropName="checked"
      >
        <Checkbox>Tồn chuẩn</Checkbox>
      </Form.Item>

      <Form.Item
        name={"backgroundColor"}
        label={"Màu nền"}
        rules={[{ required: true }]}
        layout={"horizontal"}
      >
        <ColorPicker
          showText
          defaultValue={"#FFFFFF"}
          mode={"single"}
          disabledAlpha
          format="hex"
        />
      </Form.Item>

      <Form.Item
        name={"foregroundColor"}
        label={"Màu chữ"}
        rules={[{ required: true }]}
        layout={"horizontal"}
      >
        <ColorPicker
          showText
          defaultValue={"#000000"}
          disabledAlpha
          format="hex"
        />
      </Form.Item>
    </>
  );

  const onFinish = useCallback(async () => {
    const data = form.getFieldsValue();
    const { backgroundColor, foregroundColor } = data;

    data.backgroundColor =
      typeof backgroundColor === "string"
        ? backgroundColor
        : backgroundColor.toHexString();

    data.foregroundColor =
      typeof foregroundColor === "string"
        ? foregroundColor
        : foregroundColor.toHexString();

    if (action === CURD.CREATE) {
      await createOosLevelMutation.mutateAsync(data);

      showNotification({
        type: "success",
        message: "Thêm trạng thái thành công",
      });
    }

    if (action === CURD.UPDATE && selectedFeatureOosLevel) {
      await updateOosLevelMutation.mutateAsync({
        id: selectedFeatureOosLevel.id,
        ...data,
      });

      showNotification({
        type: "success",
        message: "Chỉnh sửa trạng thái thành công",
      });
    }
    cb();
  }, [
    action,
    cb,
    createOosLevelMutation,
    form,
    selectedFeatureOosLevel,
    showNotification,
    updateOosLevelMutation,
  ]);

  useEffect(() => {
    if (action === CURD.UPDATE && selectedFeatureOosLevel) {
      form.setFieldsValue({
        name: selectedFeatureOosLevel.name,
        backgroundColor: selectedFeatureOosLevel.backgroundColor,
        foregroundColor: selectedFeatureOosLevel.foregroundColor,
        isStandard: selectedFeatureOosLevel.isStandard,
      });
    }

    if (action === CURD.CREATE) {
      form.setFieldsValue({
        backgroundColor: "#FFFFFF",
        foregroundColor: "#000000",
      });
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [action, selectedFeatureOosLevel]);

  return (
    <ModalCURD
      title={
        action === CURD.CREATE ? "Thêm trạng thái" : "Chỉnh sửa trạng thái"
      }
      isOpen={true}
      formContent={formContent}
      form={form}
      onFinish={onFinish}
      onCancelCb={() => {
        form.resetFields();
        onCancelCb();
      }}
      action={action}
    />
  );
};

export default OutOfStockStatusStatusModal;
