import { randomColor } from "@/common/helper";
import ChartContanier from "@/routes/project/chart/ChartContanier";
import { <PERSON><PERSON><PERSON>, Pie } from "@ant-design/charts";
import { Col, Form, Row } from "antd";
import _ from "lodash";
import { useCallback, useMemo, useState } from "react";
import DashboardFilterZone from "../../DashboardFilterZone";
import { DashboardFilterInterface } from "../../interface";
import {
  useUrgencyFrequencyQuery,
  useUrgencyTop10EmployeeFrequencyQuery,
} from "./service";

interface UrgencyTabProps {
  projectId: number;
  dashboardId: number;
}

const UrgencyTab = ({ projectId, dashboardId }: UrgencyTabProps) => {
  const [form] = Form.useForm();
  const [filter, setFilter] = useState<DashboardFilterInterface>({});

  const urgencyFrequencyQuery = useUrgencyFrequencyQuery(
    projectId,
    dashboardId,
    filter,
  );
  const top10EmployeeFrequencyQuery = useUrgencyTop10EmployeeFrequencyQuery(
    projectId,
    dashboardId,
    filter,
  );

  const handleApply = useCallback(() => {
    const values = form.getFieldsValue();

    const {
      date,
      projectRegionIds,
      projectOutletIds,
      channelIds,
      provinceIds,
      leaderIds,
    } = values;

    const filterValue = {
      startDate: date?.[0] ? date[0].format("YYYY-MM-DD") : null,
      endDate: date?.[1] ? date[1].format("YYYY-MM-DD") : null,
      projectRegionIds,
      projectOutletIds,
      channelIds,
      provinceIds,
      leaderIds: leaderIds?.map((item: { value: number }) => item.value),
    };

    if (_.isEqual(filter, filterValue)) {
      urgencyFrequencyQuery.refetch();
      top10EmployeeFrequencyQuery.refetch();
    } else {
      setFilter(filterValue);
    }
  }, [filter, form, top10EmployeeFrequencyQuery, urgencyFrequencyQuery]);

  const pieData = useMemo(() => {
    return urgencyFrequencyQuery.data?.map((item) => ({
      type: item.name,
      value: item.count,
      backgroundColor: item.backgroundColor,
    }));
  }, [urgencyFrequencyQuery.data]);
  const total = useMemo(() => {
    return (
      urgencyFrequencyQuery.data?.reduce((sum, item) => sum + item.count, 0) ??
      0
    );
  }, [urgencyFrequencyQuery.data]);
  const pieColors = useMemo(() => {
    return urgencyFrequencyQuery.data?.map(
      (item) => item.backgroundColor ?? randomColor(),
    );
  }, [urgencyFrequencyQuery.data]);
  const pieConfig = useMemo(
    () => ({
      appendPadding: 10,
      data: pieData,
      angleField: "value",
      colorField: "type",
      radius: 0.8,
      interactions: [
        {
          type: "element-active",
        },
      ],
      label: {
        text: (d: { type: string; value: number }) =>
          `${d.value}  (${Math.round((d.value / total) * 100)}%)`,
        position: "spider",
        fontSize: 12,
        fontWeight: "bold",
      },
      legend: {
        color: {
          position: "bottom",
        },
        layout: {
          justifyContent: "center",
        },
      },
      title: {
        title: "Dashboard PA out total",
        subtitle: "Tỷ lệ số lần rời vị trí theo lý do",
      },
      tooltip: {
        field: "value",
        title: (d: { type: string }) => d.type,
        value: (d: { value: number }) => d.value,
      },
      scale: { color: { palette: pieColors } },
    }),

    [pieColors, pieData, total],
  );

  const columnData = useMemo(() => {
    return top10EmployeeFrequencyQuery.data?.map((item) => ({
      name: item.name,
      times: item.count,
    }));
  }, [top10EmployeeFrequencyQuery.data]);

  const columnConfig = useMemo(
    () => ({
      title: {
        title: "Dashboard PA out top 10",
        subtitle: "Top 10 PA rời vị trí nhiều lần nhất",
      },
      data: columnData,
      xField: "name",
      yField: "times",
      style: {
        maxWidth: 50,
        fill: "#FD5401",
      },
      label: {
        textBaseline: "bottom",
      },
      axis: {
        x: {
          labelSpacing: 4,
          style: {
            labelTransform: "rotate(-45)",
          },
        },
      },
    }),
    [columnData],
  );

  return (
    <>
      <DashboardFilterZone
        handleApply={handleApply}
        fields={["date", "region", "province", "chain", "leader", "outlet"]}
        form={form}
        projectId={projectId}
        setFilter={setFilter}
      />

      <Row gutter={[16, { xs: 16, sm: 16 }]}>
        <Col md={9} xs={24}>
          <ChartContanier>
            <Pie {...pieConfig} height={500} />
          </ChartContanier>
        </Col>

        <Col md={15} xs={24}>
          <ChartContanier>
            <Column {...columnConfig} height={500} />
          </ChartContanier>
        </Col>
      </Row>
    </>
  );
};

export default UrgencyTab;
