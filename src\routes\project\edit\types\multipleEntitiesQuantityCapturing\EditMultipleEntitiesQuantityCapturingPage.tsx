import { CURD, DATETIME_FORMAT } from "@/common/constant";
import TableActionCell from "@/components/TableActionCell";
import ReportMultipleEntitiesQuantityCapturingValueCell from "@/routes/project/report/types/multipleEntitiesQuantityCapturing/ReportMultipleEntitiesQuantityCapturingValueCell";
import { EditOutlined } from "@ant-design/icons";
import { Table } from "antd";
import dayjs from "dayjs";
import _ from "lodash";
import { useState } from "react";
import { useParams } from "react-router-dom";
import AttendanceDetailRow from "../../AttendanceDetailRow";
import {
  useAttendanceFeatureDetailQuery,
  useAttendanceQuery,
} from "../../service";
import EditMultipleEntitiesQuantityCapturingModal from "./EditMultipleEntitiesQuantityCapturingModal";
import { useEditMultipleEntitiesQuantityCapturingQuery } from "./service";

const EditMultipleEntitiesQuantityCapturingPage = () => {
  const projectId = parseInt(useParams().id ?? "0");
  const attendanceId = parseInt(useParams().attendanceId ?? "0");
  const componentFeatureId = parseInt(useParams().componentFeatureId ?? "0");

  const [action, setAction] = useState<CURD | undefined>(undefined);

  const attendanceQuery = useAttendanceQuery(projectId, attendanceId);
  const attendanceFeatureDetailQuery = useAttendanceFeatureDetailQuery(
    projectId,
    attendanceId,
    componentFeatureId,
  );
  const editMultipleEntitiesQuantityCapturingQuery =
    useEditMultipleEntitiesQuantityCapturingQuery(
      projectId,
      attendanceId,
      componentFeatureId,
    );

  return (
    <>
      <h2>{attendanceFeatureDetailQuery.data?.name}</h2>
      <div className="bg-white p-10 rounded">
        <AttendanceDetailRow
          projectRecordEmployee={attendanceQuery.data?.projectRecordEmployee}
          createdAt={attendanceQuery.data?.timeIn}
          updatedAt={attendanceQuery.data?.timeOut}
        />

        <Table
          loading={
            editMultipleEntitiesQuantityCapturingQuery.isLoading ||
            editMultipleEntitiesQuantityCapturingQuery.isFetching
          }
          rowKey={(o) => o?.id ?? ""}
          className="mt-10"
          dataSource={
            editMultipleEntitiesQuantityCapturingQuery.data
              ? [editMultipleEntitiesQuantityCapturingQuery.data]
              : [undefined]
          }
          columns={[
            {
              title: attendanceFeatureDetailQuery.data?.name,
              render: (_, record) => {
                const { recordQuantityValues } = record ?? {};
                recordQuantityValues?.sort((a, b) => b.value - a.value);
                for (const recordQuantityValue of recordQuantityValues ?? []) {
                  recordQuantityValue.featureQuantity =
                    attendanceFeatureDetailQuery.data?.featureQuantities?.find(
                      (item) =>
                        item.id === recordQuantityValue.featureQuantityId,
                    );
                }
                if (recordQuantityValues)
                  return (
                    <ReportMultipleEntitiesQuantityCapturingValueCell
                      recordQuantityValues={recordQuantityValues}
                    />
                  );

                return "-";
              },
            },
            {
              title: "Thời gian dùng tool edit",
              render: (_, record) => {
                const { toolQuantity } = record ?? {};
                if (toolQuantity)
                  return dayjs(toolQuantity?.createdAt).format(DATETIME_FORMAT);

                return "-";
              },
            },
            {
              title: "Người dùng tool edit",
              render: (_, record) => {
                const { toolQuantity } = record ?? {};
                if (toolQuantity)
                  return <>{toolQuantity?.createdByUser.name}</>;

                return "-";
              },
            },
            {
              render: (_, record) => {
                if (!record)
                  return (
                    <TableActionCell
                      actions={[
                        {
                          key: CURD.CREATE,
                          action: () => setAction(CURD.CREATE),
                        },
                      ]}
                      items={[
                        {
                          key: CURD.CREATE,
                          label: "Thêm mới",
                          icon: <EditOutlined />,
                        },
                      ]}
                      record={{ id: 0 }}
                    />
                  );

                return (
                  <TableActionCell
                    actions={[
                      {
                        key: CURD.UPDATE,
                        action: () => setAction(CURD.UPDATE),
                      },
                    ]}
                    items={[
                      {
                        key: CURD.UPDATE,
                        label: "Chỉnh sửa",
                        icon: <EditOutlined />,
                      },
                    ]}
                    record={record}
                  />
                );
              },
            },
          ]}
          pagination={false}
        />
      </div>

      {action && (
        <EditMultipleEntitiesQuantityCapturingModal
          title={
            action === CURD.CREATE
              ? `Thêm ${_.lowerFirst(attendanceFeatureDetailQuery.data?.name)}`
              : `Chỉnh sửa ${_.lowerFirst(attendanceFeatureDetailQuery.data?.name)}`
          }
          componentFeature={attendanceFeatureDetailQuery.data}
          editMultipleEntitiesQuantityCapturing={
            editMultipleEntitiesQuantityCapturingQuery.data
          }
          projectId={projectId}
          attendanceId={attendanceId}
          componentFeatureId={componentFeatureId}
          cb={() => {
            setAction(undefined);
            editMultipleEntitiesQuantityCapturingQuery.refetch();
          }}
          cancelCb={() => setAction(undefined)}
          createdAt={attendanceQuery.data?.createdAt}
          projectAgencyId={
            attendanceQuery.data?.projectRecordEmployee.projectRecord
              .projectAgencyId
          }
          action={action}
        />
      )}
    </>
  );
};

export default EditMultipleEntitiesQuantityCapturingPage;
