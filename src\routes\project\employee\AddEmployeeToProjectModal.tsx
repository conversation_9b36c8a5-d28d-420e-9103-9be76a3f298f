import { CURD } from "@/common/constant.ts";
import { filterOption } from "@/common/helper.ts";
import DebounceSelect from "@/components/DebounceSelectComponent.tsx";
import ModalCURD from "@/components/ModalCURD.tsx";
import UserOptionComponent from "@/components/UserOptionComponent.tsx";
import { useApp } from "@/UseApp.tsx";
import { CloseCircleOutlined } from "@ant-design/icons";
import { ProjectEmployeeUserInterface } from "@project/employee/interface.ts";
import {
  useCreateEmployeeMutation,
  useGetEmployeesAvailablesMutation,
} from "@project/employee/service.ts";
import { useProjectAgenciesQuery } from "@project/general/services.ts";
import { RoleInterface } from "@project/role/interface.ts";
import { Form, Select, Tag } from "antd";
import { useCallback, useState } from "react";

interface AddEmployeeToProjectModalProps {
  role: RoleInterface;
  isOpen: boolean;
  projectId: number;
  cb: () => void;
  notFoundCb: () => void;
  cancelCb: () => void;
}

const AddEmployeeToProjectModal = ({
  role,
  isOpen,
  projectId,
  cb,
  notFoundCb,
  cancelCb,
}: AddEmployeeToProjectModalProps) => {
  const { showNotification } = useApp();

  const [form] = Form.useForm();
  const [selectedValueSearch, setSelectedValueSearch] = useState<
    string | null | ProjectEmployeeUserInterface
  >(null);
  const [options, setOptions] = useState<ProjectEmployeeUserInterface[]>([]);

  const projectAgencyQuery = useProjectAgenciesQuery(projectId);

  const getEmployeesAvailablesMutation =
    useGetEmployeesAvailablesMutation(projectId);
  const createEmployeeMutation = useCreateEmployeeMutation(projectId);

  const fetchOptions = useCallback(
    async (keyword?: string) => {
      if (keyword?.length === 0) {
        return [];
      }
      const response = await getEmployeesAvailablesMutation.mutateAsync({
        keyword,
        take: 10,
      });

      setOptions(response.entities);
      return response.entities.map((item) => ({
        value: item.user.id,
        label: item.user.username,
        user: item.user,
        isAvailable: item.isAvailable,
        disabled: !item.isAvailable,
      }));
    },
    [getEmployeesAvailablesMutation],
  );

  const formContent = (
    <>
      <Form.Item
        label="Nhập SĐT hoặc Email của nhân viên"
        name="phoneOrEmail"
        rules={[
          {
            required: true,
            message: "Vui lòng nhập SĐT hoặc Email của nhân viên.",
          },
        ]}
      >
        {selectedValueSearch ? (
          <Tag
            closable
            onClose={() => {
              setSelectedValueSearch(null);
              form.setFieldValue("phoneOrEmail", []);
            }}
            className="w-full justify-between flex pt-2 pb-2 pl-3 pr-3"
            closeIcon={<CloseCircleOutlined style={{ fontSize: 14 }} />}
            style={{
              fontSize: 14,
              backgroundColor:
                typeof selectedValueSearch === "string" ? "" : "#F0F8FF",
              borderColor:
                typeof selectedValueSearch === "string" ? "" : "#C4D6FF",
            }}
          >
            {typeof selectedValueSearch === "string" ? (
              <p>{selectedValueSearch}</p>
            ) : (
              <UserOptionComponent
                avatarUrl={selectedValueSearch?.user.picture}
                name={selectedValueSearch?.user.name}
                phone={selectedValueSearch?.user.phone}
                email={selectedValueSearch?.user.email}
                isAvailable={selectedValueSearch?.isAvailable}
              />
            )}
          </Tag>
        ) : (
          <DebounceSelect
            allowClear
            mode="tags"
            fetchOptions={fetchOptions}
            style={{ width: "100%" }}
            optionRender={(option) => {
              if (option.data.user) {
                return (
                  <UserOptionComponent
                    avatarUrl={option.data.user?.pictureUrl}
                    name={option.data.user?.name}
                    phone={option.data.user?.phone}
                    email={option.data.user?.email}
                    isAvailable={option.data?.isAvailable}
                  />
                );
              }
              return option.label;
            }}
            onSelect={({ value }) => {
              if (typeof value === "number") {
                const option = options.find((item) => item.user.id === value);
                if (option) setSelectedValueSearch(option);
              } else setSelectedValueSearch(value);
            }}
          />
        )}
      </Form.Item>

      {selectedValueSearch && typeof selectedValueSearch === "object" && (
        <Form.Item
          name="projectAgencyId"
          label="Agency quản lý nhân viên này"
          rules={[
            {
              required: true,
              message: "Agency quản lý không được bỏ trống",
            },
          ]}
        >
          <Select
            showSearch
            optionFilterProp="children"
            filterOption={filterOption}
            options={projectAgencyQuery.data?.map((projectAgency) => ({
              label: projectAgency.agency.name,
              value: projectAgency.id,
            }))}
          />
        </Form.Item>
      )}
    </>
  );

  const formSearchSubmit = useCallback(async () => {
    if (selectedValueSearch && typeof selectedValueSearch === "object") {
      try {
        await createEmployeeMutation.mutateAsync({
          projectAgencyId: form.getFieldValue("projectAgencyId"),
          userId: selectedValueSearch.user.id,
          roleId: role.id,
        });
        showNotification({
          type: "success",
          message: `Thêm nhân viên ${role.name} thành công.`,
        });

        form.resetFields();
        setSelectedValueSearch(null);
        cb();
      } catch (e) {
        console.error(e);
      }
    }

    if (selectedValueSearch && typeof selectedValueSearch === "string") {
      form.resetFields();
      setSelectedValueSearch(null);
      notFoundCb();
    }
  }, [
    cb,
    createEmployeeMutation,
    form,
    notFoundCb,
    role.id,
    role.name,
    selectedValueSearch,
    showNotification,
  ]);

  return (
    <ModalCURD
      title={`Thêm ${role.name}`}
      isOpen={isOpen}
      formContent={formContent}
      form={form}
      onFinish={formSearchSubmit}
      action={CURD.CREATE}
      btnText="Thêm vào dự án"
      btnConfirmDisable={!selectedValueSearch}
      onCancelCb={() => {
        cancelCb();
      }}
    />
  );
};

export default AddEmployeeToProjectModal;
