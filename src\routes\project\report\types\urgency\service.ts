import { AbstractFilterInterface } from "@/common/interface";
import { AppContextInterface } from "@/interface";
import { useApp } from "@/UseApp";
import { useMutation, useQuery } from "@tanstack/react-query";
import { RecordUrgencyInterface } from "./interface";

export const getReportUrgencies = (
  axiosGet: AppContextInterface["axiosGet"],
  projectId: number,
  featureId: number,
  filter?: object & AbstractFilterInterface,
) =>
  axiosGet<{ entities: RecordUrgencyInterface[]; count: number }, unknown>(
    `/projects/${projectId}/reports/features/${featureId}/urgencies`,
    filter,
  );

export const useReportUrgenciesQuery = (
  projectId: number,
  featureId: number,
  filter?: object & AbstractFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["urgencies", projectId, featureId, filter],
    queryFn: () => getReportUrgencies(axiosGet, projectId, featureId, filter),
  });
};

export const useGetReportUrgenciesMutation = (
  projectId: number,
  featureId: number,
) => {
  const { axiosGet } = useApp();

  return useMutation({
    mutationKey: ["getReportUrgencies", projectId, featureId],
    mutationFn: (filter?: object & AbstractFilterInterface) =>
      getReportUrgencies(axiosGet, projectId, featureId, filter),
  });
};
