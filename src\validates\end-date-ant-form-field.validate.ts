import dayjs from "dayjs";
import type { Rule } from "rc-field-form/lib/interface";

export const endDateAntFormFieldValidate: Rule = ({ getFieldValue }) => ({
  validator(_, value) {
    const startDate = getFieldValue("startDate");
    if (!startDate) {
      return Promise.reject(
        new Error(
          "Ch<PERSON>a có ngày bắt đầu dự án. Vui lòng chọn ngày bắt đầu trước.",
        ),
      );
    }
    if (value && dayjs(value) >= dayjs(startDate)) {
      return Promise.resolve();
    }

    return Promise.reject(
      new Error("<PERSON><PERSON><PERSON> kết thúc phải sau ngày bắt đầu dự án."),
    );
  },
});
