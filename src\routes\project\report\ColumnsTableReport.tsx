import { DATETIME_FORMAT, DATE_FORMAT } from "@/common/constant";
import UserOptionComponent from "@/components/UserOptionComponent";
import dayjs from "dayjs";
import { ProjectBoothInterface } from "../configOutlet/interface";
import { ProjectEmployeeUserInterface } from "../employee/interface";
import { ProjectAgencyInterface } from "../interface";
import { ProjectOutletInterface } from "../outlet/interface";
import { ProjectRecordEmployeeInterface } from "./interface";
import { AttendanceInterface } from "./types/attendanceClocking/interface";

type TableColumn =
  | "outletCode"
  | "outletName"
  | "boothName"
  | "address"
  | "channelName"
  | "projectRecordEmployee"
  | "subChannelName"
  | "agencyName"
  | "teamLeader"
  | "recordEmployee"
  | "attendance"
  | "date"
  | "dataTimestamp"
  | "projectBooth";
const getColumnsTableReport = (
  showColumns: {
    tableColumn: TableColumn;
    fixed?: "left" | "right";
  }[],
) => {
  const columns = [
    {
      key: "outletCode",
      title: "Mã outlet",
      className: "min-w-[150px]",
      dataIndex: "projectOutlet",
      render: (projectOutlet: ProjectOutletInterface) => projectOutlet.code,
    },
    {
      key: "outletName",
      className: "min-w-[150px]",
      title: "Tên outlet",
      dataIndex: "projectOutlet",
      render: (projectOutlet: ProjectOutletInterface) => projectOutlet.name,
    },
    {
      key: "boothName",
      className: "min-w-[150px]",
      title: "Loại booth",
      dataIndex: "projectBooth",
      render: (projectBooth: ProjectBoothInterface) => projectBooth.name,
    },
    {
      key: "address",
      className: "min-w-[150px]",
      title: "Địa chỉ",
      dataIndex: "projectOutlet",
      render: (projectOutlet: ProjectOutletInterface) => {
        const { district, province } = projectOutlet;
        const address = [];
        if (district) {
          address.push(district.name);
        }
        if (province) {
          address.push(province.name);
        }

        return address.join(", ");
      },
    },
    {
      key: "channelName",
      className: "min-w-[150px]",
      title: "Kênh",
      dataIndex: "projectOutlet",
      render: (projectOutlet: ProjectOutletInterface) =>
        projectOutlet.projectAgencyChannel.channel.name,
    },
    {
      key: "subChannelName",
      className: "min-w-[100px]",
      title: "Mức outlet",
      dataIndex: "projectOutlet",
      render: (_: unknown, record: { projectOutlet: ProjectOutletInterface }) =>
        record.projectOutlet.subChannel?.name,
    },
    {
      key: "agencyName",
      className: "min-w-[150px]",
      title: "Agency phụ trách",
      dataIndex: "projectAgency",
      render: (projectAgency: ProjectAgencyInterface) =>
        projectAgency.agency.name,
    },
    {
      key: "recordEmployee",
      className: "min-w-[150px]",
      title: "Nhân viên ghi nhận",
      dataIndex: "projectRecordEmployee",
      render: (projectRecordEmployee: ProjectRecordEmployeeInterface) => {
        const user = projectRecordEmployee.employee.user;

        return (
          <UserOptionComponent
            avatarUrl={user.picture}
            name={user.name}
            id={projectRecordEmployee.employee.id}
            roleName={projectRecordEmployee.employee.role.name}
            roleLocation="bottom"
          />
        );
      },
    },
    {
      key: "teamLeader",
      className: "min-w-[170px]",
      title: "Trưởng nhóm quản lý",
      dataIndex: "leader",
      render: (projectEmployeeUser: ProjectEmployeeUserInterface) => {
        const user = projectEmployeeUser.user;
        return (
          <UserOptionComponent
            avatarUrl={user.picture}
            name={user.name}
            id={projectEmployeeUser.id}
          />
        );
      },
    },
    {
      key: "attendance",
      className: "min-w-[150px]",
      title: "Thời gian chấm công",
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      render: (_: any, record: any) => {
        const {
          attendanceIn,
          attendanceOut,
        }: {
          attendanceIn?: AttendanceInterface;
          attendanceOut?: AttendanceInterface;
        } = record;

        return (
          <>
            <p>
              <span className="font-semibold">Vào: </span>
              {dayjs(attendanceIn?.deviceTime).format(DATETIME_FORMAT)}
            </p>
            <p>
              <span className="font-semibold">Ra: </span>
              {attendanceOut?.deviceTime
                ? dayjs(attendanceOut?.deviceTime).format(DATETIME_FORMAT)
                : ""}
            </p>
          </>
        );
      },
    },
    {
      key: "date",
      className: "min-w-[150px]",
      dataIndex: "date",
      title: "Ngày thực hiện",
      render: (value: string) => dayjs(value).format(DATE_FORMAT),
    },
    {
      key: "dataTimestamp",
      className: "min-w-[150px]",
      dataIndex: "dataTimestamp",
      title: "Thời gian gửi dữ liệu",
      render: (value: string) => dayjs(value).format(DATETIME_FORMAT),
    },
  ];

  return showColumns.map(({ tableColumn, fixed }) => {
    const column = columns.find((column) => column.key === tableColumn);

    if (column) {
      return {
        ...column,
        fixed,
      };
    }
    return {};
  });
};

export default getColumnsTableReport;
