import { AbstractEntityInterface } from "@/common/interface";
import {
  DistrictInterface,
  ProvinceInterface,
  WardInterface,
} from "@location/interface";
import { SubChannelInterface } from "../../subChannel/interface";
import { FeatureSchemeOutletInterface } from "../component/feature/config/types/customerInformationCapturing/interface";
import {
  OosGroupInterface,
  OosMergedProductInterface,
  OosProductInterface,
  OosThresholdInterface,
  OosZoneInterface,
} from "../component/feature/config/types/outOfStockStatus/interface";
import { ProjectLuckDrawItemInterface } from "../configLuckyWheel/interface";
import { ProjectEmployeeUserInterface } from "../employee/interface";
import {
  ProjectAgencyChannelInterface,
  ProjectAgencyInterface,
} from "../interface";
import { RegionInterface } from "../region/interface";

export interface ProjectOutletStockDetailInterface
  extends AbstractEntityInterface {
  featureOosThreshold: OosThresholdInterface;
  featureOosProduct: OosProductInterface | null;
  featureOosMergedProduct: OosMergedProductInterface | null;
  quantity: number;
}

export interface ProjectOutletStockInterface extends AbstractEntityInterface {
  featureOosZone: OosZoneInterface;
  stockDate: string;
  finalized: boolean;
  projectOutletStockDetails: ProjectOutletStockDetailInterface[];
}

export interface ProjectLuckyDrawAllocationInterface
  extends AbstractEntityInterface {
  allocatedDate: null | string;
  allocatedQuantity: number;
  issuedQuantity: number;
  projectLuckyDrawItem: ProjectLuckDrawItemInterface;
}

export interface ProjectOutletInterface extends AbstractEntityInterface {
  name: string;
  code: string;
  projectId: number;
  projectAgencyId: number;
  projectAgencyChannelId: number;
  projectEmployeeUserId: number;
  provinceId: number;
  province: ProvinceInterface;
  districtId: number;
  district: DistrictInterface;
  wardId: number;
  ward: WardInterface;
  streetName?: string;
  houseNumber?: string;
  subChannelId?: string;
  subChannel?: SubChannelInterface;
  latitude?: number | string;
  longitude?: number | string;
  projectAgency: ProjectAgencyInterface;
  projectAgencyChannel: ProjectAgencyChannelInterface;
  projectEmployeeUser: ProjectEmployeeUserInterface;
  isAvailable?: boolean;
  featureSchemeOutlets?: FeatureSchemeOutletInterface[];
  hasOvernightShift: boolean;
  featureOosGroupOutlets?: { featureOosGroup: OosGroupInterface }[];
  projectOutletStocks?: ProjectOutletStockInterface[];
  projectRegion?: RegionInterface;
  projectLuckyDrawAllocations?: ProjectLuckyDrawAllocationInterface[];
}

export interface ApiProjectOutletResponseInterface {
  entities: ProjectOutletInterface[];
  count: number;
}

export enum ProjectOutletActionEnum {
  INACTIVE = "INACTIVE",
  ACTIVE = "ACTIVE",
  DELETE = "DELETE",
  EDIT = "EDIT",
}

export const HAS_OVERNIGHT_SHIFT_TRUE_LABEL = "Ca qua ngày";
export const HAS_OVERNIGHT_SHIFT_FALSE_LABEL = "Ca trong ngày";
