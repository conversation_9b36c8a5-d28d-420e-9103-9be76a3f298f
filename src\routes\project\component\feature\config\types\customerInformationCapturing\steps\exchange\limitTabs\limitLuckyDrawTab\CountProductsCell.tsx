import { useLuckyDrawOrderLimitProductsQuery } from "../../service";

interface CountProductsCellProps {
  componentFeatureId: number;
  orderLuckyDrawLimitId: number;
}
const CountProductsCell = ({
  componentFeatureId,
  orderLuckyDrawLimitId,
}: CountProductsCellProps) => {
  const luckyDrawOrderLimitProductsQuery = useLuckyDrawOrderLimitProductsQuery(
    componentFeatureId,
    orderLuckyDrawLimitId,
  );

  return luckyDrawOrderLimitProductsQuery.data?.count ?? 0;
};

export default CountProductsCell;
