import { useApp } from "@/UseApp";
import { CURD, SELECT_ALL, SELECT_ALL_LABEL } from "@/common/constant";
import DragSortRowComponent from "@/components/DragSortRowComponent";
import TableActionCell from "@/components/TableActionCell";
import { useUrlFilters } from "@/hooks/useUrlFilters";
import {
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { DndContext, DragEndEvent } from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
  arrayMove,
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { Button, Col, Form, Input, Row, Select, Space, Table } from "antd";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import UrgencyModal from "./UrgencyModal";
import { FeatureUrgencyInterface } from "./interface";
import {
  useArrangementFeatureUrgencyMutation,
  useDeleteUrgencyMutation,
  useUrgenciesQuery,
} from "./service";

const UrgencyPage = () => {
  const componentFeatureId = parseInt(useParams().componentFeatureId ?? "0");
  const { openDeleteModal, showNotification } = useApp();

  const [searchForm] = Form.useForm();
  const [action, setAction] = useState<CURD | undefined>(undefined);
  const [selectedUrgency, setSelectedUrgency] = useState<
    FeatureUrgencyInterface | undefined
  >(undefined);
  const { filter, handleSearch } = useUrlFilters({
    formInstance: searchForm,
    handleSearchCallback: () => {
      urgenciesQuery.refetch();
    },
  });

  const urgenciesQuery = useUrgenciesQuery(componentFeatureId, {
    ...filter,
    take: 0,
    skip: 0,
  });

  const [dataSource, setDataSource] = useState(
    urgenciesQuery.data?.entities ?? [],
  );

  const deleteUrgencyMutation = useDeleteUrgencyMutation(componentFeatureId);
  const arrangementFeatureUrgencyMutation =
    useArrangementFeatureUrgencyMutation(componentFeatureId);

  const filterOptions = useMemo(() => {
    return [
      {
        label: SELECT_ALL_LABEL,
        value: SELECT_ALL,
      },
      {
        label: "Tên loại báo cáo khẩn",
        value: "name",
      },
    ];
  }, []);

  const actionEdit = useCallback(
    (record: FeatureUrgencyInterface) => {
      setAction(CURD.UPDATE);
      setSelectedUrgency(record);
    },
    [setSelectedUrgency],
  );

  const actionDelete = useCallback(
    (record: FeatureUrgencyInterface) => {
      openDeleteModal({
        content: (
          <>
            <p>Loại báo cáo khẩn bị xóa, không thể khôi phục</p>
            <p>
              Bạn vẫn muốn xóa{" "}
              <span className={"font-semibold"}>{record.name}</span> khỏi hệ
              thống?
            </p>
          </>
        ),
        deleteText: "Xác nhận xóa",
        loading: deleteUrgencyMutation.isPending,
        onCancel(): void {},
        onDelete: async () => {
          await deleteUrgencyMutation.mutateAsync(record.id);
          showNotification({
            type: "success",
            message: "Xóa kênh thành công",
          });
          urgenciesQuery.refetch();
        },
        title: `Xóa loại báo cáo khẩn`,
        titleError: "Không thể xóa loại báo cáo khẩn",
        contentHeader: (
          <>
            Không thể loại báo cáo khẩn{" "}
            <span className="font-semibold">{record.name}</span> bởi vì:
          </>
        ),
      });
    },
    [deleteUrgencyMutation, openDeleteModal, showNotification, urgenciesQuery],
  );

  const onDragEnd = useCallback(
    async ({ active, over }: DragEndEvent) => {
      if (!over) {
        return;
      }
      if (active.id !== over?.id) {
        setDataSource((previous) => {
          const activeIndex = previous?.findIndex((i) => i.id === active.id);
          const overIndex = previous?.findIndex((i) => i.id === over?.id);
          return arrayMove(previous, activeIndex, overIndex);
        });

        await arrangementFeatureUrgencyMutation.mutateAsync({
          activeId: active.id as number,
          overFeatureUrgencyId: over?.id as number,
        });
      }
    },
    [arrangementFeatureUrgencyMutation],
  );

  useEffect(() => {
    setDataSource(
      (urgenciesQuery.data?.entities ?? []).sort(
        (a, b) => a.ordinal - b.ordinal,
      ),
    );
  }, [urgenciesQuery.data]);

  return (
    <>
      <div className="bg-white pt-5 pl-10 pr-10 rounded pb-5">
        <Row justify={"space-between"} className={"mb-6"}>
          <Col>
            <Form
              layout="inline"
              form={searchForm}
              onFinish={handleSearch}
              initialValues={{ filterField: SELECT_ALL }}
            >
              <Space.Compact>
                <Form.Item
                  name="filterField"
                  style={{ marginInlineEnd: "0px", borderRight: "none" }}
                >
                  <Select style={{ width: "150px" }} options={filterOptions} />
                </Form.Item>
                <Form.Item name="filterValue">
                  <Input
                    prefix={<SearchOutlined />}
                    placeholder={"Nhập nội dung cần tìm"}
                    allowClear
                  />
                </Form.Item>
              </Space.Compact>

              <Form.Item>
                <Button htmlType="submit" type="default">
                  Tìm kiếm
                </Button>
              </Form.Item>
            </Form>
          </Col>

          <Col>
            <Button
              icon={<PlusOutlined />}
              type="primary"
              onClick={() => {
                setAction(CURD.CREATE);
              }}
            >
              Thêm mới
            </Button>
          </Col>
        </Row>

        <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
          <SortableContext
            items={dataSource.map((i) => i.id)}
            strategy={verticalListSortingStrategy}
          >
            <Table
              loading={
                arrangementFeatureUrgencyMutation.isPending ||
                urgenciesQuery.isFetching ||
                urgenciesQuery.isPending
              }
              rowKey={"id"}
              components={{
                body: {
                  row: DragSortRowComponent,
                },
              }}
              dataSource={dataSource}
              columns={[
                {
                  key: "sort",
                },
                {
                  title: "Tên loại báo cáo khẩn",
                  dataIndex: "name",
                },
                {
                  title: "Nhập thêm ghi chú",
                  dataIndex: "isNoteRequired",
                  render: (value) =>
                    value ? "Cho phép nhập thêm ghi chú" : "-",
                },
                {
                  title: "Thời gian tiêu chuẩn",
                  dataIndex: "maxDuration",
                },
                {
                  render: (record) => {
                    return (
                      <TableActionCell
                        actions={[
                          {
                            key: "edit",
                            action: actionEdit,
                          },
                          {
                            key: "delete",
                            action: actionDelete,
                          },
                        ]}
                        items={[
                          {
                            key: "edit",
                            label: "Chỉnh sửa",
                            icon: <EditOutlined />,
                          },
                          {
                            key: "delete",
                            label: "Xóa",
                            icon: <DeleteOutlined />,
                          },
                        ]}
                        record={record}
                      />
                    );
                  },
                },
              ]}
              pagination={false}
            />
          </SortableContext>
        </DndContext>
      </div>

      {action && (
        <UrgencyModal
          action={action}
          componentFeatureId={componentFeatureId}
          cb={() => {
            setAction(undefined);
            urgenciesQuery.refetch();
          }}
          selectedUrgency={selectedUrgency}
          cancelCb={() => {
            setAction(undefined);
            setSelectedUrgency(undefined);
          }}
        />
      )}
    </>
  );
};

export default UrgencyPage;
