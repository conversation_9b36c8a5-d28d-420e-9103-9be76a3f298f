import { CURD, DATETIME_FORMAT } from "@/common/constant";
import TableActionCell from "@/components/TableActionCell";
import RecordSamplingValuesCell from "@/routes/project/report/types/sampling/RecordSamplingValuesCell";
import { EditOutlined } from "@ant-design/icons";
import { Table } from "antd";
import dayjs from "dayjs";
import _ from "lodash";
import { useState } from "react";
import { useParams } from "react-router-dom";
import AttendanceDetailRow from "../../AttendanceDetailRow";
import {
  useAttendanceFeatureDetailQuery,
  useAttendanceQuery,
} from "../../service";
import EditSamplingModal from "./EditSamplingModal";
import { useEditSamplingQuery } from "./service";

const EditSamplingPage = () => {
  const projectId = parseInt(useParams().id ?? "0");
  const attendanceId = parseInt(useParams().attendanceId ?? "0");
  const componentFeatureId = parseInt(useParams().componentFeatureId ?? "0");

  const [action, setAction] = useState<CURD | undefined>(undefined);

  const attendanceQuery = useAttendanceQuery(projectId, attendanceId);
  const attendanceFeatureDetailQuery = useAttendanceFeatureDetailQuery(
    projectId,
    attendanceId,
    componentFeatureId,
  );
  const editSamplingQuery = useEditSamplingQuery(
    projectId,
    attendanceId,
    componentFeatureId,
  );

  return (
    <>
      <h2>{attendanceFeatureDetailQuery.data?.name}</h2>
      <div className="bg-white p-10 rounded">
        <AttendanceDetailRow
          projectRecordEmployee={attendanceQuery.data?.projectRecordEmployee}
          createdAt={attendanceQuery.data?.timeIn}
          updatedAt={attendanceQuery.data?.timeOut}
        />

        <Table
          loading={editSamplingQuery.isLoading || editSamplingQuery.isFetching}
          rowKey={(o) => o?.id ?? ""}
          className="mt-10"
          dataSource={
            editSamplingQuery.data ? [editSamplingQuery.data] : [undefined]
          }
          columns={[
            {
              title: "Sampling - Quy cách phát - Quy cách đóng gói",
              render: (_, record) => {
                const { recordSamplingValues } = record ?? {};
                recordSamplingValues?.sort((a, b) => b.value - a.value);
                for (const recordSamplingValue of recordSamplingValues ?? []) {
                  recordSamplingValue.featureSampling =
                    attendanceFeatureDetailQuery.data?.featureSamplings?.find(
                      (item) =>
                        item.id === recordSamplingValue.featureSamplingId,
                    );
                }
                if (recordSamplingValues)
                  return (
                    <RecordSamplingValuesCell
                      recordSamplingValues={recordSamplingValues ?? []}
                    />
                  );

                return "-";
              },
            },
            {
              title: "Thời gian dùng tool edit",
              render: (_, record) => {
                const { toolSampling } = record ?? {};
                return toolSampling
                  ? dayjs(toolSampling?.createdAt).format(DATETIME_FORMAT)
                  : "-";
              },
            },
            {
              title: "Người dùng tool edit",
              render: (_, record) => {
                const { toolSampling } = record ?? {};
                if (toolSampling)
                  return <>{toolSampling?.createdByUser.name}</>;

                return "-";
              },
            },
            {
              render: (_, record) => {
                if (!record)
                  return (
                    <TableActionCell
                      actions={[
                        {
                          key: CURD.CREATE,
                          action: () => setAction(CURD.CREATE),
                        },
                      ]}
                      items={[
                        {
                          key: CURD.CREATE,
                          label: "Thêm mới",
                          icon: <EditOutlined />,
                        },
                      ]}
                      record={{ id: 0 }}
                    />
                  );

                return (
                  <TableActionCell
                    actions={[
                      {
                        key: CURD.UPDATE,
                        action: () => setAction(CURD.UPDATE),
                      },
                    ]}
                    items={[
                      {
                        key: CURD.UPDATE,
                        label: "Chỉnh sửa",
                        icon: <EditOutlined />,
                      },
                    ]}
                    record={record}
                  />
                );
              },
            },
          ]}
          pagination={false}
        />
      </div>

      {action && (
        <EditSamplingModal
          title={
            action === CURD.CREATE
              ? `Thêm ${_.lowerFirst(attendanceFeatureDetailQuery.data?.name)}`
              : `Chỉnh sửa ${_.lowerFirst(attendanceFeatureDetailQuery.data?.name)}`
          }
          componentFeature={attendanceFeatureDetailQuery.data}
          editSampling={editSamplingQuery.data}
          projectId={projectId}
          attendanceId={attendanceId}
          componentFeatureId={componentFeatureId}
          cb={() => {
            setAction(undefined);
            editSamplingQuery.refetch();
          }}
          cancelCb={() => setAction(undefined)}
          createdAt={attendanceQuery.data?.createdAt}
          projectAgencyId={
            attendanceQuery.data?.projectRecordEmployee.projectRecord
              .projectAgencyId
          }
          action={action}
        />
      )}
    </>
  );
};

export default EditSamplingPage;
