import { UserTypeEnum } from "@/routes/user/interface";
import { useApp } from "@/UseApp";
import { useMutation, useQuery } from "@tanstack/react-query";
import { RegionInterface } from "./interface";

export const useCreateProjectRegionMutation = (projectId: number) => {
  const { axiosPost, showNotification } = useApp();

  return useMutation({
    mutationKey: ["createProjectRegion", projectId],
    mutationFn: (data: {
      tag: string;
      name: string;
      parentId: number | null;
    }) => axiosPost(`/projects/${projectId}/regions`, data),

    onSuccess: () => {
      showNotification({
        type: "success",
        message: `Tạo khu vực thành công`,
      });
    },
  });
};

export const useProjectRegionTreesQuery = (projectId: number) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["projectRegionTrees", projectId],
    queryFn: () =>
      axiosGet<RegionInterface[], unknown>(
        `/projects/${projectId}/regions/trees`,
      ),
  });
};

export const useDeleteProjectRegionMutation = (projectId: number) => {
  const { axiosDelete, showNotification } = useApp();

  return useMutation({
    mutationKey: ["deleteProjectRegion", projectId],
    mutationFn: (id: number) =>
      axiosDelete(`/projects/${projectId}/regions/${id}`),

    onSuccess: () => {
      showNotification({
        type: "success",
        message: `Xóa khu vực thành công`,
      });
    },
  });
};

export const useUpdateProjectRegionMutation = (projectId: number) => {
  const { axiosPatch, showNotification } = useApp();

  return useMutation({
    mutationKey: ["updateProjectRegion", projectId],
    mutationFn: ({
      id,
      data,
    }: {
      id: number;
      data: { name?: string; parentId?: number | null; tag?: string };
    }) => axiosPatch(`/projects/${projectId}/regions/${id}`, data),

    onSuccess: () => {
      showNotification({
        type: "success",
        message: `Cập nhật khu vực thành công`,
      });
    },
  });
};

export const useProjectRegionTreesForMeQuery = (
  projectId: number,
  enabled?: boolean,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["projectRegionTreesForMe", projectId],
    queryFn: () =>
      axiosGet<RegionInterface[], unknown>(
        `/projects/${projectId}/users/me/regions-tree`,
      ),
    enabled,
  });
};

export const useProjectRegions = (projectId: number) => {
  const { userLogin } = useApp();

  const projectRegionTreesQuery = useProjectRegionTreesQuery(projectId);
  const projectRegionTreesForMeQuery = useProjectRegionTreesForMeQuery(
    projectId,
    userLogin?.type === UserTypeEnum.MANAGER,
  );

  if (userLogin?.type === UserTypeEnum.MANAGER) {
    return projectRegionTreesForMeQuery.data;
  }
  return projectRegionTreesQuery.data;
};
