import { Col, Row } from "antd";
import { useCallback, useEffect, useMemo } from "react";
import Bar<PERSON>hart from "../../chart-types/BarChart";
import ChartContanier from "../../ChartContanier";
import {
  ChartDataInterface,
  ChartTabProps,
  StatisticsTypeEnumToLabel,
} from "../../interface";
import {
  useChannelStatisticsQuery,
  useProvinceStatisticsQuery,
} from "../../service";

const TabTwoChartBar = ({
  projectId,
  filter,
  sort,
  type,
  activeKey,
}: ChartTabProps) => {
  const provinceStatisticsQuery = useProvinceStatisticsQuery(
    projectId,
    type,
    filter,
  );

  const channelStatisticsQuery = useChannelStatisticsQuery(
    projectId,
    type,
    filter,
  );

  const toThreeItems = useCallback(
    (item: ChartDataInterface, labelKey: "channelName" | "provinceName") => {
      const { kpi, totalValue, todayValue } = item;
      return [
        {
          label: item[labelKey],
          type: "KPI",
          value: kpi,
          KPI: kpi,
          UTD: totalValue,
          Today: todayValue,
        },
        {
          label: item[labelKey],
          type: "UTD",
          value: totalValue,
          KPI: kpi,
          UTD: totalValue,
          Today: todayValue,
        },
      ];
    },
    [],
  );

  const provinceStatistics = useMemo(() => {
    const data = provinceStatisticsQuery.data ?? [];
    if (sort === undefined)
      return data.flatMap((item) => toThreeItems(item, "provinceName"));

    const sortedData = [...data];
    if (sort === "asc") {
      sortedData.sort((a, b) => a.totalValue - b.totalValue);
    } else {
      sortedData.sort((a, b) => b.totalValue - a.totalValue);
    }

    return sortedData.flatMap((item) => toThreeItems(item, "provinceName"));
  }, [provinceStatisticsQuery.data, sort, toThreeItems]);

  const channelStatistics = useMemo(() => {
    const data = channelStatisticsQuery.data ?? [];
    if (sort === undefined)
      return data.flatMap((item) => toThreeItems(item, "channelName"));

    const sortedData = [...data];
    if (sort === "asc") {
      sortedData.sort((a, b) => a.totalValue - b.totalValue);
    } else {
      sortedData.sort((a, b) => b.totalValue - a.totalValue);
    }

    return sortedData.flatMap((item) => toThreeItems(item, "channelName"));
  }, [channelStatisticsQuery.data, sort, toThreeItems]);

  useEffect(() => {
    if (activeKey === "SALES") {
      provinceStatisticsQuery.refetch();
      channelStatisticsQuery.refetch();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeKey]);

  return (
    <Row gutter={16} className="mt-0 pt-0 mb-10">
      <Col md={12}>
        <ChartContanier>
          <BarChart
            title={`${StatisticsTypeEnumToLabel[type]} theo tỉnh`}
            subtitle="Subtitle"
            data={provinceStatistics}
            xField="label"
            yField="value"
            loading={
              provinceStatisticsQuery.isLoading ||
              provinceStatisticsQuery.isFetching
            }
            colorField="type"
          />
        </ChartContanier>
      </Col>

      <Col md={12}>
        <ChartContanier>
          <BarChart
            title={`${StatisticsTypeEnumToLabel[type]} theo kênh`}
            subtitle="Subtitle"
            data={channelStatistics}
            xField="label"
            yField="value"
            loading={
              channelStatisticsQuery.isLoading ||
              channelStatisticsQuery.isFetching
            }
            colorField="type"
          />
        </ChartContanier>
      </Col>
    </Row>
  );
};

export default TabTwoChartBar;
