import { useApp } from "@/UseApp";
import { AbstractFilterInterface } from "@/common/interface";
import { useMutation, useQuery } from "@tanstack/react-query";
import { QuantityEntityInterface } from "./interface";

export const useQuantitiesAvailableQuery = (
  componentFeatureId: number,
  filter?: object & AbstractFilterInterface,
  enabled?: boolean,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["quantitiesAvailable", componentFeatureId, filter],
    queryFn: () =>
      axiosGet<{ entities: QuantityEntityInterface[]; count: number }, unknown>(
        `/features/${componentFeatureId}/quantities/availables`,
        filter,
      ),
    enabled,
  });
};

export const useQuantitiesQuery = (
  componentFeatureId: number,
  filter?: object & AbstractFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["quantities", componentFeatureId, filter],
    queryFn: () =>
      axiosGet<{ entities: QuantityEntityInterface[]; count: number }, unknown>(
        `/features/${componentFeatureId}/quantities`,
        filter,
      ),
  });
};

export const useCreateQuantitiesMutation = (componentFeatureId: number) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createQuantities", componentFeatureId],
    mutationFn: (
      data: {
        projectProductId?: number | null;
        projectItemId?: number | null;
      }[],
    ) => axiosPost(`/features/${componentFeatureId}/quantities`, data),
  });
};

export const useUpdateIsActiveQuantityMutation = (
  componentFeatureId: number,
) => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["updateIsActiveQuantity", componentFeatureId],
    mutationFn: (data: { isActive: boolean; id: number }) =>
      axiosPatch(`/features/${componentFeatureId}/quantities/${data.id}`, data),
  });
};

export const useDeleteQuantityMutation = (componentFeatureId: number) => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deleteQuantity", componentFeatureId],
    mutationFn: (id: number) =>
      axiosDelete(`/features/${componentFeatureId}/quantities/${id}`),
  });
};

export const useArrangeQuantityMutation = (componentFeatureId: number) => {
  const { axiosPut } = useApp();

  return useMutation({
    mutationKey: ["arrangeQuantity", componentFeatureId],
    mutationFn: (data: { id: number; overId: number }) =>
      axiosPut(
        `/features/${componentFeatureId}/quantities/${data.id}/arrangement`,
        {
          overFeatureQuantityId: data.overId,
        },
      ),
  });
};
