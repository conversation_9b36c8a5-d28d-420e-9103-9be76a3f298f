import { AbstractFilterInterface } from "@/common/interface";
import { useApp } from "@/UseApp";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  OosMergedProductInterface,
  OosProductInterface,
} from "../../interface";

export const useOosThresholdProductsQuery = (
  featureId: number,
  filter?: AbstractFilterInterface & {
    featureOosGroupId?: number;
    projectBrandId?: number;
  },
  enabled?: boolean,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["oosThresholdProducts", featureId, filter],
    queryFn: () =>
      axiosGet<
        {
          entities: OosProductInterface[];
          count: number;
        },
        unknown
      >(`/features/${featureId}/oos-thresholds/products`, filter),
    enabled,
  });
};

export const useOosThresholdMergedProductsQuery = (
  featureId: number,
  filter?: AbstractFilterInterface & {
    featureOosGroupId?: number;
    projectBrandId?: number;
  },
  enabled?: boolean,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["oosThresholdMergedProducts", featureId, filter],
    queryFn: () =>
      axiosGet<
        {
          entities: OosMergedProductInterface[];
          count: number;
        },
        unknown
      >(`/features/${featureId}/oos-thresholds/merged-products`, filter),
    enabled,
  });
};

export const useCreateOosThresholdMutation = (featureId: number) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createOosThreshold", featureId],
    mutationFn: (data: {
      featureOosGroupId: number;
      featureOosProductId?: number;
      featureOosMergedProductId?: number;
      thresholds: {
        featureOosLevelId: number;
        upperValue?: number;
        lowerValue?: number;
      }[];
    }) => axiosPost(`/features/${featureId}/oos-thresholds`, data),
  });
};
