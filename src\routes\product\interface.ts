import { AbstractEntityInterface, ImageInterface } from "@/common/interface";
import { BrandInterface } from "../brand/interface";
import { UnitInterface } from "../unit/UnitPage";

export interface ProductPackagingInterface extends AbstractEntityInterface {
  unit: UnitInterface;
  barcode: string;
  price: number;
  rate?: number;
  unitId: number;
  product: ProductInterface;
  isAvailable?: boolean;
  isMainPackaging?: boolean;
}

export interface ProductInterface extends AbstractEntityInterface {
  name: string;
  code: string;
  isActive: boolean;
  id: number;
  image?: ImageInterface;
  brand: BrandInterface;
  mainPackagingId: number;
  packagings: ProductPackagingInterface[];
  mainPackaging: ProductPackagingInterface;
  shortName?: string;
}
