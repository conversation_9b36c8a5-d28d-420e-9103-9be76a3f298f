import { AbstractFilterInterface } from "@/common/interface";
import { useApp } from "@/UseApp";
import { useMutation, useQuery } from "@tanstack/react-query";
import { FeatureUrgencyInterface } from "./interface";

export const useUrgenciesQuery = (
  featureId: number,
  filter?: AbstractFilterInterface & object,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["urgencies", featureId, filter],
    queryFn: () =>
      axiosGet<{ entities: FeatureUrgencyInterface[]; count: number }, unknown>(
        `/features/${featureId}/urgencies`,
        filter,
      ),
  });
};

export const useCreateUrgencyMutation = (featureId: number) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationFn: (data: {
      name: string;
      description?: string;
      isNoteRequired?: boolean;
      maxDuration?: number;
    }) => axiosPost(`/features/${featureId}/urgencies`, data),
  });
};

export const useUpdateUrgencyMutation = (featureId: number) => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationFn: (data: {
      id: number;
      name?: string;
      description?: string;
      isNoteRequired?: boolean;
      maxDuration?: number;
    }) => axiosPatch(`/features/${featureId}/urgencies/${data.id}`, data),
  });
};

export const useDeleteUrgencyMutation = (featureId: number) => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationFn: (id: number) =>
      axiosDelete(`/features/${featureId}/urgencies/${id}`),
  });
};

export const useArrangementFeatureUrgencyMutation = (featureId: number) => {
  const { axiosPut } = useApp();

  return useMutation({
    mutationKey: ["arrangementFeatureUrgency", featureId],
    mutationFn: ({
      activeId,
      overFeatureUrgencyId,
    }: {
      activeId: number;
      overFeatureUrgencyId: number;
    }) =>
      axiosPut(`/features/${featureId}/urgencies/${activeId}/arrangement`, {
        overFeatureUrgencyId: overFeatureUrgencyId,
      }),
  });
};
