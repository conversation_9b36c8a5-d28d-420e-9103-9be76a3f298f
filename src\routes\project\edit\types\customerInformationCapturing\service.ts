import { AbstractFilterInterface } from "@/common/interface.ts";
import { useApp } from "@/UseApp.tsx";
import { RecordCustomerInterface } from "@project/report/types/customerInformationCapturing/interface.ts";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  CreateCustomerInformationDto,
  CustomerInformationDto,
  EditRecordOrderInterface,
} from "./interface.ts";

export const useCreateCustomerInformationMutation = (
  projectId: number,
  attendanceId?: number,
  componentFeatureId?: number,
) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: [
      "createCustomerInformation",
      projectId,
      attendanceId,
      componentFeatureId,
    ],
    mutationFn: (data: CreateCustomerInformationDto) =>
      axiosPost(
        `/tools/projects/${projectId}/attendances/${attendanceId}/features/${componentFeatureId}/orders`,
        data,
      ),
  });
};

export const useEditOrdersQuery = (
  projectId: number,
  attendanceId?: number,
  componentFeatureId?: number,
  filter?: {
    usesUpdateTool?: boolean;
    usesCreateTool?: boolean;
  } & AbstractFilterInterface,
  enabled?: boolean,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: [
      "editOrders",
      projectId,
      attendanceId,
      componentFeatureId,
      filter,
    ],
    queryFn: () =>
      axiosGet<
        { entities: EditRecordOrderInterface[]; count: number },
        unknown
      >(
        `/tools/projects/${projectId}/attendances/${attendanceId}/features/${componentFeatureId}/orders`,
        filter,
      ),
    enabled: !!projectId && !!attendanceId && !!componentFeatureId && !!enabled,
  });
};

export const useEditOrderQuery = (
  projectId: number,
  attendanceId?: number,
  componentFeatureId?: number,
  orderId?: number,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: [
      "editOrder",
      projectId,
      attendanceId,
      componentFeatureId,
      orderId,
    ],
    queryFn: () =>
      axiosGet<EditRecordOrderInterface, unknown>(
        `/tools/projects/${projectId}/attendances/${attendanceId}/features/${componentFeatureId}/orders/${orderId}`,
      ),
    enabled: !!projectId && !!attendanceId && !!componentFeatureId && !!orderId,
  });
};

export const useUpdateCustomerInformationMutation = (
  projectId: number,
  attendanceId?: number,
  componentFeatureId?: number,
) => {
  const { axiosPut } = useApp();

  return useMutation({
    mutationKey: [
      "updateCustomerInformation",
      projectId,
      attendanceId,
      componentFeatureId,
    ],
    mutationFn: ({ data, id }: { data: CustomerInformationDto; id: number }) =>
      axiosPut(
        `/tools/projects/${projectId}/attendances/${attendanceId}/features/${componentFeatureId}/orders/${id}`,
        data,
      ),
  });
};

export const useGetOrderCustomerMutation = (
  projectId: number,
  attendanceId?: number,
  componentFeatureId?: number,
) => {
  const { axiosGet } = useApp();

  return useMutation({
    mutationKey: [
      "getOrderCustomer",
      projectId,
      attendanceId,
      componentFeatureId,
    ],
    mutationFn: (fields: string[]) =>
      axiosGet<{ customerInfos: RecordCustomerInterface[] }, unknown>(
        `/tools/projects/${projectId}/attendances/${attendanceId}/features/${componentFeatureId}/orders/customer`,
        { fields: fields },
      ),
  });
};

export const useDeleteOrderMutation = (
  projectId: number,
  attendanceId?: number,
  componentFeatureId?: number,
) => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deleteOrder", projectId, attendanceId, componentFeatureId],
    mutationFn: (orderId: number) =>
      axiosDelete(
        `/tools/projects/${projectId}/attendances/${attendanceId}/features/${componentFeatureId}/orders/${orderId}`,
      ),
  });
};
