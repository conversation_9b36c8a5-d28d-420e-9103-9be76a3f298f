import { CURD } from "@/common/constant.ts";
import ModalCURD from "@/components/ModalCURD.tsx";
import { useApp } from "@/UseApp.tsx";
import { OosGroupInterface } from "@project/component/feature/config/types/outOfStockStatus/interface.ts";
import {
  useCreateOosGroupMutation,
  useUpdateOosGroupMutation,
} from "./service";
import { Form, Input } from "antd";
import { useCallback, useEffect } from "react";

interface OutOfStockStatusGroupModalProps {
  cancelCb: () => void;
  componentFeatureId: number;
  cb: () => void;
  action: CURD | null;
  selectedFeatureOosGroup: OosGroupInterface | undefined;
}

const OutOfStockStatusGroupModal = ({
  cancelCb,
  componentFeatureId,
  cb,
  action,
  selectedFeatureOosGroup,
}: OutOfStockStatusGroupModalProps) => {
  const { showNotification } = useApp();

  const [form] = Form.useForm();

  const createOosGroupMutation = useCreateOosGroupMutation(componentFeatureId);
  const updateOosGroupMutation = useUpdateOosGroupMutation(componentFeatureId);

  const formContent = (
    <Form.Item
      label={"Tên nhóm sản phẩm"}
      name={"name"}
      rules={[{ required: true }]}
    >
      <Input />
    </Form.Item>
  );

  const onFinish = useCallback(async () => {
    if (action === CURD.CREATE) {
      await createOosGroupMutation.mutateAsync(form.getFieldsValue());

      showNotification({
        type: "success",
        message: "Thêm nhóm sản phẩm thành công",
      });
    }

    if (action === CURD.UPDATE) {
      await updateOosGroupMutation.mutateAsync({
        ...form.getFieldsValue(),
        id: selectedFeatureOosGroup?.id,
      });

      showNotification({
        type: "success",
        message: "Chỉnh sửa nhóm sản phẩm thành công",
      });
    }

    cb();
  }, [
    action,
    cb,
    createOosGroupMutation,
    form,
    selectedFeatureOosGroup?.id,
    showNotification,
    updateOosGroupMutation,
  ]);

  useEffect(() => {
    if (action === CURD.UPDATE && selectedFeatureOosGroup) {
      form.setFieldsValue({
        name: selectedFeatureOosGroup.name,
      });
    }
  }, [action, form, selectedFeatureOosGroup]);

  return (
    <ModalCURD
      title={
        action === CURD.CREATE
          ? "Thêm nhóm sản phẩm"
          : "Chỉnh sửa nhóm sản phẩm"
      }
      isOpen={true}
      formContent={formContent}
      form={form}
      onFinish={onFinish}
      onCancelCb={() => {
        form.resetFields();
        cancelCb();
      }}
      action={action}
    />
  );
};

export default OutOfStockStatusGroupModal;
