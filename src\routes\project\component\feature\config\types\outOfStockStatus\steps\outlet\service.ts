import { AbstractFilterInterface } from "@/common/interface.ts";
import { useApp } from "@/UseApp.tsx";
import { OosGroupOutletInterface } from "@project/component/feature/config/types/outOfStockStatus/interface.ts";
import { ProjectOutletInterface } from "@project/outlet/interface.ts";
import { useMutation, useQuery } from "@tanstack/react-query";

export const useAddOosGroupOutletMutation = (featureId: number, id: number) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["addOosGroupOutlet", featureId, id],
    mutationFn: ({ projectOutletIds }: { projectOutletIds: number[] }) =>
      axiosPost(`/features/${featureId}/oos-groups/${id}/outlets`, {
        projectOutletIds,
      }),
  });
};

export const useDeleteOosGroupOutletMutation = (
  featureId: number,
  id: number,
) => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deleteOosGroupOutlet", featureId, id],
    mutationFn: ({
      featureOosGroupOutletIds,
    }: {
      featureOosGroupOutletIds: number[];
    }) =>
      axiosDelete(`/features/${featureId}/oos-groups/${id}/outlets`, {
        featureOosGroupOutletIds,
      }),
  });
};

export const useOosGroupOutletsQuery = (
  featureId: number,
  id: number,
  filter?: AbstractFilterInterface & object,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["oosGroupOutlets", featureId, id, filter],
    queryFn: () =>
      axiosGet<
        {
          entities: OosGroupOutletInterface[];
          count: number;
        },
        unknown
      >(`/features/${featureId}/oos-groups/${id}/outlets`, filter),
  });
};

export const useOosGroupOutletAvailablesQuery = (
  featureId: number,
  filter?: AbstractFilterInterface & object,
  enabled?: boolean,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["oosGroupOutletAvailables", featureId, filter],
    queryFn: () =>
      axiosGet<
        {
          entities: ProjectOutletInterface[];
          count: number;
        },
        unknown
      >(`/features/${featureId}/oos-groups/outlet-availables`, filter),
    enabled,
  });
};
