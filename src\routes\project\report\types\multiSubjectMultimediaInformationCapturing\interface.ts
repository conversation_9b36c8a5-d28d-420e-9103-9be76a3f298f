import { AbstractEntityInterface, ImageInterface } from "@/common/interface";
import { MultiSubjectMultimediaInformationCapturingInterface } from "@project/component/feature/config/types/multiSubjectMultimediaInformationCapturing/interface";

export interface RecordMultimediaInterface extends AbstractEntityInterface {
  dataUuid: string;
  dataTimestamp: string;
  featureMultimediaId: number;
  featureMultimedia: MultiSubjectMultimediaInformationCapturingInterface;
  value: string;
}

export interface RecordMultimediaPhotoInterface
  extends AbstractEntityInterface {
  id: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  dataUuid: string;
  dataTimestamp: string;
  featureMultimediaId: number;
  featureMultimedia: MultiSubjectMultimediaInformationCapturingInterface;
  image: ImageInterface;
}
