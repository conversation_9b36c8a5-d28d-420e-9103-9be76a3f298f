import { CURD } from "@/common/constant";
import ModalCURD from "@/components/ModalCURD";
import { useApp } from "@/UseApp";
import { Form, FormInstance, Input } from "antd";
import { useCallback } from "react";
import { useParams } from "react-router-dom";
import {
  useCreateGroupSamplingMutation,
  useUpdateGroupSamplingMutation,
} from "../../service";

interface ConfigGroupSamplingModalProps {
  isOpen: boolean;
  onCancelCb: () => void;
  action: CURD | null;
  form: FormInstance;
}

const ConfigGroupSamplingModal = ({
  isOpen,
  onCancelCb,
  action,
  form,
}: ConfigGroupSamplingModalProps) => {
  const componentFeatureId = parseInt(useParams().componentFeatureId ?? "0");
  const { showNotification } = useApp();

  const createGroupSamplingMutation =
    useCreateGroupSamplingMutation(componentFeatureId);
  const updateGroupSamplingMutation =
    useUpdateGroupSamplingMutation(componentFeatureId);

  const onFinish = useCallback(async () => {
    if (action === CURD.CREATE) {
      await createGroupSamplingMutation.mutateAsync(form.getFieldsValue());

      showNotification({
        type: "success",
        message: "Tạo nhóm sampling thành công",
      });
    }

    if (action === CURD.UPDATE) {
      await updateGroupSamplingMutation.mutateAsync({
        id: form.getFieldValue("id"),
        name: form.getFieldValue("name"),
      });

      showNotification({
        type: "success",
        message: "Cập nhật nhóm sampling thành công",
      });
    }

    onCancelCb();
  }, [
    action,
    createGroupSamplingMutation,
    form,
    onCancelCb,
    showNotification,
    updateGroupSamplingMutation,
  ]);

  const formContent = (
    <>
      {action === CURD.CREATE && (
        <p className="text-hint">
          Bạn có thể phân bổ outlet vào các nhóm sampling để đáp ứng cho việc
          mỗi khu vực, tỉnh... áp dụng tỷ lệ quy đổi sampling khác nhau
        </p>
      )}

      <Form.Item
        label="Tên nhóm sampling"
        name={"name"}
        rules={[{ required: true }]}
      >
        <Input />
      </Form.Item>
    </>
  );

  return (
    <ModalCURD
      title={
        action === CURD.CREATE
          ? "Thêm nhóm sampling"
          : "Chỉnh sửa tên nhóm sampling"
      }
      isOpen={isOpen}
      formContent={formContent}
      form={form}
      onFinish={onFinish}
      action={action}
      onCancelCb={onCancelCb}
    />
  );
};

export default ConfigGroupSamplingModal;
