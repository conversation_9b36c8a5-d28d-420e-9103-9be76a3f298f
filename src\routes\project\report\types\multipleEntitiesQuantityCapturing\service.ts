import { useApp } from "@/UseApp";
import { AbstractFilterInterface } from "@/common/interface";
import { AppContextInterface } from "@/interface";
import { useMutation, useQuery } from "@tanstack/react-query";
import { ReportQuantityInterface } from "./interface";

export const getReportQuantities = (
  axiosGet: AppContextInterface["axiosGet"],
  projectId: number,
  componentFeatureId: number,
  filter?: object & AbstractFilterInterface,
) =>
  axiosGet<
    {
      entities: ReportQuantityInterface[];
      count: number;
    },
    unknown
  >(
    `/projects/${projectId}/report/features/${componentFeatureId}/quantities`,
    filter,
  );

export const useReportQuantitiesQuery = (
  projectId: number,
  componentFeatureId: number,
  filter?: object & AbstractFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["reportQuantities", projectId, componentFeatureId, filter],
    queryFn: () =>
      getReportQuantities(axiosGet, projectId, componentFeatureId, filter),
  });
};

export const useGetReportQuantitiesMutation = (
  projectId: number,
  componentFeatureId: number,
) => {
  const { axiosGet } = useApp();

  return useMutation({
    mutationKey: ["getReportQuantities", projectId, componentFeatureId],
    mutationFn: (filter?: object & AbstractFilterInterface) =>
      getReportQuantities(axiosGet, projectId, componentFeatureId, filter),
  });
};
