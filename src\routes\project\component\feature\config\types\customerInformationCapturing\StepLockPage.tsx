import { LockTwoTone, UnlockTwoTone } from "@ant-design/icons";
import { UseQueryResult } from "@tanstack/react-query";
import { Button } from "antd";
import { useCallback } from "react";
import { useParams } from "react-router-dom";
import { OrderEnumToProperty, OrderInterface, StepLockEnum } from "./interface";
import { useUpdateStepStatusMutation } from "./service";

export default function StepLockPage(props: {
  readonly title: string;
  readonly description: string;
  readonly type: StepLockEnum;
  readonly orderLatestQuery: UseQueryResult<OrderInterface, unknown>;
  readonly locked?: boolean;
  readonly unlockNode?: JSX.Element;
}) {
  const { title, description, type, orderLatestQuery, locked, unlockNode } =
    props;

  const componentFeatureId = parseInt(useParams().componentFeatureId ?? "0");

  const updateStepStatusMutation =
    useUpdateStepStatusMutation(componentFeatureId);

  const onClick = useCallback(async () => {
    await updateStepStatusMutation.mutateAsync({
      [OrderEnumToProperty[type]]: true,
    });
    orderLatestQuery.refetch();
  }, [orderLatestQuery, type, updateStepStatusMutation]);

  return (
    <div className="flex justify-center w-full h-full items-center border border-solid border-[#ECEDEF] rounded">
      <div className="text-center">
        {locked ? (
          <LockTwoTone twoToneColor="#DF3C3C" style={{ fontSize: "50px" }} />
        ) : (
          <UnlockTwoTone twoToneColor="#DF3C3C" style={{ fontSize: "50px" }} />
        )}
        <p className="font-semibold text-lg">{title}</p>
        <p className="text-[#393939] font-normal text-sm">{description}</p>
        {locked && (
          <Button
            type="primary"
            onClick={onClick}
            loading={updateStepStatusMutation.isPending}
          >
            Kích hoạt
          </Button>
        )}

        {!locked && unlockNode}
      </div>
    </div>
  );
}
