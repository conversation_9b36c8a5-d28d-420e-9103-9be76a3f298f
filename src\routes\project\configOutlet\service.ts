import { useApp } from "@/UseApp";
import { AbstractFilterInterface } from "@/common/interface";
import { useQuery } from "@tanstack/react-query";
import { ApiBoothResponseInterface } from "./interface";

export const useProjectBoothsQuery = (
  projectId: number,
  filter?: (AbstractFilterInterface & { getInActive?: boolean }) | undefined,
  enabled?: boolean,
) => {
  const { axiosGet } = useApp();
  const { getInActive, ...restFilter } = filter ?? {};

  const queryFilter = {
    ...(getInActive ? {} : { isActive: true }),
    ...restFilter,
  };
  return useQuery({
    queryKey: ["projectBooths", projectId],
    queryFn: async () =>
      axiosGet<ApiBoothResponseInterface, unknown>(
        `projects/${projectId}/booths`,
        queryFilter,
      ),
    enabled,
  });
};
