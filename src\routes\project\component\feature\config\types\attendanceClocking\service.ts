import { useApp } from "@/UseApp";
import { useMutation, useQuery } from "@tanstack/react-query";
import { AttendanceClockingInterface } from "./interface";

/**
 * Creates and returns a query for fetching attendance clocking (chấm công vào/ra) data based on the provided feature ID.
 *
 * @param {number} componentFeatureId - The ID of the feature for which attendance clocking data is to be fetched.
 * @return {QueryResult<AttendanceClockingInterface, unknown>} The result of the query for fetching attendance clocking data.
 */
export const useAttendanceClockingQuery = (componentFeatureId: number) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["attendanceClocking", componentFeatureId],
    queryFn: () =>
      axiosGet<AttendanceClockingInterface, unknown>(
        `/features/${componentFeatureId}/attendance`,
      ),
  });
};

/**
 * Returns a mutation function for updating attendance clocking (Chấm công vào/ra).
 *
 * @param {number} componentFeatureId - the ID of the feature
 * @return {MutationFunction} the mutation function for updating attendance clocking
 */
export const useUpdateAttendanceClockingMutation = (
  componentFeatureId: number,
) => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["updateAttendanceClocking", componentFeatureId],
    mutationFn: (data: AttendanceClockingInterface) =>
      axiosPatch<AttendanceClockingInterface, unknown>(
        `/features/${componentFeatureId}/attendance`,
        data,
      ),
  });
};
