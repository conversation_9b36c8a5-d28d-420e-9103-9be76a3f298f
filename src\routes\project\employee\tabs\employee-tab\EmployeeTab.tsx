import {
  ACTIVE_LABEL,
  CHUNK_SIZE,
  CURD,
  DEFAULT_CURRENT_PAGE,
  DEFAULT_PAGE_SIZE,
  INACTIVE_LABEL,
} from "@/common/constant.ts";
import { createFileAndDownLoad } from "@/common/export-excel.helper.ts";
import { filterOption } from "@/common/helper.ts";
import CustomTable from "@/components/CustomTable/CustomTable.tsx";
import DebounceSelect from "@/components/DebounceSelectComponent.tsx";
import FilterClassicComponent from "@/components/FilterClassicComponent.tsx";
import { renderTableCell } from "@/components/table-cell.tsx";
import UserOptionComponent from "@/components/UserOptionComponent.tsx";
import { UserInterface } from "@/routes/user/interface.ts";
import { SearchOutlined } from "@ant-design/icons";
import AddEmployeeToProjectModal from "@project/employee/AddEmployeeToProjectModal.tsx";
import EmployeeActionCell from "@project/employee/tabs/employee-tab/EmployeeActionCell.tsx";
import EmployeeTabModal from "@project/employee/tabs/employee-tab/EmployeeTabModal.tsx";
import { useProjectAgenciesQuery } from "@project/general/services.ts";
import { ProjectAgencyInterface } from "@project/interface.ts";
import { RoleInterface } from "@project/role/interface.ts";
import { Form, Input, Select } from "antd";
import { ColumnsType } from "antd/es/table";
import _ from "lodash";
import { useCallback, useEffect, useMemo, useState } from "react";
import {
  GENDER_ENUM_TO_LABEL,
  GenderEnum,
  ProjectEmployeeUserInterface,
} from "../../interface.ts";
import { useEmployeesQuery, useGetEmployeeMutation } from "../../service.ts";
import ImportEmployeeModal from "./ImportEmployeeModal.tsx";

const EmployeeTab = (props: {
  role: RoleInterface;
  leaderRole?: RoleInterface;
  cb: () => Promise<void>;
  selectedTab: string;
  projectId: number;
}) => {
  const { role, leaderRole, cb, selectedTab, projectId } = props;

  const [searchForm] = Form.useForm();
  const [action, setAction] = useState<CURD | null>(null);
  const [isFormSearchOpen, setIsFormSearchOpen] = useState<boolean>(false);
  const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [isOpenImport, setIsOpenImport] = useState(false);
  const [filter, setFilter] = useState({});
  const [exportLoading, setExportLoading] = useState(false);

  const employeesQuery = useEmployeesQuery(projectId, {
    ...filter,
    take: pageSize,
    skip: (currentPage - 1) * pageSize,
    roleId: role.id,
  });
  const projectAgencyQuery = useProjectAgenciesQuery(projectId);

  const getEmployeeMutation = useGetEmployeeMutation(projectId);

  const fetchLeaderFilterOptions = useCallback(
    async (keyword?: string) => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      let result: any[] = [];
      if (keyword?.length !== 0 && leaderRole) {
        const response = await getEmployeeMutation.mutateAsync({
          keyword,
          take: 10,
          roleId: leaderRole?.id,
        });

        result = response.entities.map((item) => ({
          value: item.id,
          label: item.user.name,
          user: item.user,
          isAvailable: true,
        }));
      }

      result.unshift(
        ...[
          {
            value: "",
            label: "Tất cả",
          },
          {
            value: "0",
            label: "Chưa có trưởng nhóm quản lý",
          },
        ],
      );
      return result;
    },
    [getEmployeeMutation, leaderRole],
  );

  const searchContent = (
    <>
      <Form.Item name="keyword">
        <Input
          placeholder="Tìm theo tên, sđt, email, username"
          prefix={<SearchOutlined />}
          allowClear
          className={"h-10"}
        />
      </Form.Item>
      <Form.Item name="projectAgencyId">
        <Select
          allowClear
          placeholder="Agency phụ trách"
          showSearch
          optionFilterProp="children"
          filterOption={filterOption}
          options={projectAgencyQuery.data?.map((projectAgency) => ({
            label: projectAgency.agency.name,
            value: projectAgency.id,
          }))}
          className={"h-10"}
        />
      </Form.Item>
      <Form.Item name="leaderId">
        <DebounceSelect
          className={"h-10"}
          popupMatchSelectWidth={false}
          placeholder="Trưởng nhóm quản lý"
          allowClear
          showSearch
          fetchOptions={fetchLeaderFilterOptions}
          optionRender={(option) => {
            if (option.data.user) {
              return (
                <UserOptionComponent
                  avatarUrl={option.data.user?.imageUrl}
                  name={option.data.user?.name}
                  phone={option.data.user?.phone}
                  email={option.data.user?.email}
                  key={option.data.user.id}
                />
              );
            }
            return option.label;
          }}
        />
      </Form.Item>
      <Form.Item name="isActive">
        <Select
          className={"h-10"}
          allowClear
          placeholder="Tình trạng"
          optionFilterProp="children"
          filterOption={filterOption}
          options={[
            {
              label: "Đang hoạt động",
              value: 1,
            },
            {
              label: "Ngừng hoạt động",
              value: 0,
            },
          ]}
          popupMatchSelectWidth={false}
        />
      </Form.Item>
    </>
  );

  const columns: ColumnsType<ProjectEmployeeUserInterface> = [
    {
      title: "Nhân viên",
      dataIndex: "user",
      key: "user.name",
      render: (value: UserInterface) => {
        if (value)
          return (
            <UserOptionComponent
              avatarUrl={value.picture}
              name={value.name}
              phone={value.phone}
              email={value.email}
              isAvailable={true}
            />
          );
      },
    },
    {
      title: "Mã nhân viên",
      dataIndex: "user",
      key: "user.code",
      render: (value: UserInterface) => value?.code,
      className: "min-w-[100px]",
    },
    {
      title: "Username",
      dataIndex: "user",
      key: "user.username",
      className: "min-w-[100px]",
      render: (value: UserInterface) => value?.username,
    },
    {
      title: "Agency phụ trách",
      dataIndex: "projectAgency",
      key: "projectAgency",
      className: "min-w-[100px]",
      render: (value?: ProjectAgencyInterface) => value?.agency?.name,
    },
    {
      title: "Trưởng nhóm quản lý",
      dataIndex: "projectEmployeeUser",
      className: "min-w-[100px]",
      key: "projectEmployeeUser",
      render: (value?: ProjectEmployeeUserInterface) => {
        const user = value?.user;
        if (!user) return "";
        return (
          <UserOptionComponent
            avatarUrl={user.picture}
            name={user.name}
            phone={user.phone}
            email={user.email}
            isAvailable={true}
          />
        );
      },
    },
    {
      title: "Tình trạng",
      dataIndex: "isActive",
      key: "isActive",
      className: "min-w-[100px]",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "isActive");
      },
    },
    {
      key: "actions",
      render: (_, record) => {
        return (
          <EmployeeActionCell
            record={record}
            role={role}
            cb={() => {
              employeesQuery.refetch();
              cb();
            }}
            leaderRole={leaderRole}
          />
        );
      },
      fixed: "right",
      width: 100,
    },
  ];

  useEffect(() => {
    if (selectedTab === role.name) {
      searchForm.resetFields();
      employeesQuery.refetch();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedTab]);

  const searchHandler = useCallback(() => {
    setCurrentPage(DEFAULT_CURRENT_PAGE);

    const searchData = searchForm.getFieldsValue();
    searchData["leaderId"] = searchData?.leaderId?.value;
    if (searchData["leaderId"] === "") {
      delete searchData["leaderId"];
    }

    const newFilter = {
      roleId: role.id,
      ...searchData,
    };
    if (_.isEqual(filter, newFilter)) {
      employeesQuery.refetch();
    }

    setFilter(newFilter);
  }, [employeesQuery, filter, role.id, searchForm]);

  const handlePaginationChange = useCallback(
    (page: number) => {
      setCurrentPage(page);
    },
    [setCurrentPage],
  );

  const handlePageSizeChange = useCallback(
    (_current: number, size: number) => {
      setPageSize(size);
      setCurrentPage(DEFAULT_CURRENT_PAGE);
    },
    [setPageSize, setCurrentPage],
  );

  const pagination = useMemo(() => {
    return {
      current: currentPage,
      total: employeesQuery.data?.count ?? 0,
      pageSize: pageSize,
      onChange: handlePaginationChange,
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: handlePageSizeChange,
    };
  }, [
    currentPage,
    employeesQuery.data?.count,
    pageSize,
    handlePaginationChange,
    handlePageSizeChange,
  ]);

  const loading = useMemo(
    () =>
      employeesQuery.isLoading ||
      employeesQuery.isFetching ||
      employeesQuery.isRefetching ||
      exportLoading,
    [
      employeesQuery.isFetching,
      employeesQuery.isLoading,
      employeesQuery.isRefetching,
      exportLoading,
    ],
  );

  const handleExcelButtonClick = useCallback(async () => {
    const total = pagination.total ?? 0;

    if (total === 0) {
      return;
    }

    const fetchAllData = async () => {
      const requests = [];

      for (let skip = 0; skip < total; skip += CHUNK_SIZE) {
        requests.push(
          getEmployeeMutation.mutateAsync({
            take: CHUNK_SIZE,
            skip: skip,
            roleId: role.id,
            ...filter,
          }),
        );
      }

      const results = await Promise.all(requests);
      const allEntities = results.flatMap((result) => result.entities);

      return allEntities;
    };

    setExportLoading(true);
    try {
      const allData = await fetchAllData();

      const data = allData.map((projectEmployeeUser) => {
        const { user, role, projectAgency, isActive } = projectEmployeeUser;

        const { name, username, email, gender } = user;

        return [
          name,
          username,
          email,
          GENDER_ENUM_TO_LABEL[gender as GenderEnum],
          role.name,
          projectAgency?.agency.name,
          projectEmployeeUser.projectEmployeeUser?.user.username ?? "",
          isActive ? ACTIVE_LABEL : INACTIVE_LABEL,
        ];
      });

      const headers = [
        "Họ và tên",
        "Số điện thoại/ Username",
        "Email",
        "Giới tính",
        "Vị trí",
        "Agency phụ trách",
        "SĐT trưởng nhóm quản lý nhân viên này",
        "Tình trạng",
      ];

      const fileName = `Danh sach nhan vien trong du an ID ${projectId}`;

      await createFileAndDownLoad({ data, headers, fileName });
    } catch (e) {
      console.error(e);
    } finally {
      setExportLoading(false);
    }
  }, [filter, getEmployeeMutation, pagination.total, projectId, role.id]);

  return (
    <>
      <FilterClassicComponent
        searchHandler={searchHandler}
        searchForm={searchForm}
        handleAddButtonClick={function (): void {
          setIsFormSearchOpen(true);
        }}
        content={searchContent}
        showAddButton={true}
        onImportClick={() => {
          setIsOpenImport(true);
        }}
        className={"mb-6"}
        disableAddButton={!role.isActive}
        btnLoading={loading}
        showExportButton
        handleExcelButtonClick={handleExcelButtonClick}
      />
      <CustomTable
        dataSource={employeesQuery.data?.entities}
        columns={columns}
        scroll={{
          x: "max-content",
          y: pagination.total ? "80vh" : undefined,
        }}
        pagination={pagination}
        loading={loading}
      />
      {isFormSearchOpen && (
        <AddEmployeeToProjectModal
          role={role}
          isOpen={isFormSearchOpen}
          projectId={projectId}
          cb={() => {
            setIsFormSearchOpen(false);
            employeesQuery.refetch();
            cb();
          }}
          notFoundCb={() => {
            setIsFormSearchOpen(false);
            setAction(CURD.CREATE);
          }}
          cancelCb={() => {
            setIsFormSearchOpen(false);
          }}
        />
      )}
      {!!action && (
        <EmployeeTabModal
          action={action}
          role={role}
          leaderRole={leaderRole}
          projectId={projectId}
          cb={() => {
            setAction(null);
            employeesQuery.refetch();
            cb();
          }}
          cancelCb={() => setAction(null)}
        />
      )}

      <ImportEmployeeModal
        isOpen={isOpenImport}
        role={role}
        projectAgencies={projectAgencyQuery.data ?? []}
        setIsOpen={setIsOpenImport}
        projectId={projectId}
      />
    </>
  );
};

export default EmployeeTab;
