import FormNumberInput from "@/components/FormNumberInput";
import InnerContainer from "@/components/InnerContainer/InnerContainer";
import { Button, Col, Form, Input, Modal, Row, Select, Switch } from "antd";
import { useCallback, useEffect, useState } from "react";
import {
  useCreateProjectOtpMutation,
  useDeleteProjectOtpMutation,
  usePatchProjectOtpMutation,
  useProjectOtpsQuery,
} from "./service";

interface ConfigSmsTopSectionProps {
  projectId: number;
}
const ConfigSmsTopSection = ({ projectId }: ConfigSmsTopSectionProps) => {
  const [isConfigSMSOTP, setIsConfigSMSOTP] = useState(false);
  const [form] = Form.useForm();
  const [modal, contextHolder] = Modal.useModal();

  const projectOtpsQuery = useProjectOtpsQuery(projectId);

  const createProjectOtpMutation = useCreateProjectOtpMutation(projectId);
  const deleteProjectOtpMutation = useDeleteProjectOtpMutation(projectId);
  const patchProjectOtpMutation = usePatchProjectOtpMutation(projectId);

  const onConfigSMSOTPChange = useCallback(
    async (value: boolean) => {
      if (value) {
        setIsConfigSMSOTP(value);
      } else {
        modal.confirm({
          onOk: async () => {
            await deleteProjectOtpMutation.mutateAsync(
              projectOtpsQuery.data?.id,
            );

            projectOtpsQuery.refetch();
          },
          title: "Xác nhận",
          content: "Xóa cấu hình SMS OTP",
          okText: "Xóa",
        });
      }
    },
    [deleteProjectOtpMutation, modal, projectOtpsQuery],
  );

  const onFinish = useCallback(
    async (values: {
      provider: string;
      clientId: string;
      clientSecret: string;
      "otpSmsTemplate.senderName": string;
      "otpSmsTemplate.content": string;
      "otpSmsTemplate.contentType": string;
      otpLength: number;
      otpResendTimeout: number;
      otpTimeout: number;
    }) => {
      const data = {
        primaryType: "sms" as const,
        provider: values.provider,
        clientId: values.clientId,
        clientSecret: values.clientSecret,
        otpLength: values.otpLength,
        otpResendTimeout: values.otpResendTimeout,
        otpTimeout: values.otpTimeout,
        otpSmsTemplate: {
          senderName: values["otpSmsTemplate.senderName"],
          content: values["otpSmsTemplate.content"],
          contentType: values["otpSmsTemplate.contentType"],
        },
      };

      if (projectOtpsQuery?.data?.id) {
        await patchProjectOtpMutation.mutateAsync({
          ...data,
          id: projectOtpsQuery?.data?.id ?? 0,
        });
      } else {
        await createProjectOtpMutation.mutateAsync(data);
        projectOtpsQuery.refetch();
      }
    },
    [createProjectOtpMutation, patchProjectOtpMutation, projectOtpsQuery],
  );

  useEffect(() => {
    if (!projectOtpsQuery.isLoading) {
      setIsConfigSMSOTP(!!projectOtpsQuery?.data?.id);
      if (projectOtpsQuery?.data?.id) {
        form.setFieldsValue({
          provider: projectOtpsQuery?.data?.provider,
          clientId: projectOtpsQuery?.data?.clientId,
          clientSecret: projectOtpsQuery?.data?.clientSecret,
          "otpSmsTemplate.senderName":
            projectOtpsQuery?.data?.otpSmsTemplate?.senderName,
          "otpSmsTemplate.content":
            projectOtpsQuery?.data?.otpSmsTemplate?.content,
          "otpSmsTemplate.contentType":
            projectOtpsQuery?.data?.otpSmsTemplate?.contentType,
          otpLength: projectOtpsQuery?.data?.otpLength,
          otpResendTimeout: projectOtpsQuery?.data?.otpResendTimeout,
          otpTimeout: projectOtpsQuery?.data?.otpTimeout,
        });
      }
    }
  }, [
    form,
    projectOtpsQuery?.data?.clientId,
    projectOtpsQuery?.data?.clientSecret,
    projectOtpsQuery?.data?.id,
    projectOtpsQuery?.data?.otpLength,
    projectOtpsQuery?.data?.otpResendTimeout,
    projectOtpsQuery?.data?.otpSmsTemplate?.content,
    projectOtpsQuery?.data?.otpSmsTemplate?.contentType,
    projectOtpsQuery?.data?.otpSmsTemplate?.senderName,
    projectOtpsQuery?.data?.otpTimeout,
    projectOtpsQuery?.data?.provider,
    projectOtpsQuery.isLoading,
  ]);

  return (
    <InnerContainer className="mt-5">
      <Row>
        <Col md={8}>
          <h4>SMS OTP</h4>
          <p className="text-gray-400 max-w-[350px]">
            Cho phép liên kết với bên cung cấp thứ ba để gửi OTP để khách hàng
          </p>
        </Col>

        <Col md={16}>
          <p>
            Liên kết SMS OTP
            <Switch
              className="ml-5"
              value={isConfigSMSOTP}
              onChange={onConfigSMSOTPChange}
            />
          </p>

          {isConfigSMSOTP && (
            <Form form={form} layout="vertical" onFinish={onFinish}>
              <Form.Item
                label={"Nhà cung cấp"}
                name={"provider"}
                rules={[{ required: true }]}
              >
                <Select
                  options={[
                    { label: "tingting", value: "tingting" },
                    { label: "test", value: "test" },
                  ]}
                  className="!w-1/2"
                />
              </Form.Item>

              <Form.Item
                label={"Client Id"}
                rules={[{ required: true }]}
                name={"clientId"}
              >
                <Input />
              </Form.Item>

              <Form.Item
                label={"Client Secret"}
                rules={[{ required: true }]}
                name={"clientSecret"}
              >
                <Input.Password />
              </Form.Item>

              <Form.Item
                label={"Sender"}
                rules={[{ required: true }]}
                name={"otpSmsTemplate.senderName"}
              >
                <Input className="w-1/2" />
              </Form.Item>

              <Form.Item
                label={"Độ dài OTP"}
                rules={[{ required: true }]}
                name={"otpLength"}
              >
                <FormNumberInput className="w-1/2" />
              </Form.Item>

              <Form.Item
                label={"Nội dung tin nhắn"}
                rules={[{ required: true }]}
                name={"otpSmsTemplate.content"}
              >
                <Input.TextArea showCount={true} maxLength={300} />
              </Form.Item>

              <Form.Item
                label={"Content Type"}
                name={"otpSmsTemplate.contentType"}
              >
                <Select
                  className="!w-1/2"
                  options={[
                    {
                      label: "ascii",
                      value: "ascii",
                    },
                    {
                      label: "unicode",
                      value: "unicode",
                    },
                  ]}
                />
              </Form.Item>

              <Form.Item
                label={"Thời gian cho phép gửi lại (giây)"}
                rules={[{ required: true }]}
                name={"otpResendTimeout"}
              >
                <FormNumberInput className="w-1/2" />
              </Form.Item>

              <Form.Item
                label={"Thời gian sống của OTP (giây)"}
                rules={[{ required: true }]}
                name={"otpTimeout"}
              >
                <FormNumberInput className="w-1/2" />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={createProjectOtpMutation.isPending}
                >
                  Lưu
                </Button>
              </Form.Item>
            </Form>
          )}
        </Col>
      </Row>

      {contextHolder}
    </InnerContainer>
  );
};

export default ConfigSmsTopSection;
