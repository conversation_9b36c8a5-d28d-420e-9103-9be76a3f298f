import { AbstractEntityInterface } from "@/common/interface";

export interface CustomerInfoFieldInterface extends AbstractEntityInterface {
  id: number;
  label: string;
  value: string;
}

export interface Item extends AbstractEntityInterface {
  id: number;
  name: string;
  code: string;
  backgroundColor: string;
  foregroundColor: string;
  unitName: string;
  itemTypeName: string;
  imageUrl: string;
  thumbnailUrl: string;
}

export interface ProjectItem extends AbstractEntityInterface {
  id: number;
  item: Item;
}

export interface PrizeItem extends AbstractEntityInterface {
  id: number;
  ordinal: number;
  projectItem: ProjectItem;
}

export interface PrizeResult extends AbstractEntityInterface {
  id: number;
  createdAt: string;
  prizeItemId: number;
}

export interface CustomerLuckyDrawInterface extends AbstractEntityInterface {
  id: number;
  name: string;
  description: string;
  availableTurns: number;
  prizeItems: PrizeItem[];
  prizeResults: PrizeResult[];
}

export interface CustomerDataInterface {
  customerInfoFields: CustomerInfoFieldInterface[];
  customerLuckyDraws: CustomerLuckyDrawInterface[];
}

export interface ValidateData {
  token: string;
  orderId: string;
  attendanceId: string;
}
