import { CURD } from "@/common/constant";
import { filterOption, formError<PERSON>esponse<PERSON><PERSON><PERSON> } from "@/common/helper.ts";
import { getImageVariants } from "@/common/image.helper.ts";
import { uploadImage } from "@/common/upload-image.helper.ts";
import DebounceSelect from "@/components/DebounceSelectComponent.tsx";
import ModalCURD from "@/components/ModalCURD";
import UploadImageComponent from "@/components/UploadImageComponent.tsx";
import UserOptionComponent from "@/components/UserOptionComponent.tsx";
import { RoleInterface } from "@/routes/project/role/interface";
import {
  useCreateUserMutation,
  useUpdateUserMutation,
} from "@/routes/user/services.ts";
import { useApp } from "@/UseApp.tsx";
import { CloseCircleOutlined } from "@ant-design/icons";
import {
  GENDER_ENUM_TO_LABEL,
  GenderEnum,
  ProjectEmployeeUserInterface,
} from "@project/employee/interface.ts";
import {
  useCreateEmployeeMutation,
  useGetAgencyEmployeeLeadersMutation,
  useUpdateEmployeeMutation,
} from "@project/employee/service.ts";
import { useProjectAgenciesQuery } from "@project/general/services.ts";
import { Alert, Form, Input, Radio, Select, Tag } from "antd";
import { useCallback, useEffect, useState } from "react";

interface EmployeeTabModalProps {
  action: CURD | null;
  role: RoleInterface | null;
  leaderRole: RoleInterface | undefined;
  projectId: number;
  cb: () => void;
  selectedProjectEmployeeUser?: ProjectEmployeeUserInterface;
  cancelCb: () => void;
}

const EmployeeTabModal = ({
  action,
  role,
  leaderRole,
  projectId,
  cb,
  selectedProjectEmployeeUser,
  cancelCb,
}: EmployeeTabModalProps) => {
  const { showNotification, axiosPost } = useApp();

  const [selectedValue, setSelectedValue] = useState<
    string | null | ProjectEmployeeUserInterface | undefined
  >(null);
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<ProjectEmployeeUserInterface[]>([]);
  const [form] = Form.useForm();

  const projectAgencyQuery = useProjectAgenciesQuery(projectId);

  const getAgencyEmployeeLeadersMutation =
    useGetAgencyEmployeeLeadersMutation(projectId);
  const createUserMutation = useCreateUserMutation();
  const updateUserMutation = useUpdateUserMutation();
  const createEmployeeMutation = useCreateEmployeeMutation(projectId);
  const updateEmployeeMutation = useUpdateEmployeeMutation(projectId);

  const fetchLeaderOptions = useCallback(
    async (keyword?: string) => {
      if (keyword?.length === 0) {
        return [];
      }

      if (!leaderRole) {
        return [];
      }

      const projectAgencyId = form.getFieldValue("projectAgencyId");
      if (!projectAgencyId) {
        return [];
      }

      const leadersResponse =
        await getAgencyEmployeeLeadersMutation.mutateAsync({
          keyword,
          take: 10,
          projectAgencyId,
        });

      setOptions(leadersResponse.entities);

      return leadersResponse.entities.map((item) => ({
        value: item.id,
        label: item.user.username,
        user: item.user,
        isAvailable: true,
      }));
    },
    [form, getAgencyEmployeeLeadersMutation, leaderRole],
  );

  const formContent = (
    <>
      <Alert
        message="Nhân viên chưa có trong hệ thống nên cần cung cấp thông tin ở phía dưới."
        type="info"
        showIcon
      />
      <Form.Item name={"employeeId"} hidden>
        <Input />
      </Form.Item>

      <Form.Item
        className="pt-5"
        label="Họ và tên"
        name="name"
        rules={[{ required: true, message: "Họ và tên không được để trống." }]}
      >
        <Input />
      </Form.Item>
      <Form.Item
        label="Số điện thoại"
        name="phone"
        rules={[
          {
            required: true,
          },
          {
            pattern: new RegExp(/^0\d+$/),
            message: "Vui lòng nhập đúng định dạng số điện thoại",
          },
          {
            len: 10,
          },
        ]}
        extra="Nhân viên sẽ sử dụng số điện thoại này để đăng nhập vào app"
      >
        <Input disabled={action === CURD.UPDATE} />
      </Form.Item>
      <Form.Item
        label="Email"
        name="email"
        rules={[
          {
            required: true,
            message: "Email không được để trống.",
          },
          {
            type: "email",
          },
        ]}
        extra="Nhân viên sẽ nhận được lời mời qua địa chỉ email này"
      >
        <Input />
      </Form.Item>
      <Form.Item label="Mã nhân viên" name="code">
        <Input />
      </Form.Item>
      <Form.Item label="Giới tính" name="gender">
        <Radio.Group>
          {Object.values(GenderEnum).map((gender) => (
            <Radio key={gender} value={gender}>
              {GENDER_ENUM_TO_LABEL[gender]}
            </Radio>
          ))}
        </Radio.Group>
      </Form.Item>
      <UploadImageComponent
        form={form}
        fieldLabel="Ảnh đại diện"
        imageUrlFieldName="imageUrl"
      />
      <hr color="#DDE1EA" />
      <Form.Item
        className="pt-5"
        label="Agency quản lý nhân viên này"
        name="projectAgencyId"
        rules={[
          {
            required: true,
            message: "Agency quản lý không được để trống.",
          },
        ]}
      >
        <Select
          showSearch
          optionFilterProp="children"
          filterOption={filterOption}
          options={projectAgencyQuery.data?.map((projectAgency) => ({
            label: projectAgency.agency.name,
            value: projectAgency.id,
          }))}
          disabled={action === CURD.UPDATE}
        />
      </Form.Item>
      <Form.Item
        label={`Trưởng nhóm (${leaderRole?.name}) quản lý nhân viên này`}
        name="leaderId"
        extra={`Có thể phân bổ trưởng nhóm (${leaderRole?.name}) quản lý sau nếu không tìm được`}
      >
        {selectedValue ? (
          <Tag
            closable
            onClose={() => {
              setSelectedValue(null);
              form.setFieldValue("leaderId", []);
            }}
            className="w-full justify-between flex pt-2 pb-2 pl-3 pr-3"
            closeIcon={<CloseCircleOutlined style={{ fontSize: 14 }} />}
            style={{
              fontSize: 14,
              backgroundColor:
                typeof selectedValue === "string" ? "" : "#F0F8FF",
              borderColor: typeof selectedValue === "string" ? "" : "#C4D6FF",
            }}
          >
            {typeof selectedValue === "string" ? (
              <p>{selectedValue}</p>
            ) : (
              <UserOptionComponent
                avatarUrl={selectedValue?.user.picture}
                name={selectedValue?.user.name}
                phone={selectedValue?.user.phone}
                email={selectedValue?.user.email}
              />
            )}
          </Tag>
        ) : (
          <DebounceSelect
            allowClear
            autoClearSearchValue
            mode="multiple"
            fetchOptions={fetchLeaderOptions}
            style={{ width: "100%" }}
            optionRender={(option) => {
              if (option.data.user) {
                return (
                  <UserOptionComponent
                    avatarUrl={option.data.user?.imageUrl}
                    name={option.data.user?.name}
                    phone={option.data.user?.phone}
                    email={option.data.user?.email}
                  />
                );
              }
              return option.label;
            }}
            onSelect={({ value }) => {
              const option = options.find((item) => item.id === value);
              if (option) setSelectedValue(option);
            }}
          />
        )}
      </Form.Item>
    </>
  );

  const formSubmit = useCallback(async () => {
    try {
      if (!role) {
        return;
      }
      setLoading(true);
      await form.validateFields();
      const data = form.getFieldsValue();
      const id = form.getFieldValue("id");

      if (data.imageFile) {
        setLoading(true);
        data.picture = getImageVariants(
          (await uploadImage(axiosPost, data.imageFile.file))?.result
            .variants ?? [],
          "public",
        );
        delete data.imageFile;
        setLoading(false);
      }

      switch (action) {
        case CURD.CREATE:
          {
            const user = await createUserMutation.mutateAsync({
              ...data,
              username: data.phone,
              type: "EMPLOYEE",
              password: "123456",
            });

            if (!Array.isArray(user)) {
              await createEmployeeMutation.mutateAsync({
                projectAgencyId: data.projectAgencyId,
                userId: user.id,
                roleId: role.id,
                leaderId:
                  typeof selectedValue === "object"
                    ? selectedValue?.id
                    : undefined,
              });

              showNotification({
                type: "success",
                message: `Thêm ${role?.name} thành công`,
              });
            }
          }
          break;
        case CURD.UPDATE:
          await updateUserMutation.mutateAsync({
            ...data,
            username: data.phone,
            id,
          });

          await updateEmployeeMutation.mutateAsync({
            projectAgencyId: data.projectAgencyId,
            leaderId:
              selectedValue !== null && typeof selectedValue === "object"
                ? (selectedValue?.id ?? 0)
                : 0,
            id: form.getFieldValue("employeeId"),
          });

          showNotification({
            type: "success",
            message: `Cập nhật ${role.name} thành công`,
          });
          break;
        default:
          return;
      }

      form.resetFields();
      setSelectedValue(null);
      setOptions([]);
      cb();

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      formErrorResponseHandler(form, error);
    } finally {
      setLoading(false);
    }
  }, [
    action,
    axiosPost,
    cb,
    createEmployeeMutation,
    createUserMutation,
    form,
    role,
    selectedValue,
    showNotification,
    updateEmployeeMutation,
    updateUserMutation,
  ]);

  useEffect(() => {
    if (action === CURD.UPDATE && selectedProjectEmployeeUser) {
      const { user, projectAgency, id, projectEmployeeUser } =
        selectedProjectEmployeeUser;
      form.setFieldsValue({
        name: user.name,
        phone: user.phone,
        email: user.email,
        username: user.username,
        initUrl: user.picture,
        projectAgencyId: projectAgency?.id,
        id: user.id,
        employeeId: id,
        gender: user.gender,
        code: user.code,
      });
      setSelectedValue(projectEmployeeUser);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [action, selectedProjectEmployeeUser]);

  return (
    <ModalCURD
      title={
        action === CURD.CREATE
          ? `Thêm ${role?.name}`
          : `Thông tin ${role?.name}`
      }
      isOpen={!!action}
      formContent={formContent}
      form={form}
      onFinish={formSubmit}
      action={action}
      onCancelCb={() => {
        form.resetFields();
        setSelectedValue(null);
        setOptions([]);
        cancelCb();
      }}
      btnConfirmLoading={loading}
    />
  );
};

export default EmployeeTabModal;
