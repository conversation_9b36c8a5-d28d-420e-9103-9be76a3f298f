import logoCollapsed from "@/assets/img/logoCollapsed.png";
import logoLeftMenu from "@/assets/img/logoLeftMenu.png";
import { FALLBACK_IMAGE_STRING } from "@/common/constant";
import { HOME_PAGE } from "@/common/url.helper.ts";
import useDeviceType from "@/hooks/useDeviceType.ts";
import { useMainLayoutStore } from "@/state.ts";
import { BellOutlined, MenuOutlined, SettingOutlined } from "@ant-design/icons";
import { useAuth0 } from "@auth0/auth0-react";
import { Avatar, Button, Image, Layout, Skeleton, Space, Tooltip } from "antd";
import { Suspense } from "react";
import { Outlet, useLocation, useNavigate, useParams } from "react-router-dom";
import MobileLayout from "../MobileLayout/MobileLayout.tsx";
import LeftMenu from "./LeftMenu.tsx";
import "./main-layout.css";

export default function MainLayout() {
  const { Header, Sider, Content, Footer } = Layout;

  const navigate = useNavigate();
  const location = useLocation();
  const projectId = parseInt(useParams().id ?? "0");
  const { user, logout } = useAuth0();
  const isMobile = useDeviceType();

  const { collapsed, setCollapsed } = useMainLayoutStore();

  const onCollapse = () => {
    setCollapsed(!collapsed);
  };

  if (isMobile) {
    return <MobileLayout />;
  }

  return (
    <Layout style={{ minHeight: "100vh" }} hasSider>
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        style={{
          overflow: "auto",
          height: "100vh",
          position: "fixed",
          left: 0,
          top: 0,
          bottom: 0,
        }}
        width={270}
      >
        <div className="image-logo" style={{ marginBottom: "10px" }}>
          <Image
            fallback={FALLBACK_IMAGE_STRING}
            preview={false}
            height={31}
            src={collapsed ? logoCollapsed : logoLeftMenu}
            onClick={() => navigate("/")}
            className={"cursor-pointer"}
          />
        </div>
        <LeftMenu projectId={projectId} />
      </Sider>
      <Layout
        style={!collapsed ? { marginLeft: "270px" } : { marginLeft: "80px" }}
        className="layout"
      >
        <Header className="site-layout-background p-0 text-end pr-5 flex justify-between items-center">
          <Button icon={<MenuOutlined />} onClick={onCollapse} type="link" />
          <Space size={"large"}>
            <BellOutlined />
            <SettingOutlined />
            <Tooltip title={user?.name}>
              <Avatar
                src={user?.picture}
                onClick={() =>
                  logout({
                    logoutParams: {
                      returnTo: HOME_PAGE,
                    },
                  })
                }
              />
            </Tooltip>
          </Space>
        </Header>

        <Content
          style={{
            margin:
              location.pathname.startsWith(`/project/${projectId}/report`) ||
              location.pathname === `/project/${projectId}/edit`
                ? "0px 0px 0px 40px"
                : "0px 40px",
            overflow: "initial",
          }}
        >
          <Suspense fallback={<Skeleton active title />}>
            <Outlet />
          </Suspense>
        </Content>

        <Footer>
          <div className={""}></div>
        </Footer>
      </Layout>
    </Layout>
  );
}
