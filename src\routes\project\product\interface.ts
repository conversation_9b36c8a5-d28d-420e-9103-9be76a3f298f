import { AbstractEntityInterface } from "@/common/interface";
import {
  ProductInterface,
  ProductPackagingInterface,
} from "../../product/interface";

export interface ProjectProductPrice extends AbstractEntityInterface {
  projectProductId: number;
  price: number;
}

export interface ProjectProductInterface extends AbstractEntityInterface {
  product: ProductInterface;
  productPackaging?: ProductPackagingInterface;
  projectProductPrices?: ProjectProductPrice[];
  isAvailable?: boolean;
  featureOrderLuckyDrawLimitProducts: [];
}

export interface ApiProjectProductResponseInterface {
  entities: ProjectProductInterface[];
  count: number;
}
