import undrawSecurity from "@/assets/img/undraw_security_on.svg";
import { useAuth0 } from "@auth0/auth0-react";
import { HOME_PAGE } from "./common/url.helper";

export default function ForbiddenPage() {
  const { logout } = useAuth0();

  return (
    <>
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "90vh",
        }}
      >
        <img
          src={undrawSecurity}
          className={"w-[35%]"}
          alt={"Click to logout"}
          onClick={() => {
            logout({ logoutParams: { returnTo: HOME_PAGE } });
          }}
        />
      </div>

      <div className="text-center">
        <a href={import.meta.env.VITE_BASENAME}>Home</a>
      </div>
    </>
  );
}
