import { stringIncludes } from "@/common/helper";
import CustomModal from "@/components/CustomModal";
import { SearchOutlined } from "@ant-design/icons";
import { Input, Table } from "antd";
import { useCallback, useMemo, useState } from "react";
import { useProjectItemsQuery } from "../item/services";
import { useProjectProductsQuery } from "../product/service";
import { ITEM_KPI_TYPE_LABEL, ItemKpiTypeEnum } from "./interface";
import KPIEditInput from "./KPIEditInput";

interface KPIModalProps {
  isOpen: boolean;
  title: JSX.Element;
  itemKpiType: ItemKpiTypeEnum;
  cancel: () => void;
  projectId: number;
  modalTitle: string;
  items: {
    projectProductId?: number;
    projectItemId?: number;
    type: ItemKpiTypeEnum;
    kpi: string;
  }[];
  channelId?: number;
  provinceId?: number;
  cb: () => void;
}
const KPIModal = ({
  isOpen,
  title,
  cancel,
  projectId,
  modalTitle,
  itemKpiType,
  items,
  cb,
  channelId,
  provinceId,
}: KPIModalProps) => {
  const [searchValue, setSearchValue] = useState("");

  const projectProductQuery = useProjectProductsQuery(projectId, {
    take: 0,
  });

  const projectItemsQuery = useProjectItemsQuery(
    projectId,
    { take: 0 },
    itemKpiType !== ItemKpiTypeEnum.SAMPLING,
  );

  const projectProductDataSource = useMemo(
    () =>
      projectProductQuery.data?.entities.map((projectProduct) => {
        const { id, productPackaging, product } = projectProduct;
        const foundItem = items.find(
          (item) => item.type === itemKpiType && item.projectProductId === id,
        );
        return {
          label: `${productPackaging?.unit.name} - ${product.code} - ${product.name}`,
          kpi: foundItem?.kpi ?? null,
          id: `${id}_`,
          projectProductId: id,
        };
      }) ?? [],
    [itemKpiType, items, projectProductQuery.data?.entities],
  );

  const projectItemDataSource = useMemo(
    () =>
      projectItemsQuery.data?.entities?.map((projectItem) => {
        const { id, item } = projectItem;

        const foundItem = items.find(
          (item) => item.type === itemKpiType && item.projectItemId === id,
        );
        return {
          label: `${item.unit?.name} - ${item.code} - ${item.name}`,
          kpi: foundItem?.kpi ?? null,
          id: `_${id}`,
          projectItemId: id,
        };
      }) ?? [],
    [itemKpiType, items, projectItemsQuery.data?.entities],
  );

  const dataSource = useMemo(() => {
    if (itemKpiType === ItemKpiTypeEnum.SAMPLING) {
      return projectProductDataSource.filter((item) =>
        stringIncludes(item.label, searchValue),
      );
    }

    if (
      itemKpiType === ItemKpiTypeEnum.GIFT ||
      itemKpiType === ItemKpiTypeEnum.GAME
    ) {
      return [...projectItemDataSource, ...projectProductDataSource].filter(
        (item) => stringIncludes(item.label, searchValue),
      );
    }

    return [];
  }, [
    itemKpiType,
    projectItemDataSource,
    projectProductDataSource,
    searchValue,
  ]);

  const sortedDataSource = useMemo(
    () =>
      [...dataSource].sort((a, b) => {
        if (a.kpi && b.kpi) {
          return Number(b.kpi) - Number(a.kpi);
        }
        if (a.kpi && !b.kpi) {
          return -1;
        }
        if (!a.kpi && b.kpi) {
          return 1;
        }
        return 0;
      }),
    [dataSource],
  );

  const onCancel = useCallback(() => {
    setSearchValue("");
    cancel();
  }, [cancel]);

  return (
    <CustomModal
      width={800}
      title={modalTitle}
      isOpen={isOpen}
      hideConfirm
      onCancel={onCancel}
      content={
        <>
          <p>{title}</p>

          <Input
            placeholder={`Nhập mã, tên, quy cách cần tìm`}
            prefix={<SearchOutlined />}
            className="w-[50%]"
            onChange={(e) => {
              setSearchValue(e.target.value);
            }}
            allowClear
          />

          <Table
            className="mt-[17px]"
            rowKey={"id"}
            dataSource={sortedDataSource}
            scroll={{
              y: 550,
            }}
            pagination={false}
            columns={[
              {
                title:
                  ITEM_KPI_TYPE_LABEL[
                    itemKpiType as keyof typeof ITEM_KPI_TYPE_LABEL
                  ],
                dataIndex: "label",
              },
              {
                title: "KPI",
                dataIndex: "kpi",
                align: "right",
                render: (
                  kpi: number,
                  {
                    projectProductId,
                    projectItemId,
                  }: { projectProductId?: number; projectItemId?: number },
                ) => (
                  <KPIEditInput
                    kpi={kpi ? Number(kpi) : null}
                    cb={cb}
                    projectId={projectId}
                    itemKpiType={itemKpiType}
                    projectProductId={projectProductId}
                    projectItemId={projectItemId}
                    channelId={channelId}
                    provinceId={provinceId}
                  />
                ),
              },
            ]}
          />
        </>
      }
    />
  );
};

export default KPIModal;
