import FilterClassicComponent from "@/components/FilterClassicComponent";
import { renderTableCell } from "@/components/table-cell";
import { SearchOutlined } from "@ant-design/icons";
import { Form, Input, Select, Table } from "antd";
import { ColumnsType } from "antd/es/table";
import { ProjectUserInterface } from "./interface";

export default function ProjectAdminPage() {
  const [searchForm] = Form.useForm();

  const searchContent = (
    <>
      <Form.Item name="filterValue">
        <Input
          placeholder="Tìm theo tên, sđt, email, username"
          prefix={<SearchOutlined />}
        />
      </Form.Item>
      <Form.Item name="roleId">
        <Select placeholder="Vị trí" style={{ width: 100 }} />
      </Form.Item>
      <Form.Item name="agencyId">
        <Select placeholder="Agency phụ trách" />
      </Form.Item>
    </>
  );

  const columns: ColumnsType<ProjectUserInterface> = [
    {
      title: "Nh<PERSON> viên",
      dataIndex: "name",
      key: "name",
      render: renderTableCell,
    },
    {
      title: "Usernamen",
      dataIndex: "username",
      key: "username",
      render: renderTableCell,
    },
  ];

  return (
    <div>
      <h2>Nhân viên cấp quản lý phía Client</h2>
      <div className="bg-white p-10 rounded">
        <FilterClassicComponent
          searchHandler={function (): void {
            throw new Error("Function not implemented.");
          }}
          searchForm={searchForm}
          handleExcelButtonClick={function (): void {
            throw new Error("Function not implemented.");
          }}
          handleAddButtonClick={function (): void {
            throw new Error("Function not implemented.");
          }}
          content={searchContent}
          className="mb-6"
        />

        <Table columns={columns} />
      </div>
    </div>
  );
}
