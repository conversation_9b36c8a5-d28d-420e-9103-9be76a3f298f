import { CURD } from "@/common/constant";
import { formError<PERSON><PERSON>ponse<PERSON><PERSON><PERSON> } from "@/common/helper";
import ModalCURD from "@/components/ModalCURD";
import { ProjectAgencyInterface } from "@/routes/project/interface";
import { useApp } from "@/UseApp";
import { Form, Input } from "antd";
import { useCallback, useEffect } from "react";
import { FeatureSchemeInterface } from "../../../interface";
import { useCreateSchemeMutation, useUpdateSchemeMutation } from "../service";

interface ExchangeTabSchemeModalProps {
  open: boolean;
  projectAgency: ProjectAgencyInterface;
  componentFeatureId: number;
  cb: () => void;
  action: CURD | null;
  selectedScheme: FeatureSchemeInterface | null;
  cancelCb: () => void;
}

const ExchangeTabSchemeModal = ({
  open,
  projectAgency,
  componentFeatureId,
  cb,
  action,
  selectedScheme,
  cancelCb,
}: ExchangeTabSchemeModalProps) => {
  const { showNotification } = useApp();

  const [formAddScheme] = Form.useForm();

  const createSchemeMutation = useCreateSchemeMutation(componentFeatureId);
  const updateSchemeMutation = useUpdateSchemeMutation(componentFeatureId);

  const formAddSchemeContent = (
    <>
      <p>
        <span className="text-hint">
          Bạn đang {action === CURD.CREATE ? "thêm" : "chỉnh sửa"} scheme cho
          agency:
        </span>{" "}
        <span className="text-blue">{projectAgency.agency.name}</span>
      </p>
      <Form.Item
        label="Tên scheme"
        name={"name"}
        rules={[
          {
            required: true,
            message: "Tên scheme không được bỏ trống.",
          },
        ]}
      >
        <Input />
      </Form.Item>
    </>
  );

  const addSchemeFinish = useCallback(async () => {
    try {
      if (action === CURD.CREATE) {
        await createSchemeMutation.mutateAsync({
          name: formAddScheme.getFieldValue("name"),
          projectAgencyId: projectAgency.id,
        });

        showNotification({
          type: "success",
          message: "Thêm scheme thành công",
        });
      }

      if (action === CURD.UPDATE && selectedScheme) {
        await updateSchemeMutation.mutateAsync({
          name: formAddScheme.getFieldValue("name") as string,
          id: selectedScheme.id,
        });

        showNotification({
          type: "success",
          message: "Chỉnh sửa scheme thành công",
        });
      }

      formAddScheme.resetFields();
      cb();
    } catch (e) {
      formErrorResponseHandler(formAddScheme, e);
    }
  }, [
    action,
    cb,
    createSchemeMutation,
    formAddScheme,
    projectAgency.id,
    selectedScheme,
    showNotification,
    updateSchemeMutation,
  ]);

  useEffect(() => {
    if (action === CURD.UPDATE && selectedScheme) {
      formAddScheme.setFieldsValue({
        name: selectedScheme.name,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [action, selectedScheme]);

  return (
    <ModalCURD
      title={
        action === CURD.CREATE ? "Thêm scheme quà" : "Chỉnh sửa scheme quà"
      }
      isOpen={open}
      formContent={formAddSchemeContent}
      form={formAddScheme}
      onFinish={addSchemeFinish}
      action={action}
      onCancelCb={() => {
        formAddScheme.resetFields();
        cancelCb();
      }}
    />
  );
};

export default ExchangeTabSchemeModal;
