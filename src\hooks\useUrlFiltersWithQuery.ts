/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  DEFAULT_CURRENT_PAGE,
  DEFAULT_PAGE_SIZE,
  MAX_PAGE_SIZE,
} from "@/common/constant";
import { UseQueryResult } from "@tanstack/react-query";
import { FormInstance } from "antd";
import _ from "lodash";
import { useCallback, useEffect, useState } from "react";
import { useSearchParams } from "react-router-dom";

interface PaginatedResponse<T> {
  entities: T[];
  count: number;
}

interface PaginationType {
  current: number;
  pageSize: number;
  total?: number;
  onChange: (page: number, pageSize: number) => void;
  showTotal: (total: number) => string;
}

interface UseUrlFiltersOptions {
  defaultPageSize?: number;
  defaultCurrentPage?: number;
  parseParamValue?: (key: string, value: string) => any;
  onFilterChange?: (newFilter: any) => void;
  transformations?: {
    toFormValues?: Record<string, (value: any) => Record<string, any>>;
    toUrlValues?: Record<string, (value: any) => Record<string, any>>;
    toFilterValues?: Record<string, (value: any) => Record<string, any>>;
  };
  urlSync?: {
    enabled?: boolean; // Master switch for URL synchronization
    includePagination?: boolean; // Whether to include page and pageSize in URL
    includeFields?: string[]; // Specific fields to include in URL
    excludeFields?: string[]; // Fields to exclude from URL
    clearUrlOnReset?: boolean; // Whether to clear URL params on reset
  };
  defaultFilter?: Record<string, any>;
}

interface UseUrlFiltersProps<T> {
  formInstance: FormInstance;
  options?: UseUrlFiltersOptions;
  useQueryHook: (...args: any[]) => UseQueryResult<PaginatedResponse<T>>;
  queryParams?: any[];
}

interface UseUrlFiltersResult<T> {
  query: UseQueryResult<PaginatedResponse<T>>;
  filter: Record<string, any>;
  currentPage: number;
  pageSize: number;
  handleSearch: () => void;
  handleReset: () => void;
  getPaginationProps: () => PaginationType;
  setFilterFromUrl: () => void; // New function to manually sync from URL
  updateUrl: () => void; // New function to manually sync to URL
}

/**
 * A hook that combines `useUrlFilters` and a custom query hook to enable server-side filtering and pagination.
 *
 * @param formInstance The form instance to sync with the URL filter.
 * @param options The options for the hook.
 * @param useQueryHook The custom query hook to use for fetching data.
 * @param queryParams The parameters to pass to the custom query hook.
 *
 * @returns An object with the following properties:
 * - `query`: The result of the custom query hook.
 * - `filter`: The current filter state.
 * - `currentPage`: The current page number.
 * - `pageSize`: The current page size.
 * - `handleSearch`: A function to handle the search action.
 * - `handleReset`: A function to handle the reset action.
 * - `getPaginationProps`: A function to get the pagination props.
 * - `setFilterFromUrl`: A function to manually sync the filter state from the URL.
 * - `updateUrl`: A function to manually sync the URL with the current filter state.
 */
export function useUrlFiltersWithQuery<T>({
  formInstance,
  options = {},
  useQueryHook,
  queryParams = [],
}: UseUrlFiltersProps<T>): UseUrlFiltersResult<T> {
  const {
    defaultPageSize = DEFAULT_PAGE_SIZE,
    defaultCurrentPage = DEFAULT_CURRENT_PAGE,
    parseParamValue,
    onFilterChange,
    transformations = {},
    urlSync = {
      enabled: true,
      includePagination: true,
      includeFields: [],
      excludeFields: [],
      clearUrlOnReset: true,
    },
    defaultFilter,
  } = options;

  const [searchParams, setSearchParams] = useSearchParams();
  const [filter, setFilter] = useState<Record<string, any>>({});
  const [currentPage, setCurrentPage] = useState(() =>
    parseInt(searchParams.get("page") ?? defaultCurrentPage.toString()),
  );
  const [pageSize, setPageSize] = useState(() => {
    const currentPageSize = parseInt(
      searchParams.get("pageSize") ?? defaultPageSize.toString(),
    );
    return currentPageSize > MAX_PAGE_SIZE ? MAX_PAGE_SIZE : currentPageSize;
  });

  // Helper function to check if a field should be included in URL
  const shouldIncludeInUrl = useCallback(
    (key: string) => {
      if (!urlSync.enabled) return false;
      if (urlSync.excludeFields?.includes(key)) return false;
      if (urlSync.includeFields?.length && !urlSync.includeFields.includes(key))
        return false;
      return true;
    },
    [urlSync],
  );

  const query = useQueryHook(...queryParams, {
    take: pageSize,
    skip: (currentPage - 1) * pageSize,
    ...filter,
    ...defaultFilter,
  });

  // Function to parse URL parameters and update filter/form
  const setFilterFromUrl = useCallback(() => {
    if (!urlSync.enabled) return;

    const initialFilter: Record<string, any> = {};
    const params = Object.fromEntries(searchParams.entries());

    Object.entries(params).forEach(([key, value]) => {
      if (key !== "page" && key !== "pageSize" && shouldIncludeInUrl(key)) {
        let parsedValue = parseParamValue ? parseParamValue(key, value) : value;

        if (transformations.toFormValues?.[key]) {
          Object.assign(
            initialFilter,
            transformations.toFormValues[key](parsedValue),
          );
        } else {
          // Type conversion logic
          if (!isNaN(Number(value))) {
            parsedValue = Number(value);
          } else if (value === "true" || value === "false") {
            parsedValue = value === "true";
          }
          initialFilter[key] = parsedValue;
        }
      }
    });

    setFilter(initialFilter);
    formInstance.setFieldsValue(initialFilter);
  }, [
    searchParams,
    parseParamValue,
    transformations.toFormValues,
    formInstance,
    shouldIncludeInUrl,
    urlSync.enabled,
  ]);

  // Function to update URL with current filter state
  const updateUrl = useCallback(() => {
    if (!urlSync.enabled) return;

    const params = new URLSearchParams();

    if (urlSync.includePagination) {
      if (currentPage !== defaultCurrentPage) {
        params.set("page", currentPage.toString());
      }
      if (pageSize !== defaultPageSize) {
        params.set("pageSize", pageSize.toString());
      }
    }

    Object.entries(filter).forEach(([key, value]) => {
      if (
        value !== undefined &&
        value !== null &&
        value !== "" &&
        shouldIncludeInUrl(key)
      ) {
        if (transformations.toUrlValues?.[key]) {
          const transformedValues = transformations.toUrlValues[key](value);
          Object.entries(transformedValues).forEach(
            ([transformedKey, transformedValue]) => {
              if (shouldIncludeInUrl(transformedKey)) {
                params.set(transformedKey, transformedValue.toString());
              }
            },
          );
        } else {
          params.set(key, value.toString());
        }
      }
    });

    setSearchParams(params);
    onFilterChange?.(filter);
  }, [
    filter,
    currentPage,
    pageSize,
    setSearchParams,
    defaultCurrentPage,
    defaultPageSize,
    onFilterChange,
    shouldIncludeInUrl,
    transformations.toUrlValues,
    urlSync.enabled,
    urlSync.includePagination,
  ]);

  // Initialize from URL params
  useEffect(() => {
    setFilterFromUrl();
  }, []);

  // Update URL when filter changes
  useEffect(() => {
    updateUrl();
  }, [filter, currentPage, pageSize]);

  const handleSearch = useCallback(() => {
    setCurrentPage(defaultCurrentPage);
    const searchData = formInstance.getFieldsValue();

    Object.keys(transformations.toUrlValues || {}).forEach((key) => {
      if (searchData[key] !== undefined) {
        const transformed = transformations.toUrlValues![key](searchData[key]);
        Object.assign(searchData, transformed);
        delete searchData[key];
      }
    });

    Object.keys(transformations.toFilterValues || {}).forEach((key) => {
      if (searchData[key] !== undefined) {
        const transformed = transformations.toFilterValues![key](
          searchData[key],
        );
        Object.assign(searchData, transformed);
        delete searchData[key];
      }
    });

    if (!_.isEqual(searchData, filter)) {
      setFilter(searchData);
    } else {
      query.refetch();
    }
  }, [
    defaultCurrentPage,
    formInstance,
    filter,
    transformations.toUrlValues,
    query,
  ]);

  const handleReset = useCallback(() => {
    formInstance.resetFields();
    setFilter({});
    setCurrentPage(defaultCurrentPage);
    setPageSize(defaultPageSize);

    if (urlSync.enabled && urlSync.clearUrlOnReset) {
      setSearchParams(new URLSearchParams());
    }
  }, [
    formInstance,
    defaultCurrentPage,
    defaultPageSize,
    urlSync.enabled,
    urlSync.clearUrlOnReset,
  ]);

  const getPaginationProps = useCallback(
    (): PaginationType => ({
      current: currentPage,
      pageSize: pageSize,
      total: query.data?.count ?? 0,
      onChange: (page: number, size: number) => {
        if (size !== pageSize) {
          setPageSize(size);
          setCurrentPage(defaultCurrentPage);
        } else {
          setCurrentPage(page);
        }
      },
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
    }),
    [currentPage, pageSize, defaultCurrentPage, query.data?.count],
  );

  // Handle empty results
  useEffect(() => {
    if (
      query.data?.entities &&
      (query.data?.entities.length ?? 0) === 0 &&
      currentPage > 1
    ) {
      setCurrentPage((currentPage) => currentPage - 1);
    }
  }, [query.data?.count, currentPage]);

  return {
    query,
    filter,
    currentPage,
    pageSize,
    handleSearch,
    handleReset,
    getPaginationProps,
    setFilterFromUrl,
    updateUrl,
  };
}
