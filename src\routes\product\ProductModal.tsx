import { useApp } from "@/UseApp.tsx";
import { CURD } from "@/common/constant.ts";
import { filterOption } from "@/common/helper.ts";
import { uploadImage } from "@/common/upload-image.helper.ts";
import CreateUnitInSelectComponent from "@/components/CreateUnitInSelectComponent.tsx";
import {
  CloseOutlined,
  DeleteOutlined,
  FolderOpenOutlined,
  LoadingOutlined,
} from "@ant-design/icons";
import {
  Alert,
  Button,
  Col,
  ColorPicker,
  Form,
  FormInstance,
  Input,
  InputNumber,
  Modal,
  Row,
  Select,
  Space,
  Upload,
} from "antd";
import React, { useCallback, useEffect, useState } from "react";
import { useBrandsQuery } from "../brand/services.ts";
import { useClientsQuery } from "../client/service.ts";
import { useUnitsQuery } from "../unit/service.ts";
import {
  useCreateProductMutation,
  useUpdateProductMutation,
} from "./service.ts";

interface ProductModalInterface {
  isOpen: boolean;
  modalTitle: string;
  setIsOpen: (isOpen: boolean) => void;
  form: FormInstance;
  disabledSelectBrand?: boolean;
  formAction: CURD | null;
  callback?: () => void;
  setModalTitle?: (modalTitle: string) => void;
}

const ProductModal = (props: ProductModalInterface) => {
  const {
    isOpen,
    modalTitle,
    setIsOpen,
    form,
    disabledSelectBrand,
    formAction,
    callback,
    setModalTitle,
  } = props;

  const { axiosPost, showNotification, loading, setLoading } = useApp();

  const [imageUrl, setImageUrl] = useState<string>();
  const [isLoadingUpload, setIsLoadingUpload] = useState(false);
  const [mainPackagingOptions, setMainPackagingOptions] = useState<
    { label: string; value: number }[]
  >([]);
  const [selectedClientId, setSelectedClientId] = useState<number | undefined>(
    undefined,
  );

  const clientsQuery = useClientsQuery(
    {
      take: 0,
      skip: 0,
      getInActive: true,
    },
    isOpen,
  );
  const brandsQuery = useBrandsQuery(
    {
      take: 0,
      skip: 0,
      clientId: selectedClientId ?? form.getFieldValue("clientId"),
    },
    (!!selectedClientId || !!form.getFieldValue("clientId")) && isOpen,
  );
  const unitsQuery = useUnitsQuery(
    {
      take: 0,
      skip: 0,
      clientId: selectedClientId ?? form.getFieldValue("clientId"),
    },
    (!!selectedClientId || !!form.getFieldValue("clientId")) && isOpen,
  );

  const createProductMutation = useCreateProductMutation();
  const updateProductMutation = useUpdateProductMutation();

  useEffect(() => {
    setImageUrl(form.getFieldValue("imageUrl"));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [form.getFieldValue("id")]);

  const onModalClose = useCallback(() => {
    setIsOpen(false);
    setImageUrl(undefined);
    form.resetFields();
    if (setModalTitle) setModalTitle("");
    setSelectedClientId(undefined);
  }, [form, setIsOpen, setModalTitle]);

  const handleFormSubmit = useCallback(async () => {
    setLoading(true);
    try {
      await form.validateFields();
      const data = form.getFieldsValue();
      const id = form.getFieldValue("id");

      const { backgroundColor, foregroundColor } = data;

      data.backgroundColor =
        typeof backgroundColor === "string"
          ? backgroundColor
          : backgroundColor?.toHexString();

      data.foregroundColor =
        typeof foregroundColor === "string"
          ? foregroundColor
          : foregroundColor?.toHexString();

      for (let i = 0; i < data.packagings.length; i++) {
        const element = data.packagings[i];
        element.price = parseInt(element.price);

        if (element.price < 0) {
          form.setFields([
            {
              name: ["packagings", i, "price"],
              errors: ["Giá bán không được nhỏ hơn 0"],
            },
          ]);
          return;
        }
        if (element.price > 999999999) {
          form.setFields([
            {
              name: ["packagings", i, "price"],
              errors: ["Giá bán không được lớn hơn 999,999,999"],
            },
          ]);
          return;
        }

        if (element.rate) element.rate = parseInt(element.rate);
      }

      if (data.imageFile) {
        data.imageId = (await uploadImage(axiosPost, data.imageFile.file))?.id;
        delete data.imageFile;
      }

      switch (formAction) {
        case CURD.CREATE:
          await createProductMutation.mutateAsync(data);

          showNotification({
            type: "success",
            message: "Thêm sản phẩm thành công",
          });
          break;
        case CURD.UPDATE:
          await updateProductMutation.mutateAsync({ ...data, id });

          showNotification({
            type: "success",
            message: "Cập nhật sản phẩm thành công",
          });
          break;
        default:
          return;
      }

      onModalClose();
      if (setModalTitle) setModalTitle("");
      if (callback) callback();

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      const data: { message: { field: string; message: string }[] } =
        error?.response?.data;
      const { message } = data;
      message.forEach((item: { field: string; message: string }) => {
        form.setFields([{ name: item.field, errors: [item.message] }]);
      });
    } finally {
      setLoading(false);
    }
  }, [
    axiosPost,
    callback,
    createProductMutation,
    form,
    formAction,
    onModalClose,
    setLoading,
    setModalTitle,
    showNotification,
    updateProductMutation,
  ]);

  const beforeUpload = useCallback(
    async (file: File) => {
      setIsLoadingUpload(true);
      const imageUrl = URL.createObjectURL(file);
      if (imageUrl) {
        setImageUrl(imageUrl);
      }
      setIsLoadingUpload(false);
      return false;
    },
    [setIsLoadingUpload],
  );

  const uploadButton = (
    <div>{isLoadingUpload ? <LoadingOutlined /> : <FolderOpenOutlined />}</div>
  );

  const handleSelectMainPackagingClick = useCallback(() => {
    const mainPackagingOptions = unitsQuery.data?.entities
      .filter((unit) => {
        const packagingUnits = form
          .getFieldValue("packagings")
          ?.filter((item: { unitId?: number }) => item?.unitId)
          ?.map((item: { unitId?: number }) => item?.unitId);
        return packagingUnits?.includes(unit.id);
      })
      ?.map((unit) => ({ label: unit.name, value: unit.id }));
    setMainPackagingOptions(mainPackagingOptions ?? []);
  }, [form, unitsQuery.data?.entities]);

  const packagings = form.getFieldValue("packagings");

  useEffect(() => {
    handleSelectMainPackagingClick();
  }, [packagings, handleSelectMainPackagingClick]);

  const selectUnitDropdownRender = (menu: React.ReactElement) => {
    return (
      <CreateUnitInSelectComponent
        menu={menu}
        cb={() => {
          unitsQuery.refetch();
        }}
        clientId={selectedClientId ?? form.getFieldValue("clientId")}
      />
    );
  };

  useEffect(() => {
    if (formAction === CURD.CREATE) {
      form.setFieldsValue({
        backgroundColor: "#FFFFFF",
        foregroundColor: "#000000",
      });
    }
  }, [form, formAction]);

  return (
    <Modal
      open={isOpen}
      footer={null}
      closeIcon={null}
      width={1100}
      styles={{ content: { padding: 0 } }}
    >
      <div className={"pl-10 pr-10"}>
        <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
          <h2 className="text-neutral-700 text-2xl font-semibold ">
            {modalTitle}
          </h2>
          <div className="pt-5">
            <Button
              type="link"
              onClick={onModalClose}
              size="large"
              icon={<CloseOutlined />}
              disabled={loading}
            />
          </div>
        </div>
      </div>

      <Form
        name="productForm"
        onFinish={handleFormSubmit}
        layout={"vertical"}
        form={form}
        initialValues={{ packagings: [{}] }}
      >
        <div className={"pl-10 pr-10"}>
          <p className="uppercase text-main-color font-bold">
            Thông tin cơ bản
          </p>
          <Form.Item name={"id"} hidden={true}></Form.Item>
          <Form.Item name={"imageUrl"} hidden={true}></Form.Item>
          <Form.Item
            style={{ width: 500 }}
            name="name"
            label={"Tên sản phẩm"}
            rules={[
              {
                required: true,
                message: "Tên sản phẩm không được để trống",
              },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            style={{ width: 500 }}
            name="shortName"
            label={"Tên rút gọn của sản phẩm"}
          >
            <Input />
          </Form.Item>
          <Form.Item
            style={{ width: 500 }}
            name="code"
            label={"Mã sản phẩm/SKU"}
            rules={[
              {
                required: true,
              },
            ]}
          >
            <Input />
          </Form.Item>

          {(() => {
            const clientOptions = clientsQuery.data?.entities
              .filter(
                (client) =>
                  client.isActive ||
                  client.id === selectedClientId ||
                  client.id === form.getFieldValue("clientId"),
              )
              .map((client) => ({
                label: client.name,
                value: client.id,
              }));
            return (
              <Form.Item
                style={{ width: 500 }}
                name="clientId"
                label={"Client"}
                rules={[{ required: true }]}
              >
                <Select
                  options={clientOptions}
                  showSearch
                  optionFilterProp="children"
                  filterOption={filterOption}
                  disabled={formAction === CURD.UPDATE || disabledSelectBrand}
                  onSelect={(value) => {
                    setSelectedClientId(value);
                    form.resetFields([
                      "brandId",
                      "packagings",
                      "mainPackagingId",
                    ]);
                  }}
                />
              </Form.Item>
            );
          })()}

          <Form.Item
            name="brandId"
            label="Nhãn hàng trực thuộc"
            rules={[
              { required: true, message: "Nhãn hàng không được bỏ trống." },
            ]}
          >
            <Select
              disabled={disabledSelectBrand}
              style={{
                width: "500px",
              }}
              showSearch
              optionFilterProp="children"
              filterOption={filterOption}
              options={brandsQuery.data?.entities.map((brand) => ({
                label: brand.name,
                value: brand.id,
              }))}
            />
          </Form.Item>
          <Form.Item name="imageFile" label={"Ảnh sản phẩm"}>
            <Upload
              name="avatar"
              listType="picture-card"
              showUploadList={false}
              beforeUpload={beforeUpload}
              accept={"image/*"}
            >
              {(() => {
                if (isLoadingUpload) {
                  return <LoadingOutlined />;
                }
                if (imageUrl) {
                  return (
                    <img
                      src={imageUrl}
                      alt="avatar"
                      style={{ width: "100%", height: "100%" }}
                    />
                  );
                } else {
                  return uploadButton;
                }
              })()}
            </Upload>
          </Form.Item>

          <Form.Item
            name={"backgroundColor"}
            label={"Màu nền"}
            layout={"horizontal"}
          >
            <ColorPicker
              showText
              defaultValue={"#FFFFFF"}
              mode={"single"}
              disabledAlpha
              format="hex"
            />
          </Form.Item>

          <Form.Item
            name={"foregroundColor"}
            label={"Màu chữ"}
            layout={"horizontal"}
          >
            <ColorPicker
              showText
              defaultValue={"#000000"}
              disabledAlpha
              format="hex"
            />
          </Form.Item>

          <p className="uppercase text-main-color font-bold">
            Quy cách đóng gói
          </p>
          <div className={"inline-block mb-5"}>
            <Alert
              message="Khai báo quy cách đóng gói từ nhỏ nhất đến lớn nhất. Ví dụ: Gói -> Hộp -> Thùng"
              type="info"
              showIcon
            />
          </div>
          <br />
          <Form.List name="packagings">
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <Row gutter={16} key={key}>
                    <Form.Item {...restField} name={[name, "id"]} hidden>
                      <Input />
                    </Form.Item>
                    <Col className="gutter-row" span={6}>
                      <Form.Item noStyle dependencies={[["packagings", name]]}>
                        {() => {
                          const packagings: { unitId?: number }[] =
                            form.getFieldValue("packagings");

                          const unitOptions = unitsQuery.data?.entities
                            .map((unit) => ({
                              label: unit.name,
                              value: unit.id,
                            }))
                            .filter((unitOption) => {
                              return !packagings.some(
                                (packaging, index) =>
                                  index != name &&
                                  packaging?.unitId === unitOption.value,
                              );
                            });

                          return (
                            <Form.Item
                              {...restField}
                              name={[name, "unitId"]}
                              label="Tên quy cách"
                              rules={[
                                {
                                  required: true,
                                },
                                ({ getFieldValue }) => ({
                                  validator(rule, value) {
                                    const packagings: { unitId?: number }[] =
                                      getFieldValue("packagings");

                                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                                    // @ts-ignore
                                    const field = rule["field"] as unknown as
                                      | string
                                      | undefined;

                                    if (
                                      packagings.some(
                                        (item, index) =>
                                          index !==
                                            Number(field?.split(".")[1]) &&
                                          item.unitId === value,
                                      )
                                    ) {
                                      return Promise.reject(
                                        new Error("Quy cách đã tồn tại"),
                                      );
                                    }

                                    return Promise.resolve();
                                  },
                                }),
                              ]}
                            >
                              <Select
                                style={{
                                  width: "230px",
                                }}
                                showSearch
                                optionFilterProp="children"
                                filterOption={filterOption}
                                options={unitOptions}
                                dropdownRender={selectUnitDropdownRender}
                                popupMatchSelectWidth={false}
                                disabled={formAction === CURD.UPDATE}
                              />
                            </Form.Item>
                          );
                        }}
                      </Form.Item>
                    </Col>

                    <Col className="gutter-row" span={6}>
                      <Form.Item
                        {...restField}
                        name={[name, "barcode"]}
                        label="Barcode"
                        rules={[
                          {
                            required: true,
                            message: "Barcode không được để trống",
                          },
                          {
                            pattern: new RegExp(/^[\x20-\x7E]+$/),
                            message: "Barcode không hợp lệ",
                          },
                          {
                            max: 20,
                            message: "Barcode không được quá 20 ký tự",
                          },
                        ]}
                      >
                        <Input
                          placeholder=""
                          disabled={formAction === CURD.UPDATE}
                        />
                      </Form.Item>
                    </Col>

                    <Col className="gutter-row" span={6}>
                      <Form.Item
                        {...restField}
                        name={[name, "price"]}
                        label="Giá bán"
                        rules={[
                          {
                            required: true,
                          },
                        ]}
                      >
                        <InputNumber
                          disabled={formAction === CURD.UPDATE}
                          style={{ width: "230px" }}
                          controls={false}
                          onWheel={(e) => e.currentTarget.blur()}
                          formatter={(value) => {
                            return `${value}`.replace(
                              /\B(?=(\d{3})+(?!\d))/g,
                              ",",
                            );
                          }}
                          parser={(value) => {
                            const parsedValue = parseFloat(
                              value?.replace(/\$\s?|(,*)/g, "") ?? "",
                            );
                            const parsedValueIsPositive = parsedValue >= 0;

                            return parsedValueIsPositive
                              ? parsedValue
                              : Number(0);
                          }}
                        />
                      </Form.Item>
                    </Col>

                    {key > 0 && (
                      <Col className="gutter-row" span={6}>
                        <Space>
                          <Form.Item
                            noStyle
                            dependencies={[
                              ["packagings", name],
                              ["packagings", name, "unitId"],
                              ["packagings", 0, "unitId"],
                            ]}
                          >
                            {() => {
                              const rootUnit =
                                form.getFieldValue("packagings")[0];
                              const currentUnit =
                                form.getFieldValue("packagings")[name];

                              const rootUnitName =
                                unitsQuery.data?.entities.find(
                                  (unit) => unit.id === rootUnit?.unitId,
                                )?.name;

                              const currentUnitName =
                                unitsQuery.data?.entities.find(
                                  (unit) => unit.id === currentUnit?.unitId,
                                )?.name;

                              return (
                                <Form.Item
                                  {...restField}
                                  name={[name, "rate"]}
                                  label="Tỷ lệ quy đổi"
                                  rules={[
                                    {
                                      required: true,
                                    },
                                  ]}
                                >
                                  <InputNumber
                                    min={0}
                                    placeholder={`Một ${currentUnitName ?? ""} có bao nhiêu ${rootUnitName ?? ""}?`}
                                    style={{ width: "220px" }}
                                    controls={false}
                                    disabled={formAction === CURD.UPDATE}
                                  />
                                </Form.Item>
                              );
                            }}
                          </Form.Item>

                          {formAction === CURD.CREATE && (
                            <Form.Item label=" ">
                              <DeleteOutlined onClick={() => remove(name)} />
                            </Form.Item>
                          )}
                        </Space>
                      </Col>
                    )}
                  </Row>
                ))}
                {formAction === CURD.CREATE && (
                  <Button
                    type="link"
                    onClick={() => add()}
                    style={{
                      padding: "0px",
                      color: "#1D8EE6",
                      marginTop: "-5px",
                    }}
                  >
                    Thêm quy cách
                  </Button>
                )}
              </>
            )}
          </Form.List>

          <Form.Item noStyle dependencies={["packagings"]}>
            {() => {
              return (
                <Form.Item
                  name="mainPackagingId"
                  label="Quy cách chuẩn của sản phẩm"
                  className="mt-5"
                  extra={
                    <p className={"mt-1"}>
                      Là quy cách thường dùng để bán lẻ nhất
                    </p>
                  }
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <Select
                    style={{
                      width: "150px",
                    }}
                    showSearch
                    optionFilterProp="children"
                    filterOption={filterOption}
                    options={mainPackagingOptions}
                    onClick={handleSelectMainPackagingClick}
                    disabled={formAction === CURD.UPDATE}
                  />
                </Form.Item>
              );
            }}
          </Form.Item>
        </div>

        <div className="flex justify-end gap-4 py-4 rounded max-md:max-w-full max-md:flex-wrap pb-4 pl-10 pr-10 bg-[#F7F8FA]">
          <Button htmlType="button" onClick={onModalClose} disabled={loading}>
            Đóng
          </Button>
          <Button htmlType="submit" type={"primary"} loading={loading}>
            {formAction === CURD.CREATE ? "Thêm mới" : "Cập nhật"}
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

export default ProductModal;
