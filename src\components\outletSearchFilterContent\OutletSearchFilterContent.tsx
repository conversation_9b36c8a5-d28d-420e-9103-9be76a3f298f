import { filterOption } from "@/common/helper.ts";
import {
  useDistrictsQuery,
  useProvincesQuery,
  useWardsQuery,
} from "@location/service.ts";
import {
  useProjectChannelQuery,
  useProjectChannelsQuery,
} from "@project/outlet/service.ts";
import { Form, FormInstance, Input, Select, Space } from "antd";
import { useState } from "react";

interface OutletSearchFilterContentProps {
  open: boolean;
  projectId: number;
  searchForm: FormInstance;
}

const OutletSearchFilterContent = ({
  open,
  projectId,
  searchForm,
}: OutletSearchFilterContentProps) => {
  const [selectedProvinceId, setSelectedProvinceId] = useState<number | null>(
    null,
  );
  const [selectedDistrictId, setSelectedDistrictId] = useState<number | null>(
    null,
  );
  const [channelIdSelected, setChannelIdSelected] = useState<number | null>(
    null,
  );

  const provincesQuery = useProvincesQuery(open);
  const districtsQuery = useDistrictsQuery(selectedProvinceId);
  const wardsQuery = useWardsQuery(selectedProvinceId, selectedDistrictId);
  const projectChannelsQuery = useProjectChannelsQuery(projectId, open);
  const projectChannelQuery = useProjectChannelQuery(
    projectId,
    channelIdSelected,
  );

  return (
    <Space>
      <Form.Item name={"keyword"}>
        <Input
          allowClear
          placeholder={"Tìm theo mã, tên outlet, số nhà, tên đường"}
        />
      </Form.Item>

      <Form.Item name="provinceId">
        <Select
          allowClear
          placeholder="Tỉnh/ TP"
          showSearch
          optionFilterProp="children"
          filterOption={filterOption}
          options={provincesQuery.data?.map((province) => ({
            label: province.name,
            value: province.id,
          }))}
          popupMatchSelectWidth={false}
          onChange={(value: number) => {
            setSelectedProvinceId(value);
            searchForm.resetFields(["districtId", "wardId"]);
          }}
        />
      </Form.Item>

      <Form.Item name="districtId">
        <Select
          allowClear
          placeholder="Quận/ Huyện"
          showSearch
          optionFilterProp="children"
          filterOption={filterOption}
          options={districtsQuery.data?.map((district) => ({
            label: district.name,
            value: district.id,
          }))}
          popupMatchSelectWidth={false}
          onChange={(value: number) => {
            setSelectedDistrictId(value);
            searchForm.resetFields(["wardId"]);
          }}
        />
      </Form.Item>

      <Form.Item name="wardId">
        <Select
          allowClear
          placeholder="Phường/ Xã"
          showSearch
          optionFilterProp="children"
          filterOption={filterOption}
          options={wardsQuery.data?.map((ward) => ({
            label: ward.name,
            value: ward.id,
          }))}
          popupMatchSelectWidth={false}
        />
      </Form.Item>

      <Form.Item name={"channelId"}>
        <Select
          allowClear
          placeholder="Kênh"
          showSearch
          optionFilterProp="children"
          filterOption={filterOption}
          options={projectChannelsQuery.data?.map((channel) => ({
            label: channel.name,
            value: channel.id,
          }))}
          popupMatchSelectWidth={false}
          onSelect={(id) => setChannelIdSelected(id)}
          onChange={() => {
            searchForm.resetFields(["subChannelId"]);
            setChannelIdSelected(null);
          }}
        />
      </Form.Item>

      <Form.Item name={"subChannelId"}>
        <Select
          allowClear
          placeholder="Nhóm"
          showSearch
          optionFilterProp="children"
          filterOption={filterOption}
          options={projectChannelQuery?.data?.subChannels?.map(
            (subChannel) => ({
              label: subChannel.name,
              value: subChannel.id,
            }),
          )}
          popupMatchSelectWidth={false}
        />
      </Form.Item>
    </Space>
  );
};

export default OutletSearchFilterContent;
