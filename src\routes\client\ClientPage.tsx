import { useApp } from "@/UseApp.tsx";
import { CURD, SELECT_ALL, SELECT_ALL_LABEL } from "@/common/constant.ts";
import { formErrorResponseHandler } from "@/common/helper.ts";
import CustomTable from "@/components/CustomTable/CustomTable.tsx";
import FilterComponent from "@/components/FilterComponent.tsx";
import InnerContainer from "@/components/InnerContainer/InnerContainer.tsx";
import ModalCURD from "@/components/ModalCURD.tsx";
import { renderTableCell } from "@/components/table-cell.tsx";
import { renderTableOptionCell } from "@/components/table-option-cell.tsx";
import { useUrlFiltersWithQuery } from "@/hooks/useUrlFiltersWithQuery.ts";
import { Form, Input, Modal } from "antd";
import { ColumnsType } from "antd/es/table";
import { useCallback, useMemo, useState } from "react";
import { ClientInterface } from "./interface.ts";
import {
  useClientsQuery,
  useCreateClientMutation,
  useDeleteClientMutation,
  useUpdateClientMutation,
} from "./service.ts";

const ClientPage = () => {
  const { showNotification, openDeleteModal } = useApp();

  const [searchForm] = Form.useForm();
  const [form] = Form.useForm();
  const [formAction, setFormAction] = useState<CURD | null>(null);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const {
    query: { data, refetch, isFetching, isRefetching },
    handleSearch,
    getPaginationProps,
  } = useUrlFiltersWithQuery<ClientInterface>({
    formInstance: searchForm,
    useQueryHook: useClientsQuery,
    options: {
      defaultFilter: {
        getInActive: true,
      },
    },
  });

  const createClientMutation = useCreateClientMutation();
  const updateClientMutation = useUpdateClientMutation();
  const deleteClientMutation = useDeleteClientMutation();

  const handleBtnEditClick = useCallback(
    (record: ClientInterface) => {
      setIsOpen(true);
      setFormAction(CURD.UPDATE);
      form.setFieldsValue(record);
    },
    [form],
  );

  const handleBtnInactiveClick = useCallback(
    (record: ClientInterface) =>
      Modal.confirm({
        title: `Ngừng hoạt động client ${record.name}?`,
        content: `Bạn có chắc chắn muốn ngừng hoạt động client ${record.name}?`,
        onOk: async () => {
          await updateClientMutation.mutateAsync({
            id: record.id,
            data: { isActive: false },
          });
          showNotification({
            type: "success",
            message: "Ngừng hoạt động client thành công",
          });
          refetch();
        },
      }),
    [refetch, showNotification, updateClientMutation],
  );

  const handleBtnActiveClick = useCallback(
    (record: ClientInterface) =>
      Modal.confirm({
        title: `Kích hoạt client ${record.name}?`,
        content: `Bạn có chắc chắn muốn kích hoạt client ${record.name}?`,
        onOk: async () => {
          await updateClientMutation.mutateAsync({
            id: record.id,
            data: { isActive: true },
          });
          showNotification({
            type: "success",
            message: "Kích hoạt client thành công",
          });
          refetch();
        },
      }),
    [refetch, showNotification, updateClientMutation],
  );

  const handleBtnDeleteClick = useCallback(
    (record: ClientInterface) => {
      openDeleteModal({
        content: (
          <>
            <p>
              Client sẽ được xóa khỏi hệ thống vĩnh viễn và không thể khôi phục
            </p>
            <p>
              Bạn vẫn muốn xóa client{" "}
              <span className={"font-semibold"}>{record.name}</span> khỏi hệ
              thống?
            </p>
          </>
        ),
        deleteText: "Xác nhận xóa",
        loading: deleteClientMutation.isPending,
        onCancel(): void {},
        onDelete: async () => {
          await deleteClientMutation.mutateAsync(record.id);
          showNotification({
            type: "success",
            message: "Xóa client thành công",
          });
          await refetch();
        },
        title: `Xóa client`,
        titleError: "Không thể xóa client",
        contentHeader: (
          <>
            Không thể xóa client{" "}
            <span className="font-semibold">{record.name}</span> bởi vì:
          </>
        ),
      });
    },
    [deleteClientMutation, openDeleteModal, refetch, showNotification],
  );

  const columns: ColumnsType<ClientInterface> = [
    {
      title: "Tên Client",
      key: "name",
      dataIndex: "name",
      className: "min-w-[150px]",
      render: renderTableCell,
    },
    {
      title: "Tên đầy đủ",
      key: "legalName",
      dataIndex: "legalName",
      className: "min-w-[150px]",
      render: renderTableCell,
    },
    {
      title: "Email",
      key: "email",
      dataIndex: "email",
      className: "min-w-[150px]",
      render: renderTableCell,
    },
    {
      title: "Số điện thoại",
      key: "phone",
      dataIndex: "phone",
      className: "min-w-[150px]",
      render: renderTableCell,
    },
    {
      key: "isActive",
      title: "Tình trạng",
      dataIndex: "isActive",
      className: "min-w-[150px]",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "isActive");
      },
    },
    {
      key: "createdAt",
      title: "Thời gian tạo",
      className: "min-w-[150px]",
      dataIndex: "createdAt",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "datetime");
      },
    },
    {
      key: "createdByUserName",
      title: "Người tạo",
      className: "min-w-[150px]",
      dataIndex: "createdByUser",
      render: renderTableCell,
    },
    {
      key: "updatedAt",
      title: "Thời gian cập nhật",
      className: "min-w-[150px]",
      dataIndex: "updatedAt",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "datetime");
      },
    },
    {
      key: "updatedByUserName",
      title: "Người cập nhật",
      className: "min-w-[150px]",
      dataIndex: "updatedByUser",
      render: renderTableCell,
    },
    {
      key: "actions",
      render: (_, record) => {
        return renderTableOptionCell(
          record,
          handleBtnEditClick,
          handleBtnInactiveClick,
          handleBtnActiveClick,
          handleBtnDeleteClick,
        );
      },
      fixed: "right",
      width: 100,
    },
  ];

  const filterOptions = columns
    .filter(
      (item) =>
        !["isActive", "updatedAt", "createdAt", "actions"].includes(
          item.key as string,
        ),
    )
    .map((column) => {
      return {
        label: column.title as string,
        value: column.key as string | number,
      };
    });

  filterOptions.unshift({ label: SELECT_ALL_LABEL, value: SELECT_ALL });

  const handleExcelButtonClick = useCallback(() => {}, []);

  const handleAddButtonClick = useCallback(() => {
    setIsOpen(true);
    setFormAction(CURD.CREATE);
  }, []);

  const formContent = useMemo(
    () => (
      <>
        <Form.Item name={"id"} hidden={true}></Form.Item>

        <Form.Item
          rules={[{ required: true }]}
          name="name"
          label={"Tên Client"}
        >
          <Input />
        </Form.Item>

        <Form.Item name="legalName" label={"Tên đầy đủ"}>
          <Input />
        </Form.Item>

        <Form.Item rules={[{ type: "email" }]} name="email" label={"Email"}>
          <Input />
        </Form.Item>

        <Form.Item
          rules={[
            {
              pattern: new RegExp(/^0\d+$/),
              message: "Vui lòng nhập đúng định dạng số điện thoại",
            },
            {
              len: 10,
            },
          ]}
          name="phone"
          label={"Số điện thoại"}
        >
          <Input />
        </Form.Item>
      </>
    ),
    [],
  );

  const onFinish = useCallback(async () => {
    try {
      await form.validateFields();
      const data = form.getFieldsValue();
      const id = form.getFieldValue("id");

      switch (formAction) {
        case CURD.CREATE:
          await createClientMutation.mutateAsync(data);
          showNotification({
            type: "success",
            message: "Thêm client thành công",
          });
          break;
        case CURD.UPDATE:
          await updateClientMutation.mutateAsync({ id, data });
          showNotification({
            type: "success",
            message: "Cập nhật client thành công",
          });
          break;
        default:
          return;
      }
      form.resetFields();
      setIsOpen(false);

      refetch();

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      formErrorResponseHandler(form, error);
    }
  }, [
    createClientMutation,
    form,
    formAction,
    refetch,
    showNotification,
    updateClientMutation,
  ]);

  const pagination = useMemo(() => getPaginationProps(), [getPaginationProps]);

  const loading = useMemo(
    () =>
      isFetching ||
      isRefetching ||
      createClientMutation.isPending ||
      updateClientMutation.isPending ||
      deleteClientMutation.isPending,
    [
      isFetching,
      isRefetching,
      createClientMutation.isPending,
      updateClientMutation.isPending,
      deleteClientMutation.isPending,
    ],
  );

  return (
    <div>
      <h2>Client</h2>
      <InnerContainer>
        <FilterComponent
          filterOptions={filterOptions}
          searchHandler={handleSearch}
          handleExcelButtonClick={handleExcelButtonClick}
          handleAddButtonClick={handleAddButtonClick}
          searchForm={searchForm}
          btnLoading={loading}
        />

        <CustomTable<ClientInterface>
          dataSource={data?.entities ?? []}
          columns={columns}
          scroll={{
            x: "max-content",
            y: pagination.total ? "80vh" : undefined,
          }}
          pagination={pagination}
          loading={loading}
        />
      </InnerContainer>

      <ModalCURD
        title={formAction === CURD.CREATE ? "Thêm Client" : "Client"}
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        formContent={formContent}
        form={form}
        onFinish={onFinish}
        action={formAction}
        onCancelCb={() => {
          form.resetFields();
          setIsOpen(false);
        }}
        btnConfirmLoading={loading}
      />
    </div>
  );
};

export default ClientPage;
