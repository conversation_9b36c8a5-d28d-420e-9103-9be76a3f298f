import { AbstractEntityInterface } from "@/common/interface";

export enum PhotographyType {
  MIN_MAX = "MIN_MAX",
  FULL = "FULL",
}

export interface PhotographyInterface extends AbstractEntityInterface {
  name: string;
  description: string;
  minimum: number;
  maximum: number;
  isWatermarkRequired: boolean;
  projectFeatureId: number;
  ordinal: number;
}

export enum PhotographyActionEnum {
  EDIT = "EDIT",
  INACTIVE = "INACTIVE",
  ACTIVE = "ACTIVE",
  DELETE = "DELETE",
}
