import { DATE_FORMAT } from "@/common/constant.ts";
import { filterOption } from "@/common/helper";
import DebounceSelect from "@/components/DebounceSelectComponent";
import { useApp } from "@/UseApp.tsx";
import { ReloadOutlined, SearchOutlined } from "@ant-design/icons";
import {
  useDistrictsQuery,
  useProvincesQuery,
  useWardsQuery,
} from "@location/service";
import { Button, DatePicker, Form, Input, Select } from "antd";
import { FormInstance } from "antd/lib/form/Form";
import dayjs from "dayjs";
import { Fragment, useCallback, useEffect, useState } from "react";
import { useProjectBoothsQuery } from "../configOutlet/service.ts";
import { useGetEmployeeLeaderMutation } from "../employee/service";
import { useProjectAgenciesQuery } from "../general/services";
import {
  useProjectChannelQuery,
  useProjectChannelsQuery,
} from "../outlet/service.ts";
import { RoleInterface } from "../role/interface";

import {
  AdvancedFilterFieldType,
  AdvancedFilterFormValueInterface,
} from "./interface";
import { useAdvancedFilterFiledsStore } from "./state";

import useFetchProjectEmployeeOptions from "@/hooks/useFetchProjectEmployeeOptions.ts";
import useFetchProjectOutletOptions from "@/hooks/useFetchProjectOutletOptions.ts";
import "./style.css";

export default function AdvancedFilter(props: {
  readonly form: FormInstance;
  readonly projectId: number;
  readonly onFinish: (values: AdvancedFilterFormValueInterface) => void;
  readonly roles?: RoleInterface[];
  readonly isHideFilter: boolean;
}) {
  const { loading } = useApp();

  const { form, projectId, onFinish, roles, isHideFilter } = props;
  const { RangePicker } = DatePicker;

  const [keyword, setKeyword] = useState<string>("");
  const [selectedProvinceId, setSelectedProvinceId] = useState<number | null>(
    null,
  );
  const [selectedDistrictId, setSelectedDistrictId] = useState<number | null>(
    null,
  );
  const [channelIdSelected, setChannelIdSelected] = useState<number | null>(
    null,
  );
  const [relativeDivWidth, setRelativeDivWidth] = useState(0);
  const { fields: advancedFilterFileds } = useAdvancedFilterFiledsStore();

  const provincesQuery = useProvincesQuery(!isHideFilter);
  const districtsQuery = useDistrictsQuery(selectedProvinceId);
  const wardsQuery = useWardsQuery(selectedProvinceId, selectedDistrictId);
  const projectAgenciesQuery = useProjectAgenciesQuery(
    projectId,
    !isHideFilter,
  );
  const projectBoothsQuery = useProjectBoothsQuery(
    projectId,
    {
      take: 50,
      skip: 0,
    },
    !isHideFilter,
  );
  const projectChannelsQuery = useProjectChannelsQuery(
    projectId,
    !isHideFilter,
  );
  const projectChannelQuery = useProjectChannelQuery(
    projectId,
    channelIdSelected,
  );

  const findEmployeeLeaderMutation = useGetEmployeeLeaderMutation(projectId);

  const fetchProjectOutletOptions = useFetchProjectOutletOptions({ projectId });
  const fetchProjectEmployeeOptions = useFetchProjectEmployeeOptions(projectId);

  useEffect(() => {
    const relativeDiv = document.querySelector(
      ".border-bottom.filter-zone",
    ) as HTMLElement;
    if (relativeDiv) {
      const relativeDivWidth = relativeDiv.offsetWidth;
      setRelativeDivWidth(relativeDivWidth);
    }
  }, [isHideFilter]);

  const handleSelectProvinceChange = useCallback(
    (value: number) => {
      setSelectedProvinceId(value);
      form.resetFields(["districtId", "wardId"]);
    },
    [form],
  );

  const handleSelectDistrictChange = useCallback(
    (value: number) => {
      setSelectedDistrictId(value);
      form.resetFields(["wardId"]);
    },
    [form],
  );

  const fetchLeaderOptions = useCallback(
    async (keyword?: string) => {
      const { entities } = await findEmployeeLeaderMutation.mutateAsync({
        keyword,
        take: 10,
        skip: 0,
      });

      return entities.map((employee) => ({
        label: employee.user.name,
        value: employee.id,
      }));
    },
    [findEmployeeLeaderMutation],
  );

  const items: { keyword: AdvancedFilterFieldType; item: JSX.Element }[] = [
    {
      keyword: "Agency phụ trách",
      item: (
        <Form.Item label="Agency phụ trách" name={"projectAgencyId"}>
          <Select
            allowClear
            showSearch
            optionFilterProp="children"
            filterOption={filterOption}
            placeholder="Tất cả"
            options={projectAgenciesQuery.data?.map((projectAgency) => ({
              label: projectAgency.agency.name,
              value: projectAgency.id,
            }))}
          />
        </Form.Item>
      ),
    },
    {
      keyword: "Role ghi nhận",
      item: (
        <Form.Item label="Role ghi nhận" name={"roleId"}>
          <Select
            allowClear
            placeholder="Tất cả"
            showSearch
            optionFilterProp="children"
            filterOption={filterOption}
            options={roles?.map((projectRole) => ({
              label: projectRole.name,
              value: projectRole.id,
            }))}
          />
        </Form.Item>
      ),
    },
    {
      keyword: "Ngày chấm công",
      item: (
        <Form.Item label="Ngày chấm công" name={"attendance"}>
          <RangePicker allowClear format={DATE_FORMAT} />
        </Form.Item>
      ),
    },
    {
      keyword: "Nhân viên ghi nhận",
      item: (
        <Form.Item label="Nhân viên ghi nhận" name={"employeeId"}>
          <DebounceSelect
            fetchOptions={fetchProjectEmployeeOptions}
            showSearch
            labelInValue={false}
            allowClear
            placeholder={"Nhân viên ghi nhận"}
          />
        </Form.Item>
      ),
    },
    {
      keyword: "Thông tin khách",
      item: (
        <Form.Item label="Thông tin khách" name={"customer"}>
          <Input allowClear placeholder={"Số điện thoại, Họ tên khách"} />
        </Form.Item>
      ),
    },
    {
      keyword: "Mã/ Tên outlet",
      item: (
        <Form.Item label="Mã/ Tên outlet" name={"outletId"}>
          <DebounceSelect
            fetchOptions={fetchProjectOutletOptions}
            showSearch
            labelInValue={false}
            allowClear
            placeholder={"Mã/ Tên outlet"}
          />
        </Form.Item>
      ),
    },
    {
      keyword: "Tỉnh/ TP",
      item: (
        <Form.Item label="Tỉnh/ TP" name={"provinceId"}>
          <Select
            allowClear
            placeholder="Tỉnh/ TP"
            showSearch
            optionFilterProp="children"
            filterOption={filterOption}
            options={provincesQuery.data?.map((province) => ({
              label: province.name,
              value: province.id,
            }))}
            popupMatchSelectWidth={false}
            onChange={handleSelectProvinceChange}
          />
        </Form.Item>
      ),
    },
    {
      keyword: "Quận/ Huyện",
      item: (
        <Form.Item label="Quận/ Huyện" name={"districtId"}>
          <Select
            allowClear
            placeholder="Quận/ Huyện"
            showSearch
            optionFilterProp="children"
            filterOption={filterOption}
            options={districtsQuery.data?.map((district) => ({
              label: district.name,
              value: district.id,
            }))}
            popupMatchSelectWidth={false}
            onChange={handleSelectDistrictChange}
          />
        </Form.Item>
      ),
    },
    {
      keyword: "Phường/ Xã",
      item: (
        <Form.Item label="Phường/ Xã" name={"wardId"}>
          <Select
            allowClear
            placeholder="Tất cả"
            showSearch
            optionFilterProp="children"
            filterOption={filterOption}
            options={wardsQuery.data?.map((ward) => ({
              label: ward.name,
              value: ward.id,
            }))}
            popupMatchSelectWidth={false}
          />
        </Form.Item>
      ),
    },
    {
      keyword: "Kênh",
      item: (
        <Form.Item label="Kênh" name={"channelId"}>
          <Select
            allowClear
            placeholder="Tất cả"
            showSearch
            optionFilterProp="children"
            filterOption={filterOption}
            options={projectChannelsQuery.data?.map((channel) => ({
              label: channel.name,
              value: channel.id,
            }))}
            popupMatchSelectWidth={false}
            onSelect={(id) => setChannelIdSelected(id)}
            onChange={() => {
              form.resetFields(["subChannelId"]);
              setChannelIdSelected(null);
            }}
          />
        </Form.Item>
      ),
    },
    {
      keyword: "Nhóm",
      item: (
        <Form.Item label="Nhóm" name={"subChannelId"}>
          <Select
            showSearch
            allowClear
            placeholder="Tất cả"
            optionFilterProp="children"
            filterOption={filterOption}
            popupMatchSelectWidth={false}
            options={projectChannelQuery?.data?.subChannels?.map(
              (subChannel) => ({
                label: subChannel.name,
                value: subChannel.id,
              }),
            )}
          />
        </Form.Item>
      ),
    },
    {
      keyword: "Loại booth",
      item: (
        <Form.Item label="Loại booth" name={"projectBoothId"}>
          <Select
            allowClear
            placeholder="Tất cả"
            showSearch
            optionFilterProp="children"
            filterOption={filterOption}
            options={projectBoothsQuery.data?.entities.map((projectBooth) => ({
              label: projectBooth.name,
              value: projectBooth.id,
            }))}
            popupMatchSelectWidth={false}
          />
        </Form.Item>
      ),
    },
    {
      keyword: "Trưởng nhóm quản lý",
      item: (
        <Form.Item label="Trưởng nhóm quản lý" name={"leaderId"}>
          <DebounceSelect
            popupMatchSelectWidth={false}
            placeholder="Trưởng nhóm quản lý"
            allowClear
            showSearch
            fetchOptions={fetchLeaderOptions}
            labelInValue={false}
          />
        </Form.Item>
      ),
    },
    {
      keyword: "Cách hiển thị data",
      item: (
        <Form.Item label="Cách hiển thị data" className="mb-0">
          <Select
            allowClear
            options={[
              {
                label: "Chỉ trả về outlet có ghi nhận dữ liệu",
                value: 1,
              },
              {
                label: "Trả về tất cả outlet",
                value: 2,
              },
            ]}
          />
        </Form.Item>
      ),
    },
  ];

  return (
    <>
      <div className="border-bottom filter-zone">
        <div className="flex justify-between items-center">
          <h4>Tìm nâng cao</h4>
          <Button
            type="link"
            icon={<ReloadOutlined />}
            onClick={() => {
              form.resetFields();
              onFinish(form.getFieldsValue());
            }}
          >
            Reset
          </Button>
        </div>
        <Input
          allowClear
          placeholder="Filter cần tìm"
          prefix={<SearchOutlined />}
          value={keyword}
          onChange={(value) => {
            setKeyword(value.currentTarget.value);
          }}
        />
      </div>

      <Form
        layout="vertical"
        className="mt-3 overflow-y-auto pb-14"
        form={form}
        onFinish={onFinish}
        initialValues={{ attendance: [dayjs(), dayjs()] }}
      >
        <div className="border-bottom filter-zone mb-0 pb-0 overflow-y-auto">
          {advancedFilterFileds
            .filter(
              (field) =>
                field
                  .toLocaleLowerCase()
                  .search(keyword.toLocaleLowerCase()) !== -1,
            )
            .map((field) => (
              <Fragment key={field}>
                {items.find((item) => item.keyword === field)?.item}
              </Fragment>
            ))}
        </div>
      </Form>

      <div
        className="filter-zone mt-3 text-center pb-3 bottom-0 fixed bg-[#F8FCFF]"
        style={{ width: `${relativeDivWidth}px` }}
      >
        <Button
          type="primary"
          htmlType="submit"
          style={{ width: "100%" }}
          onClick={() => onFinish(form.getFieldsValue())}
          loading={loading}
        >
          Tìm kiếm
        </Button>
      </div>
    </>
  );
}
