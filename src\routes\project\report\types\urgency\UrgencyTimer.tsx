import { formatSecondsToHMS } from "@/common/helper";
import { FeatureUrgencyInterface } from "@/routes/project/component/feature/config/types/urgency/interface";
import dayjs from "dayjs";
import { useStopwatch } from "react-timer-hook";

interface UrgencyTimerProps {
  startAt: string;
  endAt?: string;
  featureUrgency: FeatureUrgencyInterface;
}

const UrgencyTimer = ({
  startAt,
  endAt,
  featureUrgency,
}: UrgencyTimerProps) => {
  const { seconds, minutes, hours } = useStopwatch({
    autoStart: true,
    offsetTimestamp: dayjs()
      .add(dayjs().diff(dayjs(startAt), "seconds"), "seconds")
      .toDate(),
  });

  if (!endAt) {
    const duration = dayjs().diff(dayjs(startAt), "seconds");
    const isMoreThanMaxDuration = featureUrgency.maxDuration
      ? duration > featureUrgency.maxDuration * 60
      : false;

    return (
      <>
        <span
          className={
            isMoreThanMaxDuration
              ? "bg-red-600 text-white p-2 rounded-3xl"
              : "bg-[#FFF1E5] text-black p-2 rounded-3xl"
          }
        >
          <span>{String(hours).padStart(2, "0")}</span>:{""}
          <span>{String(minutes).padStart(2, "0")}</span>:{""}
          <span>{String(seconds).padStart(2, "0")}</span>
        </span>
        {isMoreThanMaxDuration && (
          <span className="text-red-600 ml-3">Quá giờ</span>
        )}
      </>
    );
  }

  const duration = dayjs(endAt).diff(dayjs(startAt), "seconds");
  const isMoreThanMaxDuration = featureUrgency.maxDuration
    ? duration > featureUrgency.maxDuration * 60
    : false;

  return (
    <>
      {!!endAt && <span>{formatSecondsToHMS(duration)}</span>}

      {isMoreThanMaxDuration && (
        <span className="text-red-600 ml-3">Quá giờ</span>
      )}
    </>
  );
};

export default UrgencyTimer;
