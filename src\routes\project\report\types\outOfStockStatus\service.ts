import { AbstractFilterInterface } from "@/common/interface";
import { AppContextInterface } from "@/interface";
import {
  OosMergedProductInterface,
  OosProductInterface,
  OosZoneInterface,
} from "@/routes/project/component/feature/config/types/outOfStockStatus/interface";
import { ProjectOutletInterface } from "@/routes/project/outlet/interface";
import { useApp } from "@/UseApp";
import { useMutation, useQuery } from "@tanstack/react-query";
import { RecordOosStatusInterface } from "./interface";

export const getReportOOSStatuses = (
  axiosGet: AppContextInterface["axiosGet"],
  projectId: number,
  componentFeatureId: number,
  filter?: object & AbstractFilterInterface,
) =>
  axiosGet<{ entities: RecordOosStatusInterface[]; count: number }, unknown>(
    `/projects/${projectId}/reports/features/${componentFeatureId}/oos-statuses`,
    filter,
  );

export const useReportOOSStatusesQuery = (
  projectId: number,
  componentFeatureId: number,
  filter?: object & AbstractFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["reportOOSStatuses", projectId, componentFeatureId, filter],
    queryFn: () =>
      getReportOOSStatuses(axiosGet, projectId, componentFeatureId, filter),
  });
};

export const useGetReportOOSStatusesMutation = (
  projectId: number,
  componentFeatureId: number,
) => {
  const { axiosGet } = useApp();

  return useMutation({
    mutationKey: ["reportOOSStatuses", projectId, componentFeatureId],
    mutationFn: (filter?: object & AbstractFilterInterface) =>
      getReportOOSStatuses(axiosGet, projectId, componentFeatureId, filter),
  });
};

export const getReportOOSStatusesHighlights = (
  axiosGet: AppContextInterface["axiosGet"],
  projectId: number,
  componentFeatureId: number,
  filter?: object & AbstractFilterInterface,
) =>
  axiosGet<{ entities: ProjectOutletInterface[]; count: number }, unknown>(
    `/projects/${projectId}/reports/features/${componentFeatureId}/oos-statuses/highlights`,
    filter,
  );

export const useReportOOSStatusesHighlightsQuery = (
  projectId: number,
  componentFeatureId: number,
  filter?: object & AbstractFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: [
      "reportOOSStatusesHighlights",
      projectId,
      componentFeatureId,
      filter,
    ],
    queryFn: () =>
      getReportOOSStatusesHighlights(
        axiosGet,
        projectId,
        componentFeatureId,
        filter,
      ),
  });
};

export const useGetReportOOSStatusesHighlightsMutation = (
  projectId: number,
  componentFeatureId: number,
) => {
  const { axiosGet } = useApp();

  return useMutation({
    mutationKey: ["reportOOSStatusesHighlights", projectId, componentFeatureId],
    mutationFn: (filter?: object & AbstractFilterInterface) =>
      getReportOOSStatusesHighlights(
        axiosGet,
        projectId,
        componentFeatureId,
        filter,
      ),
  });
};

export const getReportOOSStatusesProducts = (
  axiosGet: AppContextInterface["axiosGet"],
  projectId: number,
  componentFeatureId: number,
  filter?: object & AbstractFilterInterface,
) =>
  axiosGet<{ entities: OosProductInterface[]; count: number }, unknown>(
    `/projects/${projectId}/reports/features/${componentFeatureId}/oos-statuses/products`,
    filter,
  );

export const useReportOOSStatusesProductsQuery = (
  projectId: number,
  componentFeatureId: number,
  filter?: object & AbstractFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: [
      "reportOOSStatusesProducts",
      projectId,
      componentFeatureId,
      filter,
    ],
    queryFn: () =>
      getReportOOSStatusesProducts(
        axiosGet,
        projectId,
        componentFeatureId,
        filter,
      ),
  });
};

export const getReportOOSStatusesMergedProducts = (
  axiosGet: AppContextInterface["axiosGet"],
  projectId: number,
  componentFeatureId: number,
  filter?: object & AbstractFilterInterface,
) =>
  axiosGet<{ entities: OosMergedProductInterface[]; count: number }, unknown>(
    `/projects/${projectId}/reports/features/${componentFeatureId}/oos-statuses/merged-products`,
    filter,
  );

export const useReportOOSStatusesMergedProductsQuery = (
  projectId: number,
  componentFeatureId: number,
  filter?: object & AbstractFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: [
      "reportOOSStatusesMergedProducts",
      projectId,
      componentFeatureId,
      filter,
    ],
    queryFn: () =>
      getReportOOSStatusesMergedProducts(
        axiosGet,
        projectId,
        componentFeatureId,
        filter,
      ),
  });
};

export const getReportOOSStatusesZones = (
  axiosGet: AppContextInterface["axiosGet"],
  projectId: number,
  componentFeatureId: number,
  filter?: object & AbstractFilterInterface,
) =>
  axiosGet<{ entities: OosZoneInterface[]; count: number }, unknown>(
    `/projects/${projectId}/reports/features/${componentFeatureId}/oos-statuses/zones`,
    filter,
  );

export const useReportOOSStatusesZonesQuery = (
  projectId: number,
  componentFeatureId: number,
  filter?: object & AbstractFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["reportOOSStatusesZones", projectId, componentFeatureId, filter],
    queryFn: () =>
      getReportOOSStatusesZones(
        axiosGet,
        projectId,
        componentFeatureId,
        filter,
      ),
  });
};

export const useCreateReportOOSStatusHighlightMutation = (
  projectId: number,
  outletId: number,
) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createReportOOSStatusHighlight", projectId, outletId],
    mutationFn: (data: {
      projectFeatureId: number;
      featureOosZoneId: number;
      values: {
        featureOosProductId: number;
        value: number | null;
      }[];
    }) => axiosPost(`/projects/${projectId}/outlets/${outletId}/stocks`, data),
  });
};

export const useOutletStockProductsQuery = (
  projectId: number,
  outletId: number,
  filter?: { projectFeatureId: number } & AbstractFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["outletStockProducts", projectId, outletId, filter],
    queryFn: () =>
      axiosGet<{ entities: OosProductInterface[]; count: number }, unknown>(
        `/projects/${projectId}/outlets/${outletId}/stock-products`,
        filter,
      ),
  });
};
