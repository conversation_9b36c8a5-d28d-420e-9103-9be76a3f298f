import {
  CURD,
  DEFAULT_CURRENT_PAGE,
  DEFAULT_PAGE_SIZE,
} from "@/common/constant";
import { formatMoney } from "@/common/helper";
import FilterComponent from "@/components/FilterComponent";
import ProductItemCell from "@/components/ProductItemCell";
import TableActionCell from "@/components/TableActionCell";
import { useApp } from "@/UseApp";
import { DeleteOutlined } from "@ant-design/icons";
import { ProjectProductInterface } from "@project/product/interface";
import { UseMutationResult, UseQueryResult } from "@tanstack/react-query";
import {
  Form,
  InputNumber,
  Modal,
  Switch,
  Table,
  TableColumnsType,
} from "antd";
import _ from "lodash";
import { useCallback, useMemo, useState } from "react";
import { useOutletContext, useParams } from "react-router-dom";
import {
  OrderInterface,
  OrderProductInterface,
  StepLockEnum,
} from "../../../interface.ts";
import { useUpdateStepStatusMutation } from "../../../service.ts";
import StepLockPage from "../../../StepLockPage";
import {
  useDeleteOrderProductMutation,
  useOrderProductsQuery,
  useUpdateOrderProductPriceMutation,
} from "../service.ts";
import ModalConfigPurchaseAvailables from "./ModalConfigPurchaseAvailables";

export function PriceEditInput(props: {
  readonly orderProductId: number;
  readonly price: number;
  readonly cb: () => void;
  readonly projectProduct: ProjectProductInterface;
  readonly updatePriceMutation: UseMutationResult<
    unknown,
    Error,
    { id: number; price: number },
    unknown
  >;
}): JSX.Element {
  const { price, updatePriceMutation, cb, projectProduct, orderProductId } =
    props;

  const { showNotification } = useApp();

  const [value, setValue] = useState(price);

  const onChange = useCallback((newValue: number | null) => {
    if (newValue === null) return;
    setValue(newValue);
  }, []);

  const onSubmit = useCallback(async () => {
    if (value !== price) {
      try {
        await updatePriceMutation.mutateAsync({
          id: orderProductId,
          price: value,
        });
        cb();
        showNotification({
          type: "success",
          message: `Thay đổi giá thực tế của sản phẩm ${projectProduct.product.name} (${price} => ${value}) thành công`,
        });
      } catch (e) {
        console.error(e);

        showNotification({
          type: "error",
          message: `Thay đổi giá thực tế của sản phẩm ${projectProduct.product.name} (${price} => ${value}) thất bại`,
        });
      }
    }
  }, [
    cb,
    orderProductId,
    price,
    projectProduct?.product?.name,
    showNotification,
    updatePriceMutation,
    value,
  ]);

  return (
    <InputNumber
      style={{ textAlign: "right" }}
      value={value}
      formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",")}
      onChange={onChange}
      onBlur={onSubmit}
      controls={false}
      step={0}
      onPressEnter={onSubmit}
    />
  );
}

export default function ConfigPurchasePage() {
  const [orderLatestQuery]: [UseQueryResult<OrderInterface, unknown>] =
    useOutletContext();
  const { showNotification } = useApp();

  const projectId = parseInt(useParams().id ?? "0");
  const componentFeatureId = parseInt(useParams().componentFeatureId ?? "0");

  const [modal, contextHolder] = Modal.useModal();
  const [searchForm] = Form.useForm();
  const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [filter, setFilter] = useState({});
  const [isOpen, setIsOpen] = useState(false);

  const orderProductsQuery = useOrderProductsQuery(componentFeatureId, {
    ...filter,
    take: pageSize,
    skip: (currentPage - 1) * pageSize,
  });

  const updateOrderProductPriceMutation =
    useUpdateOrderProductPriceMutation(componentFeatureId);
  const deleteOrderProductMutation =
    useDeleteOrderProductMutation(componentFeatureId);
  const updateStepStatusMutation =
    useUpdateStepStatusMutation(componentFeatureId);

  const searchHandler = useCallback(() => {
    const values = searchForm.getFieldsValue();

    setCurrentPage(DEFAULT_CURRENT_PAGE);
    if (_.isEqual(values, filter)) {
      orderProductsQuery.refetch();
    }
    setFilter(values);
  }, [filter, orderProductsQuery, searchForm]);

  const pagination = useMemo(() => {
    return {
      current: currentPage,
      total: orderProductsQuery.data?.count ?? 0,
      pageSize: pageSize,
      onChange: (page: number) => {
        setCurrentPage(page);
      },
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: (_current: number, size: number) => {
        setPageSize(size);
        setCurrentPage(DEFAULT_CURRENT_PAGE);
      },
    };
  }, [currentPage, orderProductsQuery.data?.count, pageSize]);

  const columns: TableColumnsType<OrderProductInterface> = [
    {
      title: "Tên sản phẩm",
      dataIndex: "name",
      key: "name",
      render: (_, record) => {
        return (
          <ProductItemCell
            variants={record?.projectProduct.product.image?.variants ?? []}
            name={record.projectProduct.product.name}
          />
        );
      },
      className: "min-w-[50px]",
    },
    {
      title: "Mã sản phẩm",
      dataIndex: "projectProduct",
      key: "product.code",
      render: (projectProduct: ProjectProductInterface) =>
        projectProduct?.product.code,
      className: "min-w-[50px]",
    },
    {
      title: "Barcode",
      dataIndex: "projectProduct",
      key: "product.code",
      render: (projectProduct: ProjectProductInterface) =>
        projectProduct?.productPackaging?.barcode,
      className: "min-w-[50px]",
    },
    {
      title: "Nhãn hàng",
      dataIndex: "projectProduct",
      key: "product.brand",
      render: (projectProduct: ProjectProductInterface) =>
        projectProduct?.product.brand.name,
      className: "min-w-[50px]",
    },
    {
      title: "Quy cách",
      dataIndex: "projectProduct",
      key: "product.code",
      render: (projectProduct: ProjectProductInterface) =>
        projectProduct?.productPackaging?.unit.name,
      className: "min-w-[50px]",
    },
    {
      align: "right",
      title: "Giá chuẩn (VNĐ)",
      dataIndex: "projectProduct",
      key: "productPackaging.price",
      render: (projectProduct: ProjectProductInterface) =>
        formatMoney(projectProduct?.productPackaging?.price ?? 0),
      fixed: "right",
      className: "min-w-[100px]",
    },

    {
      title: "Giá thực tế (VNĐ)",
      dataIndex: "price",
      key: "projectProductPrices",
      align: "right",
      render: (price: number, record: OrderProductInterface) => {
        return (
          <PriceEditInput
            price={price}
            cb={() => orderProductsQuery.refetch()}
            updatePriceMutation={updateOrderProductPriceMutation}
            orderProductId={record.id}
            projectProduct={record.projectProduct}
          />
        );
      },
      className: "min-w-[50px]",
      fixed: "right",
    },
    {
      render: (_, record) => {
        return (
          <TableActionCell
            actions={[
              {
                key: CURD.DELETE,
                action: (record: OrderProductInterface) => {
                  const name = record.projectProduct.product.name;

                  modal.confirm({
                    title: `Xóa sản phẩm: ${name}`,
                    content: `Bạn có chắc chắn muốn xóa sản phẩm ${name}?`,
                    okText: "Xóa",
                    cancelText: "Hủy",
                    onOk: async () => {
                      try {
                        await deleteOrderProductMutation.mutateAsync(record.id);

                        showNotification({
                          type: "success",
                          message: `Xóa sản phẩm ${name} thành công`,
                        });

                        await orderProductsQuery.refetch();
                      } catch (error) {
                        console.error(error);

                        showNotification({
                          type: "error",
                          message: `Xóa sản phẩm ${name} thất bại`,
                        });
                      }
                    },
                  });
                },
              },
            ]}
            items={[
              {
                key: CURD.DELETE,
                label: "Xóa khỏi chức năng",
                icon: <DeleteOutlined />,
              },
            ]}
            record={record}
          />
        );
      },
      fixed: "right",
    },
  ];

  const onAdd = useCallback(() => {
    setIsOpen(true);
  }, []);

  const onRequiredPurchaseSwitchChange = useCallback(
    (value: boolean) => {
      updateStepStatusMutation
        .mutateAsync({
          isPurchaseRequired: !value,
        })
        .then(() => {
          orderLatestQuery.refetch();
        });
    },
    [orderLatestQuery, updateStepStatusMutation],
  );

  if (!orderLatestQuery.data?.hasPurchase) {
    return (
      <StepLockPage
        title="Chức năng bán hàng"
        description="Chức năng cho phép thực hiện việc ghi nhận những sản phẩm mà khách đã mua theo từng đơn hàng"
        type={StepLockEnum.Purchase}
        orderLatestQuery={orderLatestQuery}
        locked={!orderLatestQuery.data?.hasPurchase}
      />
    );
  }

  return (
    <>
      <div className="mb-6">
        <span className="mr-4">Cho phép skip trên app</span>
        <Switch
          value={!orderLatestQuery.data.isPurchaseRequired}
          loading={updateStepStatusMutation.isPending}
          onChange={onRequiredPurchaseSwitchChange}
        />
      </div>
      <FilterComponent
        searchHandler={searchHandler}
        searchForm={searchForm}
        filterOptions={[
          {
            label: "Tất cả",
            value: "all",
          },
          {
            label: "Tên sản phẩm",
            value: "product.name",
          },
          {
            label: "Mã sản phẩm",
            value: "product.code",
          },
          {
            label: "Barcode",
            value: "product.barcode",
          },
          {
            label: "Nhãn hàng",
            value: "brand.name",
          },
          {
            label: "Quy cách ",
            value: "unit.name",
          },
        ]}
        className="mb-6"
        btnAddText={"Thêm quy cách của sản phẩm"}
        handleAddButtonClick={onAdd}
      />

      <Table
        dataSource={orderProductsQuery?.data?.entities}
        columns={columns}
        rowKey={(record) => record.id}
        scroll={{
          x: "max-content",
          y: pagination.total ? "80vh" : undefined,
        }}
        pagination={pagination}
        loading={orderProductsQuery.isFetching}
      />

      <ModalConfigPurchaseAvailables
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        projectId={projectId}
        componentFeatureId={componentFeatureId}
        cb={async () => {
          await orderProductsQuery.refetch();
        }}
      />

      {contextHolder}
    </>
  );
}
