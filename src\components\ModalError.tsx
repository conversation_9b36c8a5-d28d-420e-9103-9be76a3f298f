import { CloseOutlined, ExclamationCircleFilled } from "@ant-design/icons";
import { Button, Modal } from "antd";

const ModalError = ({
  isOpen,
  onCancel,
  title,
  content,
}: {
  title: string;
  isOpen: boolean;
  onCancel: () => void;
  content: React.ReactNode;
}) => {
  return (
    <Modal
      open={isOpen}
      footer={null}
      closeIcon={null}
      onCancel={onCancel}
      styles={{ content: { padding: 0 } }}
    >
      <div className="pl-10 pr-10 pt-3 pb-[29px]">
        <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
          <h2 className="text-2xl font-semibold text-primary">
            <ExclamationCircleFilled /> {title}
          </h2>
          <div className="pt-5">
            <Button
              type="text"
              onClick={onCancel}
              size="large"
              icon={<CloseOutlined />}
              className={"text-[#393939]"}
            />
          </div>
        </div>

        {content}
      </div>

      <div className="flex justify-end gap-4 py-4 rounded max-md:max-w-full max-md:flex-wrap pl-10 bg-[#F7F8FA] pr-10 pb-4">
        <Button htmlType="button" onClick={onCancel}>
          Đóng
        </Button>
      </div>
    </Modal>
  );
};

export default ModalError;
