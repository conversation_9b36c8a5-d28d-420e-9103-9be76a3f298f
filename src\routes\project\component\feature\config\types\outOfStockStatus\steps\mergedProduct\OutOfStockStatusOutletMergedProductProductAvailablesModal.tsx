import { filterOption } from "@/common/helper.ts";
import CustomModal from "@/components/CustomModal";
import ProductItemCell from "@/components/ProductItemCell.tsx";
import { useUrlFiltersWithQuery } from "@/hooks/useUrlFiltersWithQuery.ts";
import {
  ProductInterface,
  ProductPackagingInterface,
} from "@/routes/product/interface";
import { useProjectBrandsQuery } from "@/routes/project/general/services";
import { useApp } from "@/UseApp.tsx";
import {
  OosMergedProductInterface,
  OosMergedProductItemAvailableInterface,
} from "@project/component/feature/config/types/outOfStockStatus/interface.ts";
import {
  useAddOosMergedProductMutation,
  useOosMergedProductProductAvailablesQuery,
} from "@project/component/feature/config/types/outOfStockStatus/steps/mergedProduct/service.ts";
import { Button, Form, Input, Select, Space, Table } from "antd";
import React, { useCallback, useEffect, useMemo, useState } from "react";

interface OutOfStockStatusOutletMergedProductProductAvailablesModalProps {
  cb: () => void;
  oosMergedProduct: OosMergedProductInterface | null;
  componentFeatureId: number;
  projectId: number;
  onClose: () => void;
}

const OutOfStockStatusOutletMergedProductProductAvailablesModal = ({
  cb,
  oosMergedProduct,
  componentFeatureId,
  projectId,
  onClose,
}: OutOfStockStatusOutletMergedProductProductAvailablesModalProps) => {
  const { showNotification } = useApp();

  const [searchForm] = Form.useForm();
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);

  const {
    query: { data, refetch, isFetching },
    handleSearch,
    getPaginationProps,
  } = useUrlFiltersWithQuery<OosMergedProductItemAvailableInterface>({
    formInstance: searchForm,
    useQueryHook: useOosMergedProductProductAvailablesQuery,
    queryParams: [componentFeatureId, oosMergedProduct?.id ?? 0],
    options: {
      urlSync: {
        enabled: false,
      },
    },
  });

  const projectBrandsQuery = useProjectBrandsQuery(projectId);

  const addOosMergedProductMutation = useAddOosMergedProductMutation(
    componentFeatureId,
    oosMergedProduct?.id ?? 0,
  );

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys: selectedKeys,
    onChange: onSelectChange,
    getCheckboxProps: (record: OosMergedProductItemAvailableInterface) => {
      return {
        disabled: !record.isAvailable || !record.isActive,
      };
    },
  };

  const newSelectedKeys = useMemo(() => {
    return (
      data?.entities
        .filter(
          (oosProduct) =>
            selectedKeys.includes(oosProduct.id) && oosProduct.isAvailable,
        )
        .map((item) => item.id) ?? []
    );
  }, [data?.entities, selectedKeys]);

  const onSubmit = useCallback(async () => {
    if (newSelectedKeys.length === 0) {
      return;
    }

    await addOosMergedProductMutation.mutateAsync({
      projectProductIds: newSelectedKeys,
    });
    showNotification({
      type: "success",
      message: "Thêm sản phẩm vào nhóm thành công",
    });

    setSelectedKeys([]);
    refetch();
    cb();
  }, [
    addOosMergedProductMutation,
    cb,
    newSelectedKeys,
    refetch,
    showNotification,
  ]);

  useEffect(() => {
    setSelectedKeys(
      data?.entities
        .filter((item) => !item.isAvailable)
        .map((item) => item.id) ?? [],
    );
  }, [data?.entities, oosMergedProduct?.id]);

  const pagination = useMemo(() => getPaginationProps(), [getPaginationProps]);

  const content = (
    <>
      <p>
        Sản phẩm gộp:{" "}
        <span className={"text-blue font-semibold"}>
          {oosMergedProduct?.productName}
        </span>
      </p>

      <Form layout="vertical" onFinish={handleSearch} form={searchForm}>
        <Space>
          <Form.Item label="Nhãn hàng" name={"projectBrandId"}>
            <Select
              style={{ width: "200px" }}
              placeholder={"Tất cả"}
              allowClear
              options={projectBrandsQuery.data?.map((projectBrand) => ({
                value: projectBrand.id,
                label: projectBrand.brand.name,
              }))}
              showSearch
              filterOption={filterOption}
            />
          </Form.Item>
          <Form.Item label="Tên sản phẩm" name={"keyword"}>
            <Input placeholder="Nhập tên hoặc mã" allowClear />
          </Form.Item>
          <Form.Item label=" ">
            <Button htmlType="submit">Tìm kiếm</Button>
          </Form.Item>
        </Space>
      </Form>

      <Table
        scroll={{
          y: pagination.total ? "70vh" : undefined,
          x: "max-content",
        }}
        dataSource={data?.entities ?? []}
        loading={isFetching}
        columns={[
          {
            title: "Tên sản phẩm",
            className: "min-w-[100px]",
            render: (_, record: OosMergedProductItemAvailableInterface) => {
              const { isAvailable, product } = record ?? {};

              const status = !isAvailable
                ? {
                    text: "Đã thêm vào nhóm",
                    color: "#008916",
                    bg: "#E5F5E7",
                  }
                : undefined;

              return (
                <ProductItemCell
                  name={product.name}
                  variants={product.image?.variants ?? []}
                  status={status?.text}
                  statusBg={status?.bg}
                  statusColor={status?.color}
                />
              );
            },
          },
          {
            title: "Mã sản phẩm",
            className: "min-w-[100px]",
            dataIndex: "product",
            render: (product: ProductInterface) => {
              return product.code;
            },
          },
          {
            title: "Nhãn hàng",
            dataIndex: "product",
            className: "min-w-[100px]",
            render: (product: ProductInterface) => {
              return product.brand?.name;
            },
          },
          {
            title: "Quy cách",
            className: "min-w-[100px]",
            dataIndex: "productPackaging",
            render: (productPackaging: ProductPackagingInterface) => {
              return productPackaging.unit.name;
            },
          },
        ]}
        rowSelection={rowSelection}
        rowKey={"id"}
        pagination={pagination}
      />
    </>
  );

  return (
    <CustomModal
      content={content}
      title={"Thêm sản phẩm vào sản phẩm gộp"}
      isOpen={true}
      onCancel={onClose}
      onConfirm={onSubmit}
      confirmText={`Thêm ${newSelectedKeys.length} sản phẩm`}
      confirmLoading={addOosMergedProductMutation.isPending}
      confirmDisable={!newSelectedKeys.length}
      width={1000}
    />
  );
};

export default OutOfStockStatusOutletMergedProductProductAvailablesModal;
