import { ProjectInterface } from "@project/interface";
import { <PERSON><PERSON>, Table } from "antd";
import { useCallback, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import { ItemKpiTypeEnum } from "../../../interface";
import KPIEditInput from "../../../KPIEditInput";
import KPIModal from "../../../KPIModal";
import { useProjectKPIQuery } from "../../../service";

const ProjectProjectKPIPage = () => {
  const projectId = parseInt(useParams().id ?? "0");

  const [itemKpiType, setItemKpiType] = useState<ItemKpiTypeEnum | undefined>();
  const [modalTitle, setModalTitle] = useState<string | undefined>(undefined);

  const projectKPIQuery = useProjectKPIQuery(projectId);

  const project = useMemo(
    () => projectKPIQuery?.data?.[0]?.project,
    [projectKPIQuery.data],
  );

  const items = useMemo(
    () => projectKPIQuery?.data?.[0]?.projectKpiItems ?? [],
    [projectKPIQuery?.data],
  );

  const cb = useCallback(() => {}, []);
  const cancel = useCallback(() => {
    setItemKpiType(undefined);
    setModalTitle(undefined);
    projectKPIQuery.refetch();
  }, [projectKPIQuery]);

  return (
    <>
      <Table
        rowKey={(o) => o.project?.id ?? 0}
        dataSource={projectKPIQuery.data ?? []}
        columns={[
          {
            title: "ID",
            dataIndex: "project",
            render: ({ id }: { id: number }) => id,
          },
          {
            title: "Dự án",
            dataIndex: "project",
            render: ({ name }: { name: string }) => name,
          },
          {
            title: "KPI doanh số",
            dataIndex: "salesRevenue",
            render: (
              salesRevenue: string,
              { project }: { project?: ProjectInterface },
            ) => (
              <KPIEditInput
                kpi={salesRevenue ? Number(salesRevenue) : null}
                cb={cb}
                projectId={project?.id ?? 0}
                type={"salesRevenue"}
              />
            ),
            align: "right",
          },
          {
            title: "KPI đơn hàng",
            dataIndex: "order",
            render: (
              order: string,
              { project }: { project?: ProjectInterface },
            ) => (
              <KPIEditInput
                kpi={order ? Number(order) : null}
                cb={cb}
                projectId={project?.id ?? 0}
                type={"order"}
              />
            ),
            align: "right",
          },
          {
            title: "KPI sampling (hit)",
            dataIndex: "hit",
            render: (
              hit: string,
              { project }: { project?: ProjectInterface },
            ) => (
              <KPIEditInput
                kpi={hit ? Number(hit) : null}
                cb={cb}
                projectId={project?.id ?? 0}
                type={"hit"}
              />
            ),
            align: "right",
          },
          {
            title: "KPI session",
            dataIndex: "session",
            render: (
              session: string,
              { project }: { project?: ProjectInterface },
            ) => (
              <KPIEditInput
                kpi={session ? Number(session) : null}
                cb={cb}
                projectId={project?.id ?? 0}
                type={"session"}
              />
            ),
            align: "right",
          },
          {
            title: "KPI sampling (dry)",
            align: "right",
            render: () => (
              <Button
                className="text-blue"
                type="link"
                onClick={() => {
                  setItemKpiType(ItemKpiTypeEnum.SAMPLING);
                  setModalTitle("Cài đặt KPI sampling (dry)");
                }}
              >
                Cài đặt
              </Button>
            ),
          },
          {
            title: "KPI quà thường",
            align: "right",
            render: () => (
              <Button
                className="text-blue"
                type="link"
                onClick={() => {
                  setItemKpiType(ItemKpiTypeEnum.GIFT);
                  setModalTitle("Cài đặt KPI quà thường");
                }}
              >
                Cài đặt
              </Button>
            ),
          },
          {
            title: "KPI quà game",
            align: "right",
            render: () => (
              <Button
                className="text-blue"
                type="link"
                onClick={() => {
                  setItemKpiType(ItemKpiTypeEnum.GAME);
                  setModalTitle("Cài đặt KPI quà game");
                }}
              >
                Cài đặt
              </Button>
            ),
          },
        ]}
      />

      {itemKpiType && modalTitle && (
        <KPIModal
          isOpen={true}
          title={
            <>
              Dự án đang chọn:{" "}
              <span className="text-blue">
                {project?.id} - {project?.name}
              </span>
            </>
          }
          itemKpiType={itemKpiType}
          cancel={cancel}
          projectId={projectId}
          modalTitle={modalTitle}
          items={items}
          cb={cb}
        />
      )}
    </>
  );
};

export default ProjectProjectKPIPage;
