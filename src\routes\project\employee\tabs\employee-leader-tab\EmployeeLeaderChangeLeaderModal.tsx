import { DEFAULT_PAGE_SIZE } from "@/common/constant";
import CustomModal from "@/components/CustomModal";
import DebounceSelect from "@/components/DebounceSelectComponent";
import UserOptionComponent from "@/components/UserOptionComponent";
import { ProjectOutletInterface } from "@/routes/project/outlet/interface";
import { useGetProjectEmployeeLeaderOutletsMutation } from "@/routes/project/outlet/service";
import { RoleInterface } from "@/routes/project/role/interface";
import { UserInterface } from "@/routes/user/interface";
import { CloseCircleOutlined } from "@ant-design/icons";
import type { RadioChangeEvent } from "antd";
import { Col, Radio, Row, Table, Tag } from "antd";
import { TableProps } from "antd/lib";
import { useCallback, useEffect, useState } from "react";
import { ProjectEmployeeUserInterface } from "../../interface";
import {
  useGetAgencyEmployeeLeadersMutation,
  useGetLeaderEmployeeMembersMutation,
} from "../../service";
import { EmployeeLeaderChangeLeaderAllOrSelectedEnum } from "./interface";

interface EmployeeLeaderChangeLeaderModalProps {
  isOpen: boolean;
  projectId: number;
  cancelCb: () => void;
  leader: ProjectEmployeeUserInterface | undefined;
}

const EmployeeLeaderChangeLeaderModal = ({
  isOpen,
  cancelCb,
  leader,
  projectId,
}: EmployeeLeaderChangeLeaderModalProps) => {
  const [options, setOptions] = useState<ProjectEmployeeUserInterface[]>([]);
  const [selectedValue, setSelectedValue] = useState<
    string | null | ProjectEmployeeUserInterface | undefined
  >(null);
  const [allOrSelectedEmployees, setAllOrSelectedEmployees] =
    useState<EmployeeLeaderChangeLeaderAllOrSelectedEnum>(
      EmployeeLeaderChangeLeaderAllOrSelectedEnum.ALL,
    );
  const [allOrSelectedOutlets, setAllOrSelectedOutlets] =
    useState<EmployeeLeaderChangeLeaderAllOrSelectedEnum>(
      EmployeeLeaderChangeLeaderAllOrSelectedEnum.ALL,
    );
  const [outlets, setOutlets] = useState<ProjectOutletInterface[]>([]);
  const [hasMoreOutlets, setHasMoreOutlets] = useState(true);
  const [employees, setEmployees] = useState<ProjectEmployeeUserInterface[]>(
    [],
  );
  const [hasMoreEmployees, setHasMoreEmployees] = useState(true);

  const getAgencyEmployeeLeadersMutation =
    useGetAgencyEmployeeLeadersMutation(projectId);
  const getProjectEmployeeLeaderOutletsMutation =
    useGetProjectEmployeeLeaderOutletsMutation(projectId, leader?.id);
  const getLeaderEmployeeMembersMutation = useGetLeaderEmployeeMembersMutation(
    projectId,
    leader?.id,
  );

  const fetchLeaderOptions = useCallback(
    async (keyword?: string) => {
      if (keyword?.length === 0) {
        return [];
      }

      const projectAgencyId = leader?.projectAgencyId;
      if (!projectAgencyId) {
        return [];
      }

      const leadersResponse =
        await getAgencyEmployeeLeadersMutation.mutateAsync({
          keyword,
          take: 10,
          projectAgencyId,
        });

      setOptions(leadersResponse.entities);

      return leadersResponse.entities.map((item) => ({
        value: item.id,
        label: item.user.username,
        user: item.user,
        isAvailable: true,
      }));
    },
    [getAgencyEmployeeLeadersMutation, leader?.projectAgencyId],
  );

  const loadMoreOutlets = useCallback(async () => {
    if (getProjectEmployeeLeaderOutletsMutation.isPending || !hasMoreOutlets)
      return;

    const result = await getProjectEmployeeLeaderOutletsMutation.mutateAsync({
      take: DEFAULT_PAGE_SIZE,
      skip: outlets.length,
    });

    setOutlets([...outlets, ...result.entities]);
    setHasMoreOutlets(result.count > outlets.length);
  }, [getProjectEmployeeLeaderOutletsMutation, hasMoreOutlets, outlets]);

  const loadMoreEmployees = useCallback(async () => {
    if (getLeaderEmployeeMembersMutation.isPending || !hasMoreEmployees) return;

    const result = await getLeaderEmployeeMembersMutation.mutateAsync({
      take: DEFAULT_PAGE_SIZE,
      skip: employees.length,
    });

    setEmployees([...employees, ...result.entities]);
    setHasMoreEmployees(result.count > employees.length);
  }, [employees, getLeaderEmployeeMembersMutation, hasMoreEmployees]);

  const rowEmployeeSelection: TableProps<ProjectEmployeeUserInterface>["rowSelection"] =
    {
      onChange: (
        selectedRowKeys: React.Key[],
        selectedRows: ProjectEmployeeUserInterface[],
      ) => {
        console.log(
          `selectedRowKeys: ${selectedRowKeys}`,
          "selectedRows: ",
          selectedRows,
        );
      },
    };

  const rowOutletSelection: TableProps<ProjectOutletInterface>["rowSelection"] =
    {
      onChange: (
        selectedRowKeys: React.Key[],
        selectedRows: ProjectOutletInterface[],
      ) => {
        console.log(
          `selectedRowKeys: ${selectedRowKeys}`,
          "selectedRows: ",
          selectedRows,
        );
      },
    };

  const content = (
    <>
      <p className="mb-1">Trưởng nhóm cũ</p>
      <Tag className="w-full justify-between flex pt-2 pb-2 pl-3 pr-3">
        <UserOptionComponent
          avatarUrl={leader?.user.picture}
          name={leader?.user.name}
          phone={leader?.user.phone}
          email={leader?.user.email}
        />
      </Tag>

      <p className="mt-5 mb-1">Trưởng nhóm mới</p>

      {selectedValue ? (
        <Tag
          closable
          onClose={() => {
            setSelectedValue(null);
          }}
          className="w-full justify-between flex pt-2 pb-2 pl-3 pr-3"
          closeIcon={<CloseCircleOutlined style={{ fontSize: 14 }} />}
          style={{
            fontSize: 14,
            backgroundColor: typeof selectedValue === "string" ? "" : "#F0F8FF",
            borderColor: typeof selectedValue === "string" ? "" : "#C4D6FF",
          }}
        >
          {typeof selectedValue === "string" ? (
            <p>{selectedValue}</p>
          ) : (
            <UserOptionComponent
              avatarUrl={selectedValue?.user.picture}
              name={selectedValue?.user.name}
              phone={selectedValue?.user.phone}
              email={selectedValue?.user.email}
            />
          )}
        </Tag>
      ) : (
        <DebounceSelect
          allowClear
          autoClearSearchValue
          mode="multiple"
          fetchOptions={fetchLeaderOptions}
          style={{ width: "100%" }}
          optionRender={(option) => {
            if (option.data.user) {
              return (
                <UserOptionComponent
                  avatarUrl={option.data.user?.imageUrl}
                  name={option.data.user?.name}
                  phone={option.data.user?.phone}
                  email={option.data.user?.email}
                />
              );
            }
            return option.label;
          }}
          onSelect={({ value }) => {
            const option = options.find((item) => item.id === value);
            if (option) setSelectedValue(option);
          }}
        />
      )}

      <p className="mb-1">Nhân viên được chuyển sang trưởng nhóm mới</p>
      <Radio.Group
        value={allOrSelectedEmployees}
        onChange={(e: RadioChangeEvent) => {
          setAllOrSelectedEmployees(e.target.value);
        }}
      >
        <Row>
          <Col>
            <Radio value={EmployeeLeaderChangeLeaderAllOrSelectedEnum.ALL}>
              Tất cả
            </Radio>
          </Col>
          <Col>
            <Radio value={EmployeeLeaderChangeLeaderAllOrSelectedEnum.SELECTED}>
              Nhân viên được chọn
            </Radio>
          </Col>
        </Row>
      </Radio.Group>

      {allOrSelectedEmployees ===
        EmployeeLeaderChangeLeaderAllOrSelectedEnum.SELECTED && (
        <Table
          className="mt-4"
          rowSelection={rowEmployeeSelection}
          columns={[
            {
              title: "Nhân viên",
              dataIndex: "user",
              render: (value: UserInterface) => {
                return (
                  <UserOptionComponent
                    avatarUrl={value.picture}
                    name={value.name}
                    phone={value.phone}
                    email={value.email}
                    isAvailable={true}
                  />
                );
              },
            },
            {
              title: "Vị trí",
              dataIndex: "role",
              render: (value: RoleInterface) => value.name,
            },
          ]}
          dataSource={employees}
          pagination={false}
          rowKey={(record) => record.id}
          loading={getLeaderEmployeeMembersMutation.isPending}
          scroll={{ y: 300 }}
          onScroll={(e) => {
            const target = e.target as HTMLElement;

            const { scrollTop, scrollHeight, clientHeight } = target;
            if (scrollHeight - scrollTop === clientHeight) {
              loadMoreEmployees();
            }
          }}
        />
      )}

      <p className="mb-1 mt-10">Outlet được chuyển sang trưởng nhóm mới</p>
      <Radio.Group
        value={allOrSelectedOutlets}
        onChange={(e: RadioChangeEvent) => {
          setAllOrSelectedOutlets(e.target.value);
        }}
      >
        <Row>
          <Col>
            <Radio value={EmployeeLeaderChangeLeaderAllOrSelectedEnum.ALL}>
              Tất cả
            </Radio>
          </Col>
          <Col>
            <Radio value={EmployeeLeaderChangeLeaderAllOrSelectedEnum.SELECTED}>
              Outlet được chọn
            </Radio>
          </Col>
        </Row>
      </Radio.Group>

      {allOrSelectedOutlets ===
        EmployeeLeaderChangeLeaderAllOrSelectedEnum.SELECTED && (
        <Table
          className="mt-4"
          rowSelection={rowOutletSelection}
          columns={[
            {
              title: "Mã outlet",
              dataIndex: "code",
            },
            {
              title: "Tên outlet",
              dataIndex: "name",
            },
          ]}
          dataSource={outlets}
          pagination={false}
          rowKey={(record) => record.id}
          loading={getProjectEmployeeLeaderOutletsMutation.isPending}
          scroll={{ y: 300 }}
          onScroll={(e) => {
            const target = e.target as HTMLElement;

            const { scrollTop, scrollHeight, clientHeight } = target;
            if (scrollHeight - scrollTop === clientHeight) {
              loadMoreOutlets();
            }
          }}
        />
      )}
    </>
  );

  useEffect(() => {
    loadMoreOutlets();
    loadMoreEmployees();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <CustomModal
      title={"Đổi trưởng nhóm"}
      isOpen={isOpen}
      content={content}
      onCancel={cancelCb}
      confirmText="Áp dụng"
    />
  );
};

export default EmployeeLeaderChangeLeaderModal;
