import { useApp } from "@/UseApp";
import { AbstractFilterInterface } from "@/common/interface";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  FeatureSamplingGroupInterface,
  FeatureSamplingInterface,
  FeatureSamplingOutletAvailabilityInterface,
  SamplingGroupOutletInterface,
} from "./interface";

export const useCreateGroupSamplingMutation = (componentFeatureId: number) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createGroupSampling", componentFeatureId],
    mutationFn: (data: { name: string }) =>
      axiosPost(`/features/${componentFeatureId}/sampling-groups`, data),
  });
};

export const useSamplingGroupsQuery = (
  componentFeatureId: number,
  filter?: AbstractFilterInterface & object,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["samplingGroups", componentFeatureId, filter],
    queryFn: () =>
      axiosGet<
        { entities: FeatureSamplingGroupInterface[]; count: number },
        unknown
      >(`/features/${componentFeatureId}/sampling-groups`, filter),
  });
};

export const useSamplingGroupSamplingsQuery = (
  componentFeatureId: number,
  samplingGroupId: number,
  filter?: AbstractFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: [
      "samplingGroupSamplings",
      componentFeatureId,
      samplingGroupId,
      filter,
    ],
    queryFn: () =>
      axiosGet<
        { entities: FeatureSamplingInterface[]; count: number },
        unknown
      >(
        `/features/${componentFeatureId}/sampling-groups/${samplingGroupId}/samplings`,
        filter,
      ),
  });
};

export const useCreateSamplingInSamplingGroupMutation = (
  componentFeatureId: number,
  samplingGroupId: number,
) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: [
      "createSamplingInSamplingGroup",
      componentFeatureId,
      samplingGroupId,
    ],
    mutationFn: (data: {
      projectProductId: number;
      unitId: number;
      numerator: number;
      denominator: number;
    }) =>
      axiosPost(
        `/features/${componentFeatureId}/sampling-groups/${samplingGroupId}/samplings`,
        data,
      ),
  });
};

export const useUpdateSamplingInSamplingGroupMutation = (
  componentFeatureId: number,
  samplingGroupId: number,
) => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: [
      "updateSamplingInSamplingGroup",
      componentFeatureId,
      samplingGroupId,
    ],
    mutationFn: (data: {
      id: number;
      numerator?: number;
      denominator?: number;
      unitId?: number;
      isActive?: boolean;
    }) =>
      axiosPatch(
        `/features/${componentFeatureId}/sampling-groups/${samplingGroupId}/samplings/${data.id}`,
        data,
      ),
  });
};

export const useDeleteSamplingInSamplingGroupMutation = (
  componentFeatureId: number,
  samplingGroupId: number,
) => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: [
      "deleteSamplingInSamplingGroup",
      componentFeatureId,
      samplingGroupId,
    ],
    mutationFn: (id: number) =>
      axiosDelete(
        `/features/${componentFeatureId}/sampling-groups/${samplingGroupId}/samplings/${id}`,
      ),
  });
};

export const useUpdateGroupSamplingMutation = (componentFeatureId: number) => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["updateGroupSampling", componentFeatureId],
    mutationFn: (data: { id: number; name?: string; isActive?: boolean }) =>
      axiosPatch(
        `/features/${componentFeatureId}/sampling-groups/${data.id}`,
        data,
      ),
  });
};

export const useSamplingGroupOutletAvailablesQuery = (
  componentFeatureId: number,
  filter?: AbstractFilterInterface,
  enabled?: boolean,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["samplingGroupOutletAvailables", componentFeatureId, filter],
    queryFn: () =>
      axiosGet<
        {
          entities: FeatureSamplingOutletAvailabilityInterface[];
          count: number;
        },
        unknown
      >(
        `/features/${componentFeatureId}/sampling-groups/outlet-availables`,
        filter,
      ),
    enabled,
  });
};

export const useCreateSamplingGroupOutletMutation = (
  componentFeatureId: number,
) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createSamplingGroupOutlet", componentFeatureId],
    mutationFn: (data: { projectOutletIds: number[]; id: number }) =>
      axiosPost(
        `/features/${componentFeatureId}/sampling-groups/${data.id}/outlets`,
        { projectOutletIds: data.projectOutletIds },
      ),
  });
};

export const useSamplingGroupOutletsQuery = (
  componentFeatureId: number,
  samplingGroupId?: number,
  filter?: AbstractFilterInterface,
  enabled?: boolean,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: [
      "samplingGroupOutlets",
      componentFeatureId,
      samplingGroupId,
      filter,
    ],
    queryFn: () =>
      axiosGet<
        {
          entities: SamplingGroupOutletInterface[];
          count: number;
        },
        unknown
      >(
        `/features/${componentFeatureId}/sampling-groups/${samplingGroupId}/outlets`,
        filter,
      ),
    enabled: !!enabled && !!samplingGroupId,
  });
};

export const useDeleteSamplingGroupOutletMutation = (
  componentFeatureId: number,
  samplingGroupId?: number,
) => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: [
      "deleteSamplingGroupOutlet",
      componentFeatureId,
      samplingGroupId,
    ],
    mutationFn: (ids: number[]) =>
      axiosDelete(
        `/features/${componentFeatureId}/sampling-groups/${samplingGroupId}/outlets/`,
        { featureSamplingGroupOutletIds: ids },
      ),
  });
};

export const useArrangementSamplingGroupSamplingsMutation = (
  componentFeatureId: number,
  samplingGroupId: number,
) => {
  const { axiosPut } = useApp();

  return useMutation({
    mutationKey: [
      "arrangementSamplingGroupOutlets",
      componentFeatureId,
      samplingGroupId,
    ],
    mutationFn: (data: { activeId: number; overId: number }) =>
      axiosPut(
        `/features/${componentFeatureId}/sampling-groups/${samplingGroupId}/samplings/${data.activeId}/arrangement`,
        {
          overFeatureSamplingId: data.overId,
        },
      ),
  });
};
