import { useApp } from "@/UseApp";
import { useQuery } from "@tanstack/react-query";
import { DashboardFilterInterface } from "../../interface";
import { OosStoreLevelDataInterface } from "./interface";

export const useOosStoreLevelQuery = (
  projectId: number,
  dashboardId: number,
  filter?: DashboardFilterInterface & {
    controlBy?: "region" | "leader" | "province" | "channel" | "brand";
  },
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["oosStoreLevel", projectId, dashboardId, filter],
    queryFn: () =>
      axiosGet<OosStoreLevelDataInterface[], unknown>(
        `/projects/${projectId}/dashboards/${dashboardId}/charts/oos-store-level`,
        filter,
      ),
  });
};
