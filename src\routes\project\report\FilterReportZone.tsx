import { DATE_FORMAT } from "@/common/constant.ts";
import useDeviceType from "@/hooks/useDeviceType.ts";
import {
  DownloadOutlined,
  FilterOutlined,
  FilterTwoTone,
  SearchOutlined,
} from "@ant-design/icons";
import {
  Button,
  DatePicker,
  Dropdown,
  Form,
  Input,
  Modal,
  Select,
  Space,
} from "antd";
import { FormInstance } from "antd/lib/form/Form";
import dayjs from "dayjs";
import { Fragment, useCallback, useEffect, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import { useProjectBoothsQuery } from "../configOutlet/service.ts";
import { useProjectChannelsQuery } from "../outlet/service.ts";
import { useProjectReportOutletContext } from "./UseProjectReportOutletContext.tsx";
import { useAdvancedFilterValuesStore } from "./state.ts";

type Field =
  | "roleId"
  | "keyword"
  | "attendance"
  | "projectBoothId"
  | "urgencies.status"
  | "outOfStockStatus.isMergedProduct"
  | "outOfStockStatus.channelId"
  | "outOfStockStatus.howToDisplay"
  | "outOfStockStatus.date";

interface FilterReportZoneProps {
  readonly form: FormInstance;
  readonly onFinish?: () => void;
  readonly loading?: boolean;
  readonly fields?: (Field | undefined)[];
  readonly hideExport?: boolean;
  readonly onExport?: () => void;
  readonly placeholder?: string;
  readonly customExports?: {
    label: string;
    onClick: () => void;
    key: string;
    icon: React.ReactNode;
  }[];
  readonly hideAdvancedFilter?: boolean;
}

export default function FilterReportZone({
  form,
  onFinish,
  loading,
  fields,
  hideExport,
  onExport,
  placeholder,
  hideAdvancedFilter,
  customExports = [],
}: FilterReportZoneProps) {
  const isMobile = useDeviceType();
  const projectId = parseInt(useParams().id ?? "0");

  const { RangePicker } = DatePicker;
  const { isHideFilter, setIsHideFilter, roles } =
    useProjectReportOutletContext();
  const { isFiltered, setIsFiltered } = useAdvancedFilterValuesStore();
  const [open, setOpen] = useState(false);

  const projectBoothsQuery = useProjectBoothsQuery(projectId, {
    take: 50,
    skip: 0,
  });
  const projectChannelsQuery = useProjectChannelsQuery(projectId);

  useEffect(() => {
    if (!isHideFilter) {
      form.resetFields();
      form.setFieldsValue({
        attendance: [dayjs().startOf("date"), dayjs().endOf("date")],
        date: dayjs().startOf("date"),
        isMergedProduct: true,
        howToDisplay: "updatedOnly",
      });
    }
  }, [form, isHideFilter]);

  const formFields = useMemo(() => {
    return [
      {
        key: "keyword",
        field: (
          <Form.Item name={"keyword"}>
            <Input
              placeholder={placeholder ?? "Tìm theo outlet, nhân viên ghi nhận"}
              prefix={<SearchOutlined />}
              allowClear
              className={"min-w-[279px]"}
            />
          </Form.Item>
        ),
      },
      {
        key: "roleId",
        field: (
          <Form.Item name={"roleId"}>
            <Select
              allowClear
              placeholder="Role ghi nhận"
              options={roles?.map((projectRole) => ({
                label: projectRole.name,
                value: projectRole.id,
              }))}
            />
          </Form.Item>
        ),
      },
      {
        key: "attendance",
        field: (
          <Form.Item name={"attendance"}>
            <RangePicker
              allowClear
              format={DATE_FORMAT}
              className={"w-full"}
              maxDate={dayjs().endOf("date")}
            />
          </Form.Item>
        ),
      },
      {
        key: "projectBoothId",
        field: (
          <Form.Item name={"projectBoothId"}>
            <Select
              allowClear
              placeholder="Loại booth"
              options={projectBoothsQuery.data?.entities.map(
                (projectBooth) => ({
                  label: projectBooth.name,
                  value: projectBooth.id,
                }),
              )}
              popupMatchSelectWidth={false}
            />
          </Form.Item>
        ),
      },
      {
        key: "urgencies.status",
        field: (
          <Form.Item name={"status"}>
            <Select
              allowClear
              placeholder="Tình trạng rời vị trí"
              options={[
                {
                  label: "Đang diễn ra",
                  value: "open",
                },
                {
                  label: "Đã kết thúc",
                  value: "closed",
                },
              ]}
              popupMatchSelectWidth={false}
            />
          </Form.Item>
        ),
      },
      {
        key: "outOfStockStatus.date",
        field: (
          <Form.Item name={"date"}>
            <DatePicker
              placeholder="Chọn ngày"
              allowClear={false}
              format={DATE_FORMAT}
              maxDate={dayjs().endOf("date")}
              className="w-full"
            />
          </Form.Item>
        ),
      },
      {
        key: "outOfStockStatus.isMergedProduct",
        field: (
          <Form.Item name={"isMergedProduct"}>
            <Select
              placeholder="Loại sản phẩm"
              allowClear={false}
              options={[
                {
                  label: "Sản phẩm gộp",
                  value: true,
                },
                {
                  label: "Sản phẩm riêng lẻ",
                  value: false,
                },
              ]}
              popupMatchSelectWidth={false}
            />
          </Form.Item>
        ),
      },
      {
        key: "outOfStockStatus.channelId",
        field: (
          <Form.Item name={"channelId"}>
            <Select
              placeholder="Kênh"
              allowClear={true}
              options={projectChannelsQuery.data?.map((channel) => ({
                label: channel.name,
                value: channel.id,
              }))}
              popupMatchSelectWidth={false}
            />
          </Form.Item>
        ),
      },
      {
        key: "outOfStockStatus.howToDisplay",
        field: (
          <Form.Item name={"howToDisplay"}>
            <Select
              placeholder="Cách hiển thị"
              options={[
                {
                  label: "Đánh dấu dữ liệu chưa cập nhật",
                  value: "updatedOnly",
                },
                {
                  label: "Không đánh dấu dữ liệu chưa cập nhật",
                  value: "all",
                },
              ]}
              popupMatchSelectWidth={false}
            />
          </Form.Item>
        ),
      },
    ];
  }, [
    RangePicker,
    placeholder,
    projectBoothsQuery.data?.entities,
    projectChannelsQuery.data,
    roles,
  ]);

  const handleFinish = useCallback(() => {
    setIsFiltered(false);
    onFinish?.();
  }, [onFinish, setIsFiltered]);

  if (isMobile) {
    return (
      <div className={`w-full mb-4`}>
        <Button
          className="w-full bg-input-disabled"
          icon={<SearchOutlined />}
          type="text"
          onClick={() => setOpen(true)}
        >
          Filter
        </Button>

        <Modal
          open={open}
          footer={null}
          onCancel={() => setOpen(false)}
          title={"Filter"}
        >
          <div className="mt-5">
            <Form
              form={form}
              onFinish={() => {
                handleFinish();
                setOpen(false);
              }}
              initialValues={{
                attendance: [dayjs().startOf("date"), dayjs().endOf("date")],
                date: dayjs().startOf("date"),
                isMergedProduct: true,
                howToDisplay: "updatedOnly",
              }}
            >
              {fields
                ?.filter((item) => item)
                .map((item) => (
                  <Fragment key={item}>
                    {
                      formFields.find((formFiled) => formFiled.key === item)
                        ?.field
                    }
                  </Fragment>
                ))}
              <Form.Item>
                <Button
                  htmlType="submit"
                  type="default"
                  loading={loading}
                  className="w-full bg-[#F0F1F5]"
                >
                  Tìm kiếm
                </Button>
              </Form.Item>
            </Form>

            {!hideExport && (
              <>
                {customExports?.length === 0 && (
                  <Button
                    icon={<DownloadOutlined />}
                    onClick={onExport}
                    loading={loading}
                    className="w-full"
                  >
                    Tải excel
                  </Button>
                )}

                {(customExports?.length ?? 0) > 0 && (
                  <Dropdown
                    menu={{
                      items: customExports,
                    }}
                  >
                    <Button
                      icon={<DownloadOutlined />}
                      loading={loading}
                      className="w-full"
                    >
                      Tải excel
                    </Button>
                  </Dropdown>
                )}
              </>
            )}
          </div>
        </Modal>
      </div>
    );
  }

  return (
    <div className="flex justify-between mb-6">
      <Form
        layout="inline"
        form={form}
        onFinish={handleFinish}
        initialValues={{
          attendance: [dayjs().startOf("date"), dayjs().endOf("date")],
          date: dayjs().startOf("date"),
          isMergedProduct: true,
          howToDisplay: "updatedOnly",
        }}
        disabled={!isHideFilter}
      >
        {fields
          ?.filter((item) => item)
          .map((item) => (
            <Fragment key={item}>
              {formFields.find((formFiled) => formFiled.key === item)?.field}
            </Fragment>
          ))}

        <Form.Item>
          <Button htmlType="submit" loading={loading}>
            Tìm kiếm
          </Button>
        </Form.Item>
      </Form>

      <div>
        <Space>
          {!hideAdvancedFilter && (
            <Button
              icon={isFiltered ? <FilterTwoTone /> : <FilterOutlined />}
              onClick={() => setIsHideFilter(!isHideFilter)}
              loading={loading}
            />
          )}

          {!hideExport && (
            <>
              {customExports?.length === 0 && (
                <Button
                  icon={<DownloadOutlined />}
                  onClick={onExport}
                  loading={loading}
                >
                  Tải excel
                </Button>
              )}

              {(customExports?.length ?? 0) > 0 && (
                <Dropdown
                  menu={{
                    items: customExports,
                  }}
                >
                  <Button icon={<DownloadOutlined />} loading={loading}>
                    Tải excel
                  </Button>
                </Dropdown>
              )}
            </>
          )}
        </Space>
      </div>
    </div>
  );
}
