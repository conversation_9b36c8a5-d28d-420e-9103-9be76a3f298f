import { DEFAULT_CURRENT_PAGE, DEFAULT_PAGE_SIZE } from "@/common/constant.ts";
import CustomModal from "@/components/CustomModal.tsx";
import FilterClassicComponent from "@/components/FilterClassicComponent.tsx";
import OutletSearchFilterContent from "@/components/outletSearchFilterContent/OutletSearchFilterContent.tsx";
import { useApp } from "@/UseApp.tsx";
import { ProjectOutletInterface } from "@project/outlet/interface.ts";
import { Form, Table } from "antd";
import _ from "lodash";
import React, { useCallback, useMemo, useState } from "react";
import { FeatureSamplingGroupInterface } from "../../interface.ts";
import {
  useDeleteSamplingGroupOutletMutation,
  useSamplingGroupOutletsQuery,
} from "../../service.ts";

interface ViewOutletSamplingGroupModalProps {
  isOpen: boolean;
  projectId: number;
  componentFeatureId: number;
  samplingGroup?: FeatureSamplingGroupInterface;
  onCancelCb: () => void;
}

const ViewOutletSamplingGroupModal = ({
  isOpen,
  projectId,
  componentFeatureId,
  samplingGroup,
  onCancelCb,
}: ViewOutletSamplingGroupModalProps) => {
  const { showNotification } = useApp();

  const [searchForm] = Form.useForm();

  const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [selectedOutletKeys, setSelectedOutletKeys] = useState<React.Key[]>([]);
  const [filter, setFilter] = useState({});

  const samplingGroupOutletsQuery = useSamplingGroupOutletsQuery(
    componentFeatureId,
    samplingGroup?.id,
    { ...filter, take: pageSize, skip: (currentPage - 1) * pageSize },
    isOpen,
  );

  const deleteSamplingGroupOutletMutation =
    useDeleteSamplingGroupOutletMutation(componentFeatureId, samplingGroup?.id);

  const searchHandler = useCallback(async () => {
    setCurrentPage(DEFAULT_CURRENT_PAGE);
    if (_.isEqual(searchForm.getFieldsValue(), filter)) {
      await samplingGroupOutletsQuery.refetch();
    }

    setFilter(searchForm.getFieldsValue());
  }, [filter, samplingGroupOutletsQuery, searchForm]);

  const pagination = useMemo(() => {
    return {
      current: currentPage,
      total: samplingGroupOutletsQuery.data?.count,
      pageSize: pageSize,
      onChange: (page: number) => {
        setCurrentPage(page);
      },
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: (_current: number, size: number) => {
        setPageSize(size);
        setCurrentPage(DEFAULT_CURRENT_PAGE);
      },
    };
  }, [currentPage, samplingGroupOutletsQuery.data?.count, pageSize]);

  const rowSelection = {
    selectedRowKeys: selectedOutletKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedOutletKeys(newSelectedRowKeys);
    },
  };

  const content = (
    <>
      <p>
        Nhóm sampling đang phân bổ outlet:{" "}
        <span className={"text-primary font-semibold"}>
          {samplingGroup?.name}
        </span>
      </p>

      <FilterClassicComponent
        searchHandler={searchHandler}
        searchForm={searchForm}
        content={
          <OutletSearchFilterContent
            open={isOpen}
            searchForm={searchForm}
            projectId={projectId}
          />
        }
        className={"mb-5"}
      />

      <Table
        rowKey={(o) => o.id}
        dataSource={samplingGroupOutletsQuery.data?.entities}
        pagination={pagination}
        rowSelection={rowSelection}
        columns={[
          {
            title: "Mã outlet",
            dataIndex: "projectOutlet",
            render: (projectOutlet: ProjectOutletInterface) =>
              projectOutlet?.code,
          },
          {
            title: "Tên outlet",
            dataIndex: "projectOutlet",
            render: (projectOutlet: ProjectOutletInterface) =>
              projectOutlet?.name,
          },
          {
            title: "Số nhà",
            dataIndex: "projectOutlet",
            render: (projectOutlet: ProjectOutletInterface) =>
              projectOutlet?.houseNumber,
          },
          {
            title: "Tên đường",
            dataIndex: "projectOutlet",
            render: (projectOutlet: ProjectOutletInterface) =>
              projectOutlet?.streetName,
          },
          {
            title: "Tỉnh/ TP",
            dataIndex: "projectOutlet",
            render: (projectOutlet: ProjectOutletInterface) =>
              projectOutlet?.province?.name,
          },
          {
            title: "Quận/ Huyện",
            dataIndex: "projectOutlet",
            render: (projectOutlet: ProjectOutletInterface) =>
              projectOutlet?.district?.name,
          },
          {
            title: "Phường/ Xã",
            dataIndex: "projectOutlet",
            render: (projectOutlet: ProjectOutletInterface) =>
              projectOutlet?.ward?.name,
          },
          {
            title: "Kênh",
            dataIndex: "projectOutlet",
            render: (projectOutlet: ProjectOutletInterface) =>
              projectOutlet?.projectAgencyChannel?.channel?.name,
          },
          {
            title: "Nhóm",
            dataIndex: "projectOutlet",
            render: (projectOutlet: ProjectOutletInterface) =>
              projectOutlet?.subChannel?.name,
          },
        ]}
      />
    </>
  );

  const onCancel = useCallback(() => {
    setSelectedOutletKeys([]);
    searchForm.resetFields();
    onCancelCb();
  }, [onCancelCb, searchForm]);

  const onConfirm = useCallback(async () => {
    if (samplingGroup && selectedOutletKeys.length > 0) {
      await deleteSamplingGroupOutletMutation.mutateAsync(
        selectedOutletKeys.map((item) => Number(item)),
      );

      showNotification({
        type: "success",
        message: "Bỏ outlet khỏi nhóm thành công",
      });
      onCancel();
    }
  }, [
    deleteSamplingGroupOutletMutation,
    onCancel,
    samplingGroup,
    selectedOutletKeys,
    showNotification,
  ]);

  return (
    <CustomModal
      title={"Danh sách outlet bên trong nhóm sampling"}
      isOpen={isOpen}
      content={content}
      width={1200}
      confirmText={`Bỏ ${selectedOutletKeys.length} outlet khỏi nhóm`}
      confirmDisable={selectedOutletKeys.length === 0}
      onConfirm={onConfirm}
      onCancel={onCancel}
    />
  );
};

export default ViewOutletSamplingGroupModal;
