import {
  DATE_FORMAT,
  DEFAULT_CURRENT_PAGE,
  DEFAULT_PAGE_SIZE,
} from "@/common/constant";
import { useTablePagination } from "@/common/table-pagination";
import TableActionCell from "@/components/TableActionCell";
import UserOptionComponent from "@/components/UserOptionComponent";
import { useCanPermission } from "@/layouts/MainLayout/hook";
import { useApp } from "@/UseApp";
import {
  DeleteOutlined,
  EditOutlined,
  FilterOutlined,
  FilterTwoTone,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { Button, DatePicker, Form, Input, Select, Space, Table } from "antd";
import dayjs from "dayjs";
import _ from "lodash";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import { useProjectBoothsQuery } from "../configOutlet/service";
import { PermissionEnum } from "../interface";
import AdvancedFilter from "../report/AdvancedFilter";
import getColumnsTableReport from "../report/ColumnsTableReport";
import {
  AdvancedFilterFormValueInterface,
  AdvancedFilterInterface,
  ProjectRecordEmployeeInterface,
} from "../report/interface";
import {
  useAdvancedFilterFiledsStore,
  useAdvancedFilterValuesStore,
} from "../report/state";
import { RecordAttendanceInterface } from "../report/types/attendanceClocking/interface";
import { useProjectRolesQuery } from "../role/service";
import ModalEdit from "./ModalEdit";
import { useAttendancesQuery, useDeleteAttendanceMutation } from "./service";
import AddAttendanceClockingInModal from "./types/attendanceClockingIn/AddAttendanceClockingInModal";

const ProjectEditPage = () => {
  const projectId = parseInt(useParams().id ?? "0");
  const { RangePicker } = DatePicker;
  const { openDeleteModal } = useApp();
  const { canPermissionFunction } = useCanPermission(projectId);

  const [searchForm] = Form.useForm();
  const [advancedFilterForm] = Form.useForm();
  const [isHideFilter, setIsHideFilter] = useState(true);
  const { setIsFiltered, isFiltered } = useAdvancedFilterValuesStore();
  const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [filter, setFilter] = useState<AdvancedFilterInterface>({
    startDate: dayjs().startOf("date").toDate(),
    endDate: dayjs().endOf("date").toDate(),
  });
  const [isModalEditOpen, setIsModalEditOpen] = useState(false);
  const [selectedAttendance, setSelectedAttendance] = useState<
    RecordAttendanceInterface | undefined
  >(undefined);
  const { setFileds: setAdvancedFilterFileds } = useAdvancedFilterFiledsStore();
  const [isAddAttendanceOpen, setIsAddAttendanceOpen] = useState(false);

  const projectRolesQuery = useProjectRolesQuery(projectId, {
    type: "EMPLOYEE",
  });
  const projectBoothsQuery = useProjectBoothsQuery(projectId, {
    take: 50,
    skip: 0,
  });
  const attendancesQuery = useAttendancesQuery(projectId, {
    ...filter,
    projectOutletId: filter.outletId,
    take: pageSize,
    skip: (currentPage - 1) * pageSize,
  });

  const deleteAttendanceMutation = useDeleteAttendanceMutation(projectId);

  const setFilterForQuery = useCallback(
    async (values: AdvancedFilterFormValueInterface) => {
      setCurrentPage(DEFAULT_CURRENT_PAGE);

      if (_.isEqual(filter, values)) {
        await attendancesQuery.refetch();
      }
      setFilter(values);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [filter],
  );

  useEffect(() => {
    setAdvancedFilterFileds([
      "Agency phụ trách",
      "Ngày chấm công",
      "Nhân viên ghi nhận",
      "Mã/ Tên outlet",
      "Tỉnh/ TP",
      "Quận/ Huyện",
      "Kênh",
      "Nhóm",
      "Loại booth",
      "Trưởng nhóm quản lý",
    ]);
  }, [setAdvancedFilterFileds]);

  const onAdvancedFilterFormFinish = useCallback(
    (values: AdvancedFilterFormValueInterface) => {
      if (values.attendance) {
        const [attendanceStartDate, attendanceEndDate] = values.attendance;
        values.startDate = attendanceStartDate
          ? dayjs(attendanceStartDate).startOf("date").toDate()
          : undefined;
        values.endDate = attendanceEndDate
          ? dayjs(attendanceEndDate).endOf("date").toDate()
          : undefined;

        delete values.attendance;
      }

      setFilterForQuery(values);
      setIsFiltered(true);
    },
    [setFilterForQuery, setIsFiltered],
  );

  const loading = useMemo(
    () => attendancesQuery.isLoading,
    [attendancesQuery.isLoading],
  );

  const onFilterFormFinish = useCallback(() => {
    const values = searchForm.getFieldsValue();

    if (values.attendance) {
      const [startDate, endDate] = values.attendance;
      values.startDate = startDate
        ? dayjs(startDate).startOf("date").toDate()
        : undefined;
      values.endDate = endDate
        ? dayjs(endDate).endOf("date").toDate()
        : undefined;

      delete values.attendance;
    }
    setFilterForQuery(values);
  }, [searchForm, setFilterForQuery]);

  const pagination = useTablePagination({
    currentPage,
    pageSize,
    setCurrentPage,
    setPageSize,
    total: attendancesQuery.data?.count ?? 0,
  });

  const columns = useMemo(
    () => [
      ...getColumnsTableReport([
        { tableColumn: "outletCode" },
        { tableColumn: "outletName" },
        { tableColumn: "boothName" },
        { tableColumn: "address" },
        { tableColumn: "attendance" },
        { tableColumn: "channelName" },
        { tableColumn: "subChannelName" },
        { tableColumn: "agencyName" },
        { tableColumn: "teamLeader" },
      ]),
      {
        key: "recordEmployee",
        fixed: "right",
        className: "content-baseline",
        title: "Nhân viên ghi nhận",
        dataIndex: "projectRecordEmployee",
        render: (projectRecordEmployee: ProjectRecordEmployeeInterface) => {
          const user = projectRecordEmployee.employee.user;

          return (
            <UserOptionComponent
              avatarUrl={user.picture}
              name={user.name}
              id={projectRecordEmployee.employee.id}
              roleName={projectRecordEmployee.employee.role.name}
              roleLocation="bottom"
            />
          );
        },
      },
      {
        dataIndex: "id",
        fixed: "right",
        render: (_: number, record: RecordAttendanceInterface) => {
          return (
            <TableActionCell
              actions={[
                {
                  key: "EDIT",
                  action: () => {
                    setIsModalEditOpen(true);
                    setSelectedAttendance(record);
                  },
                },
                {
                  key: "DELETE",
                  action: () => {
                    openDeleteModal({
                      content: (
                        <>
                          <p>
                            Xóa chấm công của nhân viên{" "}
                            <span className={"font-semibold"}>
                              {record.projectRecordEmployee.employee.user.name}
                            </span>
                          </p>
                          <p>Bạn vẫn muốn chấm công khỏi hệ thống?</p>
                        </>
                      ),
                      deleteText: "Xác nhận xóa",
                      loading: deleteAttendanceMutation.isPending,
                      onCancel(): void {},
                      onDelete: async () => {
                        await deleteAttendanceMutation.mutateAsync(record.id);

                        attendancesQuery.refetch();
                      },
                      title: `Xóa vật phẩm`,
                      titleError: "Không thể xóa vật phẩm",
                      contentHeader: (
                        <>
                          Không thể xóa chấm công{" "}
                          <span className="font-semibold">
                            {record.projectRecordEmployee.employee.user.name}
                          </span>{" "}
                          bởi vì:
                        </>
                      ),
                    });
                  },
                },
              ]}
              items={[
                {
                  key: "EDIT",
                  label: "Chỉnh sửa",
                  icon: <EditOutlined />,
                },
                canPermissionFunction(PermissionEnum.NONE_ACCESS)
                  ? {
                      key: "DELETE",
                      label: "Xóa",
                      icon: <DeleteOutlined />,
                    }
                  : null,
              ]}
              record={record}
            />
          );
        },
      },
    ],
    [
      attendancesQuery,
      canPermissionFunction,
      deleteAttendanceMutation,
      openDeleteModal,
    ],
  );

  const modalEditCancel = useCallback(() => {
    setIsModalEditOpen(false);
    setSelectedAttendance(undefined);
  }, []);

  return (
    <div className="flex justify-between overflow-x-hidden m-0 p-0">
      <div className={`w-full h-full ml-0 mr-10  overflow-x-auto`}>
        <div>
          <h2>Danh sách chấm công</h2>
          <div className="bg-white p-10 rounded">
            <div className="flex justify-between mb-6">
              <Form
                layout="inline"
                form={searchForm}
                onFinish={onFilterFormFinish}
                initialValues={{
                  attendance: [dayjs().startOf("date"), dayjs().endOf("date")],
                }}
                disabled={!isHideFilter}
              >
                <Form.Item name={"keyword"}>
                  <Input
                    placeholder={"Tìm theo outlet, nhân viên ghi nhận"}
                    prefix={<SearchOutlined />}
                    allowClear
                    className={"min-w-[279px]"}
                  />
                </Form.Item>

                <Form.Item name={"projectBoothId"}>
                  <Select
                    allowClear
                    placeholder="Loại booth"
                    options={projectBoothsQuery.data?.entities.map(
                      (projectBooth) => ({
                        label: projectBooth.name,
                        value: projectBooth.id,
                      }),
                    )}
                    popupMatchSelectWidth={false}
                  />
                </Form.Item>

                <Form.Item name={"attendance"}>
                  <RangePicker allowClear format={DATE_FORMAT} />
                </Form.Item>

                <Form.Item>
                  <Button htmlType="submit" loading={loading}>
                    Tìm kiếm
                  </Button>
                </Form.Item>
              </Form>

              <div>
                <Space>
                  <Button
                    icon={isFiltered ? <FilterTwoTone /> : <FilterOutlined />}
                    onClick={() => setIsHideFilter(!isHideFilter)}
                    loading={loading}
                  ></Button>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => setIsAddAttendanceOpen(true)}
                  >
                    Thêm mới chấm công
                  </Button>
                </Space>
              </div>
            </div>
            <Table
              pagination={pagination}
              rowKey={"id"}
              dataSource={attendancesQuery.data?.entities.map((item) => ({
                projectBooth:
                  item.projectRecordEmployee.projectRecord.projectBooth,
                projectOutlet:
                  item.projectRecordEmployee.projectRecord.projectOutlet,
                attendanceIn: item.in,
                attendanceOut: item.out,
                leader: item.projectRecordEmployee.projectRecord.leader,
                projectAgency:
                  item.projectRecordEmployee.projectRecord.projectAgency,
                ...item,
              }))}
              scroll={{
                x: "max-content",
                y: pagination.total ? "80vh" : undefined,
              }}
              columns={columns}
              loading={loading}
            />
          </div>
        </div>
      </div>

      {isModalEditOpen && (
        <ModalEdit
          isOpen={isModalEditOpen}
          projectId={projectId}
          attendance={selectedAttendance}
          onCancel={modalEditCancel}
          setOpen={setIsModalEditOpen}
          cb={() => {
            attendancesQuery.refetch();
          }}
        />
      )}

      {!isHideFilter && (
        <div hidden={isHideFilter} className="bg-[#F8FCFF] m-0 text-nowrap">
          <AdvancedFilter
            isHideFilter={isHideFilter}
            form={advancedFilterForm}
            projectId={projectId}
            onFinish={onAdvancedFilterFormFinish}
            roles={projectRolesQuery.data?.entities}
          />
        </div>
      )}

      {isAddAttendanceOpen && (
        <AddAttendanceClockingInModal
          setOpen={setIsAddAttendanceOpen}
          cb={() => {
            attendancesQuery.refetch();
          }}
        />
      )}
    </div>
  );
};

export default ProjectEditPage;
