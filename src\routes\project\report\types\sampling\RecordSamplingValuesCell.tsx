import equal from "@/assets/equal.svg";
import { formatNumber } from "@/common/helper";
import { Col, Row } from "antd";
import { RecordSamplingValueInterface } from "./interface";

interface RecordSamplingValuesCellProps {
  recordSamplingValues: RecordSamplingValueInterface[];
  minWidth?: string;
}

const RecordSamplingValuesCell = ({
  recordSamplingValues,
  minWidth = "350px",
}: RecordSamplingValuesCellProps) => {
  recordSamplingValues.sort((a, b) => b.value - a.value);

  return (
    <div className={`min-w-[${minWidth}]`}>
      <ul className="pl-2">
        {recordSamplingValues.map((item, index) => (
          <li
            key={item.id}
            className={index !== recordSamplingValues.length - 1 ? "pb-3" : ""}
          >
            <Row gutter={8} className="items-center">
              <Col md={12}>
                <div className="">
                  {item.featureSampling?.projectProduct.product.code} -{" "}
                  {item.featureSampling?.projectProduct.product.name}
                </div>
              </Col>

              <Col md={3} className={"text-blue font-semibold text-right"}>
                {item.value || item.value === 0
                  ? formatNumber(item.value)
                  : "_"}
              </Col>
              <Col md={2}>
                {" "}
                <span className="text-nowrap">
                  {item.featureSampling?.unit.name}
                </span>
              </Col>
              <Col md={2}>
                <img
                  src={equal}
                  alt="equal"
                  className="w-8 h-8 bg-[#ECEDEF] rounded-full p-2"
                />
              </Col>
              <Col md={3} className={"text-blue font-semibold text-right"}>
                {item.conversionValue || item.conversionValue === 0
                  ? formatNumber(item.conversionValue)
                  : "_"}
              </Col>
              <Col md={2}>
                <span className="text-nowrap">
                  {
                    item.featureSampling?.projectProduct.productPackaging?.unit
                      .name
                  }
                </span>
              </Col>
            </Row>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default RecordSamplingValuesCell;
