import { stringIncludes } from "@/common/helper";
import CustomModal from "@/components/CustomModal";
import FormNumberInput from "@/components/FormNumberInput";
import { ProjectOutletInterface } from "@/routes/project/outlet/interface";
import { ProjectProductInterface } from "@/routes/project/product/interface";
import { useApp } from "@/UseApp";
import { SearchOutlined } from "@ant-design/icons";
import { Form, Input, Radio, Table } from "antd";
import _ from "lodash";
import { useCallback, useEffect, useMemo, useState } from "react";
import {
  useCreateReportOOSStatusHighlightMutation,
  useOutletStockProductsQuery,
  useReportOOSStatusesZonesQuery,
} from "./service";

interface ReportOutOfStockStatusHighlightModalProps {
  projectId: number;
  componentFeatureId: number;
  projectOutlet: ProjectOutletInterface | null;
  cancelCb: () => void;
  cb: () => void;
}

const ReportOutOfStockStatusHighlightModal = ({
  projectId,
  componentFeatureId,
  projectOutlet,
  cancelCb,
  cb,
}: ReportOutOfStockStatusHighlightModalProps) => {
  const { showNotification } = useApp();

  const [form] = Form.useForm();
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");

  const reportOOSStatusesZonesQuery = useReportOOSStatusesZonesQuery(
    projectId,
    componentFeatureId,
    {
      take: 0,
    },
  );
  const outletStockProductsQuery = useOutletStockProductsQuery(
    projectId,
    projectOutlet?.id ?? 0,
    {
      take: 0,
      projectFeatureId: componentFeatureId,
    },
  );

  const createReportOOSStatusHighlightMutation =
    useCreateReportOOSStatusHighlightMutation(
      projectId,
      projectOutlet?.id ?? 0,
    );

  const debouncedSetSearchTerm = useCallback((value: string) => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(value);
    }, 300);

    return () => clearTimeout(timer);
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const value = e.target.value;
    setSearchTerm(value);
    debouncedSetSearchTerm(value);
  };

  const filteredOptions = useMemo(() => {
    return outletStockProductsQuery.data?.entities.filter((item) =>
      stringIncludes(item.projectProduct.product.name, debouncedSearchTerm),
    );
  }, [debouncedSearchTerm, outletStockProductsQuery.data?.entities]);

  const content = (
    <>
      <p>
        <span className="text-hint">Outlet: </span>
        <span className="text-blue font-medium">
          {projectOutlet?.code} - {projectOutlet?.name}
        </span>
      </p>
      <Form form={form}>
        <Form.Item
          label="Vị trí ghi nhận:"
          layout="vertical"
          name={"zoneId"}
          rules={[{ required: true }]}
          className="h-16"
        >
          <Radio.Group buttonStyle="solid">
            {reportOOSStatusesZonesQuery.data?.entities.map((zone) => (
              <Radio
                key={zone.id}
                value={zone.id}
                style={{ border: "1px solid #d9d9d9", padding: "8px 16px" }}
                className="rounded"
              >
                {zone.name}
              </Radio>
            ))}
          </Radio.Group>
        </Form.Item>

        <Form.Item noStyle>
          <Input
            placeholder="Nhập mã, tên sản phẩm cần tìm"
            className=" w-1/2"
            prefix={<SearchOutlined />}
            allowClear
            value={searchTerm}
            onChange={handleChange}
          />
        </Form.Item>

        <Table
          className="my-2"
          rowKey={"id"}
          columns={[
            {
              title: "Tên sản phẩm",
              dataIndex: "projectProduct",
              render: (projectProduct: ProjectProductInterface) =>
                projectProduct.product.name,
              className: "min-w-[100px]",
            },
            {
              title: "Tồn chuẩn",
              className: "min-w-[100px]",
              dataIndex: "expectedStock",
              align: "end",
            },
            {
              title: "Tồn nhân viên nhập",
              dataIndex: "projectProductId",
              className: "min-w-[100px]",
              align: "end",
              render: (projectProductId: number) => {
                const projectOutletStock =
                  projectOutlet?.projectOutletStocks?.[0]?.projectOutletStockDetails?.find(
                    (item) =>
                      item.featureOosProduct?.projectProductId ===
                      projectProductId,
                  );
                return projectOutletStock?.quantity ?? "-";
              },
            },
            {
              title: "Tồn của Sale",
              align: "center",
              className: "min-w-[100px]",
              dataIndex: "id",
              render: (id) => {
                return (
                  <Form.Item name={id} noStyle>
                    <FormNumberInput />
                  </Form.Item>
                );
              },
            },
          ]}
          dataSource={filteredOptions}
          pagination={false}
          scroll={{
            y: "70vh",
            x: "max-content",
          }}
        />
      </Form>
    </>
  );

  const confirm = useCallback(async () => {
    setDebouncedSearchTerm("");

    await form.validateFields();

    const data = form.getFieldsValue();

    if (projectOutlet) {
      await createReportOOSStatusHighlightMutation.mutateAsync({
        featureOosZoneId: data.zoneId as number,
        projectFeatureId: componentFeatureId,
        values: Object.entries(data)
          .filter(([key]) => key !== "zoneId")
          .map(([key, value]) => ({
            featureOosProductId: Number(key),
            value: _.isNumber(value) ? value : null,
          })),
      });

      showNotification({
        type: "success",
        message: "Cập nhật thành công",
      });

      cb();
    }
  }, [
    cb,
    componentFeatureId,
    createReportOOSStatusHighlightMutation,
    form,
    projectOutlet,
    showNotification,
  ]);

  useEffect(() => {
    if (projectOutlet?.projectOutletStocks?.length ?? 0 > 0) {
      const selectedZone = reportOOSStatusesZonesQuery.data?.entities.find(
        (zone) =>
          zone.code ===
          projectOutlet?.projectOutletStocks?.[0].featureOosZone?.code,
      );

      form.setFieldValue("zoneId", selectedZone?.id);

      outletStockProductsQuery.data?.entities.forEach((product) => {
        const projectOutletStock =
          projectOutlet?.projectOutletStocks?.[0]?.projectOutletStockDetails?.find(
            (item) =>
              item.featureOosProduct?.projectProductId ===
              product.projectProductId,
          );

        form.setFieldValue(product.id, projectOutletStock?.quantity);
      });
    }
  }, [
    form,
    outletStockProductsQuery.data?.entities,
    projectOutlet?.projectOutletStocks,
    reportOOSStatusesZonesQuery.data,
  ]);

  const loading = useMemo(
    () => createReportOOSStatusHighlightMutation.isPending,
    [createReportOOSStatusHighlightMutation.isPending],
  );

  return (
    <CustomModal
      title={"Cập nhật hàng tồn"}
      isOpen={true}
      content={content}
      confirmText="Cập nhật"
      width={900}
      onConfirm={confirm}
      onCancel={cancelCb}
      confirmLoading={loading}
    />
  );
};

export default ReportOutOfStockStatusHighlightModal;
