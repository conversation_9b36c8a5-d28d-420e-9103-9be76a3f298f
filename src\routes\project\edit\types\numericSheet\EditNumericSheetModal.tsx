import { CURD } from "@/common/constant";
import CustomModal from "@/components/CustomModal";
import CustomTable from "@/components/CustomTable/CustomTable";
import FormNumberInput from "@/components/FormNumberInput";
import {
  FeatureNumericAttributeInterface,
  FeatureNumericAttributeTypeEnum,
} from "@/routes/project/component/feature/config/types/numericSheet/interface";
import { FeatureTypeEnum } from "@/routes/project/component/feature/interface";
import { ToolSettingSettingTypeEnum } from "@/routes/project/config/interface";
import {
  useProjectToolAgencyDetailQuery,
  useToolSettingsQuery,
} from "@/routes/project/config/service";
import { RecordNumericValueInterface } from "@/routes/project/report/types/numericSheet/interface";
import { SearchOutlined } from "@ant-design/icons";
import { Form, Input } from "antd";
import dayjs from "dayjs";
import _ from "lodash";
import { useCallback, useMemo, useState } from "react";
import { v4 as uuidv4 } from "uuid";
import { useCreateAttendanceNumericSheetMutation } from "./service";

interface EditNumericSheetModalProps {
  title: string;
  cancelCb: () => void;
  projectId: number;
  attendanceId: number;
  componentFeatureId: number;
  action: CURD;
  recordNumericValues: RecordNumericValueInterface[];
  featureNumericAttributes: FeatureNumericAttributeInterface[];
  projectAgencyId?: number;
  createdAt?: string;
  cb?: () => void;
}
const EditNumericSheetModal = ({
  title,
  cancelCb,
  projectId,
  attendanceId,
  componentFeatureId,
  action,
  recordNumericValues,
  featureNumericAttributes,
  projectAgencyId,
  createdAt,
  cb,
}: EditNumericSheetModalProps) => {
  const [form] = Form.useForm();
  const [, setDebouncedSearchTerm] = useState("");

  const projectToolAgencyDetailQuery = useProjectToolAgencyDetailQuery(
    projectId,
    projectAgencyId,
  );
  const toolSettingsQuery = useToolSettingsQuery(
    projectToolAgencyDetailQuery.data?.id ?? 0,
  );

  const createAttendanceNumericSheetMutation =
    useCreateAttendanceNumericSheetMutation(
      projectId,
      attendanceId,
      componentFeatureId,
    );

  const debouncedSetSearchTerm = useCallback((value: string) => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(value);
    }, 300);

    return () => clearTimeout(timer);
  }, []);

  const isDisallowEditNullValue = useMemo(() => {
    const toolSetting = toolSettingsQuery.data?.find(
      (item) =>
        item.featureType === FeatureTypeEnum.NumericSheet &&
        item.settingType === ToolSettingSettingTypeEnum.READONLY_IF_NULL,
    );
    return toolSetting?.enabled ?? false;
  }, [toolSettingsQuery.data]);

  const isDisallowEditZeroValue = useMemo(() => {
    const toolSetting = toolSettingsQuery.data?.find(
      (item) =>
        item.featureType ===
          FeatureTypeEnum.MultipleEntitiesQuantityCapturing &&
        item.settingType === ToolSettingSettingTypeEnum.READONLY_IF_ZERO,
    );
    return toolSetting?.enabled ?? false;
  }, [toolSettingsQuery.data]);

  const dataSource = useMemo(() => {
    const groupRecordNumericValues = _.groupBy(
      recordNumericValues,
      "featureNumeric.id",
    );

    return Object.entries(groupRecordNumericValues).map(
      ([, groupRecordNumericValues]) => {
        const { projectItem, projectProduct } =
          groupRecordNumericValues[0].featureNumeric;

        const name = projectItem?.item?.name ?? projectProduct?.product?.name;

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const result: { [key: string]: any } = {};

        groupRecordNumericValues.forEach((item) => {
          result[`id_${item.featureNumericAttributeId}`] = item.value;
          result[`featureNumericId`] = item.featureNumericId;
        });

        return {
          name,
          ...result,
        };
      },
    );
  }, [recordNumericValues]);

  const content = (
    <Form layout="vertical" form={form}>
      <Form.Item>
        <Input
          prefix={<SearchOutlined />}
          placeholder="Tên"
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
            const value = e.target.value;
            debouncedSetSearchTerm(value);
          }}
        />
      </Form.Item>

      <CustomTable
        columns={[
          {
            title: "Tên sản phẩm",
            dataIndex: "name",
          },
          ...featureNumericAttributes.map((item) => ({
            title: item.name,
            dataIndex: `id_${item.id}`,
            key: `id_${item.id}`,

            render: (
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              value: any,
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              { featureNumericId }: any,
            ) => {
              return (
                <Form.Item
                  name={`id_${featureNumericId}_${item.id}`}
                  initialValue={value}
                >
                  <FormNumberInput
                    className="w-full"
                    disabled={
                      (isDisallowEditZeroValue && value === 0) ||
                      (isDisallowEditNullValue && value === null)
                    }
                    hasDot={
                      item.type === FeatureNumericAttributeTypeEnum.DECIMAL
                    }
                  />
                </Form.Item>
              );
            },
          })),
        ]}
        className="my-5"
        data={dataSource}
        scroll={undefined}
        pagination={false}
      />
    </Form>
  );

  const confirm = useCallback(async () => {
    console.log(form.getFieldsValue());
    const values = form.getFieldsValue();

    if (action === CURD.UPDATE) {
      const result: {
        featureNumericId: number;
        featureNumericAttributeId: number;
        value: number | null;
      }[] = [];
      Object.entries(values).forEach(([key, value]) => {
        const [, featureNumericId, featureNumericAttributeId] = key.split("_");

        result.push({
          featureNumericId: Number(featureNumericId),
          featureNumericAttributeId: Number(featureNumericAttributeId),
          value: value as number,
        });
      });

      await createAttendanceNumericSheetMutation.mutateAsync({
        dataUuid: uuidv4(),
        dataTimestamp: dayjs(createdAt).add(1, "second").toISOString() ?? "",
        values: result,
      });
    }

    cb?.();
  }, [action, cb, createAttendanceNumericSheetMutation, createdAt, form]);

  return (
    <CustomModal
      title={title}
      isOpen={true}
      content={content}
      onConfirm={confirm}
      confirmText="Lưu thông tin"
      onCancel={cancelCb}
      confirmLoading={createAttendanceNumericSheetMutation.isPending}
      width={800}
    />
  );
};

export default EditNumericSheetModal;
