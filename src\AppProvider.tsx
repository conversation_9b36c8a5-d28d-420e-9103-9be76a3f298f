import { useAuth0 } from "@auth0/auth0-react";
import { Modal, notification } from "antd";
import axios, { AxiosError } from "axios";
import React, {
  createContext,
  useCallback,
  useEffect,
  useMemo,
  useReducer,
  useState,
} from "react";
import {
  confirmModalReducer,
  deleteModalReducer,
  errorModalReducer,
  initialStateModal,
} from "./app.reducer.ts";
import { HOME_PAGE } from "./common/url.helper.ts";
import CustomModal from "./components/CustomModal.tsx";
import ModalDelete from "./components/ModalDelete.tsx";
import ModalError from "./components/ModalError.tsx";
import {
  AlertTypeInterface,
  AppContextInterface,
  ConfirmModalPropsInterface,
  DeleteModalPropsInterface,
  NotificationTypeInterface,
} from "./interface.ts";
import { UserInterface } from "./routes/user/interface.ts";

export const AppContext = createContext<AppContextInterface>(null!);

export const AppProvider = ({ children }: { children: React.ReactNode }) => {
  const [modal, contextHolder] = Modal.useModal();
  const { getAccessTokenSilently, logout } = useAuth0();
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [notificationApi, notificationContextHolder] =
    notification.useNotification();
  const [loading, setLoading] = useState(false);
  const [userLogin, setUserLogin] = useState<UserInterface | null>(null);

  const [deleteModalState, deleteModalDispatch] = useReducer(
    deleteModalReducer,
    initialStateModal,
  );

  const [errorModalState, errorModalDispatch] = useReducer(
    errorModalReducer,
    initialStateModal,
  );

  const [confirmModalState, confirmModalDispatch] = useReducer(
    confirmModalReducer,
    initialStateModal,
  );

  useEffect(() => {
    (async () => {
      setAccessToken(
        await getAccessTokenSilently({
          authorizationParams: {
            audience: import.meta.env.VITE_AUTH0_AUDIENCE,
          },
        }),
      );
    })();
  }, [getAccessTokenSilently]);

  const openDeleteModal = useCallback((data: DeleteModalPropsInterface) => {
    deleteModalDispatch({
      type: "OPEN_MODAL",
      data: data,
    });
  }, []);

  const openConfirmModal = useCallback((data: ConfirmModalPropsInterface) => {
    confirmModalDispatch({
      type: "OPEN_MODAL",
      data: data,
    });
  }, []);

  const axiosInstance = useMemo(
    () =>
      axios.create({
        baseURL: import.meta.env.VITE_API_URL,
        headers: { Authorization: `Bearer ${accessToken}` },
      }),
    [accessToken],
  );
  const showAlert = useCallback(
    (alert: AlertTypeInterface) => {
      const config = {
        title: alert.title,
        content: alert.message,
      };
      switch (alert.type) {
        case "info":
          modal.info(config);
          break;
        case "error":
          modal.error(config);
          break;
        case "success":
          modal.success(config);
          break;
        default:
      }
    },
    [modal],
  );

  const showNotification = useCallback(
    (notification: NotificationTypeInterface) => {
      const TIME_TO_CLOSE_SECOND = 3;
      const { message, type } = notification;
      const config = {
        message,
        placement: "bottom" as const,
        duration: TIME_TO_CLOSE_SECOND,
      };
      switch (type) {
        case "info":
          notificationApi.info(config);
          break;
        case "error":
          notificationApi.error(config);
          break;
        case "success":
          notificationApi.success(config);
          break;
        default:
      }
    },
    [notificationApi],
  );

  const handleAxiosError = useCallback(
    (error: AxiosError) => {
      interface ErrorResponse {
        message: string;
      }

      const data = error?.response?.data as ErrorResponse;
      const handleSignOut = () => {
        logout({ logoutParams: { returnTo: HOME_PAGE } });
      };

      if (error?.response?.status === 401) {
        handleSignOut();
      } else {
        if (error.code === "ERR_NETWORK") {
          showNotification({
            type: "error",
            message: error.message,
          });
          throw error;
        }

        if (
          data["message"] !== undefined &&
          (typeof data["message"] === "string" ||
            (Array.isArray(data["message"]) &&
              typeof data["message"][0] === "string"))
        ) {
          const message = Array.isArray(data["message"])
            ? data["message"][0]
            : data["message"];
          showNotification({
            type: "error",
            message: message,
          });
        }
      }

      setLoading(false);
      throw error;
    },
    [logout, showNotification],
  );

  const axiosGet = useCallback(
    function <E, P>(url: string, params: P): Promise<E> {
      return axiosInstance
        .get<E>(url, { params: params })
        .then((result) => {
          return result.data;
        })
        .catch(handleAxiosError);
    },
    [axiosInstance, handleAxiosError],
  );

  const axiosPost = useCallback(
    function <E, P>(url: string, body: P): Promise<E> {
      return axiosInstance
        .post<E>(url, body)
        .then((result) => {
          return result.data;
        })
        .catch(handleAxiosError);
    },
    [axiosInstance, handleAxiosError],
  );

  const axiosPatch = useCallback(
    function <E, P>(url: string, body: P): Promise<E> {
      return axiosInstance
        .patch<E>(url, body)
        .then((result) => {
          return result.data;
        })
        .catch(handleAxiosError);
    },
    [axiosInstance, handleAxiosError],
  );

  const axiosPut = useCallback(
    function <E, P>(url: string, body: P): Promise<E> {
      return axiosInstance
        .put<E>(url, body)
        .then((result) => {
          return result.data;
        })
        .catch(handleAxiosError);
    },
    [axiosInstance, handleAxiosError],
  );

  const axiosDelete = useCallback(
    function <E, P>(url: string, body?: P): Promise<E> {
      return axiosInstance
        .delete<E>(url, { data: body })
        .then((result) => {
          return result.data;
        })
        .catch(handleAxiosError);
    },
    [axiosInstance, handleAxiosError],
  );

  const appContextValue: AppContextInterface = useMemo(
    () => ({
      showAlert,
      axiosInstance,
      axiosGet,
      axiosPost,
      axiosPatch,
      axiosPut,
      axiosDelete,
      showNotification,
      loading,
      setLoading,
      userLogin,
      setUserLogin,
      openDeleteModal,
      openConfirmModal,
    }),
    [
      showAlert,
      axiosInstance,
      axiosGet,
      axiosPost,
      axiosPatch,
      axiosPut,
      axiosDelete,
      showNotification,
      loading,
      userLogin,
      openDeleteModal,
      openConfirmModal,
    ],
  );
  return (
    <AppContext.Provider value={appContextValue}>
      {children}
      {contextHolder}
      {notificationContextHolder}

      <ModalDelete
        title={deleteModalState.data?.title ?? ""}
        isOpen={deleteModalState.isOpen}
        loading={deleteModalState.data?.loading ?? false}
        onDelete={async () => {
          deleteModalDispatch({ type: "SET_LOADING" });
          try {
            await deleteModalState.data?.onDelete();
            deleteModalDispatch({
              type: "CLOSE_MODAL",
              data: undefined,
            });
          } catch (error) {
            if (error instanceof AxiosError) {
              const data = error?.response?.data;
              if (data["message"] !== undefined) {
                const errorMessages = Array.isArray(data["message"])
                  ? data["message"]
                  : [data["message"]];

                errorModalDispatch({
                  type: "OPEN_MODAL",
                  data: {
                    title: deleteModalState.data?.titleError ?? "",
                    content: (
                      <>
                        <p>{deleteModalState.data?.contentHeader}</p>
                        <ul>
                          {errorMessages.map((message, index) => (
                            <li key={index}>{message}</li>
                          ))}
                        </ul>
                      </>
                    ),
                  },
                });

                deleteModalDispatch({
                  type: "CLOSE_MODAL",
                  data: undefined,
                });
              }
            }
          }

          deleteModalDispatch({ type: "SET_LOADING_FALSE" });
        }}
        onCancel={() => {
          deleteModalState.data?.onCancel();
          deleteModalDispatch({
            type: "CLOSE_MODAL",
            data: undefined,
          });
        }}
        deleteText={deleteModalState.data?.deleteText ?? ""}
        content={deleteModalState.data?.content ?? <></>}
      />

      <ModalError
        title={errorModalState.data?.title ?? ""}
        isOpen={errorModalState.isOpen}
        onCancel={() => {
          errorModalDispatch({ type: "CLOSE_MODAL" });
        }}
        content={errorModalState.data?.content ?? <></>}
      />

      <CustomModal
        title={confirmModalState?.data?.title}
        isOpen={confirmModalState.isOpen}
        content={confirmModalState?.data?.content}
        onCancel={() => {
          confirmModalState?.data?.onCancel();
          confirmModalDispatch({ type: "CLOSE_MODAL" });
        }}
        onConfirm={async () => {
          confirmModalDispatch({ type: "SET_LOADING" });
          setLoading(true);

          try {
            await confirmModalState?.data?.onConfirm();

            confirmModalDispatch({ type: "CLOSE_MODAL" });
          } catch (error) {
            /**/
            console.error(error);
          } finally {
            confirmModalDispatch({ type: "SET_LOADING_FALSE" });
            setLoading(false);
          }
        }}
        confirmText={"Xác nhận"}
        confirmLoading={confirmModalState?.data?.loading ?? false}
      />
    </AppContext.Provider>
  );
};
