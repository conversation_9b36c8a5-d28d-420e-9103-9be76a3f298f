import { useApp } from "@/UseApp";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  FeatureCustomerDataTypeEnum,
  FeatureCustomerInterface,
} from "../../interface";

export const useFeatureCustomersQuery = (componentFeatureId: number) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["featureCustomers", componentFeatureId],
    queryFn: () =>
      axiosGet<
        { entities: FeatureCustomerInterface[]; count: number },
        unknown
      >(`/features/${componentFeatureId}/customers`, { take: 0, skip: 0 }),
  });
};

export const useCreateFeatureCustomerMutation = (
  componentFeatureId: number,
) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createFeatureCustomer", componentFeatureId],
    mutationFn: (data: {
      name: string;
      description?: string;
      dataType: FeatureCustomerDataTypeEnum;
      isRequired: boolean;
      isIdentity: boolean;
      options?: { name: string }[];
    }) => axiosPost(`/features/${componentFeatureId}/customers`, data),
  });
};

export const useUpdateFeatureCustomerMutation = (
  componentFeatureId: number,
) => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["updateFeatureCustomer", componentFeatureId],
    mutationFn: (data: {
      id: number;
      name?: string;
      description?: string;
      isActive?: boolean;
    }) =>
      axiosPatch(`/features/${componentFeatureId}/customers/${data.id}`, data),
  });
};

export const useDeleteFeatureCustomerMutation = (
  componentFeatureId: number,
) => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deleteFeatureCustomer", componentFeatureId],
    mutationFn: (id: number) =>
      axiosDelete(`/features/${componentFeatureId}/customers/${id}`),
  });
};

export const useArrangementFeatureCustomerMutation = (
  componentFeatureId: number,
) => {
  const { axiosPut } = useApp();

  return useMutation({
    mutationKey: ["arrangementFeatureCustomer", componentFeatureId],
    mutationFn: (data: { id: number; overFeatureCustomerId: number }) =>
      axiosPut(
        `/features/${componentFeatureId}/customers/${data.id}/arrangement`,
        data,
      ),
  });
};
