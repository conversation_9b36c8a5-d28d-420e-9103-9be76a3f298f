import {
  CURD,
  DEFAULT_CURRENT_PAGE,
  DEFAULT_PAGE_SIZE,
} from "@/common/constant";
import { filterOption } from "@/common/helper";
import CustomTable from "@/components/CustomTable/CustomTable.tsx";
import FilterClassicComponent from "@/components/FilterClassicComponent";
import { renderTableCell } from "@/components/table-cell";
import UserOptionComponent from "@/components/UserOptionComponent";
import { UserInterface } from "@/routes/user/interface";
import { SearchOutlined } from "@ant-design/icons";
import AddEmployeeToProjectModal from "@project/employee/AddEmployeeToProjectModal.tsx";
import EmployeeLeaderTabModal from "@project/employee/tabs/employee-leader-tab/EmployeeLeaderTabModal.tsx";
import { useProjectAgenciesQuery } from "@project/general/services";
import { ProjectAgencyInterface } from "@project/interface";
import { RoleInterface } from "@project/role/interface";
import { Form, Input, Select } from "antd";
import { ColumnsType } from "antd/es/table";
import _ from "lodash";
import { useCallback, useEffect, useMemo, useState } from "react";
import ProjectEmployeeLeaderEmployeeModal from "../../employee-leader/modal/ProjectEmployeeLeaderEmployeeModal";
import {
  EmployeeFilterInterface,
  ProjectEmployeeUserInterface,
} from "../../interface";
import { useEmployeesQuery } from "../../service";
import EmployeeLeaderActionCell from "./EmployeeLeaderActionCell.tsx";
import ImportEmployeeLeaderModal from "./ImportEmployeeLeaderModal.tsx";

const EmployeeLeaderTab = (props: {
  role: RoleInterface;
  cb: () => Promise<void>;
  selectedTab: string;
  projectId: number;
}) => {
  const { role, cb, selectedTab, projectId } = props;

  const [searchForm] = Form.useForm();
  const [action, setAction] = useState<CURD | null>(null);
  const [isFormLeaderSearchOpen, setIsFormLeaderSearchOpen] =
    useState<boolean>(false);

  const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [isModalAddEmployeeOpen, setIsModalAddEmployeeOpen] = useState(false);

  const [isOpenImport, setIsOpenImport] = useState(false);
  const [filter, setFilter] = useState<EmployeeFilterInterface>({});

  const employeesQuery = useEmployeesQuery(projectId, {
    ...filter,
    take: pageSize,
    skip: (currentPage - 1) * pageSize,
    roleId: role.id,
  });
  const projectAgenciesQuery = useProjectAgenciesQuery(projectId);

  const searchHandler = useCallback(() => {
    const values = searchForm.getFieldsValue();
    setCurrentPage(DEFAULT_CURRENT_PAGE);
    if (_.isEqual(filter, values)) {
      employeesQuery.refetch();
      return;
    }
    setFilter(values);
  }, [employeesQuery, filter, searchForm]);

  useEffect(() => {
    if (selectedTab === role.name) {
      searchForm.resetFields();
      searchHandler();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedTab]);

  const searchContent = (
    <>
      <Form.Item name="keyword">
        <Input
          placeholder="Tìm theo tên, sđt, email, username"
          prefix={<SearchOutlined />}
          className={"h-10"}
          allowClear
        />
      </Form.Item>
      <Form.Item name="projectAgencyId">
        <Select
          allowClear
          placeholder="Agency phụ trách"
          showSearch
          optionFilterProp="children"
          filterOption={filterOption}
          options={projectAgenciesQuery.data?.map((projectAgency) => ({
            label: projectAgency.agency.name,
            value: projectAgency.id,
          }))}
          className={"h-10"}
          popupMatchSelectWidth={false}
        />
      </Form.Item>
      <Form.Item name="isActive">
        <Select
          allowClear
          placeholder="Tình trạng"
          optionFilterProp="children"
          filterOption={filterOption}
          options={[
            {
              label: "Đang hoạt động",
              value: "true",
            },
            {
              label: "Ngừng hoạt động",
              value: "false",
            },
          ]}
          className={"h-10"}
          popupMatchSelectWidth={false}
        />
      </Form.Item>
    </>
  );

  const columns: ColumnsType<ProjectEmployeeUserInterface> = [
    {
      title: "Nhân viên",
      dataIndex: "user",
      key: "user.name",
      render: (value: UserInterface) => {
        return (
          <UserOptionComponent
            avatarUrl={value.picture}
            name={value.name}
            phone={value.phone}
            email={value.email}
            isAvailable={true}
          />
        );
      },
    },
    {
      title: "Mã nhân viên",
      dataIndex: "user",
      key: "user.code",
      render: (value: UserInterface) => value.code,
      className: "min-w-[100px]",
    },
    {
      title: "Username",
      dataIndex: "user",
      key: "user.username",
      render: (value: UserInterface) => value.username,
      className: "min-w-[100px]",
    },
    {
      title: "Agency phụ trách",
      dataIndex: "projectAgency",
      key: "projectAgency",
      render: (value?: ProjectAgencyInterface) => value?.agency?.name,
      className: "min-w-[100px]",
    },
    {
      title: "Outlet quản lý",
      dataIndex: "projectOutletsCount",
      key: "projectOutletsCount",
      align: "right",
      render: (value: number) => value,
      className: "min-w-[100px]",
    },
    {
      title: "Thành viên trong nhóm",
      dataIndex: "projectEmployeeUsersCount",
      key: "projectEmployeeUsersCount",
      align: "right",
      render: (value: number) => value,
      className: "min-w-[100px]",
    },
    {
      title: "Tình trạng",
      dataIndex: "isActive",
      key: "isActive",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "isActive");
      },
    },
    {
      key: "actions",
      render: (_, record) => (
        <EmployeeLeaderActionCell
          record={record}
          role={role}
          cb={() => {
            employeesQuery.refetch();
          }}
        />
      ),
      fixed: "right",
      width: 100,
    },
  ];

  const handlePaginationChange = useCallback(
    (page: number) => {
      setCurrentPage(page);
    },
    [setCurrentPage],
  );

  const handlePageSizeChange = useCallback(
    (_current: number, size: number) => {
      setPageSize(size);
      setCurrentPage(DEFAULT_CURRENT_PAGE);
    },
    [setPageSize, setCurrentPage],
  );

  const pagination = useMemo(() => {
    return {
      current: currentPage,
      total: employeesQuery.data?.count,
      pageSize: pageSize,
      onChange: handlePaginationChange,
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: handlePageSizeChange,
    };
  }, [
    currentPage,
    employeesQuery.data?.count,
    pageSize,
    handlePaginationChange,
    handlePageSizeChange,
  ]);

  return (
    <div>
      <FilterClassicComponent
        searchHandler={searchHandler}
        searchForm={searchForm}
        handleExcelButtonClick={function (): void {
          throw new Error("Function not implemented.");
        }}
        handleAddButtonClick={function (): void {
          setIsFormLeaderSearchOpen(true);
        }}
        content={searchContent}
        className={"mb-6"}
        showAddButton={true}
        btnLoading={employeesQuery.isFetching}
        onImportClick={() => setIsOpenImport(true)}
        disableAddButton={!role.isActive}
      />

      <CustomTable
        dataSource={employeesQuery.data?.entities}
        columns={columns}
        scroll={{
          x: "max-content",
          y: pagination.total ? "80vh" : undefined,
        }}
        pagination={pagination}
        loading={employeesQuery.isFetching}
      />

      {isFormLeaderSearchOpen && (
        <AddEmployeeToProjectModal
          role={role}
          isOpen={isFormLeaderSearchOpen}
          projectId={projectId}
          cb={() => {
            setIsFormLeaderSearchOpen(false);
            employeesQuery.refetch();
            cb();
          }}
          notFoundCb={() => {
            setIsFormLeaderSearchOpen(false);
            setAction(CURD.CREATE);
          }}
          cancelCb={() => {
            setIsFormLeaderSearchOpen(false);
          }}
        />
      )}

      {!!action && (
        <EmployeeLeaderTabModal
          action={action}
          role={role}
          cb={() => {
            setAction(null);
            employeesQuery.refetch();
            cb();
          }}
          cancelCb={() => setAction(null)}
        />
      )}

      {isModalAddEmployeeOpen && (
        <ProjectEmployeeLeaderEmployeeModal
          isOpen={isModalAddEmployeeOpen}
          setIsOpen={setIsModalAddEmployeeOpen}
          projectId={projectId}
          cb={() => employeesQuery.refetch()}
        />
      )}

      <ImportEmployeeLeaderModal
        isOpen={isOpenImport}
        role={role}
        projectAgencies={projectAgenciesQuery.data ?? []}
        setIsOpen={setIsOpenImport}
        projectId={projectId}
      />
    </div>
  );
};

export default EmployeeLeaderTab;
