import { AbstractFilterInterface } from "@/common/interface";
import { useApp } from "@/UseApp";
import { useMutation, useQuery } from "@tanstack/react-query";
import { ProductPackagingInterface } from "../../product/interface";
import { ApiProjectProductResponseInterface } from "./interface";

export const useProjectProductsQuery = (
  projectId: number,
  filter?: object & AbstractFilterInterface,
  enabled?: boolean,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["projectProducts", projectId, filter],
    queryFn: async () =>
      axiosGet<ApiProjectProductResponseInterface, unknown>(
        `projects/${projectId}/products`,
        filter,
      ),
    enabled,
  });
};

export const useUpdateProjectProductPriceMutation = (
  projectId: number,
  projectProductId: number,
) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["updateProjectProductPrice", projectId, projectProductId],
    mutationFn: (price: number) =>
      axiosPost(`projects/${projectId}/products/${projectProductId}/prices`, {
        price,
      }),
  });
};

export const useProjectProductsAvailablesQuery = (
  projectId: number,
  filter: AbstractFilterInterface & object,
  enabled?: boolean,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["projectProductsAvailables", projectId, filter],
    queryFn: () =>
      axiosGet<
        { entities: ProductPackagingInterface[]; count: number },
        unknown
      >(`/projects/${projectId}/products/availables`, filter),
    enabled,
  });
};

export const useCreateProjectProductMutation = (projectId: number) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createProjectProduct", projectId],
    mutationFn: (data: { productPackagingIds: number[] }) =>
      axiosPost(`projects/${projectId}/products`, data),
  });
};

export const useFindProjectProductMutation = (projectId: number) => {
  const { axiosGet } = useApp();
  return useMutation({
    mutationKey: ["findProjectProduct", projectId],
    mutationFn: (filter: AbstractFilterInterface & { brandId?: number }) =>
      axiosGet<ApiProjectProductResponseInterface, unknown>(
        `projects/${projectId}/products`,
        filter,
      ),
  });
};
