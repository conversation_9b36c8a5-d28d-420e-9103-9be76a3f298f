import { randomColor } from "@/common/helper";
import Chart<PERSON>ontanier from "@/routes/project/chart/ChartContanier";
import { Pie } from "@ant-design/charts";
import { Col, Form, Row } from "antd";
import dayjs from "dayjs";
import _ from "lodash";
import { useCallback, useMemo, useState } from "react";
import ReactApexChart from "react-apexcharts";
import DashboardFilterZone from "../../DashboardFilterZone";
import { DashboardFilterInterface } from "../../interface";
import { useNumericSheetTotalQuery } from "./service";

interface NumericSheetTabProps {
  projectId: number;
  dashboardId: number;
}
const NumericSheetTab = ({ projectId, dashboardId }: NumericSheetTabProps) => {
  const [form] = Form.useForm();
  const [filter, setFilter] = useState<DashboardFilterInterface>({});

  const numericSheetTotalQuery = useNumericSheetTotalQuery(
    projectId,
    dashboardId,
    filter,
  );

  const handleApply = useCallback(() => {
    const values = form.getFieldsValue();

    const {
      dateSingle,
      projectRegionIds,
      projectOutletIds,
      channelIds,
      provinceIds,
      leaderIds,
    } = values;

    const filterValue = {
      startDate: dayjs(dateSingle).startOf("date").toISOString(),
      endDate: dayjs(dateSingle).endOf("date").toISOString(),
      projectRegionIds,
      projectOutletIds,
      channelIds,
      provinceIds,
      leaderIds: leaderIds?.map((item: { value: number }) => item.value),
    };

    if (_.isEqual(filter, filterValue)) {
      numericSheetTotalQuery.refetch();
    } else {
      setFilter(filterValue);
    }
  }, [filter, form, numericSheetTotalQuery]);

  const pieData = useMemo(() => {
    const result = [];
    const group = _.groupBy(numericSheetTotalQuery.data ?? [], "name");

    for (const [key, value] of Object.entries(group)) {
      result.push({
        name: key,
        total: value.reduce((acc, cur) => acc + cur.total, 0),
        backgroundColor: value[0].backgroundColor,
      });
    }

    return result;
  }, [numericSheetTotalQuery.data]);
  const total = useMemo(
    () =>
      numericSheetTotalQuery.data?.reduce((acc, cur) => acc + cur.total, 0) ??
      0,
    [numericSheetTotalQuery.data],
  );
  const pieConfig = useMemo(
    () => ({
      appendPadding: 10,
      data: pieData,
      angleField: "total",
      colorField: "name",
      radius: 0.8,
      interactions: [
        {
          type: "element-active",
        },
      ],
      label: {
        text: (d: { total: number; name: string }) =>
          `${d.total}  (${Math.round((d.total / total) * 100)}%)`,
        position: "spider",
        fontSize: 12,
        fontWeight: "bold",
      },
      legend: {
        color: {
          position: "bottom",
          layout: {
            justifyContent: "center",
          },
        },
      },
      title: {
        title: "Dashboard PA instore total",
        subtitle: "Tỷ lệ PA giữa Sabeco và đối thủ",
      },
      tooltip: {
        field: "value",
        title: (d: { name: string }) => d.name,
        value: (d: { total: number }) => d.total,
      },
      scale: {
        color: {
          palette: pieData.map((item) => item.backgroundColor ?? randomColor()),
        },
      },
    }),
    [pieData, total],
  );

  const columnCategories = useMemo(() => {
    const group = _.groupBy(numericSheetTotalQuery.data ?? [], "name");

    return Object.keys(group);
  }, [numericSheetTotalQuery.data]);

  const columnSeries = useMemo(() => {
    const group = _.groupBy(
      numericSheetTotalQuery.data ?? [],
      "attribute.name",
    );

    return Object.entries(group).map(([key, values]) => ({
      name: key,
      data: values.map((value) => value.total),
      color: values[0].attribute.backgroundColor ?? undefined,
    }));
  }, [numericSheetTotalQuery.data]);

  const columnOptions = useMemo(
    () => ({
      title: {
        text: "Dashboard PA instore by location",
        style: {
          fontSize: "16px",
          fontWeight: "bold",
        },
      },
      subtitle: {
        text: "Số lượng PA giữa Sabeco và đối thủ",
        style: {
          fontSize: "12px",
        },
      },
      chart: {
        type: "bar" as const,
        stacked: true,
        toolbar: {
          show: false,
        },
      },
      responsive: [
        {
          breakpoint: 480,
          options: {
            legend: {
              position: "bottom" as const,
              offsetX: -10,
              offsetY: 0,
            },
          },
        },
      ],
      plotOptions: {
        bar: {
          horizontal: false,
          dataLabels: {
            total: {
              enabled: true,
              style: {
                fontSize: "13px",
                fontWeight: 900,
              },
            },
          },
        },
      },
      dataLabels: {
        enabled: true,
      },
      xaxis: {
        categories: columnCategories,
      },

      fill: {
        opacity: 1,
      },
    }),
    [columnCategories],
  );

  return (
    <>
      <DashboardFilterZone
        handleApply={handleApply}
        fields={[
          "date.single",
          "region",
          "province",
          "chain",
          "leader",
          "outlet",
        ]}
        form={form}
        projectId={projectId}
        setFilter={setFilter}
      />

      <Row gutter={[16, { xs: 16, sm: 16 }]}>
        <Col md={9} xs={24}>
          <ChartContanier>
            <Pie {...pieConfig} height={500} />
          </ChartContanier>
        </Col>

        <Col md={15} xs={24}>
          <ChartContanier>
            <ReactApexChart
              options={columnOptions}
              series={columnSeries}
              type="bar"
              height={491}
            />
          </ChartContanier>
        </Col>
      </Row>
    </>
  );
};

export default NumericSheetTab;
