import { useApp } from "@/UseApp.tsx";
import { CURD, SELECT_ALL, SELECT_ALL_LABEL } from "@/common/constant.ts";
import { formErrorResponseHandler } from "@/common/helper.ts";
import FilterComponent from "@/components/FilterComponent.tsx";
import ModalCURD from "@/components/ModalCURD.tsx";
import TableActionCell from "@/components/TableActionCell.tsx";
import {
  activeAction,
  inactiveAction,
} from "@/components/activeOrInactiveAction.ts";
import { renderTableCell } from "@/components/table-cell.tsx";
import {
  DeleteOutlined,
  EditOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined,
} from "@ant-design/icons";
import { Form, Input, Modal, Table } from "antd";
import { ColumnsType } from "antd/es/table";
import { useCallback, useEffect, useState } from "react";
import {
  ApiBoothResponseInterface,
  ProjectBoothInterface,
  ProjectConfigOutletBoothActionEnum,
} from "../interface.ts";

interface BoothTabProps {
  projectId: number;
}

const BoothTab = (props: BoothTabProps) => {
  const { projectId } = props;
  const {
    axiosGet,
    setLoading,
    showNotification,
    axiosPost,
    axiosPatch,
    axiosDelete,
    openDeleteModal,
  } = useApp();
  const [searchForm] = Form.useForm();
  const [data, setData] = useState<ProjectBoothInterface[]>([]);
  const [formAction, setFormAction] = useState<CURD | null>(null);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [form] = Form.useForm();
  const [modal, contextHolder] = Modal.useModal();

  const fetchData = useCallback(async () => {
    const response = await axiosGet<ApiBoothResponseInterface, unknown>(
      `/projects/${projectId}/booths`,
      searchForm.getFieldsValue(),
    );
    if (!Array.isArray(response)) {
      setData(response.entities);
    }
  }, [axiosGet, projectId, searchForm]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const onFinish = async () => {
    try {
      await form.validateFields();
      const data = form.getFieldsValue();
      const id = form.getFieldValue("id");
      setLoading(true);

      switch (formAction) {
        case CURD.CREATE:
          await axiosPost(`/projects/${projectId}/booths`, data);
          showNotification({
            type: "success",
            message: "Thêm booth cho outlet thành công",
          });
          break;
        case CURD.UPDATE:
          await axiosPatch(`/projects/${projectId}/booths/${id}`, data);
          showNotification({
            type: "success",
            message: "Cập nhật booth cho outlet thành công",
          });
          break;
        default:
          return;
      }

      form.resetFields();
      setIsOpen(false);
      fetchData();

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      formErrorResponseHandler(form, error);
    } finally {
      setLoading(false);
    }
  };

  const formContent = (
    <>
      <Form.Item
        label="Tên booth"
        name="name"
        rules={[
          {
            required: true,
            message: "Tên booth không được để trống.",
          },
        ]}
      >
        <Input />
      </Form.Item>
      <Form.Item name="description" label="Mô tả">
        <Input.TextArea />
      </Form.Item>
    </>
  );

  function searchHandler(): void {
    fetchData();
  }

  function handleExcelButtonClick(): void {
    throw new Error("Function not implemented.");
  }

  function handleAddButtonClick(): void {
    setFormAction(CURD.CREATE);
    setIsOpen(true);
  }

  const handleActionEditClick = async (record: ProjectBoothInterface) => {
    setIsOpen(true);
    setFormAction(CURD.UPDATE);
    form.setFieldsValue(record);
  };

  const handleActionInActiveClick = async (record: ProjectBoothInterface) => {
    inactiveAction({
      axiosPatch,
      cb: fetchData,
      modal,
      record,
      recordName: "booth",
      showNotification,
      url: `/projects/${projectId}/booths/${record.id}`,
    });
  };
  const handleActionActiveClick = async (record: ProjectBoothInterface) => {
    activeAction({
      axiosPatch,
      cb: fetchData,
      modal,
      record,
      recordName: "booth",
      showNotification,
      url: `/projects/${projectId}/booths/${record.id}`,
    });
  };
  const handleActionDeleteClick = async (record: ProjectBoothInterface) => {
    openDeleteModal({
      content: (
        <p>
          Bạn muốn xóa booth{" "}
          <span className={"font-semibold"}>{record.name}</span> khỏi dự án?
        </p>
      ),
      deleteText: "Xác nhận xóa",
      loading: false,
      onCancel(): void {},
      onDelete: async () => {
        await axiosDelete(`/projects/${projectId}/booths/${record.id}`);
        showNotification({
          type: "success",
          message: "Xóa booth thành công",
        });
        await fetchData();
      },
      title: `Xóa booth`,
      titleError: "Không thể xóa booth",
      contentHeader: (
        <>
          Không thể xóa booth{" "}
          <span className="font-semibold">{record.name}</span> khỏi dự án bởi
          vì:
        </>
      ),
    });
  };

  const actionActions = [
    {
      key: ProjectConfigOutletBoothActionEnum.EDIT,
      action: handleActionEditClick,
    },
    {
      key: ProjectConfigOutletBoothActionEnum.INACTIVE,
      action: handleActionInActiveClick,
    },
    {
      key: ProjectConfigOutletBoothActionEnum.ACTIVE,
      action: handleActionActiveClick,
    },
    {
      key: ProjectConfigOutletBoothActionEnum.DELETE,
      action: handleActionDeleteClick,
    },
  ];

  const actionItems = [
    {
      key: ProjectConfigOutletBoothActionEnum.EDIT,
      label: "Chỉnh sửa",
      icon: <EditOutlined />,
    },
    {
      key: ProjectConfigOutletBoothActionEnum.INACTIVE,
      label: "Ngừng hoạt động",
      icon: <PauseCircleOutlined />,
    },
    {
      key: ProjectConfigOutletBoothActionEnum.ACTIVE,
      label: "Hoạt động trở lại",
      icon: <PlayCircleOutlined />,
    },
    {
      key: ProjectConfigOutletBoothActionEnum.DELETE,
      label: "Xóa khỏi chương trình",
      icon: <DeleteOutlined />,
    },
  ];

  const ACTION_ACTIVE = [
    ProjectConfigOutletBoothActionEnum.INACTIVE,
    ProjectConfigOutletBoothActionEnum.DELETE,
    ProjectConfigOutletBoothActionEnum.EDIT,
  ];
  const ACTION_INACTIVE = [
    ProjectConfigOutletBoothActionEnum.ACTIVE,
    ProjectConfigOutletBoothActionEnum.DELETE,
    ProjectConfigOutletBoothActionEnum.EDIT,
  ];

  const columns: ColumnsType<ProjectBoothInterface> = [
    {
      title: "Tên booth",
      key: "name",
      dataIndex: "name",
      render: renderTableCell,
    },
    {
      title: "Mô tả",
      key: "description",
      dataIndex: "description",
      render: renderTableCell,
    },
    {
      key: "isActive",
      title: "Tình trạng",
      dataIndex: "isActive",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "isActive");
      },
    },
    {
      key: "actions",
      render: (_, record) => {
        const actionKeys = record.isActive ? ACTION_ACTIVE : ACTION_INACTIVE;
        const items = actionItems.filter((item) =>
          actionKeys.includes(item.key),
        );
        return (
          <TableActionCell
            actions={actionActions}
            items={items}
            record={record}
          />
        );
      },
      fixed: "right",
      width: 100,
    },
  ];

  const filterOptions = columns
    .filter(
      (item) =>
        !["isActive", "updatedAt", "createdAt", "actions"].includes(
          item.key as string,
        ),
    )
    .map((column) => {
      return {
        label: column.title as string,
        value: column.key as string | number,
      };
    });

  filterOptions.unshift({ label: SELECT_ALL_LABEL, value: SELECT_ALL });

  return (
    <div>
      <FilterComponent
        filterOptions={filterOptions}
        searchHandler={searchHandler}
        handleExcelButtonClick={handleExcelButtonClick}
        handleAddButtonClick={handleAddButtonClick}
        searchForm={searchForm}
        className="mb-6"
      />
      <Table
        columns={columns}
        dataSource={data}
        pagination={false}
        rowKey={"id"}
      />
      <p className={"pb-0 mb-0"}>Số kết quả trả về: {data.length}</p>

      <ModalCURD
        title={
          formAction === CURD.CREATE
            ? "Thêm booth cho outlet"
            : "Booth cho outlet"
        }
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        formContent={formContent}
        form={form}
        onFinish={onFinish}
        action={formAction}
      />

      {contextHolder}
    </div>
  );
};

export default BoothTab;
