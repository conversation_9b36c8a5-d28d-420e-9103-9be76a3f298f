export interface RoleInterface {
  id: number;
  name: string;
  description?: string;
  isActive: boolean;
  projectId: number;
  isLeader: boolean;
  isApplied?: boolean;
  isEnabled?: boolean;
  isNotAllowed?: boolean;
}

export interface ApiRoleResponseInterface {
  entities: RoleInterface[];
  count: number;
}

export enum ProjectRoleActionEnum {
  INACTIVE = "INACTIVE",
  ACTIVE = "ACTIVE",
  DELETE = "DELETE",
  EDIT = "EDIT",
}
