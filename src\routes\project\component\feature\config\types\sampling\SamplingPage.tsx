import CustomMenu from "@/components/customMenu/CustomMenu";
import { Col, Row, Skeleton } from "antd";
import { Suspense, useCallback, useEffect, useMemo, useState } from "react";
import { Outlet, useLocation, useNavigate } from "react-router-dom";
import "./style.css";

const SamplingPage = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const [activeKey, setActiveKey] = useState<string>("purchase");

  const items: {
    label: string;
    key: string;
  }[] = useMemo(() => {
    return [
      {
        key: "",
        label: "• Khai báo sampling",
      },
      {
        key: "outlet",
        label: "• Phân bổ outlet vào nhóm sampling",
      },
    ];
  }, []);

  const setRouteActive = useCallback(
    (value: string) => {
      navigate(value);
    },
    [navigate],
  );

  useEffect(() => {
    if (location.pathname.endsWith("sampling")) {
      setActiveKey("");
    }
    if (location.pathname.endsWith("outlet")) {
      setActiveKey("outlet");
    }
  }, [setActiveKey, location.pathname]);

  return (
    <div className="bg-white pt-5 pl-10 rounded pb-5 mt-5 pr-10">
      <Row className="" justify={"space-between"}>
        <Col span={4}>
          <CustomMenu
            className="bg-[#FAFAFA]"
            items={items}
            mode="inline"
            onClick={({ key }) => setRouteActive(key)}
            selectedKeys={[activeKey]}
          />
        </Col>
        <Col span={19}>
          <Suspense fallback={<Skeleton loading />}>
            <Outlet />
          </Suspense>
        </Col>
      </Row>
    </div>
  );
};

export default SamplingPage;
