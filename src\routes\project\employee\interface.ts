import { AbstractEntityInterface, ImageInterface } from "@/common/interface";
import {
  DistrictInterface,
  ProvinceInterface,
  WardInterface,
} from "@location/interface.ts";
import { UserInterface } from "../../user/interface";
import { ProjectAgencyInterface } from "../interface";
import { ProjectRecordInterface } from "../report/interface";
import { RoleInterface } from "../role/interface";

export interface ProjectEmployeeUserInterface extends AbstractEntityInterface {
  projectAgency?: ProjectAgencyInterface;
  user: UserInterface;
  isAvailable?: boolean;
  projectEmployeeUser?: ProjectEmployeeUserInterface;
  role: RoleInterface;
  projectEmployeeUserId?: number;
  projectAgencyId: number;
  roleId: number;
  projectRecord: ProjectRecordInterface;
  projectEmployeeUsersCount?: number;
  projectOutletsCount?: number;
}

export interface ApiProjectEmployeeUserResponseInterface {
  entities: ProjectEmployeeUserInterface[];
  count: number;
}

export enum EmployeeActionEnum {
  INACTIVE = "INACTIVE",
  ACTIVE = "ACTIVE",
  DELETE = "DELETE",
  EDIT = "EDIT",
  ADD_OUTLET = "ADD_OUTLET",
  VIEW_OUTLET = "VIEW_OUTLET",
  ADD_EMPLOYEE = "ADD_EMPLOYEE",
  VIEW_EMPLOYEE = "VIEW_EMPLOYEE",
  PROFILE = "PROFILE",
  CHANGE_LEADER = "CHANGE_LEADER",
}

export interface EmployeeFilterInterface {
  keyword?: string;
}

export interface ExperienceInterface extends AbstractEntityInterface {
  title: string;
  companyName: string;
  startedAt: string;
  endedAt: string;
  description: string;
  businessLine: string;
  leaveReason: string;
}

export interface ProfileImageInterface extends AbstractEntityInterface {
  type: string;
  image: ImageInterface;
}

export interface ProfileInterface extends AbstractEntityInterface {
  status: string;
  reason: string;
  reviewedAt: string;
  reviewedByUser: UserInterface;
  fullName: string;
  identityCardNumber: string;
  phoneNumber: string;
  email: string;
  gender: string;
  birthdate: string;
  birthplace: string;
  socialInsuranceNumber: string;
  personalTaxCode: string;
  maritalStatus: string;
  numberOfChildren: number;
  emergencyContactName: string;
  emergencyContactRelationship: string;
  emergencyContactPhoneNumber: string;
  emergencyContactAddress: string;
  permanentProvince: ProvinceInterface;
  permanentDistrict: DistrictInterface;
  permanentWard: WardInterface;
  permanentAddress: string;
  province: ProvinceInterface;
  district: DistrictInterface;
  ward: WardInterface;
  address: string;
  bodyHeight: number;
  bodyWeight: number;
  bodyBust: number;
  bodyWaist: number;
  bodyHips: number;
  shirtSize: string;
  pantsSize: string;
  dressSize: string;
  shoeSize: number;
  educationLevel: string;
  desiredPosition: string;
  desiredLocation: string;
  recruitmentSource: string;
  experiences?: ExperienceInterface[];
  userProfileExperiences?: ExperienceInterface[];
  photos?: ProfileImageInterface[];
  userProfilePhotos?: ProfileImageInterface[];
  bank?: BankInterface;
}

export interface BankInterface {
  id: number;
  code: string;
  name: string;
  shortName: string;
  logo: string;
}

export enum GenderEnum {
  MALE = "male",
  FEMALE = "female",
  OTHER = "other",
}

export const GENDER_MALE_LABEL = "Nam";
export const GENDER_FEMALE_LABEL = "Nữ";
export const GENDER_OTHER_LABEL = "Khác";

export const GENDER_ENUM_TO_LABEL = {
  [GenderEnum.MALE]: GENDER_MALE_LABEL,
  [GenderEnum.FEMALE]: GENDER_FEMALE_LABEL,
  [GenderEnum.OTHER]: GENDER_OTHER_LABEL,
};
