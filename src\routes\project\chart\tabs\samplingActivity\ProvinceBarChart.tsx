import { useCallback, useEffect, useMemo } from "react";
import <PERSON><PERSON>hart from "../../chart-types/BarChart";
import ChartContanier from "../../ChartContanier";
import { ChartDataInterface, StatisticsTypeEnum } from "../../interface";
import { useProvinceStatisticsQuery } from "../../service";

interface ProvinceBarChartProps {
  projectId: number;
  type: StatisticsTypeEnum;
  filter?: { provinceIds?: number[]; channelIds?: number[] };
  sort: "asc" | "desc" | undefined;
  title: string;
  activeKey: string;
  ratio?: number;
}

const ProvinceBarChart = ({
  projectId,
  type,
  filter,
  sort,
  title,
  ratio,
  activeKey,
}: ProvinceBarChartProps) => {
  const provinceStatisticsQuery = useProvinceStatisticsQuery(
    projectId,
    type,
    filter,
    true,
  );

  const toThreeItems = useCallback(
    (item: ChartDataInterface, labelKey: "channelName" | "provinceName") => {
      const { kpi, totalValue, todayValue, rollingTarget } = item;
      return [
        {
          label: item[labelKey],
          type: "KPI",
          value: kpi,
          KPI: kpi,
          UTD: totalValue,
          Today: todayValue,
        },
        {
          label: item[labelKey],
          type: "Rolling Target",
          value: rollingTarget,
          KPI: kpi,
          UTD: totalValue,
          Today: todayValue,
        },
        {
          label: item[labelKey],
          type: "UTD",
          value: totalValue,
          KPI: kpi,
          UTD: totalValue,
          Today: todayValue,
        },
      ];
    },
    [],
  );

  const statistics = useMemo(() => {
    const data = provinceStatisticsQuery.data ?? [];
    if (sort === undefined)
      return data.flatMap((item) => toThreeItems(item, "provinceName"));

    const sortedData = [...data];
    if (sort === "asc") {
      sortedData.sort((a, b) => a.totalValue - b.totalValue);
    } else {
      sortedData.sort((a, b) => b.totalValue - a.totalValue);
    }

    return sortedData.flatMap((item) => toThreeItems(item, "provinceName"));
  }, [provinceStatisticsQuery.data, sort, toThreeItems]);

  useEffect(() => {
    if (activeKey === "SAMPLING/ACTIVITIES") {
      provinceStatisticsQuery.refetch();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeKey]);

  const defaultRatio = useMemo(
    () => (statistics.length > 50 ? 0.3 : 0.5),
    [statistics.length],
  );

  return (
    <ChartContanier>
      <BarChart
        title={title}
        subtitle="Subtitle"
        data={statistics}
        xField="label"
        yField="value"
        loading={
          provinceStatisticsQuery.isLoading ||
          provinceStatisticsQuery.isFetching
        }
        colorField="type"
        ratio={ratio ?? defaultRatio}
      />
    </ChartContanier>
  );
};

export default ProvinceBarChart;
