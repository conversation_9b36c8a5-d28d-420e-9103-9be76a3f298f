import { useMemo } from "react";
import { useParams } from "react-router-dom";
import { useProjectQuery } from "../services";

const ProjectDashboardPage = () => {
  const projectId = parseInt(useParams().id ?? "0");

  const projectQuery = useProjectQuery(projectId);

  const projectDashboards = useMemo(
    () => projectQuery.data?.projectDashboards ?? [],
    [projectQuery.data?.projectDashboards],
  );

  if (projectDashboards.length === 0) {
    return <></>;
  }

  return (
    <iframe
      className="mt-10 h-[calc(100vh-10rem)] w-full"
      title="project-dashboard"
      src={projectDashboards[0]?.link}
      frameBorder={0}
      allowFullScreen={true}
    ></iframe>
  );
};

export default ProjectDashboardPage;
