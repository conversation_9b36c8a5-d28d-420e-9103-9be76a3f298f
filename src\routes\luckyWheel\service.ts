import { useMutation, useQuery } from "@tanstack/react-query";
import axios from "axios";
import { CustomerDataInterface } from "./interface";

const BASE_URL = import.meta.env.VITE_API_URL;

export const useLuckyDrawsOfOrderQuery = (
  token: string,
  attendanceId: number,
  orderId: number,
) => {
  return useQuery({
    queryKey: ["luckyDrawsOfOrder", attendanceId, orderId, token],
    queryFn: async () => {
      const result = await axios.get<CustomerDataInterface>(
        `${BASE_URL}/app/attendances/${attendanceId}/orders/${orderId}/lucky-draws`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );
      return result.data;
    },
    enabled: !!token && !!attendanceId && !!orderId,
  });
};

export const useCreateLuckyDrawsOfOrderMutation = (
  token: string,
  attendanceId: number,
  orderId: number,
) => {
  return useMutation({
    mutationKey: ["createLuckyDrawsOfOrder", attendanceId, orderId, token],
    mutationFn: async (luckyDrawId: number) => {
      const result = await axios.post<{
        createdBy: string | null;
        updatedBy: string | null;
        deletedBy: string | null;
        projectLuckyDrawId: number;
        projectLuckyDrawItemId: number;
        projectLuckyDrawAllocationId: number;
        projectCustomerId: string | null;
        availableTurns: number;
        prizeItemId: number;
      }>(
        `${BASE_URL}/app/attendances/${attendanceId}/orders/${orderId}/lucky-draws`,
        {
          luckyDrawId,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      return result.data;
    },
  });
};
