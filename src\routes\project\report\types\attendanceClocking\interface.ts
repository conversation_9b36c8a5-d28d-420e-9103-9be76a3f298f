import { AbstractEntityInterface, ImageInterface } from "@/common/interface.ts";
import { ProjectRecordEmployeeInterface } from "../../interface";

export interface AttendanceInterface {
  latitude: number;
  longitude: number;
  deviceId?: string;
  deviceTime: string;
  image: ImageInterface;
}

export interface RecordAttendanceInterface extends AbstractEntityInterface {
  projectRecordEmployee: ProjectRecordEmployeeInterface;
  in: AttendanceInterface;
  out: AttendanceInterface;
  auto: boolean;
  timeIn: string;
  timeOut: string;
}
