import { DEFAULT_CURRENT_PAGE, DEFAULT_PAGE_SIZE } from "@/common/constant";
import { formatMoney } from "@/common/helper";
import BarcodeView from "@/components/BarcodeView.tsx";
import CustomTable from "@/components/CustomTable/CustomTable.tsx";
import FilterComponent from "@/components/FilterComponent";
import InnerContainer from "@/components/InnerContainer/InnerContainer.tsx";
import ProductItemCell from "@/components/ProductItemCell.tsx";
import { Form, TableColumnsType } from "antd";
import _ from "lodash";
import { useCallback, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import {
  ProductInterface,
  ProductPackagingInterface,
} from "../../product/interface";
import { ProjectProductInterface } from "./interface";
import ModalProjectProductAvailables from "./ModalProjectProductAvailables.tsx";
import { useProjectProductsQuery } from "./service";

export default function ProjectProductPage() {
  const projectId = parseInt(useParams().id ?? "0");

  const [searchForm] = Form.useForm();
  const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [isOpen, setIsOpen] = useState(false);
  const [filter, setFilter] = useState({});

  const projectProductsQuery = useProjectProductsQuery(projectId, {
    ...filter,
    take: pageSize,
    skip: (currentPage - 1) * pageSize,
  });

  const handlePaginationChange = useCallback(
    (page: number) => {
      setCurrentPage(page);
    },
    [setCurrentPage],
  );

  const handlePageSizeChange = useCallback(
    (_current: number, size: number) => {
      setPageSize(size);
      setCurrentPage(DEFAULT_CURRENT_PAGE);
    },
    [setPageSize, setCurrentPage],
  );

  const pagination = useMemo(() => {
    return {
      current: currentPage,
      total: projectProductsQuery.data?.count ?? 0,
      pageSize: pageSize,
      onChange: handlePaginationChange,
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: handlePageSizeChange,
    };
  }, [
    currentPage,
    projectProductsQuery.data?.count,
    pageSize,
    handlePaginationChange,
    handlePageSizeChange,
  ]);

  const searchHandler = useCallback(() => {
    setCurrentPage(DEFAULT_CURRENT_PAGE);
    const values = searchForm.getFieldsValue();
    if (_.isEqual(filter, values)) {
      projectProductsQuery.refetch();
    }
    setFilter(values);
  }, [filter, projectProductsQuery, searchForm]);

  const columns: TableColumnsType<ProjectProductInterface> = [
    {
      title: "Tên sản phẩm",
      dataIndex: "product",
      key: "product",
      render: (product: ProductInterface) => {
        return (
          <ProductItemCell
            variants={product?.image?.variants ?? []}
            name={product.name}
          />
        );
      },
      className: "min-w-[100px]",
    },
    {
      title: "Mã sản phẩm",
      dataIndex: "product",
      key: "product.code",
      render: (product: ProductInterface) => product.code,
      className: "min-w-[100px]",
    },
    {
      title: "Barcode",
      dataIndex: "productPackaging",
      key: "productPackaging.barcode",
      render: (value: ProductPackagingInterface) => {
        return <BarcodeView packaging={value} />;
      },
      className: "min-w-[100px]",
    },
    {
      title: "Nhãn hàng",
      dataIndex: "product",
      key: "product.brand",
      render: (product: ProductInterface) => product.brand?.name,
      className: "min-w-[100px]",
    },
    {
      title: "Quy cách",
      dataIndex: "productPackaging",
      key: "productPackaging.unit",
      render: (productPackaging: ProductPackagingInterface) =>
        productPackaging?.unit?.name,
      className: "min-w-[100px]",
    },
    {
      align: "right",
      title: "Giá chuẩn (VNĐ)",
      dataIndex: "productPackaging",
      key: "productPackaging.price",
      render: (productPackaging: ProductPackagingInterface) =>
        formatMoney(productPackaging?.price ?? 0),
      className: "min-w-[100px]",
    },
  ];

  return (
    <div>
      <h2>Sản phẩm trong dự án</h2>
      <InnerContainer>
        <FilterComponent
          searchHandler={searchHandler}
          searchForm={searchForm}
          filterOptions={[
            {
              label: "Tất cả",
              value: "all",
            },
            {
              label: "Tên sản phẩm",
              value: "product.name",
            },
            {
              label: "Mã sản phẩm",
              value: "product.code",
            },
            {
              label: "Barcode",
              value: "product.barcode",
            },
            {
              label: "Nhãn hàng",
              value: "brand.name",
            },
            {
              label: "Quy cách ",
              value: "unit.name",
            },
          ]}
          className="mb-6"
          btnAddText="Thêm quy cách của sản phẩm"
          handleAddButtonClick={() => setIsOpen(true)}
        />

        <CustomTable
          dataSource={projectProductsQuery?.data?.entities}
          columns={columns}
          scroll={{
            x: "max-content",
            y: pagination.total ? "80vh" : undefined,
          }}
          pagination={pagination}
          loading={projectProductsQuery.isFetching}
        />
      </InnerContainer>

      <ModalProjectProductAvailables
        projectId={projectId}
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        cb={searchHandler}
      />
    </div>
  );
}
