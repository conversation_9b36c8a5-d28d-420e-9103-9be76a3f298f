import { LoadingOutlined } from "@ant-design/icons";
import { useOosGroupsQuery } from "@project/component/feature/config/types/outOfStockStatus/steps/group/service.ts";
import OutOfStockStatusThresholdCollapseChild from "@project/component/feature/config/types/outOfStockStatus/steps/threshold/OutOfStockStatusThresholdCollapseChild.tsx";
import { Collapse } from "antd";
import { CollapseProps } from "antd/lib";
import { useMemo } from "react";
import { useParams } from "react-router-dom";
import { OosThresholdTypeEnum } from "../../interface";

interface OutOfStockStatusThresholdPageProps {
  type: OosThresholdTypeEnum;
}
const OutOfStockStatusThresholdPage = ({
  type,
}: OutOfStockStatusThresholdPageProps) => {
  const componentFeatureId = parseInt(useParams().componentFeatureId ?? "0");

  const oosGroupsQuery = useOosGroupsQuery(componentFeatureId, {
    take: 0,
  });

  const items: CollapseProps["items"] = useMemo(() => {
    return oosGroupsQuery.data?.entities.map((oosGroup) => ({
      key: oosGroup.id,
      label: (
        <span className={"font-semibold text-primary mr-3"}>
          {oosGroup.name}
        </span>
      ),
      children: (
        <OutOfStockStatusThresholdCollapseChild
          componentFeatureId={componentFeatureId}
          oosGroup={oosGroup}
          type={type}
        />
      ),
    }));
  }, [componentFeatureId, oosGroupsQuery.data?.entities, type]);

  return (
    <>
      {(() => {
        if (oosGroupsQuery.isLoading)
          return (
            <p>
              <LoadingOutlined />
            </p>
          );
        return (
          <Collapse
            defaultActiveKey={[oosGroupsQuery.data?.entities[0]?.id ?? ""]}
            ghost
            items={items}
            destroyInactivePanel
            collapsible="icon"
          />
        );
      })()}
    </>
  );
};

export default OutOfStockStatusThresholdPage;
