import ChartContanier from "@/routes/project/chart/ChartContanier";
import { <PERSON><PERSON><PERSON>, <PERSON> } from "@ant-design/charts";
import { Col, Row } from "antd";
import { useCallback } from "react";
import DashboardFilterZone from "../../DashboardFilterZone";

const CompetitorPATab = () => {
  const handleApply = useCallback(() => {}, []);

  const config = {
    appendPadding: 10,
    data: [
      { type: "Carlsberg", value: 28.22 },
      { type: "Heineken", value: 23.31 },
      { type: "Tiger", value: 17.18 },
      { type: "Larue", value: 15.95 },
      { type: "Sài G n Chill", value: 15.34 },
    ],
    angleField: "value",
    colorField: "type",
    radius: 0.8,
    interactions: [
      {
        type: "element-active",
      },
    ],
    color: ["#7986CB", "#FF8A65", "#4DD0E1", "#FFB74D", "#4FC3F7"],
    label: {
      text: (d: { type: string; value: number }) => `${d.type}\n ${d.value}`,
      position: "spider",
    },
    legend: false,
    title: {
      title: "% Competitor PA",
      subtitle: "Tỷ lệ PA giữa Sabeco và đối thủ",
    },
    tooltip: {
      field: "value",
      title: (d: { type: string }) => d.type,
      value: (d: { value: number }) => d.value,
    },
  };
  return (
    <>
      <DashboardFilterZone handleApply={handleApply} />

      <Row gutter={[16, { xs: 16, sm: 16 }]}>
        <Col md={9} xs={24}>
          <ChartContanier>
            <Pie {...config} />
          </ChartContanier>
        </Col>

        <Col md={15} xs={24}>
          <ChartContanier>
            <Column
              title={{
                title: "Competitor PA",
                subtitle: "Số lượng PA giữa Sabeco và đối thủ",
              }}
              data={[
                { date: "20/12", category: "333", value: 2764 },
                { date: "20/12", category: "Chill", value: 3218 },
                { date: "20/12", category: "Export", value: 2000 },
                { date: "20/12", category: "Gold", value: 2158 },
                { date: "20/12", category: "Lager", value: 3502 },
                { date: "20/12", category: "Special", value: 3722 },
                // 21/12
                { date: "21/12", category: "333", value: 2764 },
                { date: "21/12", category: "Chill", value: 4245 },
                { date: "21/12", category: "Export", value: 4090 },
                { date: "21/12", category: "Gold", value: 3354 },
                { date: "21/12", category: "Lager", value: 3502 },
                { date: "21/12", category: "Special", value: 3722 },
                // 22/12
                { date: "22/12", category: "333", value: 2119 },
                { date: "22/12", category: "Chill", value: 3271 },
                { date: "22/12", category: "Export", value: 3933 },
                { date: "22/12", category: "Gold", value: 2830 },
                { date: "22/12", category: "Lager", value: 3031 },
                { date: "22/12", category: "Special", value: 3722 },
                // 23/12
                { date: "23/12", category: "333", value: 2119 },
                { date: "23/12", category: "Chill", value: 3271 },
                { date: "23/12", category: "Export", value: 3933 },
                { date: "23/12", category: "Gold", value: 2830 },
                { date: "23/12", category: "Lager", value: 3031 },
                { date: "23/12", category: "Special", value: 3722 },
                // 24/12
                { date: "24/12", category: "333", value: 2119 },
                { date: "24/12", category: "Chill", value: 3271 },
                { date: "24/12", category: "Export", value: 3933 },
                { date: "24/12", category: "Gold", value: 2830 },
                { date: "24/12", category: "Lager", value: 3031 },
                { date: "24/12", category: "Special", value: 3722 },
                // 25/12
                { date: "25/12", category: "333", value: 2119 },
                { date: "25/12", category: "Chill", value: 3271 },
                { date: "25/12", category: "Export", value: 3933 },
                { date: "25/12", category: "Gold", value: 2830 },
                { date: "25/12", category: "Lager", value: 3031 },
                { date: "25/12", category: "Special", value: 3722 },
                // 26/12
                { date: "26/12", category: "333", value: 2119 },
                { date: "26/12", category: "Chill", value: 3271 },
                { date: "26/12", category: "Export", value: 3933 },
                { date: "26/12", category: "Gold", value: 2830 },
                { date: "26/12", category: "Lager", value: 3031 },
                { date: "26/12", category: "Special", value: 3722 },
                // 27/12
                { date: "27/12", category: "333", value: 2119 },
                { date: "27/12", category: "Chill", value: 3271 },
                { date: "27/12", category: "Export", value: 3933 },
                { date: "27/12", category: "Gold", value: 2830 },
                { date: "27/12", category: "Lager", value: 3031 },
                { date: "27/12", category: "Special", value: 3722 },
                // 28/12
                { date: "28/12", category: "333", value: 2119 },
                { date: "28/12", category: "Chill", value: 3271 },
                { date: "28/12", category: "Export", value: 3933 },
                { date: "28/12", category: "Gold", value: 2830 },
                { date: "28/12", category: "Lager", value: 3031 },
                { date: "28/12", category: "Special", value: 3722 },
                // 29/12
                { date: "29/12", category: "333", value: 2119 },
                { date: "29/12", category: "Chill", value: 3271 },
                { date: "29/12", category: "Export", value: 3933 },
                { date: "29/12", category: "Gold", value: 2830 },
                { date: "29/12", category: "Lager", value: 3031 },
                { date: "29/12", category: "Special", value: 3722 },
                // 30/12
                { date: "30/12", category: "333", value: 2119 },
                { date: "30/12", category: "Chill", value: 3271 },
                { date: "30/12", category: "Export", value: 3933 },
                { date: "30/12", category: "Gold", value: 2830 },
                { date: "30/12", category: "Lager", value: 3031 },
                { date: "30/12", category: "Special", value: 3722 },
                // 31/12
                { date: "31/12", category: "333", value: 2119 },
                { date: "31/12", category: "Chill", value: 3271 },
                { date: "31/12", category: "Export", value: 3933 },
                { date: "31/12", category: "Gold", value: 2830 },
                { date: "31/12", category: "Lager", value: 3031 },
                { date: "31/12", category: "Special", value: 3722 },
                // 1/1
                { date: "1/1", category: "333", value: 4994 },
                { date: "1/1", category: "Chill", value: 2056 },
                { date: "1/1", category: "Export", value: 2021 },
                { date: "1/1", category: "Gold", value: 2712 },
                { date: "1/1", category: "Lager", value: 2488 },
                { date: "1/1", category: "Special", value: 2488 },
                // 2/1
                { date: "2/1", category: "333", value: 4994 },
                { date: "2/1", category: "Chill", value: 2056 },
                { date: "2/1", category: "Export", value: 2021 },
                { date: "2/1", category: "Gold", value: 2712 },
                { date: "2/1", category: "Lager", value: 2488 },
                { date: "2/1", category: "Special", value: 2488 },
                // 3/1
                { date: "3/1", category: "333", value: 4994 },
                { date: "3/1", category: "Chill", value: 2056 },
                { date: "3/1", category: "Export", value: 2021 },
                { date: "3/1", category: "Gold", value: 2712 },
                { date: "3/1", category: "Lager", value: 2488 },
                { date: "3/1", category: "Special", value: 2488 },
              ]}
              xField={"date"}
              yField={"value"}
              style={{
                maxWidth: 50,
              }}
              label={{
                textBaseline: "center",
              }}
              axis={{
                x: {
                  labelSpacing: 4,
                  style: {
                    labelTransform: "rotate(-45)",
                  },
                },
              }}
              stack={true}
              colorField={"category"}
              legend={{
                color: {
                  position: "bottom",
                  layout: {
                    justifyContent: "center",
                  },
                },
              }}
            />
          </ChartContanier>
        </Col>
      </Row>
    </>
  );
};

export default CompetitorPATab;
