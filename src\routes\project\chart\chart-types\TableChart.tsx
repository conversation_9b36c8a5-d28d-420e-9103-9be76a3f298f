import { tableCellHeaderStyle, tableCellStyle } from "@/common/css.helper";
import { formatNumber } from "@/common/helper";
import LoadingPage from "@/LoadingPage";
import { MinusSquareOutlined, PlusSquareOutlined } from "@ant-design/icons";
import { Button } from "antd";
import { ReactNode, useMemo, useState } from "react";
import { ChartDataInterface, ChartTabProps } from "../interface";

const TableChartRow = ({
  label,
  items,
  sort,
}: {
  label: string;
  items: ChartDataInterface[];
  sort: ChartTabProps["sort"];
}) => {
  const [isOpen, setIsOpen] = useState(true);

  const toggleOpen = () => setIsOpen((prev) => !prev);

  const sortedItems = useMemo(() => {
    if (sort === "asc") {
      items?.sort((a, b) => a["totalValue"] - b["totalValue"]);
      return items;
    } else if (sort === "desc") {
      items?.sort((a, b) => b["totalValue"] - a["totalValue"]);
      return items;
    } else {
      return items;
    }
  }, [items, sort]);

  const maxTotalValue = useMemo(() => {
    let max = 0;

    for (const item of sortedItems) {
      if (item["totalValue"] > max) {
        max = item["totalValue"];
      }
    }

    return max;
  }, [sortedItems]);

  const sumKpi = useMemo(
    () => sortedItems.reduce((sum, item) => sum + item["kpi"], 0),
    [sortedItems],
  );
  const sumTotalValue = useMemo(
    () => sortedItems.reduce((sum, item) => sum + item["totalValue"], 0),
    [sortedItems],
  );

  if (isOpen) {
    return (
      <>
        {sortedItems?.map((item, index) => (
          <tr key={index}>
            {index === 0 && (
              <td
                rowSpan={items?.length}
                className={`${tableCellStyle} text-left `}
              >
                <div className="flex items-center">
                  <Button type="link" onClick={toggleOpen} className="m-0 p-0">
                    <MinusSquareOutlined />
                  </Button>{" "}
                  {label}
                </div>
              </td>
            )}
            <td className={`${tableCellStyle} text-left`}>
              {item["unitName"]} - {item["name"]}
            </td>
            <td className={`${tableCellStyle} text-right`}>
              {formatNumber(item["kpi"])}
            </td>

            <td
              className={`${tableCellStyle} text-right ${maxTotalValue === Number(item["totalValue"]) ? "bg-[#FFF9D8]" : ""}`}
            >
              {formatNumber(item["totalValue"])}
            </td>
            <td className={`${tableCellStyle} text-right`}>
              {Math.round((item["totalValue"] / Number(item["kpi"])) * 100)}%
            </td>
          </tr>
        ))}
      </>
    );
  }

  return (
    <tr>
      <td className={`${tableCellStyle} text-right`}>
        <div className="flex items-center">
          <Button type="link" onClick={toggleOpen} className="m-0 p-0">
            <PlusSquareOutlined />
          </Button>{" "}
          {label}
        </div>
      </td>

      <td className={`${tableCellStyle} text-right`}></td>
      <td className={`${tableCellStyle} text-right`}>{formatNumber(sumKpi)}</td>
      <td className={`${tableCellStyle} text-right`}>
        {formatNumber(sumTotalValue)}
      </td>
      <td className={`${tableCellStyle} text-right`}>
        {Math.round((sumTotalValue / sumKpi) * 100)}%
      </td>
    </tr>
  );
};

const TableChart = ({
  data,
  sort,
  title,
  subtitle,
  loading,
  firstColumnTitle,
}: {
  data: {
    label: string;
    items: ChartDataInterface[];
  }[];
  sort: ChartTabProps["sort"];
  title: string;
  subtitle: string;
  loading: boolean;
  firstColumnTitle: string;
}): ReactNode => {
  if (loading) {
    return <LoadingPage />;
  }

  return (
    <div className="h-full w-full px-4">
      <div className="text-[18px] font-semibold mt-6">{title}</div>
      <div className="text-[14px] font-normal">{subtitle}</div>
      <div className="mt-5 max-w-full overflow-x-auto overflow-y-auto max-h-[calc(100%-120px)] relative">
        <table className="min-w-full bg-white border-collapse border border-gray-300">
          <thead className="sticky top-0 z-10 border border-solid border-gray-300">
            <tr className="bg-[#E8F3FC]">
              <th className={`${tableCellHeaderStyle} text-left`}>
                {firstColumnTitle}
              </th>
              <th className={`${tableCellHeaderStyle} text-left`}>Quà</th>
              <th className={`${tableCellHeaderStyle} text-right`}>KPI</th>
              <th className={`${tableCellHeaderStyle} text-right`}>UTD</th>
              <th className={`${tableCellHeaderStyle} text-right`}>%UTD</th>
            </tr>
          </thead>
          <tbody>
            {data.map((item, index) => (
              <TableChartRow
                key={index}
                items={item.items}
                label={item.label}
                sort={sort}
              />
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default TableChart;
