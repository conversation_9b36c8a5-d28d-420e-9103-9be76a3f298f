import { useApp } from "@/UseApp";
import { AbstractFilterInterface } from "@/common/interface";
import { AppContextInterface } from "@/interface";
import { useMutation, useQuery } from "@tanstack/react-query";
import { ProjectRecordFeatureInterface } from "../../interface";

export const getReportPhoto = (
  axiosGet: AppContextInterface["axiosGet"],
  projectId: number,
  componentFeatureId: number,
  filter?: object & AbstractFilterInterface,
) =>
  axiosGet<
    { entities: ProjectRecordFeatureInterface[]; count: number },
    unknown
  >(
    `/projects/${projectId}/report/features/${componentFeatureId}/photos`,
    filter,
  );

export const useReportPhotoQuery = (
  projectId: number,
  componentFeatureId: number,
  filter?: object & AbstractFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["reportPhoto", projectId, componentFeatureId, filter],
    queryFn: () =>
      getReportPhoto(axiosGet, projectId, componentFeatureId, filter),
  });
};

export const useGetReportPhotoMutation = (
  projectId: number,
  componentFeatureId: number,
) => {
  const { axiosGet } = useApp();

  return useMutation({
    mutationKey: ["reportPhoto", projectId, componentFeatureId],
    mutationFn: (filter?: object & AbstractFilterInterface) =>
      getReportPhoto(axiosGet, projectId, componentFeatureId, filter),
  });
};
