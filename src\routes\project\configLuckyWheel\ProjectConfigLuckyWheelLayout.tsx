import InnerContainer from "@/components/InnerContainer/InnerContainer";
import { Skeleton } from "antd";
import { Suspense } from "react";
import { Outlet } from "react-router-dom";

const ProjectConfigLuckyWheelLayout = () => {
  return (
    <>
      <h2>Cấu hình vòng quay</h2>
      <InnerContainer>
        <Suspense fallback={<Skeleton active />}>
          <Outlet />
        </Suspense>
      </InnerContainer>
    </>
  );
};
export default ProjectConfigLuckyWheelLayout;
