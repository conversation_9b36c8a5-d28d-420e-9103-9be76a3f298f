import { CURD } from "@/common/constant";
import { EditOutlined, LoadingOutlined, PlusOutlined } from "@ant-design/icons";
import { Button, Collapse } from "antd";
import { CollapseProps } from "antd/lib";
import { useCallback, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import { FeatureNumericSheetInterface } from "../../interface";
import { useNumericSheetsQuery } from "../../service";
import NumericSheetGroupCollapseChild from "./NumericSheetGroupCollapseChild";
import NumericSheetGroupModal from "./NumericSheetSheetGroupModal";

const NumericSheetGroupPage = () => {
  const componentFeatureId = parseInt(useParams().componentFeatureId ?? "0");

  const [action, setAction] = useState<CURD | null>(null);
  const [selectedNumericSheet, setSelectedNumericSheet] = useState<
    FeatureNumericSheetInterface | undefined
  >(undefined);

  const numericSheetsQuery = useNumericSheetsQuery(componentFeatureId, {
    take: 0,
  });

  const onAddGroup = useCallback(() => {
    setAction(CURD.CREATE);
  }, []);

  const items: CollapseProps["items"] = useMemo(() => {
    return numericSheetsQuery.data?.entities.map((numericSheet) => ({
      key: numericSheet.id,
      label: (
        <>
          <span className={"font-semibold text-primary mr-3"}>
            {numericSheet.name}
          </span>

          <EditOutlined
            className="cursor-pointer"
            onClick={() => {
              setAction(CURD.UPDATE);
              setSelectedNumericSheet(numericSheet);
            }}
          />
        </>
      ),
      children: (
        <NumericSheetGroupCollapseChild
          componentFeatureId={componentFeatureId}
          numericSheetId={numericSheet.id}
        />
      ),
    }));
  }, [componentFeatureId, numericSheetsQuery.data?.entities]);

  return (
    <>
      <div className={"flex justify-end"}>
        <Button type={"primary"} icon={<PlusOutlined />} onClick={onAddGroup}>
          Thêm mới nhóm item
        </Button>
      </div>

      {(() => {
        if (numericSheetsQuery.isLoading)
          return (
            <p>
              <LoadingOutlined />
            </p>
          );
        return (
          <Collapse
            defaultActiveKey={[numericSheetsQuery.data?.entities[0]?.id ?? ""]}
            ghost
            items={items}
            destroyInactivePanel
            collapsible="icon"
          />
        );
      })()}

      {action && (
        <NumericSheetGroupModal
          cancelCb={() => setAction(null)}
          componentFeatureId={componentFeatureId}
          cb={() => {
            setAction(null);
            numericSheetsQuery.refetch();
          }}
          action={action}
          selectedNumericSheet={selectedNumericSheet}
        />
      )}
    </>
  );
};

export default NumericSheetGroupPage;
