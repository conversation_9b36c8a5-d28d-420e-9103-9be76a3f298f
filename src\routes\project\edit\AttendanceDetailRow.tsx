import { DATETIME_FORMAT } from "@/common/constant";
import { Col, Row, Space } from "antd";
import dayjs from "dayjs";
import { ProjectRecordEmployeeInterface } from "../report/interface";

interface AttendanceDetailRowProps {
  projectRecordEmployee?: ProjectRecordEmployeeInterface;
  createdAt?: string;
  updatedAt?: string;
}

const AttendanceDetailRow = ({
  projectRecordEmployee,
  createdAt,
  updatedAt,
}: AttendanceDetailRowProps) => {
  return (
    <Row
      className={
        "bg-[#F5F5F5] border border-[#DDE1EA] rounded-md pt-3 pb-3 pl-[19px]"
      }
    >
      <Space size={"large"}>
        <Col>
          <span className={"font-semibold"}>Mã outlet:</span>{" "}
          {projectRecordEmployee?.projectRecord.projectOutlet.code}
        </Col>

        <Col>
          <span className={"font-semibold"}>Tên outlet:</span>{" "}
          {projectRecordEmployee?.projectRecord.projectOutlet.name}
        </Col>

        <Col>
          <span className={"font-semibold"}>Loại booth:</span>{" "}
          {projectRecordEmployee?.projectRecord.projectBooth.name}
        </Col>

        <Col>
          <span className={"font-semibold"}>Thời gian chấm công:</span>{" "}
          {dayjs(createdAt).format(DATETIME_FORMAT)} -{" "}
          {dayjs(updatedAt).format(DATETIME_FORMAT)}
        </Col>

        <Col>
          <span className={"font-semibold"}>Nhân viên chấm công:</span>{" "}
          {projectRecordEmployee?.employee.user.name}
        </Col>
      </Space>
    </Row>
  );
};

export default AttendanceDetailRow;
