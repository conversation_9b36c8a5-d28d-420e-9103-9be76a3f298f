import { useApp } from "@/UseApp";
import { useQuery } from "@tanstack/react-query";
import { DashboardFilterInterface } from "../../interface";

export const useQuantityDailyTotalQuery = (
  projectId: number,
  dashboardId: number,
  filter?: DashboardFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["quantityDailyTotal", projectId, dashboardId, filter],
    queryFn: () =>
      axiosGet<
        {
          backgroundColor: string;
          foregroundColor: string;
          name: string;
          code: string;
          date: string;
          total: number;
        }[],
        unknown
      >(
        `/projects/${projectId}/dashboards/${dashboardId}/charts/quantity-daily-total`,
        filter,
      ),
  });
};
