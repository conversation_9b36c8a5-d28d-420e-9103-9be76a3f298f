import { AbstractFilterInterface } from "@/common/interface.ts";
import { useApp } from "@/UseApp.tsx";
import { useMutation, useQuery } from "@tanstack/react-query";
import { ProductInterface } from "./interface";

export const useGetProductByIdMutation = () => {
  const { axiosGet } = useApp();

  return useMutation({
    mutationKey: ["getProductById"],
    mutationFn: (productId: number) =>
      axiosGet<ProductInterface, unknown>(`/products/${productId}`),
  });
};

export const useDeleteProductMutation = () => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deleteProduct"],
    mutationFn: (productId: number) => axiosDelete(`/products/${productId}`),
  });
};

export const useProductsQuery = (
  filter: AbstractFilterInterface & { brandId?: number },
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["products", filter],
    queryFn: () =>
      axiosGet<{ entities: ProductInterface[]; count: number }, unknown>(
        `/products`,
        filter,
      ),
  });
};

export const useCreateProductMutation = () => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createProduct"],
    mutationFn: (data: {
      clientId: number;
      name: string;
      code: string;
      imageId?: number;
      brandId: number;
      packagings: {
        unitId: number;
        price: number;
        rate: number;
        barcode: string;
      }[];
      mainPackagingId?: number;
    }) => axiosPost<{ id: number }, unknown>(`/products`, data),
  });
};

export const useUpdateProductMutation = () => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["updateProduct"],
    mutationFn: (data: {
      id: number;
      clientId?: number;
      name?: string;
      code?: string;
      imageId?: number;
      brandId?: number;
      packagings?: {
        unitId: number;
        price: number;
        rate: number;
        barcode: string;
      }[];
      mainPackagingId?: number;
      isActive?: boolean;
    }) => axiosPatch<{ id: number }, unknown>(`/products/${data.id}`, data),
  });
};
