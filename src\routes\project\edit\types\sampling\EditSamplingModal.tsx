import equal from "@/assets/equal.svg";
import { CURD } from "@/common/constant";
import { stringIncludes } from "@/common/helper";
import CustomModal from "@/components/CustomModal";
import FormNumberInput from "@/components/FormNumberInput";
import {
  ComponentFeatureInterface,
  FeatureTypeEnum,
} from "@/routes/project/component/feature/interface";
import { ToolSettingSettingTypeEnum } from "@/routes/project/config/interface";
import {
  useProjectToolAgencyDetailQuery,
  useToolSettingsQuery,
} from "@/routes/project/config/service";
import { useApp } from "@/UseApp";
import { SearchOutlined } from "@ant-design/icons";
import { Col, Form, Input, Row } from "antd";
import dayjs from "dayjs";
import _ from "lodash";
import { useCallback, useMemo, useState } from "react";
import { v4 as uuidv4 } from "uuid";
import { EditSamplingInterface } from "./interface";
import { useCreateEditSamplingMutation } from "./service";

interface EditSamplingModalProps {
  title: string;
  editSampling?: EditSamplingInterface;
  componentFeature?: ComponentFeatureInterface;
  projectId: number;
  attendanceId: number;
  componentFeatureId: number;
  cb: () => void;
  cancelCb: () => void;
  createdAt?: string;
  projectAgencyId?: number;
  action: CURD;
}

const EditSamplingModal = ({
  title,
  editSampling,
  componentFeature,
  projectId,
  attendanceId,
  componentFeatureId,
  createdAt,
  cb,
  cancelCb,
  projectAgencyId,
  action,
}: EditSamplingModalProps) => {
  const { showNotification } = useApp();

  const [form] = Form.useForm();
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");

  const projectToolAgencyDetailQuery = useProjectToolAgencyDetailQuery(
    projectId,
    projectAgencyId,
  );
  const toolSettingsQuery = useToolSettingsQuery(
    projectToolAgencyDetailQuery.data?.id ?? 0,
  );

  const createEditSamplingMutation = useCreateEditSamplingMutation(
    projectId,
    attendanceId,
    componentFeatureId,
  );

  const debouncedSetSearchTerm = useCallback((value: string) => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(value);
    }, 300);

    return () => clearTimeout(timer);
  }, []);

  const recordSamplingValues = useMemo(() => {
    const { recordSamplingValues } = editSampling ?? {};
    recordSamplingValues?.sort((a, b) => b.value - a.value);
    for (const recordSamplingValue of recordSamplingValues ?? []) {
      recordSamplingValue.featureSampling =
        componentFeature?.featureSamplings?.find(
          (item) => item.id === recordSamplingValue.featureSamplingId,
        );
    }

    return recordSamplingValues ?? [];
  }, [componentFeature?.featureSamplings, editSampling]);

  const options = useMemo(() => {
    if (action === CURD.UPDATE)
      return recordSamplingValues.map((item) => {
        return {
          label: item.featureSampling?.projectProduct?.product?.name,
          featureSamplingId: item.featureSamplingId,
          value: item.value,
          conversionValue: item.conversionValue,
          conversionRate: item.conversionRate,
          unitName: item.featureSampling?.unit.name,
          conversionUnitName:
            item.featureSampling?.projectProduct.productPackaging?.unit.name,
        };
      });

    if (action === CURD.CREATE)
      return componentFeature?.featureSamplings?.map((item) => {
        return {
          label: item.projectProduct?.product?.name,
          featureSamplingId: item.id,
          value: null,
          conversionValue: null,
          conversionRate: item.numerator / item.denominator,
          unitName: item.unit.name,
          conversionUnitName: item.projectProduct.productPackaging?.unit.name,
        };
      });

    return [];
  }, [action, componentFeature?.featureSamplings, recordSamplingValues]);

  const filteredOptions = useMemo(() => {
    return options?.filter((item) =>
      stringIncludes(item.label ?? "", debouncedSearchTerm),
    );
  }, [debouncedSearchTerm, options]);

  const isDisallowEditNullValue = useMemo(() => {
    const toolSetting = toolSettingsQuery.data?.find(
      (item) =>
        item.featureType === FeatureTypeEnum.Sampling &&
        item.settingType === ToolSettingSettingTypeEnum.READONLY_IF_NULL,
    );
    return toolSetting?.enabled ?? false;
  }, [toolSettingsQuery.data]);

  const isDisallowEditZeroValue = useMemo(() => {
    const toolSetting = toolSettingsQuery.data?.find(
      (item) =>
        item.featureType === FeatureTypeEnum.Sampling &&
        item.settingType === ToolSettingSettingTypeEnum.READONLY_IF_ZERO,
    );
    return toolSetting?.enabled ?? false;
  }, [toolSettingsQuery.data]);

  const content = (
    <Form layout="vertical" form={form}>
      <Form.Item>
        <Input
          prefix={<SearchOutlined />}
          placeholder="Tên sampling"
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
            const value = e.target.value;
            debouncedSetSearchTerm(value);
          }}
        />
      </Form.Item>

      <Form.List name={"recordSamplingValues"}>
        {() => (
          <div className="max-h-[450px] overflow-auto">
            {filteredOptions?.map((item, index) => (
              <Row
                key={index}
                justify={"space-between"}
                className="items-center"
              >
                <Col md={10}>
                  <Form.Item
                    label={item.label}
                    name={`${item.featureSamplingId}.value`}
                    initialValue={item.value}
                  >
                    <FormNumberInput
                      className="w-full"
                      suffix={item.unitName}
                      onChange={(value) => {
                        form.setFieldValue(
                          [
                            "recordSamplingValues",
                            `${item.featureSamplingId}.conversionValue`,
                          ],
                          value || value == 0
                            ? _.ceil((value as number) * item.conversionRate)
                            : null,
                        );
                      }}
                      disabled={
                        (isDisallowEditZeroValue && item.value === 0) ||
                        (isDisallowEditNullValue && item.value === null)
                      }
                    />
                  </Form.Item>
                </Col>
                <Col md={2} className="items-center flex">
                  <img
                    src={equal}
                    alt="equal"
                    className="w-4 h-4 bg-[#ECEDEF] rounded-full p-2"
                  />
                </Col>
                <Col md={10}>
                  <Form.Item
                    name={`${item.featureSamplingId}.conversionValue`}
                    label={" "}
                    initialValue={item.conversionValue}
                  >
                    <FormNumberInput
                      className="w-full"
                      suffix={item.conversionUnitName}
                      disabled
                    />
                  </Form.Item>
                </Col>
              </Row>
            ))}
          </div>
        )}
      </Form.List>
    </Form>
  );

  const confirm = useCallback(async () => {
    const recordSamplingValues = form.getFieldsValue().recordSamplingValues;
    const data: {
      dataUuid: string;
      dataTimestamp: string;
      values: { featureSamplingId: number; value: number }[];
    } = {
      dataUuid: uuidv4(),
      values: [],
      dataTimestamp: dayjs(createdAt).add(1, "second").toISOString() ?? "",
    };

    for (const key of Object.keys(recordSamplingValues)) {
      const parts = key.split(".");
      if (parts.length >= 2) {
        const [featureSamplingId, type] = parts;
        if (type === "value") {
          data.values.push({
            featureSamplingId: Number(featureSamplingId),
            value: recordSamplingValues[key],
          });
        }
      }
    }

    await createEditSamplingMutation.mutateAsync(data);

    showNotification({
      type: "success",
      message: title + " thành công",
    });
    cb();
  }, [
    cb,
    createEditSamplingMutation,
    createdAt,
    form,
    showNotification,
    title,
  ]);

  return (
    <CustomModal
      title={title}
      isOpen={true}
      content={content}
      onConfirm={confirm}
      confirmText="Lưu thông tin"
      onCancel={cancelCb}
      confirmLoading={createEditSamplingMutation.isPending}
    />
  );
};

export default EditSamplingModal;
