import { FeatureCustomerInterface } from "@project/component/feature/config/types/customerInformationCapturing/interface";
import { Form, Input, Radio } from "antd";
import { Rule } from "antd/es/form";
import { Fragment } from "react/jsx-runtime";

const CustomerInput = ({
  featureCustomer,
}: {
  featureCustomer: FeatureCustomerInterface;
}) => {
  const { name, dataType, id, isRequired, featureCustomerOptions } =
    featureCustomer;

  const rules: Rule[] = [];
  if (isRequired) {
    rules.push({ required: true });
    if (dataType !== "radio") {
      rules.push({ whitespace: true });
    }
  }
  if (dataType === "email") {
    rules.push({ type: "email" });
  }

  return (
    <Fragment key={id}>
      <Form.Item name={`featureCustomerId_${id}`} hidden={true}>
        <Input hidden value={id} />
      </Form.Item>
      <Form.Item
        key={id}
        label={name}
        name={`${id}`}
        rules={rules}
        validateTrigger={"onBlur"}
      >
        {dataType === "string" && <Input />}
        {dataType === "number" && <Input />}
        {dataType === "phoneNumber" && <Input />}
        {dataType === "email" && <Input type="email" />}
        {dataType === "radio" && (
          <Radio.Group>
            {featureCustomerOptions.map((featureCustomerOption, index) => (
              <Radio value={featureCustomerOption.id} key={index}>
                {featureCustomerOption.name}
              </Radio>
            ))}
          </Radio.Group>
        )}
      </Form.Item>
    </Fragment>
  );
};

export default CustomerInput;
