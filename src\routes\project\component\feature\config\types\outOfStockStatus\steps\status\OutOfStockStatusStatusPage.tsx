import { CURD } from "@/common/constant.ts";
import DragSortRowComponent from "@/components/DragSortRowComponent.tsx";
import TableActionCell from "@/components/TableActionCell.tsx";
import { useUrlFiltersWithQuery } from "@/hooks/useUrlFiltersWithQuery.ts";
import { useApp } from "@/UseApp.tsx";
import {
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { DndContext, DragEndEvent } from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
  arrayMove,
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { OosLevelInterface } from "@project/component/feature/config/types/outOfStockStatus/interface.ts";
import OutOfStockStatusStatusModal from "@project/component/feature/config/types/outOfStockStatus/steps/status/OutOfStockStatusStatusModal.tsx";
import { Button, Col, Form, Input, Row, Space, Table } from "antd";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import {
  useArrangeOosLevelMutation,
  useDeleteOosLevelMutation,
  useOosLevelsQuery,
} from "./service";

const OutOfStockStatusStatusPage = () => {
  const { showNotification, openDeleteModal } = useApp();

  const componentFeatureId = parseInt(useParams().componentFeatureId ?? "0");

  const [searchForm] = Form.useForm();
  const [action, setAction] = useState<CURD | null>(null);
  const [selectedOosLevel, setSelectedOosLevel] =
    useState<OosLevelInterface | null>(null);

  const {
    query: { data, refetch, isFetching },
    handleSearch,
  } = useUrlFiltersWithQuery<OosLevelInterface>({
    formInstance: searchForm,
    useQueryHook: useOosLevelsQuery,
    queryParams: [componentFeatureId],
    options: {
      urlSync: {
        enabled: false,
      },
      defaultPageSize: 0,
    },
  });
  const [dataSource, setDataSource] = useState(data?.entities ?? []);

  const deleteOosLevelMutation = useDeleteOosLevelMutation(componentFeatureId);
  const arrangeOosLevelMutation =
    useArrangeOosLevelMutation(componentFeatureId);

  const loading = useMemo(
    () =>
      isFetching ||
      deleteOosLevelMutation.isPending ||
      arrangeOosLevelMutation.isPending,
    [
      arrangeOosLevelMutation.isPending,
      deleteOosLevelMutation.isPending,
      isFetching,
    ],
  );

  const onDragEnd = useCallback(
    async ({ active, over }: DragEndEvent) => {
      if (!over) {
        return;
      }
      if (active.id !== over?.id) {
        setDataSource((previous) => {
          const activeIndex = previous?.findIndex((i) => i.id === active.id);
          const overIndex = previous?.findIndex((i) => i.id === over?.id);
          return arrayMove(previous, activeIndex, overIndex);
        });

        await arrangeOosLevelMutation.mutateAsync({
          id: active.id as number,
          overFeatureOosLevelId: over?.id as number,
        });
      }
    },
    [arrangeOosLevelMutation],
  );

  useEffect(() => {
    setDataSource(data?.entities ?? []);
  }, [data?.entities]);

  const actions = useMemo(
    () => [
      {
        key: "edit",
        action: (record: OosLevelInterface) => {
          setAction(CURD.UPDATE);
          setSelectedOosLevel(record);
        },
      },
      {
        key: "delete",
        action: async (record: OosLevelInterface) => {
          openDeleteModal({
            content: (
              <p>
                Bạn muốn xóa trạng thái{" "}
                <span className={"font-semibold"}>{record.name}</span> khỏi chức
                năng?
              </p>
            ),
            deleteText: "Xác nhận xóa",
            loading: false,
            onCancel(): void {},
            onDelete: async () => {
              await deleteOosLevelMutation.mutateAsync(record.id);

              showNotification({
                type: "success",
                message: "Xóa trạng thái thành công",
              });

              await refetch();
            },
            title: `Xóa trạng thái`,
            titleError: "Không thể xóa trạng thái",
            contentHeader: (
              <>
                Không thể xóa trạng thái{" "}
                <span className="font-semibold">{record.name}</span> bởi vì:
              </>
            ),
          });
        },
      },
    ],
    [deleteOosLevelMutation, openDeleteModal, refetch, showNotification],
  );

  const items = useMemo(
    () => [
      {
        key: "edit",
        label: "Chỉnh sửa",
        icon: <EditOutlined />,
      },
      {
        key: "delete",
        label: "Xóa",
        icon: <DeleteOutlined />,
      },
    ],
    [],
  );

  return (
    <>
      <Row justify={"space-between"}>
        <Col>
          <Form form={searchForm} onFinish={handleSearch}>
            <Space>
              <Form.Item name={"keyword"}>
                <Input
                  placeholder="Tìm theo tên giá trị"
                  prefix={<SearchOutlined />}
                />
              </Form.Item>

              <Form.Item>
                <Button type="default" htmlType="submit" loading={loading}>
                  Tìm kiếm
                </Button>
              </Form.Item>
            </Space>
          </Form>
        </Col>
        <Col>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setAction(CURD.CREATE)}
          >
            Thêm giá trị
          </Button>
        </Col>
      </Row>

      <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
        <SortableContext
          items={dataSource.map((i) => i.id)}
          strategy={verticalListSortingStrategy}
        >
          <Table
            rowKey={(o) => o.id}
            dataSource={dataSource}
            loading={loading}
            components={{
              body: {
                row: DragSortRowComponent,
              },
            }}
            columns={[
              {
                key: "sort",
              },
              {
                title: "Trạng thái",
                dataIndex: "name",
                render: (name, record) => (
                  <Space>
                    <span>{name}</span>
                    {record.isStandard && (
                      <span className="text-blue rounded-full font-medium bg-[#EEF4FF] px-2 py-1">
                        Tồn chuẩn
                      </span>
                    )}
                  </Space>
                ),
              },
              {
                title: "Màu nền",
                dataIndex: "backgroundColor",
                render: (backgroundColor) => (
                  <Space>
                    <span
                      className="rounded"
                      style={{
                        display: "inline-block",
                        width: "48px",
                        height: "48px",
                        backgroundColor,
                        border: "1px solid #ECEDEF",
                      }}
                    ></span>
                    <span>{backgroundColor}</span>
                  </Space>
                ),
              },
              {
                title: "Màu chữ",
                className: "min-w-[100px]",
                dataIndex: "foregroundColor",
                render: (foregroundColor) => (
                  <Space>
                    <span
                      className="rounded"
                      style={{
                        display: "inline-block",
                        width: "48px",
                        height: "48px",
                        backgroundColor: foregroundColor,
                        border: "1px solid #ECEDEF",
                      }}
                    ></span>
                    <span>{foregroundColor}</span>
                  </Space>
                ),
              },

              {
                key: "actions",
                align: "right",
                render: (_, record) => {
                  return (
                    <TableActionCell
                      actions={actions}
                      items={items}
                      record={record}
                    />
                  );
                },
              },
            ]}
            pagination={false}
          />
        </SortableContext>
      </DndContext>

      {action && (
        <OutOfStockStatusStatusModal
          action={action}
          onCancelCb={() => {
            setAction(null);
            setSelectedOosLevel(null);
          }}
          componentFeatureId={componentFeatureId}
          selectedFeatureOosLevel={selectedOosLevel}
          cb={() => {
            setAction(null);
            setSelectedOosLevel(null);
            refetch();
          }}
        />
      )}
    </>
  );
};

export default OutOfStockStatusStatusPage;
