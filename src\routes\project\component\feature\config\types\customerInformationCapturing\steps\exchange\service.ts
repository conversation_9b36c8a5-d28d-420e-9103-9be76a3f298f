import { useApp } from "@/UseApp";
import { AbstractFilterInterface } from "@/common/interface";
import { ProjectProductInterface } from "@/routes/project/product/interface";
import { ProjectOutletInterface } from "@project/outlet/interface";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  FeatureSchemeInterface,
  FeatureSchemeOutletInterface,
} from "../../interface";
import { LuckyDrawLimitInterface } from "./interface";

/**
 * Creates a mutation for creating a scheme.
 *
 * @param {number} componentFeatureId - the ID of the component feature
 */
export const useCreateSchemeMutation = (componentFeatureId: number) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createScheme", componentFeatureId],
    mutationFn: (data: {
      projectAgencyId: number;
      name: string;
      description?: string;
    }) => {
      return axiosPost(`/features/${componentFeatureId}/schemes`, data);
    },
  });
};

/**
 * Generates a query for fetching schemes based on the component feature ID and feature order ID.
 *
 * @param {number} componentFeatureId - The ID of the component feature
 * @param {number} projectAgencyId - The ID of the feature order
 */
export const useSchemesQuery = (
  componentFeatureId: number,
  projectAgencyId?: number,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["schemes", componentFeatureId, projectAgencyId],
    enabled: !!componentFeatureId,
    queryFn: () =>
      axiosGet<{ entities: FeatureSchemeInterface[]; count: number }, unknown>(
        `/features/${componentFeatureId}/schemes`,
        {
          projectAgencyId,
          take: 50,
          skip: 0,
        },
      ),
  });
};

export const useAddSchemeExchangeMutation = (componentFeatureId: number) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["addSchemeExchange", componentFeatureId],
    mutationFn: (data: {
      name: string;
      description?: string;
      schemeId: number;
      hasPlayedGame?: boolean;
      maxReceiveQuantity: number;
      reachAmount?: number;
      logical?: "and" | "or" | "relaxed";
      exchangeConditions: {
        projectProductId: number;
        quantity: number;
      }[];
      exchangeProceeds: {
        projectProductId: null | number;
        projectItemId: null | number;
        quantity: number;
      }[];
      luckyDrawId?: number;
      luckyDrawCount?: number;
      reachQuantity?: number;
    }) =>
      axiosPost(
        `/features/${componentFeatureId}/schemes/${data.schemeId}/exchanges`,
        data,
      ),
  });
};

export const useUpdateSchemeExchangeMutation = (componentFeatureId: number) => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["updateSchemeExchange", componentFeatureId],
    mutationFn: (data: {
      schemeId: number;
      exchangeId: number;
      isActive?: boolean;
      name?: string;
      code?: string;
    }) =>
      axiosPatch(
        `/features/${componentFeatureId}/schemes/${data.schemeId}/exchanges/${data.exchangeId}`,
        { isActive: data.isActive, name: data.name, code: data.code },
      ),
  });
};

export const useDeleteSchemeExchangeMutation = (componentFeatureId: number) => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deleteSchemeExchange", componentFeatureId],
    mutationFn: (data: { schemeId: number; exchangeId: number }) =>
      axiosDelete(
        `/features/${componentFeatureId}/schemes/${data.schemeId}/exchanges/${data.exchangeId}`,
      ),
  });
};

export const useSchemeOutletsAvailablesQuery = (
  componentFeatureId: number,
  schemeId?: number,
  filter?: object & AbstractFilterInterface,
  enabled?: boolean,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["schemeOutletsAvailables", componentFeatureId, schemeId, filter],
    enabled: !!componentFeatureId && !!schemeId && !!enabled,
    queryFn: () =>
      axiosGet<{ entities: ProjectOutletInterface[]; count: number }, unknown>(
        `/features/${componentFeatureId}/schemes/${schemeId}/outlets/availables`,
        filter,
      ),
  });
};

export const useSchemeOutletsQuery = (
  componentFeatureId: number,
  schemeId?: number,
  filter?: object & AbstractFilterInterface,
  enabled?: boolean,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["schemeOutlets", componentFeatureId, schemeId, filter],
    enabled: !!componentFeatureId && !!schemeId && !!enabled,
    queryFn: () =>
      axiosGet<
        { entities: FeatureSchemeOutletInterface[]; count: number },
        unknown
      >(`/features/${componentFeatureId}/schemes/${schemeId}/outlets`, filter),
  });
};

export const useCreateSchemeOutletsMutation = (
  componentFeatureId: number,
  schemeId?: number,
) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createSchemeOutlets", componentFeatureId, schemeId],
    mutationFn: (data: { projectOutletIds: number[] }) =>
      axiosPost(
        `/features/${componentFeatureId}/schemes/${schemeId}/outlets`,
        data,
      ),
  });
};

export const useDeleteSchemeOutletsMutation = (
  componentFeatureId: number,
  schemeId?: number,
) => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deleteSchemeOutlets", componentFeatureId, schemeId],
    mutationFn: (data: { featureSchemeOutletIds: number[] }) =>
      axiosDelete(
        `/features/${componentFeatureId}/schemes/${schemeId}/outlets`,
        data,
      ),
  });
};

export const useGetSchemesMutation = (componentFeatureId: number) => {
  const { axiosGet } = useApp();

  return useMutation({
    mutationKey: ["getSchemes", componentFeatureId],
    mutationFn: () =>
      axiosGet<{ entities: FeatureSchemeInterface[]; count: number }, unknown>(
        `/features/${componentFeatureId}/schemes`,
        {
          take: 50,
          skip: 0,
        },
      ),
  });
};

export const useUpdateSchemeMutation = (componentFeatureId: number) => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["updateScheme", componentFeatureId],
    mutationFn: (data: {
      id: number;
      name?: string;
      description?: string;
      maxReceiveQuantity?: number | null;
      isActive?: boolean;
    }) =>
      axiosPatch(`/features/${componentFeatureId}/schemes/${data.id}`, data),
  });
};

export const useLuckyDrawOrderLimitsQuery = (
  componentFeatureId: number,
  filter?: object & AbstractFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["luckyDrawOrderLimits", componentFeatureId, filter],
    queryFn: () =>
      axiosGet<
        {
          entities: LuckyDrawLimitInterface[];
          count: number;
        },
        unknown
      >(`/features/${componentFeatureId}/order/lucky-draw-limits`, filter),
  });
};

export const useCreateLuckyDrawOrderLimitMutation = (
  componentFeatureId: number,
) => {
  const { axiosPost, showNotification } = useApp();

  return useMutation({
    mutationKey: ["createLuckyDrawOrderLimit", componentFeatureId],
    mutationFn: (data: {
      projectLuckyDrawId: number;
      type: "number_of_products" | "number_of_times";
      maximum: number;
    }) =>
      axiosPost(
        `/features/${componentFeatureId}/order/lucky-draw-limits`,
        data,
      ),

    onSuccess: () => {
      showNotification({
        type: "success",
        message: "Tạo mới thành công",
      });
    },
  });
};

export const useUpdateLuckyDrawOrderLimitMutation = (
  componentFeatureId: number,
) => {
  const { axiosPatch, showNotification } = useApp();

  return useMutation({
    mutationKey: ["updateLuckyDrawOrderLimit", componentFeatureId],
    mutationFn: (data: {
      id: number;
      projectLuckyDrawId?: number;
      type?: "number_of_products" | "number_of_times";
      maximum?: number;
      isActive?: boolean;
    }) =>
      axiosPatch(
        `/features/${componentFeatureId}/order/lucky-draw-limits/${data.id}`,
        data,
      ),

    onSuccess: () => {
      showNotification({
        type: "success",
        message: "Cập nhật thành công",
      });
    },
  });
};

export const useDeleteLuckyDrawOrderLimitMutation = (
  componentFeatureId: number,
) => {
  const { axiosDelete, showNotification } = useApp();

  return useMutation({
    mutationKey: ["deleteLuckyDrawOrderLimit", componentFeatureId],
    mutationFn: (orderLuckyDrawLimitId: number) =>
      axiosDelete(
        `/features/${componentFeatureId}/order/lucky-draw-limits/${orderLuckyDrawLimitId}`,
      ),

    onSuccess: () => {
      showNotification({
        type: "success",
        message: "Xoa thành công",
      });
    },
  });
};

export const useLuckyDrawOrderLimitProductsQuery = (
  componentFeatureId: number,
  orderLuckyDrawLimitId: number,
  filter?: AbstractFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: [
      "luckyDrawOrderLimitProducts",
      componentFeatureId,
      orderLuckyDrawLimitId,
      filter,
    ],
    queryFn: () =>
      axiosGet<
        {
          entities: { projectProduct: ProjectProductInterface; id: number }[];
          count: number;
        },
        unknown
      >(
        `/features/${componentFeatureId}/order/lucky-draw-limits/${orderLuckyDrawLimitId}/products`,
        filter,
      ),
  });
};

export const useLuckyDrawOrderLimitProductsAvailableQuery = (
  componentFeatureId: number,
  orderLuckyDrawLimitId: number,
  filter?: AbstractFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: [
      "luckyDrawOrderLimitProductsAvailable",
      componentFeatureId,
      orderLuckyDrawLimitId,
      filter,
    ],
    queryFn: () =>
      axiosGet<
        {
          entities: ProjectProductInterface[];
          count: number;
        },
        unknown
      >(
        `/features/${componentFeatureId}/order/lucky-draw-limits/${orderLuckyDrawLimitId}/products/availables`,
        filter,
      ),
  });
};

export const useAddLuckyDrawOrderLimitProductMutation = (
  componentFeatureId: number,
  orderLuckyDrawLimitId: number,
) => {
  const { axiosPost, showNotification } = useApp();

  return useMutation({
    mutationKey: [
      "addLuckyDrawOrderLimitProduct",
      componentFeatureId,
      orderLuckyDrawLimitId,
    ],
    mutationFn: (projectProductIds: number[]) =>
      axiosPost(
        `/features/${componentFeatureId}/order/lucky-draw-limits/${orderLuckyDrawLimitId}/products`,
        projectProductIds.map((projectProductId) => ({ projectProductId })),
      ),

    onSuccess: () => {
      showNotification({
        type: "success",
        message: "Thêm sản phẩm thành công",
      });
    },
  });
};

export const useDeleteLuckyDrawOrderLimitProductMutation = (
  componentFeatureId: number,
  orderLuckyDrawLimitId: number,
) => {
  const { axiosDelete, showNotification } = useApp();

  return useMutation({
    mutationKey: [
      "deleteLuckyDrawOrderLimitProduct",
      componentFeatureId,
      orderLuckyDrawLimitId,
    ],
    mutationFn: (id: number) =>
      axiosDelete(
        `/features/${componentFeatureId}/order/lucky-draw-limits/${orderLuckyDrawLimitId}/products/${id}`,
      ),

    onSuccess: () => {
      showNotification({
        type: "success",
        message: "Xóa sản phẩm thành công",
      });
    },
  });
};
