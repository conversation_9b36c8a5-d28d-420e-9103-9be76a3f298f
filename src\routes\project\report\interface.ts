import { AbstractEntityInterface } from "@/common/interface";
import { UseQueryResult } from "@tanstack/react-query";
import { FormInstance } from "antd";
import React from "react";
import { ComponentFeatureInterface } from "../component/feature/interface";
import { ProjectBoothInterface } from "../configOutlet/interface";
import { ProjectEmployeeUserInterface } from "../employee/interface";
import { ProjectAgencyInterface, ProjectInterface } from "../interface";
import { ProjectOutletInterface } from "../outlet/interface";
import { RoleInterface } from "../role/interface";
import { RecordAttendanceInterface } from "./types/attendanceClocking/interface";
import {
  RecordMultimediaInterface,
  RecordMultimediaPhotoInterface,
} from "./types/multiSubjectMultimediaInformationCapturing/interface";
import { RecordPhotoInterface } from "./types/photography/interface";

export type ProjectReportOutletContextType = {
  isHideFilter: boolean;
  setIsHideFilter: React.Dispatch<React.SetStateAction<boolean>>;
  componentFeatureQuery: UseQueryResult<ComponentFeatureInterface, Error>;
  advancedFilterForm: FormInstance;
  projectId: number;
  componentFeatureId: number;
  projectComponentId: number;
  advancedFilterValues: AdvancedFilterFormValueInterface;
  roles?: RoleInterface[];
  project?: ProjectInterface;
};

export interface ProjectRecordInterface extends AbstractEntityInterface {
  projectId: number;
  projectAgencyId: number;
  projectOutletId: number;
  projectOutlet: ProjectOutletInterface;
  projectBoothId: number;
  projectBooth: ProjectBoothInterface;
  leaderId: number;
  workday: string;
  projectRecordEmployees: ProjectRecordEmployeeInterface[];
  projectAgency: ProjectAgencyInterface;
  leader: ProjectEmployeeUserInterface;
}

export interface ProjectRecordEmployeeInterface
  extends AbstractEntityInterface {
  projectRecord: ProjectRecordInterface;
  employee: ProjectEmployeeUserInterface;
  recordAttendances: RecordAttendanceInterface[];
  recordPhotos: RecordPhotoInterface[];
}

export interface AdvancedFilterInterface {
  projectAgencyId?: number;
  roleId?: number;
  provinceId?: number;
  districtId?: number;
  wardId?: number;
  channelId?: number;
  subChannelId?: number;
  projectBoothId?: number;
  leaderId?: number;
  attendanceStartDate?: Date;
  attendanceEndDate?: Date;
  keyword?: string;
  startDate?: Date;
  endDate?: Date;
  outletId?: number;
}

export interface AdvancedFilterFormValueInterface {
  projectAgencyId?: number;
  roleId?: number;
  provinceId?: number;
  districtId?: number;
  wardId?: number;
  channelId?: number;
  subChannelId?: number;
  projectBoothId?: number;
  leaderId?: number;
  leader?: { label: string; value: number; key: number };
  attendance?: [Date, Date];
  keyword?: string;
  attendanceStartDate?: Date;
  attendanceEndDate?: Date;
  startDate?: Date;
  endDate?: Date;
}

export type AdvancedFilterFieldType =
  | "Agency phụ trách"
  | "Role ghi nhận"
  | "Nhân viên ghi nhận"
  | "Mã/ Tên outlet"
  | "Ngày chấm công"
  | "Tỉnh/ TP"
  | "Quận/ Huyện"
  | "Kênh"
  | "Phường/ Xã"
  | "Loại booth"
  | "Trưởng nhóm quản lý"
  | "Nhóm"
  | "Thông tin khách"
  | "Cách hiển thị data";

export interface ProjectRecordFeatureInterface extends AbstractEntityInterface {
  projectRecordId: number;
  projectRecord: ProjectRecordInterface;
  projectRecordEmployeeId: number;
  projectRecordEmployee: ProjectRecordEmployeeInterface;
  projectFeatureId: number;
  attendanceId: number;
  attendance: RecordAttendanceInterface;
  recordMultimedias: RecordMultimediaInterface[];
  recordMultimediaPhotos: RecordMultimediaPhotoInterface[];
  recordPhotos: RecordPhotoInterface[];
}
