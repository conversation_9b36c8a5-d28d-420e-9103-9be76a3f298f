import InnerContainer from "@/components/InnerContainer/InnerContainer";
import SuspenseComponent from "@/components/SuspenseComponent";
import { Skeleton, Tabs, type TabsProps } from "antd";
import React, { Suspense } from "react";

// const CheckInOutTab = React.lazy(
//   () => import("./tabs/CheckInOut/CheckInOutTab"),
// );

const CheckInOutV2Tab = React.lazy(
  () => import("./tabs/CheckInOutV2/CheckInOutV2Tab"),
);
const BreaktimeLateEarlyTab = React.lazy(
  () => import("./tabs/BreaktimeLateEarly/BreaktimeLateEarlyTab"),
);
const SalesOutTab = React.lazy(() => import("./tabs/SalesOut/SalesOutTab"));
const PricingTab = React.lazy(() => import("./tabs/Pricing/PricingTab"));
const MainshelfTab = React.lazy(() => import("./tabs/Mainshelf/MainshelfTab"));
const CriticalStockTab = React.lazy(
  () => import("./tabs/CriticalStock/CriticalStockTab"),
);
const CompetitorPATab = React.lazy(
  () => import("./tabs/CompetitorPA/CompetitorPATab"),
);
const CompetitorGiftingBoothPitstopTab = React.lazy(
  () =>
    import(
      "./tabs/CompetitorGiftingBoothPitstop/CompetitorGiftingBoothPitstopTab"
    ),
);

const ProjectDashboadrdV2Page = () => {
  const items: TabsProps["items"] = [
    {
      key: "Check In/Out",
      label: "Check In/Out",
      children: (
        <SuspenseComponent>
          <CheckInOutV2Tab />
        </SuspenseComponent>
      ),
    },
    {
      key: "BreaktimeLateEarly",
      label: "Breaktime/ Late, Early",
      children: (
        <SuspenseComponent>
          <BreaktimeLateEarlyTab />
        </SuspenseComponent>
      ),
    },
    {
      key: "SALES_OUT",
      label: "Sales out",
      children: (
        <SuspenseComponent>
          <SalesOutTab />
        </SuspenseComponent>
      ),
    },
    {
      key: "CRITICAL_STOCK",
      label: "% Critical stock",
      children: (
        <SuspenseComponent>
          <CriticalStockTab />
        </SuspenseComponent>
      ),
    },
    {
      key: "INVENTORY",
      label: "Inventory",
      children: <Suspense fallback={<Skeleton />}></Suspense>,
    },
    {
      key: "PRICING",
      label: "Pricing",
      children: (
        <SuspenseComponent>
          <PricingTab />
        </SuspenseComponent>
      ),
    },
    {
      key: "MAIN_SHELF",
      label: "% Mainshelf, Display share",
      children: (
        <SuspenseComponent>
          <MainshelfTab />
        </SuspenseComponent>
      ),
    },
    {
      key: "COMPETITOR_PA",
      label: "Competitor PA",
      children: (
        <SuspenseComponent>
          <CompetitorPATab />
        </SuspenseComponent>
      ),
    },
    {
      key: "COMPETITOR_GIFTING_BOOTH",
      label: "Competitor Gifting booth, Pitstop",
      children: (
        <SuspenseComponent>
          <CompetitorGiftingBoothPitstopTab />
        </SuspenseComponent>
      ),
    },
  ];

  return (
    <>
      <h2>Dashboard</h2>
      <InnerContainer>
        <Tabs items={items} />
      </InnerContainer>
    </>
  );
};

export default ProjectDashboadrdV2Page;
