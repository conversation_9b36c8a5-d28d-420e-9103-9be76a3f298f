import TableActionCell from "@/components/TableActionCell.tsx";
import { FileSearchOutlined, PlusOutlined } from "@ant-design/icons";
import { Table } from "antd";
import { useCallback, useState } from "react";
import { useParams } from "react-router-dom";
import { FeatureSamplingGroupInterface } from "../../interface.ts";
import { useSamplingGroupsQuery } from "../../service.ts";
import AddOutletSamplingGroupModal from "./AddOutletSamplingGroupModal.tsx";
import ViewOutletSamplingGroupModal from "./ViewOutletSamplingGroupModal.tsx";

const ConfigSamplingOutletPage = () => {
  const projectId = parseInt(useParams().id ?? "0");
  const componentFeatureId = parseInt(useParams().componentFeatureId ?? "0");

  const [isAddOpen, setIsAddOpen] = useState(false);
  const [isViewOpen, setIsViewOpen] = useState(false);
  const [selectedSamplingGroup, setSelectedSamplingGroup] = useState<
    FeatureSamplingGroupInterface | undefined
  >(undefined);

  const samplingGroupsQuery = useSamplingGroupsQuery(componentFeatureId, {
    take: 0,
    skip: 0,
  });

  const addOutlet = useCallback((record: FeatureSamplingGroupInterface) => {
    setIsAddOpen(true);
    setSelectedSamplingGroup(record);
  }, []);

  const viewOutlet = useCallback((record: FeatureSamplingGroupInterface) => {
    setSelectedSamplingGroup(record);
    setIsViewOpen(true);
  }, []);

  return (
    <>
      <div>
        <Table
          pagination={false}
          rowKey={"id"}
          dataSource={samplingGroupsQuery.data?.entities}
          columns={[
            {
              title: "Tên nhóm sampling",
              dataIndex: "name",
            },
            {
              title: "Outlet đã phân bổ",
              dataIndex: "featureSamplingGroupOutletsCount",
            },
            {
              render: (record) => {
                return (
                  <TableActionCell
                    actions={[
                      {
                        key: "add",
                        action: addOutlet,
                      },
                      {
                        key: "view",
                        action: viewOutlet,
                      },
                    ]}
                    items={[
                      {
                        key: "add",
                        label: "Thêm outlet vào nhóm",
                        icon: <PlusOutlined />,
                      },
                      {
                        key: "view",
                        label: "Danh sách outlet trong nhóm",
                        icon: <FileSearchOutlined />,
                      },
                    ]}
                    record={record}
                  />
                );
              },
            },
          ]}
        />
      </div>

      <AddOutletSamplingGroupModal
        isOpen={isAddOpen}
        projectId={projectId}
        componentFeatureId={componentFeatureId}
        samplingGroup={selectedSamplingGroup}
        onCancelCb={async () => {
          setIsAddOpen(false);
          setSelectedSamplingGroup(undefined);
          await samplingGroupsQuery.refetch();
        }}
      />

      <ViewOutletSamplingGroupModal
        isOpen={isViewOpen}
        projectId={projectId}
        componentFeatureId={componentFeatureId}
        samplingGroup={selectedSamplingGroup}
        onCancelCb={async () => {
          setIsViewOpen(false);
          setSelectedSamplingGroup(undefined);
          await samplingGroupsQuery.refetch();
        }}
      />
    </>
  );
};

export default ConfigSamplingOutletPage;
