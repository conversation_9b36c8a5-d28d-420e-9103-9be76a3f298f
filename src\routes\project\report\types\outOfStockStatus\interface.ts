import { AbstractEntityInterface } from "@/common/interface";
import {
  OosProductInterface,
  OosZoneInterface,
} from "@/routes/project/component/feature/config/types/outOfStockStatus/interface";
import { ProjectRecordFeatureInterface } from "../../interface";

export interface RecordOosStatusValue {
  id: number;
  featureOosProduct: OosProductInterface;
  value: number;
}

export interface RecordOosStatusInterface extends AbstractEntityInterface {
  dataUuid: string;
  dataTimestamp: string;
  projectRecordFeature: ProjectRecordFeatureInterface;
  featureOosZoneId: number;
  featureOosZone: OosZoneInterface;
  recordOosStatusValues: RecordOosStatusValue[];
}

export const NOT_SALE_VALUE_BG_COLOR = "#8C8C8D";
export const NOT_SALE_VALUE_TEXT_COLOR = "#FFFFFF";

export const NOT_HAVE_VALUE_BG_COLOR = "#FFFFFF";
export const NOT_HAVE_VALUE_TEXT_COLOR = "#000000";
