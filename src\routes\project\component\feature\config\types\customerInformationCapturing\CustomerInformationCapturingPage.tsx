import { ExclamationCircleFilled } from "@ant-design/icons";
import { Col, <PERSON>u, Modal, Row, Skeleton, Switch } from "antd";
import { Suspense, useCallback, useEffect, useMemo, useState } from "react";
import { Outlet, useLocation, useNavigate, useParams } from "react-router-dom";
import { FeatureTypeEnum } from "../../../interface";
import { OrderEnumToProperty, StepLockEnum } from "./interface";
import { useOrderQuery, useUpdateStepStatusMutation } from "./service";
import "./style.css";

export const StepTitle = (props: {
  title: string;
  enabled?: boolean;
  type: StepLockEnum;
  cb: () => void;
}) => {
  const { title, enabled, type, cb } = props;
  const componentFeatureId = parseInt(useParams().componentFeatureId ?? "0");

  const updateStepStatusMutation =
    useUpdateStepStatusMutation(componentFeatureId);

  const onChange = useCallback(
    async (value: boolean) => {
      if (!value) {
        Modal.confirm({
          title: "Tắt chức năng",
          content: (
            <>
              Bạn muốn tắt chức năng{" "}
              <span className={"font-semibold"}>
                {title.slice(3, title.length)}
              </span>
              ?{" "}
              <span>
                Sau khi tắt, chức năng sẽ không còn khả dụng trên app.
              </span>
            </>
          ),
          onOk: async () => {
            await updateStepStatusMutation.mutateAsync({
              [OrderEnumToProperty[type]]: false,
            });
            cb();
          },
          icon: <ExclamationCircleFilled style={{ color: "#DF3C3C" }} />,
          okType: "danger",
          okText: "Xác nhận",
          centered: true,
        });
      }
    },
    [cb, title, type, updateStepStatusMutation],
  );
  return (
    <div className="flex justify-between">
      <span>{title}</span>
      <span className="" style={{ display: enabled ? "block" : "none" }}>
        <Switch
          checked={enabled}
          onChange={onChange}
          loading={updateStepStatusMutation.isPending}
        />
      </span>
    </div>
  );
};

export default function CustomerInformationCapturingPage() {
  const navigate = useNavigate();
  const location = useLocation();

  const componentFeatureId = parseInt(useParams().componentFeatureId ?? "0");

  const [activeKey, setActiveKey] = useState<string>("purchase");

  const orderLatestQuery = useOrderQuery(componentFeatureId);

  const items: {
    label: JSX.Element;
    key: string;
    type: "group" | "submenu";
    children: { key: string; icon?: JSX.Element; label: string }[];
  }[] = useMemo(() => {
    return [
      {
        key: "1",
        label: (
          <StepTitle
            title={"1. Bán hàng"}
            enabled={orderLatestQuery.data?.hasPurchase}
            type={StepLockEnum.Purchase}
            cb={() => orderLatestQuery.refetch()}
          />
        ),
        children: [
          { key: "purchase", label: "• Cài đặt" },
          {
            key: "purchase/limit",
            label: "• Cài đặt giới hạn",
            disabled: !orderLatestQuery.data?.hasPurchase,
          },
        ],
        type: "group",
      },
      {
        key: "2",
        type: "group",
        label: (
          <StepTitle
            title={"2. Đổi quà"}
            enabled={orderLatestQuery.data?.hasExchange}
            type={StepLockEnum.Exchange}
            cb={() => orderLatestQuery.refetch()}
          />
        ),
        children: [
          { key: "exchange", label: "• Cài đặt scheme quà " },
          {
            key: "exchange/limit",
            label: "• Cài đặt giới hạn nhận quà",
            disabled: !orderLatestQuery.data?.hasExchange,
          },
          {
            key: "exchange/outlet",
            label: "• Phân bổ outlet vào scheme quà ",
            disabled: !orderLatestQuery.data?.hasExchange,
          },
        ],
      },
      {
        key: "3",
        label: (
          <StepTitle
            title={"3. Thông tin khách"}
            enabled={orderLatestQuery.data?.hasCustomer}
            type={StepLockEnum.Customer}
            cb={() => orderLatestQuery.refetch()}
          />
        ),
        children: [{ key: "customer", label: "• Cài đặt" }],
        type: "group",
      },
      {
        key: "4",
        label: (
          <StepTitle
            title={"4. Sampling"}
            enabled={orderLatestQuery.data?.hasSampling}
            type={StepLockEnum.Sampling}
            cb={() => orderLatestQuery.refetch()}
          />
        ),
        children: [
          { key: "sampling", label: "• Cài đặt" },
          {
            key: "sampling/outlet",
            label: "• Phân bổ outlet vào scheme sampling",
            disabled: !orderLatestQuery.data?.hasSampling,
          },
        ],
        type: "group",
      },
      {
        key: "5",
        label: (
          <StepTitle
            title={"5. Chụp hình"}
            enabled={orderLatestQuery.data?.hasPhoto}
            type={StepLockEnum.Photo}
            cb={() => orderLatestQuery.refetch()}
          />
        ),
        children: [{ key: "photo", label: "• Cài đặt" }],
        type: "group",
      },
    ];
  }, [orderLatestQuery]);

  const setRouteActive = useCallback(
    (value: string) => {
      navigate(value);
    },
    [navigate],
  );

  useEffect(() => {
    if (
      location.pathname.endsWith(
        FeatureTypeEnum.CustomerInformationCapturing.toString(),
      )
    ) {
      navigate(`${location.pathname}/purchase`);
    }
  }, [location.pathname, navigate]);

  useEffect(() => {
    const activeItem = items
      .flatMap((item) => item.children)
      .find((item) => location.pathname.endsWith(item.key));
    if (activeItem) {
      setActiveKey(activeItem.key.toString());
    }
  }, [items, setActiveKey, location.pathname]);

  return (
    <div className="bg-white pt-5 pl-10 rounded pb-5 mt-5 pr-10">
      <Row className="" justify={"space-between"}>
        <Col span={4}>
          <Menu
            className="bg-[#FAFAFA] config-customer-menu"
            items={items}
            mode="inline"
            style={{ borderInline: "none" }}
            onClick={({ key }) => setRouteActive(key)}
            selectedKeys={[activeKey]}
          />
        </Col>
        <Col span={19}>
          <Suspense fallback={<Skeleton loading />}>
            <Outlet context={[orderLatestQuery]} />
          </Suspense>
        </Col>
      </Row>
    </div>
  );
}
