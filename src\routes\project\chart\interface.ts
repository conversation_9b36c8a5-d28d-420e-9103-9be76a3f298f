export interface ChartDataInterface {
  projectId: number;
  provinceId?: number;
  provinceName?: string;
  totalValue: number;
  todayValue: number;
  rollingTarget: number;
  kpi: number;
  channelId?: number;
  channelName?: string;
  [key: string]: string | number | undefined;
}

export enum StatisticsTypeEnum {
  SALES_REVENUE = "salesRevenue",
  HIT = "hit",
  SESSION = "session",
  ORDER = "order",
  GIFT = "gift",
  GAME = "game",
  SAMPLING = "sampling",
}

export interface ChartTabProps {
  projectId: number;
  filter: { provinceIds?: number[]; channelIds?: number[] };
  sort: "asc" | "desc" | undefined;
  type: StatisticsTypeEnum;
  activeKey: StatisticsTypeEnum | string;
}

export const StatisticsTypeEnumToLabel = {
  [StatisticsTypeEnum.SALES_REVENUE]: "Doanh số",
  [StatisticsTypeEnum.HIT]: "Sampling (hit)",
  [StatisticsTypeEnum.SESSION]: "Session",
  [StatisticsTypeEnum.ORDER]: "Đơn hàng",
  [StatisticsTypeEnum.GIFT]: "Quà thường",
  [StatisticsTypeEnum.GAME]: "Quà game",
  [StatisticsTypeEnum.SAMPLING]: "Sampling (dry)",
};
