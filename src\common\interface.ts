export interface AbstractEntityInterface {
  id: number;
  createdAt?: string;
  updatedAt?: string;
  isActive?: boolean;
  createdByUser?: {
    id: number;
    username: string;
  };
  updatedByUser?: {
    id: number;
    username: string;
  };
}

export interface AbstractFilterInterface {
  take?: number;
  skip?: number;
  isActive?: boolean;
  keyword?: string;
  sort?: "asc" | "desc";
}

export interface ImageInterface extends AbstractEntityInterface {
  filename: string;
  variants?: string[];
}

export type ImportStatus = "pending" | "doing" | "success" | "error";

export interface FormPhotoInterface {
  type: string;
  image: ImageInterface;
  dataUuid?: string;
  dataTimestamp?: string;
}
