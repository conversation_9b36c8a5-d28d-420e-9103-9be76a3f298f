import useMenuItems from "@/hooks/useMenuItems";
import { useCanPermission } from "@/layouts/MainLayout/hook";
import { Skeleton } from "antd";
import { useCallback, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { PermissionEnum } from "../interface";

const ProjectDetailPage = () => {
  const projectId = parseInt(useParams().id ?? "0");
  const navigate = useNavigate();

  const { canPermissionFunction, isLoading } = useCanPermission(projectId);
  const { menuItems } = useMenuItems(projectId);

  const processProjectDefaultAction = useCallback(() => {
    for (let i = 1; i <= menuItems.length; i++) {
      const menuItem = menuItems[i];
      if (
        menuItem &&
        "children" in menuItem &&
        Array.isArray(menuItem?.children) &&
        menuItem.children.length > 0
      ) {
        for (let j = 0; j < menuItem.children.length; j++) {
          const child = menuItem.children[j];

          if (child) {
            return navigate(child.key?.toString() ?? "");
          }
        }
      }
    }

    if (
      !canPermissionFunction(PermissionEnum.DASHBOARD) &&
      !canPermissionFunction(PermissionEnum.REPORT) &&
      !canPermissionFunction(PermissionEnum.PROFILE) &&
      !canPermissionFunction(PermissionEnum.TOOL) &&
      !canPermissionFunction(PermissionEnum.EMPLOYEE) &&
      !canPermissionFunction(PermissionEnum.NONE_ACCESS)
    ) {
      return navigate(`/forbidden`);
    }
  }, [canPermissionFunction, menuItems, navigate]);

  useEffect(() => {
    if (!isLoading) {
      processProjectDefaultAction();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLoading, menuItems]);

  return <Skeleton active title />;
};

export default ProjectDetailPage;
