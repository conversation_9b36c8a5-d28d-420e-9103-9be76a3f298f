import { ArrowLeftOutlined } from "@ant-design/icons";
import { Skeleton } from "antd";
import { Suspense } from "react";
import { Link, Outlet, useParams } from "react-router-dom";

const EditLayout = () => {
  const projectId = parseInt(useParams().id ?? "0");

  return (
    <>
      <Link to={`/project/${projectId}/edit`}>
        <p className={"text-hint mt-[34px]"}>
          <ArrowLeftOutlined /> Quay lại
        </p>
      </Link>
      <Suspense fallback={<Skeleton active />}>
        <Outlet />
      </Suspense>
    </>
  );
};

export default EditLayout;
