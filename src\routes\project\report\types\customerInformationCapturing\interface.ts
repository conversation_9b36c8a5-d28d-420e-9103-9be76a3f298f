import { AbstractEntityInterface } from "@/common/interface";
import { ProjectItemInterface } from "@/routes/project/item/interface";
import {
  CustomerFeatureSamplingInterface,
  FeatureCustomerInterface,
  FeatureSchemeExchangeInterface,
} from "@project/component/feature/config/types/customerInformationCapturing/interface";
import { ProjectProductInterface } from "@project/product/interface";
import { ProjectRecordFeatureInterface } from "../../interface";
import { RecordPhotoInterface } from "../photography/interface";

export interface RecordOrderPhotoInterface extends AbstractEntityInterface {
  recordOrderId: number;
  recordPhotoId: number;
  recordPhoto: RecordPhotoInterface;
}

export interface RecordOrderExchangeInterface extends AbstractEntityInterface {
  featureSchemeExchangeId: number;
  featureSchemeExchange: FeatureSchemeExchangeInterface;
  quantity: number;
}

export interface RecordOrderSamplingInterface extends AbstractEntityInterface {
  featureSamplingId: number;
  featureSampling: CustomerFeatureSamplingInterface;
  quantity: number;
}

export interface RecordOrderPurchaseInterface extends AbstractEntityInterface {
  featureOrderProductId: number;
  featureOrderProduct: {
    id: number;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
    projectProduct: ProjectProductInterface;
    price: number;
    projectProductId: number;
  };
  quantity: number;
}

export interface RecordCustomerOptionInterface extends AbstractEntityInterface {
  featureCustomerOptionId: number;
  featureCustomerOption: {
    id: number;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
    name: string;
  };
}

export interface RecordCustomerInterface extends AbstractEntityInterface {
  featureCustomerId: number;
  featureCustomer?: FeatureCustomerInterface;
  value: string | null;
  recordCustomerOptions: RecordCustomerOptionInterface[];
  projectCustomerId: number;
  otpDelivery?: {
    id: number;
    otpCode: string;
    verified: boolean;
  };
}

export interface RecordOrderCustomerInterface extends AbstractEntityInterface {
  recordCustomerId: number;
  recordCustomer: RecordCustomerInterface;
}

interface ProjectLuckyDrawItemInterface extends AbstractEntityInterface {
  projectItem: ProjectItemInterface;
  ordinal: number;
}

interface ProjectLuckyDrawInterface extends AbstractEntityInterface {
  name: string;
  description: string | null;
}

export interface ProjectLuckyDrawResultInterface
  extends AbstractEntityInterface {
  projectLuckyDraw: ProjectLuckyDrawInterface;
  projectLuckyDrawItem: ProjectLuckyDrawItemInterface;
}

export interface RecordOrderPrizeInterface extends AbstractEntityInterface {
  projectLuckyDrawResult: ProjectLuckyDrawResultInterface;
}

export interface RecordOrderInterface extends AbstractEntityInterface {
  dataUuid: string;
  dataTimestamp: string;
  projectRecordFeature: ProjectRecordFeatureInterface;
  recordOrderCustomers?: RecordOrderCustomerInterface[];
  recordOrderPurchases?: RecordOrderPurchaseInterface[];
  recordOrderExchanges?: RecordOrderExchangeInterface[];
  recordOrderSamplings?: RecordOrderSamplingInterface[];
  recordOrderPhotos?: RecordOrderPhotoInterface[];
  recordOrderPrizes: RecordOrderPrizeInterface[];
}
