import { getImageVariants } from "@/common/image.helper";
import { ImageInterface } from "@/common/interface";
import { PlusOutlined } from "@ant-design/icons";
import { Form, Upload, UploadProps } from "antd";
import { UploadFile } from "antd/lib";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useFormPhotosStore } from "./state";

interface FormImageUploadComponentProps {
  label: string;
  fieldName: string[] | string;
  max?: number;
  required?: boolean;
  imagesFile?: ImageInterface[];
  requiredOnForm?: boolean;
}

const FormImageUploadComponent = ({
  label,
  fieldName,
  max = 1,
  required = false,
  requiredOnForm = false,
  imagesFile = [],
}: FormImageUploadComponentProps) => {
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [isSetFileList, setIsSetFileList] = useState(false);
  const { setFormPhotos: setPhotos, formPhotos: photos } = useFormPhotosStore();

  const handleChange: UploadProps["onChange"] = ({
    fileList: newFileList,
    file,
  }) => {
    setFileList(newFileList);
    setPhotos(photos.filter((photo) => photo.image.id.toString() !== file.uid));
  };

  const uploadButton = (
    <button style={{ border: 0, background: "none" }} type="button">
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </button>
  );

  const beforeUpload = useCallback(async () => {
    return false;
  }, []);

  useEffect(() => {
    if (imagesFile?.length && !isSetFileList) {
      setFileList(
        imagesFile.map((image) => ({
          uid: image.id.toString(),
          name: image.filename,
          status: "done",
          url: getImageVariants(image.variants ?? [], "public"),
        })),
      );
      setIsSetFileList(true);
    }
  }, [imagesFile, isSetFileList, setPhotos]);

  const rules = useMemo(() => {
    if (requiredOnForm) {
      return [{ required: true }];
    }
    return [];
  }, [requiredOnForm]);

  return (
    <Form.Item label={label} name={fieldName} required={required} rules={rules}>
      <Upload
        listType="picture-card"
        accept="image/*"
        beforeUpload={beforeUpload}
        maxCount={max}
        onChange={handleChange}
        fileList={fileList}
      >
        {(() => {
          if (fileList.length < max) return uploadButton;
        })()}
      </Upload>
    </Form.Item>
  );
};

export default FormImageUploadComponent;
