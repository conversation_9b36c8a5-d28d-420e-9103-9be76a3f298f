import InputNumberInCell from "@/components/InputNumberInCell";
import { LoadingOutlined } from "@ant-design/icons";
import { Space } from "antd";
import { useUpdateSchemeMutation } from "../../service";

interface InputNumberLimitOrderProps {
  maxReceiveQuantity: number | null;
  id: number;
  componentFeatureId: number;
}

const InputNumberLimitOrder = ({
  maxReceiveQuantity,
  id,
  componentFeatureId,
}: InputNumberLimitOrderProps) => {
  const updateSchemeMutation = useUpdateSchemeMutation(componentFeatureId);

  return (
    <Space>
      <InputNumberInCell
        initValue={maxReceiveQuantity}
        onSubmit={async (value) => {
          await updateSchemeMutation.mutateAsync({
            id,
            maxReceiveQuantity: value ?? null,
          });
        }}
      />
      {updateSchemeMutation.isPending && <LoadingOutlined />}
    </Space>
  );
};

export default InputNumberLimitOrder;
