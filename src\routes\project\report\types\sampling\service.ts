import { useMutation, useQuery } from "@tanstack/react-query";
import { AbstractFilterInterface } from "@/common/interface.ts";
import { AppContextInterface } from "@/interface.ts";
import { useApp } from "@/UseApp.tsx";
import { RecordSamplingInterface } from "./interface.ts";

export const getReportSamplings = async (
  axiosGet: AppContextInterface["axiosGet"],
  projectId: number,
  componentFeatureId: number,
  filter?: object & AbstractFilterInterface,
) =>
  axiosGet<
    {
      entities: RecordSamplingInterface[];
      count: number;
    },
    unknown
  >(
    `/projects/${projectId}/report/features/${componentFeatureId}/samplings`,
    filter,
  );

export const useReportSamplingsQuery = (
  projectId: number,
  componentFeatureId: number,
  filter?: object & AbstractFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["reportSamplings", projectId, componentFeatureId, filter],
    queryFn: async () =>
      getReportSamplings(axiosGet, projectId, componentFeatureId, filter),
  });
};

export const useGetReportSamplingsMutation = (
  projectId: number,
  componentFeatureId: number,
) => {
  const { axiosGet } = useApp();

  return useMutation({
    mutationKey: ["getReportSamplings", projectId, componentFeatureId],
    mutationFn: async (filter?: object & AbstractFilterInterface) =>
      getReportSamplings(axiosGet, projectId, componentFeatureId, filter),
  });
};
