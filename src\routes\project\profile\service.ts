import { useApp } from "@/UseApp.tsx";
import { AbstractFilterInterface } from "@/common/interface.ts";
import { useMutation, useQuery } from "@tanstack/react-query";
import { ProfileInterface } from "../employee/interface.ts";
import { ProfileRequestInterface } from "./interface.ts";

export interface ProjectUserProfilesQueryFilterInterface {
  keyword?: string;
  reviewedAt?: string;
  createdAt?: string;
  userId?: string;
  id?: number;
}

export const useProjectUserProfilesQuery = (
  projectId: number,
  filter: AbstractFilterInterface & ProjectUserProfilesQueryFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["projectUserProfiles", projectId, filter],
    queryFn: () =>
      axiosGet<
        {
          entities: ProfileRequestInterface[];
          count: number;
        },
        unknown
      >(`/projects/${projectId}/user-profiles`, filter),
  });
};

export const useApproveUserProfilesMutation = (projectId: number) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["approveUserProfiles", projectId],
    mutationFn: ({ ids }: { ids: number[] }) =>
      axiosPost(`/projects/${projectId}/user-profiles/approvals`, {
        userProfileIds: ids,
      }),
  });
};

export const useRejectUserProfilesMutation = (projectId: number) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["rejectUserProfiles", projectId],
    mutationFn: ({ ids, reason }: { ids: number[]; reason: string }) =>
      axiosPost(`/projects/${projectId}/user-profiles/rejections`, {
        userProfileIds: ids,
        reason,
      }),
  });
};

export const useUserProfileQuery = (projectId: number, id?: number) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["userProfile", projectId, id],
    queryFn: () =>
      axiosGet<ProfileInterface, unknown>(
        `/projects/${projectId}/user-profiles/${id}`,
      ),
    enabled: !!id,
  });
};

export const useUserProfileLinkedQuery = (projectId: number, id?: number) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["userProfileLinked", projectId, id],
    queryFn: () =>
      axiosGet<ProfileInterface, unknown>(
        `/projects/${projectId}/user-profiles/${id}/linked`,
      ),
    enabled: !!id,
  });
};
