import { CURD } from "@/common/constant.ts";
import TableActionCell from "@/components/TableActionCell.tsx";
import { useApp } from "@/UseApp.tsx";
import {
  DeleteOutlined,
  EditOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined,
} from "@ant-design/icons";
import {
  EmployeeActionEnum,
  ProjectEmployeeUserInterface,
} from "@project/employee/interface.ts";
import ProfileModal from "@project/employee/ProfileModal.tsx";
import {
  useDeleteEmployeeMutation,
  useUpdateEmployeeMutation,
} from "@project/employee/service.ts";
import EmployeeTabModal from "@project/employee/tabs/employee-tab/EmployeeTabModal.tsx";
import { RoleInterface } from "@project/role/interface.ts";
import { Modal } from "antd";
import { useCallback, useMemo, useState } from "react";
import { useParams } from "react-router-dom";

interface EmployeeActionCellProps {
  record: ProjectEmployeeUserInterface;
  role: RoleInterface;
  cb: () => void;
  leaderRole?: RoleInterface;
}

const EmployeeActionCell = ({
  record,
  role,
  cb,
  leaderRole,
}: EmployeeActionCellProps) => {
  const { showNotification, openDeleteModal } = useApp();
  const projectId = parseInt(useParams().id ?? "0");

  const [selectedProjectEmployeeUser, setSelectedProjectEmployeeUser] =
    useState<ProjectEmployeeUserInterface | undefined>(undefined);
  const [action, setAction] = useState<CURD | null>(null);
  const [isProfileOpen, setIsProfileOpen] = useState(false);

  const updateEmployeeMutation = useUpdateEmployeeMutation(projectId);
  const deleteEmployeeMutation = useDeleteEmployeeMutation(projectId);

  const actionItems = useMemo(
    () => [
      {
        key: EmployeeActionEnum.EDIT,
        label: "Chỉnh sửa",
        icon: <EditOutlined />,
      },
      {
        key: EmployeeActionEnum.PROFILE,
        label: "Chỉnh sửa profile",
        icon: <EditOutlined />,
      },
      {
        key: EmployeeActionEnum.INACTIVE,
        label: "Ngừng hoạt động",
        icon: <PauseCircleOutlined />,
      },
      {
        key: EmployeeActionEnum.ACTIVE,
        label: "Khôi phục",
        icon: <PlayCircleOutlined />,
      },
      {
        key: EmployeeActionEnum.DELETE,
        label: "Xóa khỏi dự án",
        icon: <DeleteOutlined />,
      },
    ],
    [],
  );

  const ACTION_ACTIVE = useMemo(
    () => [
      EmployeeActionEnum.INACTIVE,
      EmployeeActionEnum.DELETE,
      EmployeeActionEnum.EDIT,
      EmployeeActionEnum.PROFILE,
    ],
    [],
  );
  const ACTION_INACTIVE = useMemo(
    () => [
      EmployeeActionEnum.ACTIVE,
      EmployeeActionEnum.DELETE,
      EmployeeActionEnum.EDIT,
      EmployeeActionEnum.PROFILE,
    ],
    [],
  );

  const handleActionInActiveClick = useCallback(
    (record: ProjectEmployeeUserInterface) => {
      Modal.confirm({
        title: `Ngừng hoạt động ${role.name}: ${record.user.name}`,
        content: `Bạn có chắc chắn muốn ngừng hoạt động ${role.name} này?`,
        okText: "Ngừng hoạt động",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await updateEmployeeMutation.mutateAsync({
              id: record.id,
              isActive: false,
            });

            cb();

            showNotification({
              type: "success",
              message: `Ngừng hoạt động ${role.name} thành công`,
            });
          } catch (error) {
            console.error(error);

            showNotification({
              type: "error",
              message: `Ngừng hoạt động ${role.name} thất bại`,
            });
          }
        },
      });
    },
    [cb, role.name, showNotification, updateEmployeeMutation],
  );

  const handleActionActiveClick = useCallback(
    (record: ProjectEmployeeUserInterface) => {
      Modal.confirm({
        title: `Kích hoạt ${role.name}: ${record.user.name}`,
        content: `Bạn có chắc chắn muốn kích hoạt ${role.name} này?`,
        okText: "Kích hoạt",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await updateEmployeeMutation.mutateAsync({
              id: record.id,
              isActive: true,
            });

            showNotification({
              type: "success",
              message: `Kích hoạt ${role.name} thành công`,
            });

            cb();
          } catch (error) {
            console.error(error);

            showNotification({
              type: "error",
              message: `Kích hoạt ${role.name} thất bại`,
            });
          }
        },
      });
    },
    [cb, role.name, showNotification, updateEmployeeMutation],
  );

  const handleActionDeleteClick = useCallback(
    (record: ProjectEmployeeUserInterface) => {
      openDeleteModal({
        content: (
          <p>
            Bạn muốn xóa nhân viên{" "}
            <span className={"font-semibold"}>
              {record.user.name} - {record.user.phone}
            </span>{" "}
            khỏi dự án?
          </p>
        ),
        deleteText: "Xác nhận xóa",
        loading: false,
        onCancel(): void {},
        onDelete: async () => {
          await deleteEmployeeMutation.mutateAsync(record.id);
          showNotification({
            type: "success",
            message: "Xóa nhân viên field thành công",
          });

          cb();
        },
        title: `Xóa nhân viên field`,
        titleError: "Không thể xóa nhân viên field",
        contentHeader: (
          <>
            Không thể xóa nhân viên{" "}
            <span className={"font-semibold"}>
              {record.user.name} - {record.user.phone}
            </span>{" "}
            khỏi dự án bởi vì:
          </>
        ),
      });
    },
    [cb, deleteEmployeeMutation, openDeleteModal, showNotification],
  );

  const handleActionEditClick = useCallback(
    (record: ProjectEmployeeUserInterface) => {
      setSelectedProjectEmployeeUser(record);
      setAction(CURD.UPDATE);
    },
    [],
  );

  const handleActionProfileClick = useCallback(
    (record: ProjectEmployeeUserInterface) => {
      setSelectedProjectEmployeeUser(record);
      setIsProfileOpen(true);
    },
    [],
  );

  const actionActions = [
    {
      key: EmployeeActionEnum.EDIT,
      action: handleActionEditClick,
    },
    {
      key: EmployeeActionEnum.INACTIVE,
      action: handleActionInActiveClick,
    },
    {
      key: EmployeeActionEnum.ACTIVE,
      action: handleActionActiveClick,
    },
    {
      key: EmployeeActionEnum.DELETE,
      action: handleActionDeleteClick,
    },
    {
      key: EmployeeActionEnum.PROFILE,
      action: handleActionProfileClick,
    },
  ];

  const actionKeys = useMemo(
    () => (record.isActive ? ACTION_ACTIVE : ACTION_INACTIVE),
    [ACTION_ACTIVE, ACTION_INACTIVE, record.isActive],
  );
  const items = useMemo(
    () => actionItems.filter((item) => actionKeys.includes(item.key)),
    [actionItems, actionKeys],
  );

  return (
    <>
      <TableActionCell actions={actionActions} items={items} record={record} />

      {isProfileOpen && (
        <ProfileModal
          isOpen={isProfileOpen}
          onCloseCb={() => {
            setSelectedProjectEmployeeUser(undefined);
            setIsProfileOpen(false);
          }}
          projectEmployeeUser={selectedProjectEmployeeUser}
          projectId={projectId}
        />
      )}

      {!!action && (
        <EmployeeTabModal
          action={action}
          role={role}
          leaderRole={leaderRole}
          projectId={projectId}
          cb={() => {
            setAction(null);
            cb();
          }}
          selectedProjectEmployeeUser={selectedProjectEmployeeUser}
          cancelCb={() => setAction(null)}
        />
      )}
    </>
  );
};

export default EmployeeActionCell;
