import {
  ACTIVE_LABEL,
  CHUNK_SIZE,
  DEFAULT_CURRENT_PAGE,
  DEFAULT_PAGE_SIZE,
  INACTIVE_LABEL,
} from "@/common/constant";
import { createFileAndDownLoad } from "@/common/export-excel.helper";
import { filterOption } from "@/common/helper";
import CustomTable from "@/components/CustomTable/CustomTable";
import FilterClassicComponent from "@/components/FilterClassicComponent";
import UserOptionComponent from "@/components/UserOptionComponent";
import { renderTableCell } from "@/components/table-cell";
import { UserInterface } from "@/routes/user/interface";
import { SearchOutlined } from "@ant-design/icons";
import Employee<PERSON>eaderAction<PERSON>ell from "@project/employee/tabs/employee-leader-tab/EmployeeLeaderActionCell.tsx";
import EmployeeActionCell from "@project/employee/tabs/employee-tab/EmployeeActionCell.tsx";
import { useProjectAgenciesQuery } from "@project/general/services";
import { ProjectAgencyInterface } from "@project/interface";
import { RoleInterface } from "@project/role/interface";
import { Form, Input, Select } from "antd";
import { ColumnsType } from "antd/es/table";
import _ from "lodash";
import { useCallback, useEffect, useMemo, useState } from "react";
import {
  EmployeeFilterInterface,
  GENDER_ENUM_TO_LABEL,
  GenderEnum,
  ProjectEmployeeUserInterface,
} from "../../interface";
import { useEmployeesQuery, useGetEmployeeMutation } from "../../service";

const EmployeeAllTab = (props: {
  projectId: number;
  roles: RoleInterface[];
  selectedTab: string;
}) => {
  const { projectId, roles, selectedTab } = props;

  const [searchForm] = Form.useForm();
  const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [filter, setFilter] = useState<EmployeeFilterInterface>({});
  const [exportLoading, setExportLoading] = useState(false);

  const employeesQuery = useEmployeesQuery(projectId, {
    ...filter,
    take: pageSize,
    skip: (currentPage - 1) * pageSize,
  });
  const projectAgenciesQuery = useProjectAgenciesQuery(projectId);

  const getEmployeeMutation = useGetEmployeeMutation(projectId);

  const searchHandler = useCallback(() => {
    const values = searchForm.getFieldsValue();
    setCurrentPage(DEFAULT_CURRENT_PAGE);
    if (_.isEqual(filter, values)) {
      employeesQuery.refetch();
      return;
    }
    setFilter(values);
  }, [employeesQuery, filter, searchForm]);

  useEffect(() => {
    if (selectedTab === "ALL") {
      searchForm.resetFields();
      searchHandler();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedTab]);

  const searchContent = (
    <>
      <Form.Item name="keyword">
        <Input
          placeholder="Tìm theo tên, sđt, email, username"
          prefix={<SearchOutlined />}
          allowClear
          className={"h-10"}
        />
      </Form.Item>
      <Form.Item name="roleId">
        <Select
          allowClear
          className={"h-10"}
          placeholder="Vị trí"
          showSearch
          optionFilterProp="children"
          filterOption={filterOption}
          options={roles.map((role) => ({
            label: role.name,
            value: role.id,
          }))}
          popupMatchSelectWidth={false}
        />
      </Form.Item>
      <Form.Item name="projectAgencyId">
        <Select
          allowClear
          className={"h-10"}
          placeholder="Agency phụ trách"
          showSearch
          optionFilterProp="children"
          filterOption={filterOption}
          options={projectAgenciesQuery.data?.map((projectAgency) => ({
            label: projectAgency.agency.name,
            value: projectAgency.id,
          }))}
        />
      </Form.Item>

      <Form.Item name="isActive">
        <Select
          allowClear
          className={"h-10"}
          placeholder="Tình trạng"
          optionFilterProp="children"
          filterOption={filterOption}
          options={[
            {
              label: ACTIVE_LABEL,
              value: 1,
            },
            {
              label: INACTIVE_LABEL,
              value: 0,
            },
          ]}
          popupMatchSelectWidth={false}
        />
      </Form.Item>
    </>
  );

  const columns: ColumnsType<ProjectEmployeeUserInterface> = [
    {
      title: "Nhân viên",
      dataIndex: "user",
      key: "user.name",
      render: (value: UserInterface) => {
        if (!value) return "-";
        return (
          <UserOptionComponent
            avatarUrl={value.picture}
            name={value.name}
            phone={value.phone}
            email={value.email}
            isAvailable={true}
          />
        );
      },
    },
    {
      title: "Username",
      dataIndex: "user",
      key: "user.username",
      render: (value: UserInterface) => value?.username,
    },
    {
      title: "Vị trí",
      dataIndex: "role",
      key: "role",
      render: renderTableCell,
    },
    {
      title: "Mã nhân viên",
      dataIndex: "user",
      key: "user.username",
      render: (value: UserInterface) => value?.code,
      className: "min-w-[100px]",
    },
    {
      title: "Agency phụ trách",
      dataIndex: "projectAgency",
      key: "projectAgency",
      render: (value?: ProjectAgencyInterface) => value?.agency?.name,
    },
    {
      title: "Tình trạng",
      dataIndex: "isActive",
      key: "isActive",
      render: (_, record: ProjectEmployeeUserInterface, index) => {
        return renderTableCell(_, record, index, "isActive");
      },
    },
    {
      key: "actions",
      render: (_, record) => {
        const { role } = record;
        if (role.isLeader) {
          return (
            <EmployeeLeaderActionCell
              record={record}
              role={role}
              cb={() => {
                employeesQuery.refetch();
              }}
            />
          );
        } else {
          return (
            <EmployeeActionCell
              record={record}
              role={role}
              cb={() => {
                employeesQuery.refetch();
              }}
              leaderRole={roles.find((r) => r.isLeader)}
            />
          );
        }
      },
      fixed: "right",
      width: 100,
    },
  ];

  const handlePaginationChange = useCallback(
    (page: number) => {
      setCurrentPage(page);
    },
    [setCurrentPage],
  );

  const handlePageSizeChange = useCallback(
    (_current: number, size: number) => {
      setPageSize(size);
      setCurrentPage(DEFAULT_CURRENT_PAGE);
    },
    [setPageSize, setCurrentPage],
  );

  const pagination = useMemo(() => {
    return {
      current: currentPage,
      total: employeesQuery.data?.count,
      pageSize: pageSize,
      onChange: handlePaginationChange,
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: handlePageSizeChange,
    };
  }, [
    currentPage,
    employeesQuery.data?.count,
    pageSize,
    handlePaginationChange,
    handlePageSizeChange,
  ]);

  const handleExcelButtonClick = useCallback(async () => {
    const total = pagination.total ?? 0;

    if (total === 0) {
      return;
    }

    const fetchAllData = async () => {
      const requests = [];

      for (let skip = 0; skip < total; skip += CHUNK_SIZE) {
        requests.push(
          getEmployeeMutation.mutateAsync({
            take: CHUNK_SIZE,
            skip: skip,
            ...filter,
          }),
        );
      }

      const results = await Promise.all(requests);
      const allEntities = results.flatMap((result) => result.entities);

      return allEntities;
    };

    setExportLoading(true);
    try {
      const allData = await fetchAllData();

      const data = allData.map((projectEmployeeUser) => {
        const { user, role, projectAgency, isActive } = projectEmployeeUser;

        const { name, username, email, gender } = user;

        return [
          name,
          username,
          email,
          GENDER_ENUM_TO_LABEL[gender as GenderEnum],
          role.name,
          projectAgency?.agency.name,
          isActive ? ACTIVE_LABEL : INACTIVE_LABEL,
        ];
      });

      const headers = [
        "Họ và tên",
        "Số điện thoại/ Username",
        "Email",
        "Giới tính",
        "Vị trí",
        "Agency phụ trách",
        "Tình trạng",
      ];

      const fileName = `Danh sach nhan vien trong du an ID ${projectId}`;

      await createFileAndDownLoad({ data, headers, fileName });
    } catch (e) {
      console.error(e);
    } finally {
      setExportLoading(false);
    }
  }, [filter, getEmployeeMutation, pagination.total, projectId]);

  return (
    <>
      <FilterClassicComponent
        searchHandler={searchHandler}
        searchForm={searchForm}
        content={searchContent}
        showAddButton={false}
        btnLoading={employeesQuery.isFetching || exportLoading}
        className={"mb-6"}
        showExportButton
        handleExcelButtonClick={handleExcelButtonClick}
      />

      <CustomTable
        dataSource={employeesQuery.data?.entities}
        columns={columns}
        scroll={{
          x: "max-content",
          y: pagination.total ? "80vh" : undefined,
        }}
        pagination={pagination}
        loading={employeesQuery.isFetching}
      />
    </>
  );
};

export default EmployeeAllTab;
