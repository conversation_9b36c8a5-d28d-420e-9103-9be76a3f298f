import { useApp } from "@/UseApp";
import { AbstractFilterInterface } from "@/common/interface";
import { useMutation, useQuery } from "@tanstack/react-query";
import { MultiSubjectMultimediaInformationCapturingInterface } from "./interface";

export const useCreateMultiSubjectMultimediaInformationCapturingMutation = (
  componentFeatureId: number,
) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: [
      "createMultiSubjectMultimediaInformationCapturing",
      componentFeatureId,
    ],
    mutationFn: (data: {
      title: string;
      description?: string;
      minimumImages: number;
      maximumImages: number;
      isWatermarkRequired: boolean;
      isTextFieldRequired: boolean;
    }) => axiosPost(`/features/${componentFeatureId}/multimedias`, data),
  });
};

export const useUpdateMultiSubjectMultimediaInformationCapturingMutation = (
  componentFeatureId: number,
) => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: [
      "updateMultiSubjectMultimediaInformationCapturing",
      componentFeatureId,
    ],
    mutationFn: (data: {
      id: number;
      title: string;
      description?: string;
      minimumImages: number;
      maximumImages: number;
      isWatermarkRequired: boolean;
      isTextFieldRequired: boolean;
    }) =>
      axiosPatch(
        `/features/${componentFeatureId}/multimedias/${data.id}`,
        data,
      ),
  });
};

export const useUpdateIsActiveMultiSubjectMultimediaInformationCapturingMutation =
  (componentFeatureId: number) => {
    const { axiosPatch } = useApp();

    return useMutation({
      mutationKey: [
        "updateIsActiveMultiSubjectMultimediaInformationCapturing",
        componentFeatureId,
      ],
      mutationFn: (data: { isActive: boolean; id: number }) =>
        axiosPatch(
          `/features/${componentFeatureId}/multimedias/${data.id}`,
          data,
        ),
    });
  };

export const useDeleteMultiSubjectMultimediaInformationCapturingMutation = (
  componentFeatureId: number,
) => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: [
      "deleteMultiSubjectMultimediaInformationCapturing",
      componentFeatureId,
    ],
    mutationFn: (id: number) =>
      axiosDelete(`/features/${componentFeatureId}/multimedias/${id}`),
  });
};

export const useMultiSubjectMultimediaInformationCapturingsQuery = (
  componentFeatureId: number,
  filter?: object & AbstractFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: [
      "multiSubjectMultimediaInformationCapturings",
      componentFeatureId,
      filter,
    ],
    queryFn: () =>
      axiosGet<
        {
          entities: MultiSubjectMultimediaInformationCapturingInterface[];
          count: number;
        },
        unknown
      >(`/features/${componentFeatureId}/multimedias`, filter),
  });
};

export const useArrangementMultiSubjectMultimediaInformationCapturingMutation =
  (componentFeatureId: number) => {
    const { axiosPut } = useApp();

    return useMutation({
      mutationKey: [
        "arrangementMultiSubjectMultimediaInformationCapturing",
        componentFeatureId,
      ],
      mutationFn: (data: { activeId: number; overId: number }) =>
        axiosPut(
          `/features/${componentFeatureId}/multimedias/${data.activeId}/arrangement`,
          {
            overFeatureMultimediaId: data.overId,
          },
        ),
    });
  };
