import { AbstractFilterInterface } from "@/common/interface";
import { useApp } from "@/UseApp";
import { useMutation, useQuery } from "@tanstack/react-query";
import { RecordNumericInterface } from "./interface";

export const useReportNumericSheetsQuery = (
  projectId: number,
  featureId: number,
  filter?: AbstractFilterInterface & object,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["reportNumericSheets", projectId, featureId, filter],
    queryFn: () =>
      axiosGet<{ entities: RecordNumericInterface[]; count: number }, unknown>(
        `/projects/${projectId}/reports/features/${featureId}/numeric-sheets`,
        filter,
      ),
  });
};

export const useGetReportNumericSheetMutation = (
  projectId: number,
  featureId: number,
) => {
  const { axiosGet } = useApp();

  return useMutation({
    mutationKey: ["getReportNumericSheet", projectId, featureId],
    mutationFn: (filter?: AbstractFilterInterface & object) =>
      axiosGet<{ entities: RecordNumericInterface[]; count: number }, unknown>(
        `/projects/${projectId}/reports/features/${featureId}/numeric-sheets`,
        filter,
      ),
  });
};
