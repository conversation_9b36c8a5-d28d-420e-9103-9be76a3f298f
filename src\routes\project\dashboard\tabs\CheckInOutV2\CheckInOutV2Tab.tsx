import ChartContanier from "@/routes/project/chart/ChartContanier";
import { Column } from "@ant-design/charts";
import { Col, Row } from "antd";
import { useCallback } from "react";
import Chart from "react-apexcharts";
import DashboardFilterZone from "../../DashboardFilterZone";

const CheckInOutV2Tab = () => {
  const handleApply = useCallback(() => {}, []);

  const data = [
    { date: "20/12", shift: "Ca sáng", value: 6.5 },
    { date: "20/12", shift: "Ca chiều", value: 6.3 },
    { date: "21/12", shift: "Ca sáng", value: 6.5 },
    { date: "21/12", shift: "Ca chiều", value: 6.3 },
    { date: "22/12", shift: "Ca sáng", value: 6.4 },
    { date: "22/12", shift: "Ca chiều", value: 6.3 },
    { date: "23/12", shift: "Ca sáng", value: 6.4 },
    { date: "23/12", shift: "Ca chiều", value: 6.1 },
    { date: "24/12", shift: "Ca sáng", value: 6.5 },
    { date: "24/12", shift: "Ca chiều", value: 6.3 },
    { date: "25/12", shift: "Ca sáng", value: 6.7 },
    { date: "25/12", shift: "Ca chiều", value: 6.7 },
    { date: "26/12", shift: "Ca sáng", value: 6.7 },
    { date: "26/12", shift: "Ca chiều", value: 6.5 },
    { date: "27/12", shift: "Ca sáng", value: 6.1 },
    { date: "27/12", shift: "Ca chiều", value: 6.3 },
    { date: "28/12", shift: "Ca sáng", value: 6.7 },
    { date: "28/12", shift: "Ca chiều", value: 6.6 },
    { date: "29/12", shift: "Ca sáng", value: 6.8 },
    { date: "29/12", shift: "Ca chiều", value: 6.7 },
    { date: "30/12", shift: "Ca sáng", value: 6.7 },
    { date: "30/12", shift: "Ca chiều", value: 6.1 },
    { date: "31/12", shift: "Ca sáng", value: 6.8 },
    { date: "31/12", shift: "Ca chiều", value: 6.3 },
    { date: "1/1", shift: "Ca sáng", value: 6.7 },
    { date: "1/1", shift: "Ca chiều", value: 6.3 },
    { date: "2/1", shift: "Ca sáng", value: 6.1 },
    { date: "2/1", shift: "Ca chiều", value: 6.5 },
    { date: "3/1", shift: "Ca sáng", value: 6.7 },
    { date: "3/1", shift: "Ca chiều", value: 6.3 },
  ];

  const config = {
    data,
    isGroup: true,
    xField: "date",
    yField: "value",
    seriesField: "shift",
    columnStyle: {
      radius: [2, 2, 0, 0],
    },
    color: ["#4CAF50", "#2196F3"],
    colorField: "shift",
    label: false,
    yAxis: {
      min: 0,
      max: 10,
      grid: {
        line: {
          style: {
            stroke: "#E5E7EB",
            lineWidth: 1,
            lineDash: [4, 4],
          },
        },
      },
    },
    xAxis: {
      label: {
        style: {
          fontSize: 12,
        },
      },
    },
    legend: {
      color: {
        position: "bottom",
        layout: {
          justifyContent: "center",
        },
      },
    },
    annotations: [
      {
        type: "lineY",
        yField: 6,
        style: { stroke: "#F4664A", strokeOpacity: 1, lineWidth: 2 },
      },
    ],
    title: {
      title: "Check In/Out",
      subtitle: "Trung bình giờ làm mỗi ca",
    },
  };

  return (
    <>
      <DashboardFilterZone
        handleApply={handleApply}
        fields={["region", "province", "chain", "outlet", "booth", "date"]}
      />

      <ChartContanier>
        <Column {...config} height={400} />
      </ChartContanier>

      <Row className="mt-6" gutter={[16, { xs: 16, sm: 16 }]}>
        <Col md={6} xs={24}>
          <ChartContanier>
            <Chart
              options={{
                title: {
                  text: "Late In, Early Out Overall",
                  align: "left",
                  style: {
                    fontSize: "16px",
                    fontWeight: "bold",
                  },
                },
                subtitle: {
                  text: "Số lần đi trễ, về sớm",
                  align: "left",
                  style: {
                    fontSize: "14px",
                    fontWeight: "normal",
                  },
                },
                dataLabels: {
                  style: {
                    colors: ["#000000B7"],
                  },
                },
                xaxis: {
                  categories: ["Đi trễ", "Về sớm"],
                },
                colors: ["#FD5401", "#FF9129"],
                plotOptions: {
                  bar: {
                    distributed: true,
                    columnWidth: "40px",
                  },
                },
                legend: {
                  show: false,
                },
              }}
              series={[
                {
                  data: [15, 10],
                },
              ]}
              height={450}
              type="bar"
              width={"100%"}
            />
          </ChartContanier>
        </Col>
        <Col md={18} xs={24}>
          <ChartContanier>
            <Chart
              type="bar"
              width={"100%"}
              height={450}
              series={[
                {
                  name: "Đi trễ",
                  data: [
                    15, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27,
                  ],
                },
                {
                  name: "Về sớm",
                  data: [
                    10, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22,
                  ],
                },
              ]}
              options={{
                colors: ["#FD5401", "#FF9129"],
                title: {
                  text: "Late In, Early Out by Day",
                  align: "left",
                  style: {
                    fontSize: "16px",
                    fontWeight: "bold",
                  },
                },
                subtitle: {
                  text: "Số lần đi trễ, về sớm",
                  align: "left",
                  style: {
                    fontSize: "14px",
                    fontWeight: "normal",
                  },
                },
                chart: {
                  stacked: true,
                },
                dataLabels: {
                  style: {
                    colors: ["#000000B7"],
                  },
                },
                labels: [
                  "20/12",
                  "21/12",
                  "22/12",
                  "23/12",
                  "24/12",
                  "25/12",
                  "26/12",
                  "27/12",
                  "28/12",
                  "29/12",
                  "30/12",
                  "31/12",
                  "1/1",
                  "2/1",
                  "3/1",
                ],
              }}
            />
          </ChartContanier>
        </Col>
      </Row>
    </>
  );
};

export default CheckInOutV2Tab;
