import { useApp } from "@/UseApp.tsx";
import { AbstractFilterInterface } from "@/common/interface.ts";
import { AppContextInterface } from "@/interface.ts";
import { useMutation, useQuery } from "@tanstack/react-query";
import { ApiBrandResponseInterface } from "./interface";

export const getBrands = async (
  axiosGet: AppContextInterface["axiosGet"],
  filter: unknown,
) => {
  return await axiosGet<ApiBrandResponseInterface, unknown>("/brands", filter);
};

export const useBrandsQuery = (
  filter: AbstractFilterInterface & {
    clientId?: number;
    getInActive?: boolean;
  },
  enabled?: boolean,
) => {
  const { axiosGet } = useApp();
  const { getInActive, ...restFilter } = filter;
  const queryFilter = {
    ...(getInActive ? {} : { isActive: true }),
    ...restFilter,
  };

  return useQuery({
    queryKey: ["brands", queryFilter],
    queryFn: () => getBrands(axiosGet, queryFilter),
    enabled,
  });
};

export const useDeleteBrandMutation = () => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deleteBrand"],
    mutationFn: (id: number) => axiosDelete(`/brands/${id}`),
  });
};

export const useUpdateBrandMutation = () => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["updateBrand"],
    mutationFn: (brand: { id: number; name?: string; isActive?: boolean }) =>
      axiosPatch(`/brands/${brand.id}`, brand),
  });
};

export const useCreateBrandMutation = () => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createBrand"],
    mutationFn: (brand: { name: string; clientId: number }) =>
      axiosPost("/brands", brand),
  });
};
