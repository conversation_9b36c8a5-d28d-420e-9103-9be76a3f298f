import ChartContanier from "@/routes/project/chart/ChartContanier";
import { Column } from "@ant-design/charts";
import DashboardFilterZone from "../../DashboardFilterZone";

const DisplayShareTab = () => {
  const data = [
    // 20/12
    { date: "20/12", type: "PG time", brand: "Brand1", value: 3 },
    { date: "20/12", type: "PG Gift touch", brand: "Brand1", value: 1 },
    { date: "20/12", type: "PG kha bia", brand: "Brand1", value: 2 },
    { date: "20/12", type: "PG time", brand: "Brand2", value: 2.5 },
    { date: "20/12", type: "PG Gift touch", brand: "Brand2", value: 1 },
    { date: "20/12", type: "PG kha bia", brand: "Brand2", value: 1.5 },
    { date: "20/12", type: "PG time", brand: "Brand3", value: 2 },
    { date: "20/12", type: "PG Gift touch", brand: "Brand3", value: 0.5 },
    { date: "20/12", type: "PG kha bia", brand: "Brand3", value: 1 },
    { date: "20/12", type: "PG time", brand: "Brand4", value: 2 },
    { date: "20/12", type: "PG Gift touch", brand: "Brand4", value: 0.5 },
    { date: "20/12", type: "PG kha bia", brand: "Brand4", value: 1 },
    // 21/12
    { date: "21/12", type: "PG time", brand: "Brand1", value: 2 },
    { date: "21/12", type: "PG Gift touch", brand: "Brand1", value: 1.5 },
    { date: "21/12", type: "PG kha bia", brand: "Brand1", value: 1 },
    { date: "21/12", type: "PG time", brand: "Brand2", value: 2 },
    { date: "21/12", type: "PG Gift touch", brand: "Brand2", value: 1 },
    { date: "21/12", type: "PG kha bia", brand: "Brand2", value: 0.5 },
    { date: "21/12", type: "PG time", brand: "Brand3", value: 2 },
    { date: "21/12", type: "PG Gift touch", brand: "Brand3", value: 0.5 },
    { date: "21/12", type: "PG kha bia", brand: "Brand3", value: 1 },
    { date: "21/12", type: "PG time", brand: "Brand4", value: 2 },
    { date: "21/12", type: "PG Gift touch", brand: "Brand4", value: 0.5 },
    { date: "21/12", type: "PG kha bia", brand: "Brand4", value: 1 },
    // 22/12
    { date: "22/12", type: "PG time", brand: "Brand1", value: 2 },
    { date: "22/12", type: "PG Gift touch", brand: "Brand1", value: 0.5 },
    { date: "22/12", type: "PG kha bia", brand: "Brand1", value: 1 },
    { date: "22/12", type: "PG time", brand: "Brand2", value: 2 },
    { date: "22/12", type: "PG Gift touch", brand: "Brand2", value: 1 },
    { date: "22/12", type: "PG kha bia", brand: "Brand2", value: 0.5 },
    { date: "22/12", type: "PG time", brand: "Brand3", value: 2 },
    { date: "22/12", type: "PG Gift touch", brand: "Brand3", value: 0.5 },
    { date: "22/12", type: "PG kha bia", brand: "Brand3", value: 1 },
    { date: "22/12", type: "PG time", brand: "Brand4", value: 2 },
    { date: "22/12", type: "PG Gift touch", brand: "Brand4", value: 0.5 },
    { date: "22/12", type: "PG kha bia", brand: "Brand4", value: 1 },

    // 23/12
    { date: "23/12", type: "PG time", brand: "Brand1", value: 2 },
    { date: "23/12", type: "PG Gift touch", brand: "Brand1", value: 0.5 },
    { date: "23/12", type: "PG kha bia", brand: "Brand1", value: 1 },
    { date: "23/12", type: "PG time", brand: "Brand2", value: 2 },
    { date: "23/12", type: "PG Gift touch", brand: "Brand2", value: 1 },
    { date: "23/12", type: "PG kha bia", brand: "Brand2", value: 0.5 },
    { date: "23/12", type: "PG time", brand: "Brand3", value: 2 },
    { date: "23/12", type: "PG Gift touch", brand: "Brand3", value: 0.5 },
    { date: "23/12", type: "PG kha bia", brand: "Brand3", value: 1 },
    { date: "23/12", type: "PG time", brand: "Brand4", value: 2 },
    { date: "23/12", type: "PG Gift touch", brand: "Brand4", value: 0.5 },
    { date: "23/12", type: "PG kha bia", brand: "Brand4", value: 1 },

    // 24/12
    { date: "24/12", type: "PG time", brand: "Brand1", value: 2 },
    { date: "24/12", type: "PG Gift touch", brand: "Brand1", value: 0.5 },
    { date: "24/12", type: "PG kha bia", brand: "Brand1", value: 1 },
    { date: "24/12", type: "PG time", brand: "Brand2", value: 2 },
    { date: "24/12", type: "PG Gift touch", brand: "Brand2", value: 1 },
    { date: "24/12", type: "PG kha bia", brand: "Brand2", value: 0.5 },
    { date: "24/12", type: "PG time", brand: "Brand3", value: 2 },
    { date: "24/12", type: "PG Gift touch", brand: "Brand3", value: 0.5 },
    { date: "24/12", type: "PG kha bia", brand: "Brand3", value: 1 },
    { date: "24/12", type: "PG time", brand: "Brand4", value: 2 },
    { date: "24/12", type: "PG Gift touch", brand: "Brand4", value: 0.5 },
    { date: "24/12", type: "PG kha bia", brand: "Brand4", value: 1 },

    // 25/12
    { date: "25/12", type: "PG time", brand: "Brand1", value: 2 },
    { date: "25/12", type: "PG Gift touch", brand: "Brand1", value: 0.5 },
    { date: "25/12", type: "PG kha bia", brand: "Brand1", value: 1 },
    { date: "25/12", type: "PG time", brand: "Brand2", value: 2 },
    { date: "25/12", type: "PG Gift touch", brand: "Brand2", value: 1 },
    { date: "25/12", type: "PG kha bia", brand: "Brand2", value: 0.5 },
    { date: "25/12", type: "PG time", brand: "Brand3", value: 2 },
    { date: "25/12", type: "PG Gift touch", brand: "Brand3", value: 0.5 },
    { date: "25/12", type: "PG kha bia", brand: "Brand3", value: 1 },
    { date: "25/12", type: "PG time", brand: "Brand4", value: 2 },
    { date: "25/12", type: "PG Gift touch", brand: "Brand4", value: 0.5 },
  ];

  const config = {
    data,
    xField: "date",
    yField: "value",
    seriesField: "brand",
    stack: {
      groupBy: ["x", "series"],
      series: false,
    },
    colorField: "type",
    legend: {
      color: {
        title: true,
        position: "bottom",
        layout: {
          justifyContent: "center",
        },
      },
    },
    title: {
      title: "Competitor PA",
      subtitle: "Order: Heineken, Sabeco, Habeco, Carlsberg",
    },
    label: {
      textBaseline: "middle",
      textAlign: "center",
      position: "inside",
    },
    markBackground: {
      style: {
        fill: "#eee",
      },
    },
  };

  return (
    <>
      <DashboardFilterZone
        handleApply={function (): void {
          throw new Error("Function not implemented.");
        }}
      />
      <ChartContanier>
        <Column {...config} />
      </ChartContanier>
    </>
  );
};

export default DisplayShareTab;
