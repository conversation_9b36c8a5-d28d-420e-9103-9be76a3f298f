import { AbstractEntityInterface } from "@/common/interface";

export enum MultiSubjectMultimediaInformationCapturingType {
  MIN_MAX = "MIN_MAX",
  FULL = "FULL",
  NONE = "NONE",
}

export interface MultiSubjectMultimediaInformationCapturingInterface
  extends AbstractEntityInterface {
  title: string;
  description?: string;
  minimumImages: number;
  maximumImages: number;
  isWatermarkRequired: boolean;
  isTextFieldRequired: boolean;
  code?: string;
  ordinal: number;
}

export enum MultiSubjectMultimediaInformationCapturingActionEnum {
  EDIT = "EDIT",
  INACTIVE = "INACTIVE",
  ACTIVE = "ACTIVE",
  DELETE = "DELETE",
}
