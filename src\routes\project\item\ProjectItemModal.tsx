import { useApp } from "@/UseApp.tsx";
import { filterOption } from "@/common/helper.ts";
import ProductItemCell from "@/components/ProductItemCell.tsx";
import { renderTableCell } from "@/components/table-cell.tsx";
import { CloseOutlined } from "@ant-design/icons";
import {
  Button,
  Col,
  Form,
  Input,
  Modal,
  Row,
  Select,
  Space,
  Table,
} from "antd";
import { ColumnsType } from "antd/es/table";
import _ from "lodash";
import React, { useEffect, useMemo, useState } from "react";
import { useItemTypesQuery } from "../../item-type/services.ts";
import { ItemInterface } from "../../item/interface.ts";
import { ProjectInterface } from "../interface.ts";
import { useProjectItemsAvailableQuery } from "./services.ts";

const ProjectItemModal = ({
  isOpen,
  setIsOpen,
  project,
  projectId,
  cb,
}: {
  isOpen: boolean;
  setIsOpen: (arg: boolean) => void;
  project?: ProjectInterface;
  projectId: number;
  cb: () => void;
}) => {
  const { axiosPost, setLoading, loading, showNotification } = useApp();

  const [selectedItemKeys, setSelectedItemKeys] = useState<React.Key[]>([]);
  const [filter, setFilter] = useState({});

  const itemTypesQuery = useItemTypesQuery(
    {
      take: 50,
      skip: 0,
      clientId: project?.client.id,
      getInActive: true,
    },
    !!project?.client.id && isOpen,
  );

  const projectItemsAvailableQuery = useProjectItemsAvailableQuery(
    projectId,
    {
      ...filter,
      take: 50,
      skip: 0,
    },
    isOpen,
  );

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedItemKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys: selectedItemKeys,
    onChange: onSelectChange,
    getCheckboxProps: (record: ItemInterface) => {
      return {
        disabled: !record.isAvailable || !record.isActive,
      };
    },
  };

  useEffect(() => {
    setSelectedItemKeys(
      projectItemsAvailableQuery.data?.entities
        .filter((item) => !item.isAvailable)
        .map((item) => item.id) ?? [],
    );
  }, [projectItemsAvailableQuery.data?.entities]);

  const newSelectedRowKeys = useMemo(() => {
    return selectedItemKeys.filter(
      (key: React.Key) =>
        !projectItemsAvailableQuery.data?.entities
          .filter((item) => !item.isAvailable)
          .map((item) => item.id as React.Key)
          .some((id: React.Key) => id === key),
    );
  }, [projectItemsAvailableQuery.data?.entities, selectedItemKeys]);

  const itemTableColumns: ColumnsType<ItemInterface> = [
    {
      title: "Tên vật phẩm",
      key: "name",
      dataIndex: "name",
      render: (_: string, record: ItemInterface) => {
        const { isActive, name, image, isAvailable } = record;
        return (
          <ProductItemCell
            variants={image?.variants ?? []}
            name={name}
            isActive={isActive}
            isAvailable={isAvailable}
          />
        );
      },
    },
    {
      title: "Mã vật phẩm",
      dataIndex: "code",
      render: renderTableCell,
    },
    {
      title: "Loại vật phẩm",
      key: "itemType.name",
      dataIndex: "itemType",
      render: renderTableCell,
    },
    {
      title: "Đơn vị tính",
      key: "unit.name",
      dataIndex: "unit",
      render: renderTableCell,
    },
  ];

  const onItemSearchHandler = (value: {
    itemTypeId: number;
    filterValue: string;
  }) => {
    if (_.isEqual(value, filter)) {
      projectItemsAvailableQuery.refetch();
    }
    setFilter(value);
  };

  const handleBtnAddItemClick = async () => {
    if (selectedItemKeys.length > 0) {
      setLoading(true);
      await axiosPost(`/projects/${project?.id}/items`, {
        itemIds: newSelectedRowKeys,
      });
      showNotification({
        type: "success",
        message: "Thêm vật phẩm vào dự án thành công.",
      });
      cb();
      projectItemsAvailableQuery.refetch();
      setIsOpen(false);
      setSelectedItemKeys([]);
      setLoading(false);
    }
  };

  return (
    <Modal
      open={isOpen}
      footer={null}
      closeIcon={null}
      width={1000}
      styles={{ content: { padding: 0 } }}
    >
      <div className="pl-10 pr-10 pt-3 pb-5">
        <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
          <h2 className="text-neutral-700 text-2xl font-semibold ">
            Thêm vật phẩm vào dự án
          </h2>
          <div className="pt-5">
            <Button
              type="link"
              onClick={() => {
                setIsOpen(false);
              }}
              size="large"
              icon={<CloseOutlined />}
            />
          </div>
        </div>
        <Form layout="vertical" onFinish={onItemSearchHandler}>
          <Row justify={"space-between"}>
            <Col md={9}>
              <Form.Item name="itemTypeId" label="Loại vật phẩm">
                <Select
                  allowClear
                  showSearch
                  optionFilterProp="children"
                  filterOption={filterOption}
                  options={itemTypesQuery.data?.entities.map((itemType) => ({
                    label: itemType.name,
                    value: itemType.id,
                  }))}
                  className={"h-10"}
                  popupMatchSelectWidth={false}
                />
              </Form.Item>
            </Col>
            <Col md={9}>
              <Form.Item name="keyword" label="Vật phẩm">
                <Input
                  placeholder="Nhập tên hoặc mã vật phẩm"
                  allowClear
                  className={"h-10"}
                />
              </Form.Item>
            </Col>
            <Col md={4}>
              <Form.Item label=" ">
                <Button htmlType="submit">Tìm kiếm</Button>
              </Form.Item>
            </Col>
          </Row>
        </Form>

        <Table
          pagination={false}
          dataSource={projectItemsAvailableQuery.data?.entities}
          rowKey={"id"}
          rowSelection={rowSelection}
          columns={itemTableColumns}
          scroll={{ y: 500 }}
        />
      </div>
      <div
        className="flex justify-end pb-4 pt-4"
        style={{ backgroundColor: "#F7F8FA", borderRadius: "4px" }}
      >
        <Space className="pr-10">
          <Button
            onClick={() => {
              setIsOpen(false);
            }}
          >
            Đóng
          </Button>
          <Button
            type={"primary"}
            loading={loading}
            disabled={newSelectedRowKeys.length === 0}
            onClick={handleBtnAddItemClick}
          >
            Thêm {newSelectedRowKeys.length} vật phẩm vào dự án
          </Button>
        </Space>
      </div>
    </Modal>
  );
};

export default ProjectItemModal;
