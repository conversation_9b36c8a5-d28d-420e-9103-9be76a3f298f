import { CURD } from "@/common/constant";
import ModalCURD from "@/components/ModalCURD";
import { Form, Input } from "antd";
import { useCallback, useEffect } from "react";
import { ProjectLuckyDrawInterface } from "../interface";
import {
  useCreateLuckyDrawMutation,
  useUpdateLuckyDrawMutation,
} from "../service";

interface ProjectConfigLuckyWheelModalProps {
  open: boolean;
  onClose: () => void;
  cb: () => void;
  action?: CURD;
  projectId: number;
  selectedLuckydraw: ProjectLuckyDrawInterface | undefined;
}
const ProjectConfigLuckyWheelModal = ({
  open,
  onClose,
  cb,
  action,
  projectId,
  selectedLuckydraw,
}: ProjectConfigLuckyWheelModalProps) => {
  const [form] = Form.useForm();

  const createLuckyDrawMutation = useCreateLuckyDrawMutation(projectId);
  const updateLuckyDrawMutation = useUpdateLuckyDrawMutation(projectId);

  const content = (
    <>
      <Form.Item
        label="Tên vòng quay"
        rules={[{ required: true }]}
        name={"name"}
      >
        <Input />
      </Form.Item>
    </>
  );

  const onFinish = useCallback(async () => {
    if (action === CURD.CREATE) {
      await createLuckyDrawMutation.mutateAsync(form.getFieldsValue());
    }

    if (action === CURD.UPDATE) {
      await updateLuckyDrawMutation.mutateAsync({
        ...form.getFieldsValue(),
        id: selectedLuckydraw?.id,
      });
    }

    cb();
    onClose();
  }, [
    action,
    cb,
    createLuckyDrawMutation,
    form,
    onClose,
    selectedLuckydraw?.id,
    updateLuckyDrawMutation,
  ]);

  useEffect(() => {
    if (selectedLuckydraw && action === CURD.UPDATE) {
      form.setFieldsValue(selectedLuckydraw);
    }
  }, [action, form, selectedLuckydraw]);

  return (
    <ModalCURD
      title={
        action === CURD.CREATE
          ? "Thêm vòng quay may mắn"
          : "Chỉnh sửa vòng quay may mắn"
      }
      isOpen={open}
      formContent={content}
      form={form}
      onFinish={onFinish}
      action={action}
      onCancelCb={onClose}
    />
  );
};

export default ProjectConfigLuckyWheelModal;
