import { BTN_CANCEL_TEXT, BTN_CONFIRM_TEXT } from "@/common/constant.ts";
import DragSortRowComponent from "@/components/DragSortRowComponent.tsx";
import ProductItemCell from "@/components/ProductItemCell.tsx";
import { renderTableCell } from "@/components/table-cell.tsx";
import TableActionCell from "@/components/TableActionCell.tsx";
import { useApp } from "@/UseApp.tsx";
import {
  DeleteOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined,
} from "@ant-design/icons";
import { DndContext, DragEndEvent } from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
  arrayMove,
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { NumericSheetGroupCollapseChildActionEnum } from "@project/component/feature/config/types/numericSheet/interface.ts";
import {
  OosGroupInterface,
  OosProductInterface,
} from "@project/component/feature/config/types/outOfStockStatus/interface.ts";
import OutOfStockStatusGroupAvailablesModal from "@project/component/feature/config/types/outOfStockStatus/steps/group/OutOfStockStatusGroupAvailablesModal.tsx";
import { Button, Modal, Table } from "antd";
import { useCallback, useEffect, useState } from "react";
import {
  useArrangeOosGroupProductMutation,
  useDeleteOosGroupProductMutation,
  useOosGroupProductsQuery,
  useUpdateOosGroupProductMutation,
} from "./service";

interface OutOfStockStatusGroupCollapseChildProps {
  componentFeatureId: number;
  oosGroup: OosGroupInterface | null;
}

const OutOfStockStatusGroupCollapseChild = ({
  componentFeatureId,
  oosGroup,
}: OutOfStockStatusGroupCollapseChildProps) => {
  const { showNotification, openDeleteModal } = useApp();
  const oosGroupId = oosGroup?.id ?? 0;

  const [open, setOpen] = useState(false);

  const oosGroupProductsQuery = useOosGroupProductsQuery(
    componentFeatureId,
    oosGroupId,
    {
      take: 0,
    },
  );

  const arrangeOosGroupProductMutation = useArrangeOosGroupProductMutation(
    componentFeatureId,
    oosGroupId,
  );
  const updateOosGroupProductMutation = useUpdateOosGroupProductMutation(
    componentFeatureId,
    oosGroupId,
  );
  const deleteOosGroupProductMutation = useDeleteOosGroupProductMutation(
    componentFeatureId,
    oosGroupId,
  );

  const [dataSource, setDataSource] = useState(
    oosGroupProductsQuery.data?.entities ?? [],
  );

  const onAdd = useCallback(() => {
    setOpen(true);
  }, []);

  useEffect(() => {
    setDataSource(
      (oosGroupProductsQuery.data?.entities ?? []).sort(
        (a, b) => a.ordinal - b.ordinal,
      ),
    );
  }, [oosGroupProductsQuery.data?.entities]);

  const onDragEnd = useCallback(
    async ({ active, over }: DragEndEvent) => {
      if (!over) {
        return;
      }
      if (active.id !== over?.id) {
        setDataSource((previous) => {
          const activeIndex = previous?.findIndex((i) => i.id === active.id);
          const overIndex = previous?.findIndex((i) => i.id === over?.id);
          return arrayMove(previous, activeIndex, overIndex);
        });

        await arrangeOosGroupProductMutation.mutateAsync({
          id: active.id as number,
          overFeatureOosProductId: over?.id as number,
        });
      }
    },
    [arrangeOosGroupProductMutation],
  );

  const activeClick = useCallback(
    (record: OosProductInterface) => {
      const { projectProduct } = record;
      const name = projectProduct?.product.name;

      Modal.confirm({
        title: `Kích hoạt sản phẩm: ${name}`,
        content: "Bạn có chắc chắn muốn kích hoạt sản phẩm này?",
        okText: BTN_CONFIRM_TEXT,
        cancelText: BTN_CANCEL_TEXT,
        onOk: async () => {
          await updateOosGroupProductMutation.mutateAsync({
            isActive: true,
            id: record.id,
          });

          showNotification({
            type: "success",
            message: "Ngừng hoạt động thành công.",
          });
          await oosGroupProductsQuery.refetch();
        },
      });
    },
    [oosGroupProductsQuery, showNotification, updateOosGroupProductMutation],
  );

  const inActiveClick = useCallback(
    (record: OosProductInterface) => {
      const { projectProduct } = record;
      const name = projectProduct?.product.name;

      Modal.confirm({
        title: `Ngừng hoạt động sản phẩm: ${name}`,
        content: "Bạn có chắc chắn muốn ngừng hoạt động sản phẩm này?",
        okText: BTN_CONFIRM_TEXT,
        cancelText: BTN_CANCEL_TEXT,
        onOk: async () => {
          await updateOosGroupProductMutation.mutateAsync({
            isActive: false,
            id: record.id,
          });

          showNotification({
            type: "success",
            message: "Ngừng hoạt động thành công.",
          });
          await oosGroupProductsQuery.refetch();
        },
      });
    },
    [oosGroupProductsQuery, showNotification, updateOosGroupProductMutation],
  );

  const deleteClick = useCallback(
    (record: OosProductInterface) => {
      const { projectProduct } = record;
      const name = projectProduct?.product.name;

      openDeleteModal({
        content: (
          <p>
            Bạn muốn xóa sản phẩm{" "}
            <span className={"font-semibold"}>{name}</span> khỏi nhóm sản phẩm?
          </p>
        ),
        deleteText: "Xác nhận xóa",
        loading: false,
        onCancel(): void {},
        onDelete: async () => {
          await deleteOosGroupProductMutation.mutateAsync(record.id);

          showNotification({
            type: "success",
            message: "Xóa sản phẩm thành công",
          });

          await oosGroupProductsQuery.refetch();
        },
        title: `Xóa sản phẩm`,
        titleError: "Không thể xóa sản phẩm",
        contentHeader: (
          <>
            Không thể xóa sản phẩm <span className="font-semibold">{name}</span>{" "}
            bởi vì:
          </>
        ),
      });
    },
    [
      deleteOosGroupProductMutation,
      oosGroupProductsQuery,
      openDeleteModal,
      showNotification,
    ],
  );

  const actionActions = [
    {
      key: NumericSheetGroupCollapseChildActionEnum.ACTIVE,
      action: activeClick,
    },
    {
      key: NumericSheetGroupCollapseChildActionEnum.INACTIVE,
      action: inActiveClick,
    },
    {
      key: NumericSheetGroupCollapseChildActionEnum.DELETE,
      action: deleteClick,
    },
  ];

  const actionItems = [
    {
      key: NumericSheetGroupCollapseChildActionEnum.INACTIVE,
      label: "Ngừng hoạt động",
      icon: <PauseCircleOutlined />,
    },
    {
      key: NumericSheetGroupCollapseChildActionEnum.ACTIVE,
      label: "Hoạt động trở lại",
      icon: <PlayCircleOutlined />,
    },
    {
      key: NumericSheetGroupCollapseChildActionEnum.DELETE,
      label: "Xóa",
      icon: <DeleteOutlined />,
    },
  ];

  const ACTION_ACTIVE = [
    NumericSheetGroupCollapseChildActionEnum.INACTIVE,
    NumericSheetGroupCollapseChildActionEnum.DELETE,
  ];

  const ACTION_INACTIVE = [
    NumericSheetGroupCollapseChildActionEnum.ACTIVE,
    NumericSheetGroupCollapseChildActionEnum.DELETE,
  ];

  return (
    <>
      <div className="flex justify-end">
        <Button type="link" className="text-blue" onClick={onAdd}>
          Thêm sản phẩm
        </Button>
      </div>

      <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
        <SortableContext
          items={dataSource.map((i) => i.id)}
          strategy={verticalListSortingStrategy}
        >
          <Table
            dataSource={dataSource}
            rowKey={(o) => o.id}
            pagination={false}
            loading={
              oosGroupProductsQuery.isFetching ||
              arrangeOosGroupProductMutation.isPending
            }
            components={{
              body: {
                row: DragSortRowComponent,
              },
            }}
            columns={[
              {
                key: "sort",
              },
              {
                title: "Tên sản phẩm",
                className: "min-w-[100px]",
                render: (_, record: OosProductInterface) => {
                  const { projectProduct } = record ?? {};

                  if (projectProduct) {
                    return (
                      <ProductItemCell
                        name={projectProduct?.product.name}
                        variants={projectProduct.product.image?.variants ?? []}
                      />
                    );
                  }
                },
              },
              {
                title: "Mã sản phẩm",
                className: "min-w-[100px]",
                render: (_, record: OosProductInterface) => {
                  return record.projectProduct?.product.code;
                },
              },
              {
                title: "Nhãn hàng",
                className: "min-w-[100px]",
                render: (_, record: OosProductInterface) => {
                  return record.projectProduct?.product?.brand?.name;
                },
              },
              {
                title: "Quy cách",
                className: "min-w-[100px]",
                render: (_, record: OosProductInterface) => {
                  return record.projectProduct?.productPackaging?.unit?.name;
                },
              },
              {
                key: "isActive",
                className: "min-w-[100px]",
                title: "Tình trạng",
                dataIndex: "isActive",
                render: (value, record, index) => {
                  return renderTableCell(value, record, index, "isActive");
                },
              },
              {
                key: "actions",
                render: (_, record) => {
                  const actionKeys = record.isActive
                    ? ACTION_ACTIVE
                    : ACTION_INACTIVE;
                  const items = actionItems.filter((item) =>
                    actionKeys.includes(item.key),
                  );
                  return (
                    <TableActionCell
                      actions={actionActions}
                      items={items}
                      record={record}
                    />
                  );
                },
                width: 100,
              },
            ]}
          />
        </SortableContext>
      </DndContext>

      {open && (
        <OutOfStockStatusGroupAvailablesModal
          onModalClose={() => {
            setOpen(false);
          }}
          cb={() => {
            setOpen(false);
            oosGroupProductsQuery.refetch();
          }}
          componentFeatureId={componentFeatureId}
          oosGroup={oosGroup}
        />
      )}
    </>
  );
};
export default OutOfStockStatusGroupCollapseChild;
