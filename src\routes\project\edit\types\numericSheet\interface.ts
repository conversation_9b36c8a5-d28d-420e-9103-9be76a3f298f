import { AbstractEntityInterface } from "@/common/interface";
import { RecordNumericValueInterface } from "@/routes/project/report/types/numericSheet/interface";
import { UserInterface } from "@/routes/user/interface";

interface ToolNumericSheetInterface extends AbstractEntityInterface {
  createdByUser: UserInterface;
}

export interface EditNumericSheetInterface extends AbstractEntityInterface {
  dataUuid: string;
  dataTimestamp: string;
  createdByUser: UserInterface;
  recordNumericValues: RecordNumericValueInterface[];
  toolNumericSheet?: ToolNumericSheetInterface;
}
