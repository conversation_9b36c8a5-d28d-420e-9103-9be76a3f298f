import { useApp } from "@/UseApp";
import {
  BTN_CANCEL_TEXT,
  BTN_CONFIRM_TEXT,
  SELECT_ALL,
  SELECT_ALL_LABEL,
} from "@/common/constant";
import CustomTable from "@/components/CustomTable/CustomTable.tsx";
import FilterComponent from "@/components/FilterComponent";
import InnerContainer from "@/components/InnerContainer/InnerContainer.tsx";
import ProductItemCell from "@/components/ProductItemCell.tsx";
import TableActionCell from "@/components/TableActionCell";
import { renderTableCell } from "@/components/table-cell";
import {
  DeleteOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined,
} from "@ant-design/icons";
import { Form, Modal } from "antd";
import { ColumnsType } from "antd/es/table";
import { useCallback, useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { ItemInterface } from "../../item/interface";
import { useProjectQuery } from "../services.ts";
import ProjectItemModal from "./ProjectItemModal.tsx";
import { ProjectItemActionEnum, ProjectItemInterface } from "./interface";
import { getProjectItems } from "./services";

export default function ProjectItemPage() {
  const projectId = parseInt(useParams().id ?? "0");

  const {
    loading,
    axiosGet,
    setLoading,
    axiosDelete,
    showNotification,
    axiosPatch,
    openDeleteModal,
  } = useApp();

  const [data, setData] = useState<ProjectItemInterface[]>([]);
  const [searchForm] = Form.useForm();
  const [isOpen, setIsOpen] = useState(false);
  const [modal, contextHolder] = Modal.useModal();

  const projectQuery = useProjectQuery(projectId);

  const fetchData = useCallback(async () => {
    setLoading(true);

    try {
      const response = await getProjectItems(axiosGet, projectId, {
        ...searchForm.getFieldsValue(),
        take: 0,
        skip: 0,
      });

      if (!Array.isArray(response)) {
        setData(response.entities);
      }
    } catch (e) {
      console.error("fetchData: ", e);
    }

    setLoading(false);
  }, [axiosGet, searchForm, setLoading, projectId]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const projectItemActionItems = [
    {
      key: ProjectItemActionEnum.INACTIVE,
      label: "Ngừng hoạt động",
      icon: <PauseCircleOutlined />,
    },
    {
      key: ProjectItemActionEnum.ACTIVE,
      label: "Hoạt động trở lại",
      icon: <PlayCircleOutlined />,
    },
    {
      key: ProjectItemActionEnum.DELETE,
      label: "Xóa khỏi dự án",
      icon: <DeleteOutlined />,
    },
  ];

  const handleActionInActiveClick = async (record: ProjectItemInterface) => {
    modal.confirm({
      title: `Ngừng hoạt động vật phẩm: ${record.item.name}`,
      content: "Bạn có chắc chắn muốn ngừng hoạt động vật phẩm này?",
      okText: BTN_CONFIRM_TEXT,
      cancelText: BTN_CANCEL_TEXT,
      onOk: async () => {
        setLoading(true);
        await axiosPatch(`/projects/${projectId}/items/${record.id}`, {
          isActive: false,
        });
        showNotification({
          type: "success",
          message: "Ngừng hoạt động vật phẩm thành công.",
        });
        setLoading(false);
        fetchData();
      },
    });
  };

  const handleActionActiveClick = async (record: ProjectItemInterface) => {
    modal.confirm({
      title: `Kích hoạt vật phẩm: ${record.item.name}`,
      content: "Bạn có chắc chắn muốn kích hoạt vật phẩm này?",
      okText: BTN_CONFIRM_TEXT,
      cancelText: BTN_CANCEL_TEXT,
      onOk: async () => {
        setLoading(true);
        await axiosPatch(`/projects/${projectId}/items/${record.id}`, {
          isActive: true,
        });
        showNotification({
          type: "success",
          message: "Kích hoạt vật phẩm thành công.",
        });
        setLoading(false);
        fetchData();
      },
    });
  };

  const handleActionDeleteClick = async (record: ProjectItemInterface) => {
    openDeleteModal({
      content: (
        <p>
          Bạn muốn xóa vật phẩm{" "}
          <span className={"font-semibold"}>{record.item.name}</span> khỏi dự
          án?
        </p>
      ),
      deleteText: "Xác nhận xóa",
      loading: false,
      onCancel(): void {},
      onDelete: async () => {
        await axiosDelete(`/projects/${projectId}/items/${record.id}`);
        showNotification({
          type: "success",
          message: "Xóa vật phẩm thành công",
        });
        await fetchData();
      },
      title: `Xóa vật phẩm`,
      titleError: "Không thể xóa vật phẩm",
      contentHeader: (
        <>
          Không thể xóa vật phẩm{" "}
          <span className="font-semibold">{record.item.name}</span> khỏi dự án
          bởi vì:
        </>
      ),
    });
  };

  const projectItemActionActions = [
    {
      key: ProjectItemActionEnum.INACTIVE,
      action: handleActionInActiveClick,
    },
    {
      key: ProjectItemActionEnum.ACTIVE,
      action: handleActionActiveClick,
    },
    {
      key: ProjectItemActionEnum.DELETE,
      action: handleActionDeleteClick,
    },
  ];

  const ACTION_ACTIVE = [
    ProjectItemActionEnum.INACTIVE,
    ProjectItemActionEnum.DELETE,
  ];
  const ACTION_INACTIVE = [
    ProjectItemActionEnum.ACTIVE,
    ProjectItemActionEnum.DELETE,
  ];

  const columns: ColumnsType<ProjectItemInterface> = [
    {
      title: "Tên vật phẩm",
      key: "name",
      dataIndex: "name",
      className: "min-w-[100px]",
      render: (_, record: ProjectItemInterface) => {
        return (
          <ProductItemCell
            variants={record?.item?.image?.variants ?? []}
            name={record.item.name}
          />
        );
      },
    },
    {
      title: "Mã vật phẩm",
      key: "item.code",
      dataIndex: "item",
      className: "min-w-[100px]",
      render: (item: ItemInterface) => item.code,
    },
    {
      title: "Loại vật phẩm",
      key: "item.itemType.name",
      dataIndex: "item",
      className: "min-w-[100px]",
      render: (item: ItemInterface) => item.itemType?.name,
    },
    {
      title: "Đơn vị tính",
      key: "item.unit.name",
      dataIndex: "item",
      className: "min-w-[100px]",
      render: (item: ItemInterface) => item.unit?.name,
    },

    {
      key: "isActive",
      title: "Tình trạng",
      dataIndex: "isActive",
      className: "min-w-[100px]",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "isActive");
      },
    },
    {
      key: "actions",
      render: (_, record) => {
        const actionKeys = record.isActive ? ACTION_ACTIVE : ACTION_INACTIVE;
        const items = projectItemActionItems.filter((item) =>
          actionKeys.includes(item.key),
        );
        return (
          <TableActionCell
            actions={projectItemActionActions}
            items={items}
            record={record}
          />
        );
      },
      fixed: "right",
      width: 100,
    },
  ];

  const filterOptions = columns
    .filter(
      (item) =>
        !["isActive", "updatedAt", "createdAt", "actions"].includes(
          item.key as string,
        ),
    )
    .map((column) => {
      return {
        label: column.title as string,
        value: column.key as string | number,
      };
    });

  filterOptions.unshift({ label: SELECT_ALL_LABEL, value: SELECT_ALL });

  function searchHandler(): void {
    fetchData();
  }

  function handleExcelButtonClick(): void {
    throw new Error("Function not implemented.");
  }

  function handleAddButtonClick(): void {
    setIsOpen(true);
  }

  return (
    <div>
      <h2>Vật phẩm</h2>
      <InnerContainer>
        <div className="mb-6">
          <FilterComponent
            filterOptions={filterOptions}
            searchHandler={searchHandler}
            handleExcelButtonClick={handleExcelButtonClick}
            handleAddButtonClick={handleAddButtonClick}
            searchForm={searchForm}
          />
        </div>

        <CustomTable
          dataSource={data}
          columns={columns}
          scroll={{
            x: "max-content",
          }}
          pagination={false}
          loading={loading}
        />
        <p className={"pb-0 mb-0"}>Số kết quả trả về: {data.length}</p>
      </InnerContainer>

      <ProjectItemModal
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        project={projectQuery.data}
        projectId={projectId}
        cb={fetchData}
      />
      {contextHolder}
    </div>
  );
}
