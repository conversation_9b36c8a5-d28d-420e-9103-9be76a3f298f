import CustomModal from "@/components/CustomModal";
import {
  ComponentFeatureInterface,
  FeatureTypeEnum,
} from "@/routes/project/component/feature/interface";
import { useApp } from "@/UseApp";
import { EditMultipleEntitiesQuantityCapturingInterface } from "./interface";

import { CURD } from "@/common/constant";
import { stringIncludes } from "@/common/helper";
import FormNumberInput from "@/components/FormNumberInput";
import { ToolSettingSettingTypeEnum } from "@/routes/project/config/interface";
import {
  useProjectToolAgencyDetailQuery,
  useToolSettingsQuery,
} from "@/routes/project/config/service";
import { SearchOutlined } from "@ant-design/icons";
import { Col, Form, Input, Row } from "antd";
import dayjs from "dayjs";
import { useCallback, useMemo, useState } from "react";
import { v4 as uuidv4 } from "uuid";
import { useCreateEditMultipleEntitiesQuantityCapturingMutation } from "./service";

interface EditMultipleEntitiesQuantityCapturingModalProps {
  title: string;
  editMultipleEntitiesQuantityCapturing?: EditMultipleEntitiesQuantityCapturingInterface;
  componentFeature?: ComponentFeatureInterface;
  projectId: number;
  attendanceId: number;
  componentFeatureId: number;
  cb: () => void;
  cancelCb: () => void;
  createdAt?: string;
  projectAgencyId?: number;
  action: CURD;
}

const EditMultipleEntitiesQuantityCapturingModal = ({
  title,
  editMultipleEntitiesQuantityCapturing,
  componentFeature,
  projectId,
  attendanceId,
  componentFeatureId,
  createdAt,
  cb,
  cancelCb,
  projectAgencyId,
  action,
}: EditMultipleEntitiesQuantityCapturingModalProps) => {
  const { showNotification } = useApp();

  const [form] = Form.useForm();
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");

  const projectToolAgencyDetailQuery = useProjectToolAgencyDetailQuery(
    projectId,
    projectAgencyId,
  );
  const toolSettingsQuery = useToolSettingsQuery(
    projectToolAgencyDetailQuery.data?.id ?? 0,
  );

  const createEditMultipleEntitiesQuantityCapturingMutation =
    useCreateEditMultipleEntitiesQuantityCapturingMutation(
      projectId,
      attendanceId,
      componentFeatureId,
    );

  const debouncedSetSearchTerm = useCallback((value: string) => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(value);
    }, 300);

    return () => clearTimeout(timer);
  }, []);

  const recordQuantityValues = useMemo(() => {
    const { recordQuantityValues } =
      editMultipleEntitiesQuantityCapturing ?? {};
    recordQuantityValues?.sort((a, b) => b.value - a.value);
    for (const recordQuantityValue of recordQuantityValues ?? []) {
      recordQuantityValue.featureQuantity =
        componentFeature?.featureQuantities?.find(
          (item) => item.id === recordQuantityValue.featureQuantityId,
        );
    }

    return recordQuantityValues ?? [];
  }, [
    componentFeature?.featureQuantities,
    editMultipleEntitiesQuantityCapturing,
  ]);

  const options = useMemo(() => {
    if (action === CURD.UPDATE)
      return recordQuantityValues.map((item) => {
        let label = "";
        let unitName = "";

        if (item?.featureQuantity?.projectItem) {
          label = item?.featureQuantity?.projectItem?.item?.name;
          unitName = item?.featureQuantity?.projectItem?.item?.unit?.name ?? "";
        } else {
          label = item?.featureQuantity?.projectProduct?.product?.name ?? "";
          unitName =
            item?.featureQuantity?.projectProduct?.productPackaging?.unit
              ?.name ?? "";
        }

        return {
          label,
          featureQuantityId: item.featureQuantityId,
          value: item.value,
          unitName,
        };
      });

    if (action === CURD.CREATE)
      return componentFeature?.featureQuantities?.map((item) => {
        let label = "";
        let unitName = "";

        if (item?.projectItem) {
          label = item?.projectItem?.item?.name;
          unitName = item?.projectItem?.item?.unit?.name ?? "";
        } else {
          label = item?.projectProduct?.product?.name ?? "";
          unitName = item?.projectProduct?.productPackaging?.unit?.name ?? "";
        }

        return {
          label,
          featureQuantityId: item.id,
          value: null,
          unitName,
        };
      });

    return [];
  }, [action, componentFeature?.featureQuantities, recordQuantityValues]);

  const filteredOptions = useMemo(() => {
    return (
      options?.filter((item) =>
        stringIncludes(item.label ?? "", debouncedSearchTerm),
      ) ?? []
    );
  }, [debouncedSearchTerm, options]);

  const isDisallowEditNullValue = useMemo(() => {
    const toolSetting = toolSettingsQuery.data?.find(
      (item) =>
        item.featureType ===
          FeatureTypeEnum.MultipleEntitiesQuantityCapturing &&
        item.settingType === ToolSettingSettingTypeEnum.READONLY_IF_NULL,
    );
    return toolSetting?.enabled ?? false;
  }, [toolSettingsQuery.data]);

  const isDisallowEditZeroValue = useMemo(() => {
    const toolSetting = toolSettingsQuery.data?.find(
      (item) =>
        item.featureType ===
          FeatureTypeEnum.MultipleEntitiesQuantityCapturing &&
        item.settingType === ToolSettingSettingTypeEnum.READONLY_IF_ZERO,
    );
    return toolSetting?.enabled ?? false;
  }, [toolSettingsQuery.data]);

  const confirm = useCallback(async () => {
    const recordSamplingValues = form.getFieldsValue().recordSamplingValues;
    const data: {
      dataUuid: string;
      dataTimestamp: string;
      values: { featureQuantityId: number; value: number }[];
    } = {
      dataUuid: uuidv4(),
      values: [],
      dataTimestamp: dayjs(createdAt).add(1, "second").toISOString() ?? "",
    };

    for (const key of Object.keys(recordSamplingValues)) {
      const parts = key.split(".");
      if (parts.length >= 2) {
        const [featureQuantityId, type] = parts;
        if (type === "value") {
          data.values.push({
            featureQuantityId: Number(featureQuantityId),
            value: recordSamplingValues[key],
          });
        }
      }
    }

    await createEditMultipleEntitiesQuantityCapturingMutation.mutateAsync(data);

    showNotification({
      type: "success",
      message: title + " thành công",
    });
    cb();
  }, [
    cb,
    createEditMultipleEntitiesQuantityCapturingMutation,
    createdAt,
    form,
    showNotification,
    title,
  ]);

  const content = (
    <Form layout="vertical" form={form}>
      <Form.Item>
        <Input
          prefix={<SearchOutlined />}
          placeholder="Tên"
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
            const value = e.target.value;
            debouncedSetSearchTerm(value);
          }}
        />
      </Form.Item>

      <Form.List name={"recordSamplingValues"}>
        {() => (
          <div className="max-h-[450px] overflow-auto">
            {filteredOptions.map((item, index) => (
              <Row
                key={index}
                justify={"space-between"}
                className="items-center"
              >
                <Col md={10}>
                  <Form.Item
                    label={item.label}
                    name={`${item.featureQuantityId}.value`}
                    initialValue={item.value}
                  >
                    <FormNumberInput
                      className="w-full"
                      suffix={item.unitName}
                      disabled={
                        (isDisallowEditZeroValue && item.value === 0) ||
                        (isDisallowEditNullValue && item.value === null)
                      }
                    />
                  </Form.Item>
                </Col>
              </Row>
            ))}
          </div>
        )}
      </Form.List>
    </Form>
  );

  return (
    <CustomModal
      title={title}
      isOpen={true}
      content={content}
      onConfirm={confirm}
      confirmText="Lưu thông tin"
      onCancel={cancelCb}
      confirmLoading={
        createEditMultipleEntitiesQuantityCapturingMutation.isPending
      }
    />
  );
};

export default EditMultipleEntitiesQuantityCapturingModal;
