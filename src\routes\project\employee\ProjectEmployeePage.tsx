import InnerContainer from "@/components/InnerContainer/InnerContainer";
import { useApp } from "@/UseApp";
import { Skeleton, Tabs } from "antd";
import { Tab } from "rc-tabs/lib/interface";
import React, {
  Suspense,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from "react";
import { useParams, useSearchParams } from "react-router-dom";
import { RoleInterface } from "../role/interface";

const EmployeeAllTab = React.lazy(
  () => import("./tabs/employee-all-tab/EmployeeAllTab"),
);
const EmployeeLeaderTab = React.lazy(
  () => import("./tabs/employee-leader-tab/EmployeeLeaderTab"),
);
const EmployeeTab = React.lazy(() => import("./tabs/employee-tab/EmployeeTab"));

export default function ProjectEmployeePage() {
  const projectId = parseInt(useParams().id ?? "0");

  const { axiosGet } = useApp();

  const [projectRoles, setProjectRoles] = useState<
    { count: number; role: RoleInterface }[]
  >([]);
  const [searchParams, setSearchParams] = useSearchParams();
  const [selectedTab, setSelectedTab] = useState<string>("all");

  const fetchProjectRolesEmployee = useCallback(async () => {
    const response = await axiosGet<
      { count: number; role: RoleInterface },
      undefined
    >(`/projects/${projectId}/employee-users/stats`);
    if (Array.isArray(response)) {
      setProjectRoles(response);
    }
  }, [axiosGet, projectId]);

  useEffect(() => {
    fetchProjectRolesEmployee();
  }, [fetchProjectRolesEmployee]);

  const leaderRole = useMemo(() => {
    return projectRoles.find((item) => item.role.isLeader);
  }, [projectRoles]);

  const tabItems: Tab[] = projectRoles.map((projectRole) => ({
    key: projectRole.role.name,
    label: `${projectRole.role.name} (${projectRole.count})`,
    children: projectRole.role.isLeader ? (
      <Suspense key={projectRole.role.name} fallback={<Skeleton />}>
        <EmployeeLeaderTab
          projectId={projectId}
          role={projectRole.role}
          cb={fetchProjectRolesEmployee}
          selectedTab={selectedTab}
        />
      </Suspense>
    ) : (
      <Suspense key={projectRole.role.name} fallback={<Skeleton />}>
        <EmployeeTab
          projectId={projectId}
          role={projectRole.role}
          leaderRole={leaderRole?.role}
          cb={fetchProjectRolesEmployee}
          selectedTab={selectedTab}
        />
      </Suspense>
    ),
  }));

  const totalProjectEmployee = useMemo(() => {
    return projectRoles.reduce((total, projectRole) => {
      return total + projectRole.count;
    }, 0);
  }, [projectRoles]);

  tabItems.unshift({
    key: "ALL",
    label: `Tất cả (${totalProjectEmployee})`,
    children: (
      <Suspense key={"ALL"} fallback={<Skeleton />}>
        <EmployeeAllTab
          projectId={projectId}
          roles={projectRoles.map((projectRole) => projectRole.role)}
          selectedTab={selectedTab}
        />
      </Suspense>
    ),
  });

  return (
    <div>
      <h2>Nhân viên field</h2>
      <InnerContainer>
        <Tabs
          defaultActiveKey={searchParams.get("tab") ?? "ALL"}
          type="card"
          items={tabItems}
          onTabClick={(key) => {
            setSearchParams({ tab: key });
            setSelectedTab(key);
          }}
          activeKey={searchParams.get("tab") ?? "ALL"}
        />
      </InnerContainer>
    </div>
  );
}
