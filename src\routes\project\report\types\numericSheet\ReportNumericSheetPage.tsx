import {
  CHUNK_SIZE,
  DEFAULT_CURRENT_PAGE,
  DEFAULT_PAGE_SIZE,
} from "@/common/constant";
import { createFileAndDownLoad } from "@/common/export-excel.helper";
import { formatNumber, removeVietnameseTones } from "@/common/helper";
import { useNumericAttributesQuery } from "@/routes/project/component/feature/config/types/numericSheet/service";
import { Form, Table } from "antd";
import dayjs from "dayjs";
import _ from "lodash";
import { useCallback, useEffect, useMemo, useState } from "react";
import getColumnsTableReport from "../../ColumnsTableReport";
import FilterReportZone from "../../FilterReportZone";
import {
  AdvancedFilterFormValueInterface,
  AdvancedFilterInterface,
} from "../../interface";
import { useAdvancedFilterFiledsStore } from "../../state";
import { useProjectReportOutletContext } from "../../UseProjectReportOutletContext";
import { RecordNumericValueInterface } from "./interface";
import {
  useGetReportNumericSheetMutation,
  useReportNumericSheetsQuery,
} from "./service";

const ReportNumericSheetPage = () => {
  const {
    componentFeatureQuery,
    projectId,
    componentFeatureId,
    advancedFilterValues,
    isHideFilter,
    project,
  } = useProjectReportOutletContext();

  const { setFileds: setAdvancedFilterFileds } = useAdvancedFilterFiledsStore();

  const [filterForm] = Form.useForm();
  const [filter, setFilter] = useState<AdvancedFilterInterface>({
    attendanceStartDate: dayjs().startOf("date").toDate(),
    attendanceEndDate: dayjs().endOf("date").toDate(),
  });
  const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [exportLoading, setExportLoading] = useState(false);

  const reportNumericSheetsQuery = useReportNumericSheetsQuery(
    projectId,
    componentFeatureId,
    {
      ...filter,
      take: pageSize,
      skip: (currentPage - 1) * pageSize,
    },
  );
  const numericAttributesQuery = useNumericAttributesQuery(componentFeatureId, {
    take: 0,
  });

  const getReportNumericSheetMutation = useGetReportNumericSheetMutation(
    projectId,
    componentFeatureId,
  );

  const setFilterForQuery = useCallback(
    (values: AdvancedFilterFormValueInterface) => {
      setCurrentPage(DEFAULT_CURRENT_PAGE);

      if (_.isEqual(filter, values)) {
        reportNumericSheetsQuery.refetch();
      }

      setFilter(values);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [filter],
  );

  const onFilterFormFinish = useCallback(() => {
    const values = filterForm.getFieldsValue();

    if (values.attendance) {
      const [attendanceStartDate, attendanceEndDate] = values.attendance;
      values.attendanceStartDate = attendanceStartDate
        ? dayjs(attendanceStartDate).startOf("date").toDate()
        : undefined;
      values.attendanceEndDate = attendanceEndDate
        ? dayjs(attendanceEndDate).endOf("date").toDate()
        : undefined;

      delete values.attendance;
    }
    setFilterForQuery(values);
  }, [filterForm, setFilterForQuery]);

  useEffect(() => {
    if (!isHideFilter) {
      setFilterForQuery(advancedFilterValues);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [advancedFilterValues, isHideFilter]);

  useEffect(() => {
    setAdvancedFilterFileds([
      "Agency phụ trách",
      "Role ghi nhận",
      "Ngày chấm công",
      "Nhân viên ghi nhận",
      "Thông tin khách",
      "Mã/ Tên outlet",
      "Tỉnh/ TP",
      "Quận/ Huyện",
      "Kênh",
      "Nhóm",
      "Loại booth",
      "Trưởng nhóm quản lý",
    ]);
  }, [setAdvancedFilterFileds]);

  const pagination = useMemo(() => {
    return {
      current: currentPage,
      total: reportNumericSheetsQuery.data?.count,
      pageSize: pageSize,
      onChange: (page: number) => {
        setCurrentPage(page);
      },
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: (_current: number, size: number) => {
        setPageSize(size);
        setCurrentPage(DEFAULT_CURRENT_PAGE);
      },
    };
  }, [currentPage, pageSize, reportNumericSheetsQuery.data?.count]);

  const numericAttributeName = useMemo(() => {
    const names = new Set<string>();

    reportNumericSheetsQuery.data?.entities.forEach(
      ({ recordNumericValues }) => {
        if (recordNumericValues.length > 0) {
          recordNumericValues.forEach((recordNumericValue) => {
            names.add(recordNumericValue.featureNumericAttribute.name);
          });
        }
      },
    );

    const numericAttributes = numericAttributesQuery.data?.entities;
    numericAttributes?.sort((a, b) => a.ordinal - b.ordinal);
    const numericAttributeNamesConfig = numericAttributes?.map(
      (item) => item.name,
    );

    const result: string[] = [];

    numericAttributeNamesConfig?.forEach((name) => {
      if (names.has(name)) {
        result.push(name);
      }
    });

    if (result.length === Array.from(names).length) {
      return result;
    }

    _.difference(Array.from(names), result).forEach((name) => {
      result.push(name);
    });
    return result;
  }, [
    numericAttributesQuery.data?.entities,
    reportNumericSheetsQuery.data?.entities,
  ]);

  const onExport = useCallback(async () => {
    const total = pagination.total!;

    if (total === 0) {
      return;
    }

    const fetchAllData = async () => {
      const requests = [];

      for (let skip = 0; skip < total; skip += CHUNK_SIZE) {
        requests.push(
          getReportNumericSheetMutation.mutateAsync({
            take: CHUNK_SIZE,
            skip: skip,
            ...filter,
          }),
        );
      }

      const results = await Promise.all(requests);
      const allEntities = results.flatMap((result) => result.entities);

      return allEntities;
    };

    setExportLoading(true);

    try {
      const allData = await fetchAllData();

      const data =
        allData.flatMap((item) => {
          const { recordNumericValues, projectRecordFeature } = item;
          const { projectRecordEmployee, projectRecord, attendance } =
            projectRecordFeature;
          const { projectOutlet, projectBooth, projectAgency, leader } =
            projectRecord;

          const { in: attendanceIn, out: attendanceOut } = attendance;

          const items = recordNumericValues.map((item) => {
            const { value, featureNumeric, featureNumericAttribute } = item;
            const { projectItem, projectProduct } = featureNumeric;

            const unitName =
              projectItem?.item?.unit?.name ??
              projectProduct?.productPackaging?.unit?.name;
            const code =
              projectItem?.item?.code ?? projectProduct?.product?.code;
            const name =
              projectItem?.item?.name ?? projectProduct?.product?.name;

            return [name, code, featureNumericAttribute.name, value, unitName];
          });

          const mergedArray = [];
          for (const element of items) {
            mergedArray.push([
              ...[
                project?.id,
                project?.name,
                projectOutlet.code,
                projectOutlet.name,
                projectBooth.name,
                dayjs(attendanceIn.deviceTime).add(7, "hour").toDate(),
                attendanceOut?.deviceTime
                  ? dayjs(attendanceOut?.deviceTime).add(7, "hour").toDate()
                  : "",
                projectOutlet.province?.name,
                projectOutlet.district?.name,
                projectOutlet.projectAgencyChannel.channel.name,
                projectOutlet.subChannel?.name,
                projectAgency.agency.name,
                projectRecordEmployee.employee.role.name,
                projectRecordEmployee.employee.user.id,
                projectRecordEmployee.employee.user.name,
                leader.id,
                leader.user.name,
              ],
              ...element,
              dayjs(item.dataTimestamp).add(7, "hour").toDate(),
            ]);
          }

          return mergedArray;
        }) ?? [];

      const headers = [
        "ID dự án",
        "Tên dự án",
        "Mã outlet",
        "Tên outlet",
        "Loại booth",
        "Thời gian chấm công vào",
        "Thời gian chấm công ra",
        "Tỉnh/ TP",
        "Quận/ Huyện",
        "Kênh",
        "Nhóm",
        "Agency phụ trách",
        "Role nhân viên chấm công",
        "ID nhân viên chấm công",
        "Họ tên nhân viên chấm công",
        "ID trưởng nhóm quản lý",
        "Họ tên trưởng nhóm quản lý",
        `Item ${componentFeatureQuery.data?.name.toLocaleLowerCase()}`,
        "Mã sản phẩm",
        "Loại đối tượng",
        "Giá trị",
        "Đơn vị tính",
        "Thời gian gửi dữ liệu",
      ];
      const fileName = removeVietnameseTones(
        componentFeatureQuery.data?.name ?? "",
      );
      await createFileAndDownLoad({
        data,
        headers,
        fileName,
        dateTimeColumns: [6, 7, 23],
      });
    } catch (e) {
      console.error(e);
    } finally {
      setExportLoading(false);
    }
  }, [
    componentFeatureQuery.data?.name,
    filter,
    getReportNumericSheetMutation,
    pagination.total,
    project?.id,
    project?.name,
  ]);

  return (
    <div>
      <h2>{componentFeatureQuery.data?.name}</h2>
      <div className="bg-white p-10 rounded">
        <FilterReportZone
          form={filterForm}
          loading={
            reportNumericSheetsQuery.isLoading ||
            reportNumericSheetsQuery.isFetching ||
            exportLoading
          }
          fields={["keyword", "roleId", "attendance"]}
          onFinish={onFilterFormFinish}
          onExport={onExport}
        />
        <Table
          dataSource={reportNumericSheetsQuery.data?.entities.map((entity) => ({
            projectOutlet:
              entity.projectRecordFeature.projectRecord.projectOutlet,
            projectBooth:
              entity.projectRecordFeature.projectRecord.projectBooth,
            id: entity.id,
            attendanceIn:
              entity.projectRecordFeature.attendance?.in ?? undefined,
            attendanceOut:
              entity.projectRecordFeature.attendance?.out ?? undefined,
            projectRecordEmployee:
              entity.projectRecordFeature.projectRecordEmployee,
            projectAgency:
              entity.projectRecordFeature.projectRecord.projectAgency,
            leader: entity.projectRecordFeature.projectRecord.leader,
            recordNumericValues: entity.recordNumericValues,
          }))}
          pagination={pagination}
          scroll={{
            x: "max-content",
            y: pagination.total ? "80vh" : undefined,
          }}
          className="mt-3"
          rowKey={"id"}
          columns={[
            ...getColumnsTableReport([
              { tableColumn: "outletCode" },
              { tableColumn: "outletName" },
              { tableColumn: "boothName" },
              { tableColumn: "attendance" },
              { tableColumn: "address" },
              { tableColumn: "channelName" },
              { tableColumn: "subChannelName" },
              { tableColumn: "agencyName" },
              { tableColumn: "recordEmployee" },
              { tableColumn: "teamLeader" },
            ]),
            {
              title: `Item ${componentFeatureQuery.data?.name.toLocaleLowerCase()}`,
              className: "min-w-[250px]",
              fixed: "right",
              render: (_value, { recordNumericValues }) => {
                const groupRecordNumericValues = _.groupBy(
                  recordNumericValues,
                  "featureNumeric.id",
                );

                return Object.entries(groupRecordNumericValues).map(
                  // eslint-disable-next-line @typescript-eslint/no-unused-vars
                  ([_, groupRecordNumericValues]) => {
                    const { projectItem, projectProduct, id } =
                      groupRecordNumericValues[0].featureNumeric;
                    const unitName =
                      projectItem?.item?.unit?.name ??
                      projectProduct?.productPackaging?.unit?.name;
                    const code =
                      projectItem?.item?.code ?? projectProduct?.product?.code;
                    const name =
                      projectItem?.item?.name ?? projectProduct?.product?.name;

                    return (
                      <p key={id}>
                        {unitName} - {code} - {name}
                      </p>
                    );
                  },
                );
              },
            },
            ...numericAttributeName.map((name) => ({
              title: name,
              className: "min-w-[100px]",
              fixed: "right",
              render: (
                _value: unknown,
                {
                  recordNumericValues,
                }: { recordNumericValues: RecordNumericValueInterface[] },
              ) => {
                const groupRecordNumericValues = _.groupBy(
                  recordNumericValues,
                  "featureNumeric.id",
                );

                return Object.entries(groupRecordNumericValues).map(
                  ([index, groupRecordNumericValues]) => {
                    const recordNumericValue = groupRecordNumericValues.find(
                      (item) =>
                        _.isEqual(item.featureNumericAttribute.name, name),
                    );

                    if (!recordNumericValue) {
                      return (
                        <p key={index}>
                          <span className="text-blue font-semibold">_</span>
                        </p>
                      );
                    }
                    return (
                      <p key={index}>
                        <span className="text-blue font-semibold">
                          {recordNumericValue.value ||
                          recordNumericValue.value == 0
                            ? formatNumber(recordNumericValue.value)
                            : "_"}
                        </span>
                      </p>
                    );
                  },
                );
              },
            })),
          ]}
        />
      </div>
    </div>
  );
};

export default ReportNumericSheetPage;
