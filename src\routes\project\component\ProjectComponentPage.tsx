import InnerContainer from "@/components/InnerContainer/InnerContainer";
import { Tabs } from "antd";
import { useMemo } from "react";
import { useSearchParams } from "react-router-dom";
import FeatureTab from "./tabs/FeatureTab";
import PublicationTab from "./tabs/PublicationTab";

export default function ProjectComponentPage() {
  const [searchParams, setSearchParams] = useSearchParams();

  const items = useMemo(() => {
    return [
      {
        key: "FeatureTab",
        label: "Cấu hình chức năng",
        children: <FeatureTab />,
      },
      {
        key: "PublicationTab",
        label: "<PERSON><PERSON><PERSON> hành",
        children: <PublicationTab />,
      },
    ];
  }, []);

  return (
    <div>
      <h2>Chức năng app cho nhân viên làm việc tại booth</h2>
      <InnerContainer>
        <Tabs
          items={items}
          type="card"
          defaultActiveKey={searchParams.get("tab") ?? "FeatureTab"}
          onTabClick={(key) => {
            setSearchParams({ tab: key });
          }}
          activeKey={searchParams.get("tab") ?? "FeatureTab"}
        />
      </InnerContainer>
    </div>
  );
}
