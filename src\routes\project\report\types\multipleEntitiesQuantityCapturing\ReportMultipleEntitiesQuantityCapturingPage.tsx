import {
  CHUNK_SIZE,
  DEFAULT_CURRENT_PAGE,
  DEFAULT_PAGE_SIZE,
} from "@/common/constant";
import { createFileAndDownLoad } from "@/common/export-excel.helper";
import { removeVietnameseTones } from "@/common/helper.ts";
import CustomTable from "@/components/CustomTable/CustomTable.tsx";
import InnerContainer from "@/components/InnerContainer/InnerContainer.tsx";
import { Form } from "antd";
import dayjs from "dayjs";
import _ from "lodash";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useLocation } from "react-router-dom";
import getColumnsTableReport from "../../ColumnsTableReport";
import FilterReportZone from "../../FilterReportZone";
import {
  AdvancedFilterFormValueInterface,
  AdvancedFilterInterface,
} from "../../interface";
import { useAdvancedFilterFiledsStore } from "../../state.ts";
import { useProjectReportOutletContext } from "../../UseProjectReportOutletContext.tsx";
import {
  RecordQuantityValueInterface,
  ReportQuantityInterface,
} from "./interface";
import ReportMultipleEntitiesQuantityCapturingValueCell from "./ReportMultipleEntitiesQuantityCapturingValueCell.tsx";
import {
  useGetReportQuantitiesMutation,
  useReportQuantitiesQuery,
} from "./service";

export default function ReportMultipleEntitiesQuantityCapturingPage() {
  const {
    componentFeatureQuery,
    projectId,
    componentFeatureId,
    advancedFilterValues,
    project,
  } = useProjectReportOutletContext();

  const location = useLocation();

  const { setFileds: setAdvancedFilterFileds } = useAdvancedFilterFiledsStore();

  const [filterForm] = Form.useForm();
  const [filter, setFilter] = useState<AdvancedFilterInterface>({
    attendanceStartDate: dayjs().startOf("date").toDate(),
    attendanceEndDate: dayjs().endOf("date").toDate(),
  });
  const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [isExport, setIsExport] = useState(false);

  const reportQuantitiesQuery = useReportQuantitiesQuery(
    projectId,
    componentFeatureId,
    {
      ...filter,
      take: pageSize,
      skip: (currentPage - 1) * pageSize,
    },
  );

  const getReportQuantitiesMutation = useGetReportQuantitiesMutation(
    projectId,
    componentFeatureId,
  );

  const pagination = useMemo(() => {
    return {
      current: currentPage,
      total: reportQuantitiesQuery.data?.count,
      pageSize: pageSize,
      onChange: (page: number) => {
        setCurrentPage(page);
      },
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: (_current: number, size: number) => {
        setPageSize(size);
        setCurrentPage(DEFAULT_CURRENT_PAGE);
      },
    };
  }, [currentPage, pageSize, reportQuantitiesQuery.data?.count]);

  const setFilterForQuery = useCallback(
    (values: AdvancedFilterFormValueInterface) => {
      setCurrentPage(DEFAULT_CURRENT_PAGE);
      if (_.isEqual(filter, values)) {
        reportQuantitiesQuery.refetch();
      }
      setFilter(values);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [filter],
  );

  const onFilterFormFinish = useCallback(() => {
    const values = filterForm.getFieldsValue();

    if (values.attendance) {
      const [attendanceStartDate, attendanceEndDate] = values.attendance;
      values.attendanceStartDate = attendanceStartDate
        ? dayjs(attendanceStartDate).startOf("date").toDate()
        : undefined;
      values.attendanceEndDate = attendanceEndDate
        ? dayjs(attendanceEndDate).endOf("date").toDate()
        : undefined;

      delete values.attendance;
    }
    setFilterForQuery(values);
  }, [filterForm, setFilterForQuery]);

  useEffect(() => {
    setFilterForQuery(advancedFilterValues);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [advancedFilterValues]);

  useEffect(() => {
    setAdvancedFilterFileds([
      "Agency phụ trách",
      "Role ghi nhận",
      "Ngày chấm công",
      "Nhân viên ghi nhận",
      "Thông tin khách",
      "Mã/ Tên outlet",
      "Tỉnh/ TP",
      "Quận/ Huyện",
      "Kênh",
      "Nhóm",
      "Loại booth",
      "Trưởng nhóm quản lý",
    ]);
  }, [setAdvancedFilterFileds]);

  useEffect(() => {
    filterForm.resetFields();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location.pathname]);

  const onExport = useCallback(async () => {
    const total = pagination.total!;

    if (total === 0) {
      return;
    }

    const fetchAllData = async () => {
      const requests = [];

      for (let skip = 0; skip < total; skip += CHUNK_SIZE) {
        requests.push(
          getReportQuantitiesMutation.mutateAsync({
            take: CHUNK_SIZE,
            skip: skip,
            ...filter,
          }),
        );
      }

      const results = await Promise.all(requests);
      const allEntities = results.flatMap((result) => result.entities);

      return allEntities;
    };

    setIsExport(true);

    try {
      const allData = await fetchAllData();

      const data =
        allData.flatMap((item) => {
          const { projectOutlet, leader } =
            item.projectRecordFeature.projectRecord;

          const { projectRecordEmployee } = item.projectRecordFeature;

          const { in: attendanceIn, out: attendanceOut } =
            item.projectRecordFeature.attendance;
          const recordQuantityValues = item.recordQuantityValues;

          const quantities = recordQuantityValues.map((item) => {
            const { value, featureQuantity } = item;
            let name;
            if (featureQuantity?.projectItem) {
              name = featureQuantity.projectItem?.item?.name;
            } else {
              name = featureQuantity?.projectProduct?.product?.name;
            }

            return [name, value];
          });

          const mergedArray = [];
          for (const element of quantities) {
            mergedArray.push([
              ...[
                // project?.id,
                // project?.name,
                projectOutlet.code,
                projectOutlet.name,
                // projectBooth.name,
                dayjs(attendanceIn.deviceTime).add(7, "hour").toDate(),
                attendanceOut?.deviceTime
                  ? dayjs(attendanceOut?.deviceTime).add(7, "hour").toDate()
                  : "",
                projectOutlet.province?.name,
                projectOutlet.district?.name,
                // projectOutlet.projectAgencyChannel.channel.name,
                projectOutlet.subChannel?.name,
                // projectAgency.agency.name,
                // projectRecordEmployee.employee.role.name,
                projectRecordEmployee.employee.user.id,
                projectRecordEmployee.employee.user.name,
                leader.id,
                leader.user.name,
              ],
              ...element,
              dayjs(item.dataTimestamp).add(7, "hour").toDate(),
            ]);
          }

          return mergedArray;
        }) ?? [];

      const headers = [
        // "ID dự án",
        // "Tên dự án",
        "Mã outlet",
        "Tên outlet",
        // "Loại booth",
        "Thời gian chấm công vào",
        "Thời gian chấm công ra",
        "Tỉnh/ TP",
        "Phường", // "Quận/ Huyện",
        // "Kênh",
        "Mức outlet",
        // "Agency phụ trách",
        // "Role nhân viên chấm công",
        "ID nhân viên chấm công",
        "Họ tên nhân viên chấm công",
        "ID trưởng nhóm quản lý",
        "Họ tên trưởng nhóm quản lý",
        `Item ${componentFeatureQuery.data?.name.toLocaleLowerCase()}`,
        componentFeatureQuery.data?.name ?? "",
        "Thời gian gửi dữ liệu",
      ];
      const fileName = removeVietnameseTones(
        componentFeatureQuery.data?.name ?? "",
      );
      await createFileAndDownLoad({
        data,
        headers,
        fileName,
        dateTimeColumns: [20, 21],
      });
    } catch (e) {
      console.error(e);
    } finally {
      setIsExport(false);
    }
  }, [
    componentFeatureQuery.data?.name,
    filter,
    getReportQuantitiesMutation,
    pagination.total,
    project?.id,
    project?.name,
  ]);

  return (
    <div>
      <h2>{componentFeatureQuery.data?.name}</h2>
      <InnerContainer>
        <FilterReportZone
          form={filterForm}
          loading={
            reportQuantitiesQuery.isFetching ||
            reportQuantitiesQuery.isLoading ||
            isExport
          }
          fields={["keyword", "roleId", "attendance"]}
          onFinish={onFilterFormFinish}
          onExport={onExport}
        />

        <CustomTable<ReportQuantityInterface>
          rowKey={"id"}
          pagination={pagination}
          scroll={{
            x: "max-content",
            y: pagination.total ? "80vh" : undefined,
          }}
          className="mt-3"
          dataSource={reportQuantitiesQuery.data?.entities.map((item) => ({
            id: item.id,
            projectOutlet:
              item.projectRecordFeature.projectRecord.projectOutlet,
            projectBooth: item.projectRecordFeature.projectRecord.projectBooth,
            projectRecordEmployee:
              item.projectRecordFeature.projectRecordEmployee,
            attendanceIn: item.projectRecordFeature.attendance.in ?? undefined,
            attendanceOut:
              item.projectRecordFeature.attendance.out ?? undefined,
            recordQuantityValues: item.recordQuantityValues,
            dataTimestamp: item.dataTimestamp,
            projectAgency:
              item.projectRecordFeature.projectRecord.projectAgency,
            leader: item.projectRecordFeature.projectRecord.leader,
            dataUuid: item.dataUuid,
            projectFeatureId: item.projectRecordFeature.projectFeatureId,
            projectRecordFeature: item.projectRecordFeature,
          }))}
          columns={[
            ...getColumnsTableReport([
              { tableColumn: "outletCode" },
              { tableColumn: "outletName" },
              // { tableColumn: "boothName" },
              { tableColumn: "attendance" },
              { tableColumn: "dataTimestamp" },
              { tableColumn: "address" },
              // { tableColumn: "channelName" },
              { tableColumn: "subChannelName" },
              // { tableColumn: "agencyName" },
              { tableColumn: "recordEmployee" },
              { tableColumn: "teamLeader" },
            ]),
            {
              title: componentFeatureQuery.data?.name,
              fixed: "right",
              className: "min-w-[200px]",
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              render: (_value: any, record: any) => {
                const {
                  recordQuantityValues,
                }: { recordQuantityValues: RecordQuantityValueInterface[] } =
                  record;
                return (
                  <ReportMultipleEntitiesQuantityCapturingValueCell
                    recordQuantityValues={recordQuantityValues}
                  />
                );
              },
            },
          ]}
        />
      </InnerContainer>
    </div>
  );
}
