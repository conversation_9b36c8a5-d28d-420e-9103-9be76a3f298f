import { UserInterface } from "@/routes/user/interface.ts";
import { RecordOrderInterface } from "@project/report/types/customerInformationCapturing/interface.ts";

export interface CustomerInformationDto {
  dataTimestamp: string;
  customers?: {
    featureCustomerId: number;
    featureCustomerOptionIds?: number[];
    value?: string | null;
  }[];
  purchases?: {
    featureOrderProductId: number;
    quantity: number;
  }[];
  exchanges?: {
    featureSchemeExchangeId: number;
    quantity: number;
  }[];
  samplings?: {
    featureSamplingId: number;
    quantity: number;
  }[];
  photos?: {
    dataUuid: string;
    dataTimestamp: string;
    featurePhotoId: number;
    imageId: number;
  }[];
}

export interface CreateCustomerInformationDto extends CustomerInformationDto {
  dataUuid: string;
}

export interface EditRecordOrderInterface extends RecordOrderInterface {
  usesUpdateTool: boolean;
  usesCreateTool: boolean;
  editedByUser?: UserInterface;
  editedAt?: string;
}

export type RecordType = string | number | null;
