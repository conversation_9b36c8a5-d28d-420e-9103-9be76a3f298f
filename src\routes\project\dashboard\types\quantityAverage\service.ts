import { useApp } from "@/UseApp";
import { useQuery } from "@tanstack/react-query";
import { DashboardFilterInterface } from "../../interface";

export const useQuantityAverageQuery = (
  projectId: number,
  dashboardId: number,
  filter?: DashboardFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["quantityAverage", projectId, dashboardId, filter],
    queryFn: () =>
      axiosGet<
        {
          backgroundColor: string;
          foregroundColor: string;
          name: string;
          groupName: string;
          shortName: string;
          code: string;
          average: number;
        }[],
        unknown
      >(
        `/projects/${projectId}/dashboards/${dashboardId}/charts/quantity-average`,
        filter,
      ),
  });
};
