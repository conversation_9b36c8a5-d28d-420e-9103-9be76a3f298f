import { CURD } from "@/common/constant.ts";
import ModalCURD from "@/components/ModalCURD.tsx";
import { useApp } from "@/UseApp.tsx";
import { OosZoneInterface } from "@project/component/feature/config/types/outOfStockStatus/interface.ts";
import {
  useCreateOosZoneMutation,
  useUpdateOosZoneMutation,
} from "@project/component/feature/config/types/outOfStockStatus/steps/zone/service.ts";
import { Form, Input } from "antd";
import { useCallback, useEffect } from "react";

interface OutOfStockStatusZoneModalProps {
  onCancelCb: () => void;
  cb: () => void;
  componentFeatureId: number;
  action: CURD | null;
  selectedOosZone: OosZoneInterface | null;
}

const OutOfStockStatusZoneModal = ({
  onCancelCb,
  cb,
  action,
  componentFeatureId,
  selectedOosZone,
}: OutOfStockStatusZoneModalProps) => {
  const { showNotification } = useApp();

  const [form] = Form.useForm();

  const createOosZoneMutation = useCreateOosZoneMutation(componentFeatureId);
  const updateOosZoneMutation = useUpdateOosZoneMutation(componentFeatureId);

  const onFinish = useCallback(async () => {
    if (action === CURD.CREATE) {
      await createOosZoneMutation.mutateAsync(form.getFieldsValue());

      showNotification({
        type: "success",
        message: "Thêm vị trí ghi nhận dữ liệu thành công",
      });
    }

    if (action === CURD.UPDATE && selectedOosZone) {
      await updateOosZoneMutation.mutateAsync({
        id: selectedOosZone.id,
        ...form.getFieldsValue(),
      });

      showNotification({
        type: "success",
        message: "Cập nhật vị trí ghi nhận dữ liệu thành công",
      });
    }

    cb();
  }, [
    action,
    cb,
    createOosZoneMutation,
    form,
    selectedOosZone,
    showNotification,
    updateOosZoneMutation,
  ]);

  const formContent = (
    <>
      <Form.Item
        label="Vị trí ghi nhận dữ liệu"
        name={"name"}
        rules={[{ required: true }]}
      >
        <Input />
      </Form.Item>
      <Form.Item label="Mô tả" name={"description"}>
        <Input.TextArea />
      </Form.Item>
    </>
  );

  useEffect(() => {
    if (action === CURD.UPDATE && selectedOosZone) {
      form.setFieldsValue({
        name: selectedOosZone.name,
        description: selectedOosZone.description,
      });
    }
  }, [action, form, selectedOosZone]);

  return (
    <ModalCURD
      title={"Thêm vị trí ghi nhận dữ liệu"}
      isOpen={true}
      formContent={formContent}
      form={form}
      onFinish={onFinish}
      onCancelCb={() => {
        form.resetFields();
        onCancelCb();
      }}
      action={action}
    />
  );
};

export default OutOfStockStatusZoneModal;
