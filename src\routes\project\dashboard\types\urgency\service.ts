import { useApp } from "@/UseApp";
import { useQuery } from "@tanstack/react-query";
import { DashboardFilterInterface } from "../../interface";

export const useUrgencyFrequencyQuery = (
  projectId: number,
  dashboardId: number,
  filter?: DashboardFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["urgencyFrequency", projectId, dashboardId, filter],
    queryFn: () =>
      axiosGet<
        {
          backgroundColor: string;
          foregroundColor: string;
          name: string;
          note: string;
          count: number;
        }[],
        unknown
      >(
        `/projects/${projectId}/dashboards/${dashboardId}/charts/urgency-frequency`,
        filter,
      ),
  });
};

export const useUrgencyTop10EmployeeFrequencyQuery = (
  projectId: number,
  dashboardId: number,
  filter?: DashboardFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["urgencyTop10EmployeeFrequency", projectId, dashboardId, filter],
    queryFn: () =>
      axiosGet<
        {
          id: number;
          name: string;
          picture: string;
          count: number;
        }[],
        unknown
      >(
        `/projects/${projectId}/dashboards/${dashboardId}/charts/urgency-top-10-employees-frequency`,
        filter,
      ),
  });
};
