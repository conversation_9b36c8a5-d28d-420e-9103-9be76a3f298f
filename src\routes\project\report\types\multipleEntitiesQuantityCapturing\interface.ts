import { AbstractEntityInterface } from "@/common/interface";
import { ProjectItemInterface } from "@project/item/interface";
import { ProjectProductInterface } from "@project/product/interface";
import { ProjectRecordFeatureInterface } from "../../interface";

export interface FeatureQuantityInterface extends AbstractEntityInterface {
  projectProduct?: ProjectProductInterface;
  projectItem?: ProjectItemInterface;
  ordinal: number;
}

export interface RecordQuantityValueInterface extends AbstractEntityInterface {
  featureQuantityId: number;
  featureQuantity?: FeatureQuantityInterface;
  value: number;
}

export interface ReportQuantityInterface extends AbstractEntityInterface {
  dataUuid: string;
  dataTimestamp: string;
  projectFeatureId: number;
  recordQuantityValues: RecordQuantityValueInterface[];
  projectRecordFeature: ProjectRecordFeatureInterface;
}
