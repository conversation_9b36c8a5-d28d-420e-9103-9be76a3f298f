import { useMutation } from "@tanstack/react-query";
import axios from "axios";
import { AppContextInterface } from "../interface.ts";
import { useApp } from "../UseApp.tsx";

export const uploadImage = async (
  axiosPost: AppContextInterface["axiosPost"],
  file: File,
): Promise<{
  id: number;
  result: {
    id: string;
    filename: string;
    uploaded: string;
    requireSignedURLs: boolean;
    variants: string[];
  };
} | null> => {
  try {
    const createUploadLinkResponse = await axiosPost<
      { id: number; uploadUrl: string },
      unknown
    >("images", {});

    if (createUploadLinkResponse.uploadUrl) {
      const formData = new FormData();
      formData.append("file", file);

      return axios
        .post<{
          result: {
            id: string;
            filename: string;
            uploaded: string;
            requireSignedURLs: boolean;
            variants: string[];
          };
          success: boolean;
        }>(createUploadLinkResponse.uploadUrl, formData, {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        })
        .then((response) => {
          if (response.data.success) {
            return {
              id: createUploadLinkResponse.id,
              result: response.data.result,
            };
          } else {
            throw new Error("Upload image failed");
          }
        })
        .catch((e) => {
          console.error(e);

          throw new Error("Upload image failed");
        });
    }
  } catch (e) {
    console.error(e);
  }
  return null;
};

export const useUploadImageMutation = () => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["uploadImage"],
    mutationFn: (file: File) => uploadImage(axiosPost, file),
  });
};
