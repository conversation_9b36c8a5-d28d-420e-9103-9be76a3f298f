import { Col, <PERSON>u, Row, Skeleton } from "antd";
import { Suspense, useCallback, useEffect, useMemo, useState } from "react";
import { Link, Outlet, useNavigate } from "react-router-dom";
import { NumericSheetStepEnum } from "./interface";

const NumericSheetPage = () => {
  const navigate = useNavigate();

  const [activeKey, setActiveKey] = useState<string>(
    NumericSheetStepEnum.GROUP,
  );

  const items = useMemo(() => {
    return [
      {
        key: NumericSheetStepEnum.GROUP,
        label: (
          <Link to={NumericSheetStepEnum.GROUP}>• Khai báo nhóm item</Link>
        ),
      },
      {
        key: NumericSheetStepEnum.DATA,
        label: (
          <Link to={NumericSheetStepEnum.DATA}>
            • <PERSON>hai báo dữ liệu ghi nhận
          </Link>
        ),
      },
      {
        key: NumericSheetStepEnum.OUTLET,
        label: (
          <Link to={NumericSheetStepEnum.OUTLET}>
            • Phân bổ outlet vào nhóm item
          </Link>
        ),
      },
    ];
  }, []);

  const setRouteActive = useCallback(
    (value: string) => {
      navigate(value);
    },
    [navigate],
  );

  useEffect(() => {
    const activeItem = items.find((item) =>
      location.pathname.endsWith(item.key),
    );
    if (activeItem) {
      setActiveKey(activeItem.key.toString());
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location.pathname]);

  return (
    <div className="bg-white pt-5 pl-10 rounded pb-5 mt-5 pr-10">
      <Row className="" justify={"space-between"}>
        <Col span={4}>
          <Menu
            className="bg-[#FAFAFA] config-sampling-menu"
            items={items}
            mode="inline"
            style={{ borderInline: "none" }}
            onClick={({ key }) => setRouteActive(key)}
            selectedKeys={[activeKey]}
          />
        </Col>
        <Col span={19}>
          <Suspense fallback={<Skeleton loading />}>
            <Outlet />
          </Suspense>
        </Col>
      </Row>
    </div>
  );
};

export default NumericSheetPage;
