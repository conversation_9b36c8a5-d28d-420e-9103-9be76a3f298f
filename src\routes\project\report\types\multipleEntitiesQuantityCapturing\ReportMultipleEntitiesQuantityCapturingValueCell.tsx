import { formatNumber } from "@/common/helper";
import { RecordQuantityValueInterface } from "./interface";

interface ReportMultipleEntitiesQuantityCapturingValueCellProps {
  recordQuantityValues: RecordQuantityValueInterface[];
}

const ReportMultipleEntitiesQuantityCapturingValueCell = ({
  recordQuantityValues,
}: ReportMultipleEntitiesQuantityCapturingValueCellProps) => {
  return (
    <ul>
      {recordQuantityValues.map((item) => (
        <li key={item.id} className={"pb-3"}>
          <div className="flex justify-between">
            <div className="pr-3">
              {item?.featureQuantity?.projectItem && (
                <>
                  {item.featureQuantity.projectItem.item.unit?.name} -{" "}
                  {item.featureQuantity.projectItem.item.code} -{" "}
                  {item.featureQuantity.projectItem.item.name}
                </>
              )}
              {item?.featureQuantity?.projectProduct && (
                <>
                  {
                    item.featureQuantity.projectProduct.productPackaging?.unit
                      ?.name
                  }{" "}
                  - {item.featureQuantity.projectProduct.product.code} -{" "}
                  {item.featureQuantity.projectProduct.product.name}
                </>
              )}
            </div>

            <div className="text-blue font-semibold">
              {item.value || item.value === 0 ? formatNumber(item.value) : "_"}
            </div>
          </div>
        </li>
      ))}
    </ul>
  );
};
export default ReportMultipleEntitiesQuantityCapturingValueCell;
