import { useApp } from "@/UseApp";
import { formErrorResponseHand<PERSON> } from "@/common/helper";
import CustomTable from "@/components/CustomTable/CustomTable";
import DragSortRowComponent from "@/components/DragSortRowComponent";
import FilterComponent from "@/components/FilterComponent";
import InnerContainer from "@/components/InnerContainer/InnerContainer";
import TableActionCell from "@/components/TableActionCell";
import { renderTableCell } from "@/components/table-cell";
import {
  ArrowLeftOutlined,
  CloseOutlined,
  DeleteOutlined,
  EditOutlined,
  FileSearchOutlined,
  LeftOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { DndContext, DragEndEvent } from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
  SortableContext,
  arrayMove,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { RoleInterface } from "@project/role/interface";
import {
  Al<PERSON>,
  Button,
  Col,
  Form,
  Input,
  Modal,
  Row,
  Select,
  Space,
  Table,
  Tag,
  Tooltip,
} from "antd";
import { ColumnsType } from "antd/es/table";
import _ from "lodash";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { Link, useLocation, useNavigate, useParams } from "react-router-dom";
import { ProjectComponentInterface } from "../interface";
import { useProjectComponentQuery } from "../service";
import {
  ComponentFeatureActionEnum,
  ComponentFeatureInterface,
  FeatureInterface,
} from "./interface";
import {
  useArrangementComponentFeatureMutation,
  useComponentFeatureTypesQuery,
  useComponentFeaturesQuery,
  useCreateComponentFeatureMutation,
  useDeleteComponentFeatureMutation,
  useUpdateComponentFeatureMutation,
  useUpdateDependentOnComponentFeaturesMutation,
} from "./service";

export default function ComponentFeaturePage() {
  const projectId = parseInt(useParams().id ?? "0");
  const projectComponentId = parseInt(useParams().projectComponentId ?? "0");

  const { showNotification, openDeleteModal } = useApp();

  const location = useLocation();
  const navigate = useNavigate();

  const [searchForm] = Form.useForm();
  const [createComponentFeatureForm] = Form.useForm();
  const [isModalSelectFeatureTypeOpen, setIsModalSelectFeatureTypeOpen] =
    useState(false);
  const [selectedFeatureType, setSelectedFeatureType] = useState<string | null>(
    null,
  );
  const [isModalSetNameOpen, setIsModalSetNameOpen] = useState(false);
  const [modal, contextHolder] = Modal.useModal();
  const [selectedComponentFeature, setSelectedComponentFeature] =
    useState<ComponentFeatureInterface | null>(null);
  const [selectedDependentKeys, setSelectedDependentKeys] = useState<
    React.Key[]
  >([]);
  const [electFeatureTypeModalForm] = Form.useForm();
  const [featureTypes, setFeatureTypes] = useState<FeatureInterface[]>([]);
  const [filter, setFilter] = useState({});
  const [dependentFilter, setDependentFilter] = useState("");

  const projectComponentQuery = useProjectComponentQuery(
    projectId,
    projectComponentId,
  );

  const componentFeatureTypesQuery = useComponentFeatureTypesQuery(
    projectId,
    projectComponentId,
  );

  const componentFeaturesQuery = useComponentFeaturesQuery(
    projectId,
    projectComponentId,
    {
      ...filter,
      take: 0,
    },
  );

  const createComponentFeatureMutation = useCreateComponentFeatureMutation(
    projectId,
    projectComponentId,
  );

  const arrangementComponentFeatureMutation =
    useArrangementComponentFeatureMutation(projectId, projectComponentId);

  const updateComponentFeatureMutation = useUpdateComponentFeatureMutation(
    projectId,
    projectComponentId,
  );

  const deleteComponentFeatureMutation = useDeleteComponentFeatureMutation(
    projectId,
    projectComponentId,
  );

  const updateDependentOnComponentFeaturesMutation =
    useUpdateDependentOnComponentFeaturesMutation(
      projectId,
      projectComponentId,
    );

  const component: ProjectComponentInterface =
    location.state || projectComponentQuery.data;

  const [dataSource, setDataSource] = useState(
    componentFeaturesQuery.data?.entities ?? [],
  );

  useEffect(() => {
    setDataSource(
      (componentFeaturesQuery.data?.entities ?? []).sort(
        (a, b) => a.ordinal - b.ordinal,
      ),
    );
  }, [componentFeaturesQuery.data]);

  const handleActionInActiveClick = useCallback(
    (record: ComponentFeatureInterface) => {
      modal.confirm({
        title: `Ngừng hoạt động chức năng: ${record.name}`,
        content: `Bạn có chắc chắn muốn ngừng hoạt động chức năng (${record.name}) này?`,
        okText: "Ngừng hoạt động",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await updateComponentFeatureMutation.mutateAsync({
              id: record.id,
              data: { isActive: false },
            });

            showNotification({
              type: "success",
              message: `Ngừng hoạt động ${record.name} thành công`,
            });

            componentFeaturesQuery.refetch();
          } catch (error) {
            console.error(error);

            showNotification({
              type: "error",
              message: `Ngừng hoạt động ${record.name} thất bại`,
            });
          }
        },
      });
    },
    [
      componentFeaturesQuery,
      modal,
      showNotification,
      updateComponentFeatureMutation,
    ],
  );

  const handleActionActiveClick = useCallback(
    (record: ComponentFeatureInterface) => {
      modal.confirm({
        title: `Kích hoạt chức năng: ${record.name}`,
        content: `Bạn có chắc chắn muốn kích hoạt chức năng (${record.name}) này?`,
        okText: "Kích hoạt",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await updateComponentFeatureMutation.mutateAsync({
              id: record.id,
              data: { isActive: true },
            });

            showNotification({
              type: "success",
              message: `Kích hoạt ${record.name} thành công`,
            });

            componentFeaturesQuery.refetch();
          } catch (error) {
            console.error(error);

            showNotification({
              type: "error",
              message: `Kích hoạt ${record.name} thất bại`,
            });
          }
        },
      });
    },
    [
      componentFeaturesQuery,
      modal,
      showNotification,
      updateComponentFeatureMutation,
    ],
  );

  const handleActionDeleteClick = useCallback(
    (record: ComponentFeatureInterface) => {
      openDeleteModal({
        content: (
          <p>
            Bạn muốn xóa chức năng{" "}
            <span className={"font-semibold"}>{record.name}</span>?
          </p>
        ),
        deleteText: "Xác nhận xóa",
        loading: false,
        onCancel(): void {},
        onDelete: async () => {
          await deleteComponentFeatureMutation.mutateAsync(record.id);

          showNotification({
            type: "success",
            message: "Xóa role chức năng thành công",
          });
          await componentFeaturesQuery.refetch();
        },
        title: `Xóa chức năng`,
        titleError: "Không thể xóa chức năng",
        contentHeader: (
          <>
            Không thể xóa chức năng{" "}
            <span className="font-semibold">{record.name}</span> bởi vì:
          </>
        ),
      });
    },
    [
      componentFeaturesQuery,
      deleteComponentFeatureMutation,
      openDeleteModal,
      showNotification,
    ],
  );

  const handleActionDependentClick = useCallback(
    (record: ComponentFeatureInterface) => {
      setSelectedComponentFeature(record);
    },
    [],
  );

  const handleActionEditClick = useCallback(
    (record: ComponentFeatureInterface) => {
      navigate(
        `/project/${projectId}/component/${projectComponentId}/feature/${record.id}/${record.type}`,
        {
          state: record,
        },
      );
    },
    [navigate, projectComponentId, projectId],
  );

  const ACTION_ACTIVE = [
    ComponentFeatureActionEnum.EDIT,
    ComponentFeatureActionEnum.DEPENDENT,
    ComponentFeatureActionEnum.INACTIVE,
    ComponentFeatureActionEnum.DELETE,
  ];
  const ACTION_INACTIVE = [
    ComponentFeatureActionEnum.EDIT,
    ComponentFeatureActionEnum.DEPENDENT,
    ComponentFeatureActionEnum.ACTIVE,
    ComponentFeatureActionEnum.DELETE,
  ];

  const actionItems = [
    {
      key: ComponentFeatureActionEnum.EDIT,
      label: (
        <Space>
          <EditOutlined /> Chỉnh sửa
        </Space>
      ),
    },
    {
      key: ComponentFeatureActionEnum.DEPENDENT,
      label: (
        <Space>
          <FileSearchOutlined /> Chức năng cần hoàn thành trước
        </Space>
      ),
    },
    {
      key: ComponentFeatureActionEnum.INACTIVE,
      label: (
        <Space>
          <PauseCircleOutlined /> Ngừng hoạt động
        </Space>
      ),
    },
    {
      key: ComponentFeatureActionEnum.ACTIVE,
      label: (
        <Space>
          <PlayCircleOutlined /> Kích hoạt
        </Space>
      ),
    },
    {
      key: ComponentFeatureActionEnum.DELETE,
      label: (
        <Space>
          <DeleteOutlined /> Xóa khỏi dự án
        </Space>
      ),
    },
  ];

  const actionActions = [
    {
      key: ComponentFeatureActionEnum.EDIT,
      action: handleActionEditClick,
    },
    {
      key: ComponentFeatureActionEnum.INACTIVE,
      action: handleActionInActiveClick,
    },
    {
      key: ComponentFeatureActionEnum.ACTIVE,
      action: handleActionActiveClick,
    },
    {
      key: ComponentFeatureActionEnum.DELETE,
      action: handleActionDeleteClick,
    },
    {
      key: ComponentFeatureActionEnum.DEPENDENT,
      action: handleActionDependentClick,
    },
  ];

  const handleDependentOnFeaturesClick = (
    record: ComponentFeatureInterface,
  ) => {
    setSelectedComponentFeature(record);
  };

  const componentFeaturesColumns: ColumnsType<ComponentFeatureInterface> = [
    {
      key: "sort",
    },
    {
      title: "Tên chức năng",
      dataIndex: "name",
      key: "name",
      className: "cursor-pointer",
      onCell: (record) => {
        return {
          onClick: () => {
            handleActionEditClick(record);
          },
        };
      },
      render: (value: string, record: ComponentFeatureInterface) => {
        const componentFeatureType = componentFeatureTypesQuery.data?.find(
          (item) => item.featureType === record.type,
        );

        return (
          <Tooltip
            title={`Loại data chức năng: ${componentFeatureType?.featureTypeName}`}
          >
            {value}
          </Tooltip>
        );
      },
    },
    {
      title: "Chức năng cần hoàn thành trước",
      dataIndex: "dependentOnFeatures",
      key: "dependentOnFeatures",
      align: "right",
      render: (value: [], record: ComponentFeatureInterface) => {
        return (
          <Button
            type="link"
            onClick={() => {
              handleDependentOnFeaturesClick(record);
            }}
          >
            {value.length}
          </Button>
        );
      },
    },
    {
      title: "Vị trí áp dụng",
      dataIndex: "applicableRoles",
      key: "applicableRoles",
      align: "left",
      render: (value: RoleInterface[]) => {
        const span = Math.floor(24 / value.length);
        return (
          <Row>
            {value.map((item) => {
              return (
                <Col span={span} key={item.id} className="text-center">
                  {item.isApplied ? <Tag>{item.name}</Tag> : "-"}
                </Col>
              );
            })}
          </Row>
        );
      },
    },
    {
      title: "Tình trạng",
      key: "isActive",
      dataIndex: "isActive",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "isActive");
      },
    },
    {
      key: "actions",
      render: (_, record) => {
        const actionKeys = record.isActive ? ACTION_ACTIVE : ACTION_INACTIVE;
        const items = actionItems.filter((item) =>
          actionKeys.includes(item.key),
        );
        return (
          <TableActionCell
            actions={actionActions}
            items={items}
            record={record}
          />
        );
      },
      fixed: "right",
      width: 100,
    },
  ];

  const searchHandler = useCallback(() => {
    const values = searchForm.getFieldsValue();
    if (_.isEqual(values, filter)) {
      componentFeaturesQuery.refetch();
    }
    setFilter(values);
  }, [componentFeaturesQuery, filter, searchForm]);

  const onDragEnd = useCallback(
    async ({ active, over }: DragEndEvent) => {
      if (!over) {
        return;
      }
      if (active.id !== over?.id) {
        setDataSource((previous) => {
          const activeIndex = previous?.findIndex((i) => i.id === active.id);
          const overIndex = previous?.findIndex((i) => i.id === over?.id);
          return arrayMove(previous, activeIndex, overIndex);
        });

        await arrangementComponentFeatureMutation.mutateAsync({
          activeId: active.id as number,
          overId: over?.id as number,
        });
      }
    },
    [arrangementComponentFeatureMutation],
  );

  const handleFormCreateComponentFeature = async (values: { name: string }) => {
    if (selectedFeatureType) {
      try {
        const componentFeature =
          await createComponentFeatureMutation.mutateAsync({
            name: values.name,
            type: selectedFeatureType,
          });

        createComponentFeatureForm.resetFields();
        setSelectedFeatureType(null);
        setIsModalSelectFeatureTypeOpen(false);
        setIsModalSetNameOpen(false);

        navigate(
          `/project/${projectId}/component/${projectComponentId}/feature/${componentFeature.id}/${componentFeature.type}`,
          {
            state: componentFeature,
          },
        );
      } catch (e) {
        formErrorResponseHandler(createComponentFeatureForm, e);
      }
    }
  };

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedDependentKeys(newSelectedRowKeys);
  };

  const onSubmitDependent = useCallback(async () => {
    if (selectedComponentFeature) {
      try {
        await updateDependentOnComponentFeaturesMutation.mutateAsync({
          id: selectedComponentFeature.id,
          data: {
            dependentProjectFeatureIds: selectedDependentKeys as number[],
          },
        });

        showNotification({
          type: "success",
          message: `Cập nhật chức năng hoàn thành trước cho chức năng ${selectedComponentFeature.name} thành công.`,
        });

        setSelectedComponentFeature(null);
        componentFeaturesQuery.refetch();
        setSelectedDependentKeys([]);
      } catch (error) {
        console.error(error);

        showNotification({
          type: "error",
          message: `Cập nhật chức năng hoàn thành trước cho chức năng ${selectedComponentFeature.name} thất bại.`,
        });
      }
    }
  }, [
    componentFeaturesQuery,
    selectedComponentFeature,
    selectedDependentKeys,
    showNotification,
    updateDependentOnComponentFeaturesMutation,
  ]);

  useEffect(() => {
    const selectedKeys =
      selectedComponentFeature?.dependentOnFeatures.map(
        (i) => i.dependentProjectFeatureId,
      ) ?? [];
    setSelectedDependentKeys(selectedKeys);
  }, [selectedComponentFeature]);

  useEffect(() => {
    setFeatureTypes(componentFeatureTypesQuery.data ?? []);
  }, [componentFeatureTypesQuery.data]);

  const onSelectFeatureTypeModalSearch = useCallback(() => {
    const { name, position } = electFeatureTypeModalForm.getFieldsValue();

    setFeatureTypes(
      componentFeatureTypesQuery.data?.filter((item) => {
        let nameCondition = true;
        let positionCondition = true;
        if (name) {
          if (
            item.featureTypeName
              .toLocaleLowerCase()
              .search(name.toLocaleLowerCase()) === -1
          ) {
            nameCondition = false;
          }
        }

        if (position && position !== "ALL") {
          if (position === "LEADER" && !item.isLeaderAllowed) {
            positionCondition = false;
          }
          if (position === "NONE_LEADER" && !item.isMemberAllowed) {
            positionCondition = false;
          }
        }

        return nameCondition && positionCondition;
      }) ?? [],
    );
  }, [componentFeatureTypesQuery.data, electFeatureTypeModalForm]);

  const sortedFeatureTypes = useMemo(
    () =>
      [...featureTypes].sort((a, b) =>
        a.featureTypeName.localeCompare(b.featureTypeName),
      ),
    [featureTypes],
  );

  return (
    <>
      <Link to={`/project/${projectId}/component`}>
        <p className={"text-hint mt-[34px]"}>
          <ArrowLeftOutlined /> Quay lại
        </p>
      </Link>
      <h2>Chức năng tại booth</h2>
      <InnerContainer>
        <p>
          Tên nhóm chức năng:{" "}
          <span className="text-[#393939] font-semibold text-sm">
            {component?.name}
          </span>
        </p>
        <p>
          Loại booth áp dụng:
          {component?.projectComponentBooths?.map((projectComponentBooth) => (
            <span
              key={projectComponentBooth.id}
              className="text-[#393939] bg-[#ECEDEF] p-1 m-1 rounded-3xl font-semibold text-xs"
            >
              {projectComponentBooth.projectBooth.name}
            </span>
          ))}
        </p>

        <FilterComponent
          searchHandler={searchHandler}
          searchForm={searchForm}
          filterOptions={[
            {
              label: "Tất cả",
              value: "all",
            },
            {
              label: "Tên chức năng",
              value: "name",
            },
            {
              label: "Vị trí áp dụng",
              value: "roleName",
            },
          ]}
          handleAddButtonClick={() => {
            setIsModalSelectFeatureTypeOpen(true);
          }}
          className={"mb-6"}
        />

        <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
          <SortableContext
            items={dataSource.map((i) => i.id)}
            strategy={verticalListSortingStrategy}
          >
            <CustomTable
              loading={
                arrangementComponentFeatureMutation.isPending ||
                projectComponentQuery.isFetching ||
                projectComponentQuery.isPending
              }
              components={{
                body: {
                  row: DragSortRowComponent,
                },
              }}
              rowKey="id"
              columns={componentFeaturesColumns}
              dataSource={dataSource}
              pagination={false}
            />
          </SortableContext>
        </DndContext>
      </InnerContainer>

      <Modal
        open={isModalSelectFeatureTypeOpen}
        footer={null}
        closeIcon={null}
        styles={{ content: { padding: 0 } }}
        width={1000}
      >
        <div className="pl-10 pr-10">
          <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
            <h2 className="text-neutral-700 text-2xl font-semibold ">
              Chọn loại data chức năng cần thêm cho booth
            </h2>
            <div className="pt-5">
              <Button
                type="link"
                onClick={() => {
                  setIsModalSelectFeatureTypeOpen(false);
                  setSelectedFeatureType(null);
                }}
                size="large"
                icon={<CloseOutlined />}
              />
            </div>
          </div>

          <Form
            layout="inline"
            onValuesChange={onSelectFeatureTypeModalSearch}
            form={electFeatureTypeModalForm}
          >
            <Form.Item name={"name"}>
              <Input
                prefix={<SearchOutlined />}
                placeholder="Loại data chức năng cần tìm"
                style={{ width: "250px" }}
                allowClear
              />
            </Form.Item>
            <Form.Item name={"position"}>
              <Select
                placeholder="Vị trí áp dụng"
                style={{ width: "250px" }}
                allowClear
                options={[
                  {
                    label: "Tất cả vị trí",
                    value: "ALL",
                  },
                  {
                    label: "Chỉ áp dụng cho trưởng nhóm",
                    value: "LEADER",
                  },
                  {
                    label: "Chỉ áp dụng cho thành viên",
                    value: "NONE_LEADER",
                  },
                ]}
              />
            </Form.Item>
          </Form>

          <Table
            className="pt-5 pb-5"
            rowKey={"featureType"}
            columns={[
              {
                title: "Tên loại data chức năng",
                dataIndex: "featureTypeName",
                key: "featureTypeName",
                render: (value: string, feature: FeatureInterface) => {
                  const existingFeature =
                    componentFeaturesQuery.data?.entities.find(
                      (item) => item.type === feature.featureType,
                    );
                  return (
                    <>
                      {value}
                      &nbsp; &nbsp;
                      {existingFeature ? (
                        <span className="text-[#8C8C8D] text-sm italic">
                          Đã thêm trước đó
                        </span>
                      ) : (
                        ""
                      )}
                    </>
                  );
                },
              },
              {
                title: "Vị trí áp dụng",
                render: (_, feature: FeatureInterface) => {
                  const renderRole = (allowed: boolean, text: string) =>
                    allowed ? (
                      <span className="text-[#393939] bg-[#ECEDEF] p-1 ml-1 rounded-3xl font-semibold text-xs">
                        {text}
                      </span>
                    ) : (
                      "-"
                    );

                  return (
                    <Row>
                      <Col span={12} className="text-center">
                        {renderRole(feature.isMemberAllowed, "Thành viên")}
                      </Col>
                      <Col span={12} className="text-center">
                        {renderRole(feature.isLeaderAllowed, "Trưởng nhóm")}
                      </Col>
                    </Row>
                  );
                },
              },
            ]}
            dataSource={sortedFeatureTypes}
            rowSelection={{
              type: "radio",
              selectedRowKeys: [selectedFeatureType as React.Key],
              onChange: (selectedRowKeys) => {
                setSelectedFeatureType(selectedRowKeys[0] as string);
              },
              getCheckboxProps: (record: FeatureInterface) => {
                return {
                  disabled: !record.isAvailable,
                };
              },
            }}
            pagination={false}
          />
        </div>
        <div className="flex justify-end gap-4 py-4 max-md:max-w-full max-md:flex-wrap pb-4 pl-10 pr-10 bg-[#F7F8FA] rounded">
          <Button
            htmlType="button"
            onClick={() => {
              setIsModalSelectFeatureTypeOpen(false);
              setSelectedFeatureType(null);
            }}
          >
            Đóng
          </Button>
          <Button
            htmlType="button"
            type={"primary"}
            disabled={!selectedFeatureType}
            onClick={() => {
              setIsModalSelectFeatureTypeOpen(false);
              setIsModalSetNameOpen(true);
            }}
          >
            Tiếp tục
          </Button>
        </div>
      </Modal>

      <Modal
        open={isModalSetNameOpen}
        footer={null}
        closeIcon={null}
        styles={{ content: { padding: 0 } }}
        onCancel={() => {
          setSelectedFeatureType(null);
        }}
      >
        <Form layout="vertical" onFinish={handleFormCreateComponentFeature}>
          <div className="pl-10 pr-10">
            <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
              <h2 className="text-neutral-700 text-2xl font-semibold ">
                Đặt tên cho chức năng
              </h2>
              <div className="pt-5">
                <Button
                  type="link"
                  onClick={() => {
                    setIsModalSetNameOpen(false);
                  }}
                  size="large"
                  icon={<CloseOutlined />}
                />
              </div>
            </div>

            <p>
              Loại data chức năng đã chọn:{" "}
              <span className="text-blue font-medium text-sm">
                {
                  componentFeatureTypesQuery.data?.find(
                    (item) => item.featureType === selectedFeatureType,
                  )?.featureTypeName
                }
              </span>
            </p>

            <Form.Item
              label="Tên chức năng sẽ hiển thị trên app"
              name="name"
              rules={[
                {
                  required: true,
                  message: "Tên chức năng không được để trống",
                },
              ]}
            >
              <Input />
            </Form.Item>
          </div>
          <div className="flex justify-between gap-4 py-4 rounded max-md:max-w-full max-md:flex-wrap pb-4 pl-10 pr-10 bg-[#F7F8FA]">
            <Button
              htmlType="button"
              onClick={() => {
                setIsModalSetNameOpen(false);
                setIsModalSelectFeatureTypeOpen(true);
              }}
              icon={<LeftOutlined />}
              loading={createComponentFeatureMutation.isPending}
            >
              Quay lại
            </Button>
            <Button
              htmlType="submit"
              type={"primary"}
              loading={createComponentFeatureMutation.isPending}
            >
              Thêm và tiến hành cấu hình
            </Button>
          </div>
        </Form>
      </Modal>

      <Modal
        open={selectedComponentFeature !== null}
        footer={null}
        closeIcon={null}
        styles={{ content: { padding: 0 } }}
        width={800}
      >
        <div className="pl-10 pr-10">
          <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
            <h2 className="text-neutral-700 text-2xl font-semibold ">
              Chọn chức năng cần hoàn thành trước
            </h2>
            <div className="pt-5">
              <Button
                type="link"
                onClick={() => {
                  setSelectedComponentFeature(null);
                }}
                size="large"
                icon={<CloseOutlined />}
              />
            </div>
          </div>

          <div className="inline-block mb-3 p-1">
            <Alert
              message={
                <>
                  Hãy chọn chức năng cần hoàn thành trước khi thực hiện chức
                  năng{" "}
                  <span className="text-blue text-sm font-semibold">
                    {selectedComponentFeature?.name}
                  </span>
                </>
              }
            />
          </div>
          <div className="pb-3">
            <Input
              placeholder="Chức năng cần tìm"
              prefix={<SearchOutlined />}
              allowClear
              onChange={(e) => {
                setDependentFilter(e.target.value);
              }}
            />
          </div>

          <Table
            rowKey={(row) => row.id}
            rowSelection={{
              onChange: onSelectChange,
              selectedRowKeys: selectedDependentKeys,
            }}
            pagination={false}
            columns={[
              {
                title: "Tên loại data chức năng",
                dataIndex: "name",
              },
            ]}
            dataSource={(componentFeaturesQuery.data?.entities ?? [])
              .sort((a, b) => a.ordinal - b.ordinal)
              .filter((item) => item.id !== selectedComponentFeature?.id)
              .filter((item) =>
                item.name
                  .toLocaleLowerCase()
                  .includes(dependentFilter.toLocaleLowerCase()),
              )}
          />
        </div>

        <div className="flex justify-end gap-4 py-4 max-md:max-w-full max-md:flex-wrap pb-4 pl-10 pr-10 bg-[#F7F8FA] rounded">
          <Button
            htmlType="button"
            onClick={() => {
              setSelectedComponentFeature(null);
            }}
            loading={createComponentFeatureMutation.isPending}
          >
            Đóng
          </Button>
          <Button
            htmlType="button"
            type={"primary"}
            loading={updateDependentOnComponentFeaturesMutation.isPending}
            onClick={onSubmitDependent}
          >
            Xác nhận {selectedDependentKeys.length} chức năng cần hoàn thành
            trước
          </Button>
        </div>
      </Modal>

      {contextHolder}
    </>
  );
}
