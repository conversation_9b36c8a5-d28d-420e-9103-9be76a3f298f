import React from "react";

const ErrorPage: React.FC = () => {
  const styles = {
    container: {
      display: "flex",
      flexDirection: "column" as const,
      justifyContent: "center",
      alignItems: "center",
      height: "80vh",
      fontSize: "20px",
      color: "#444",
    },
    title: {
      fontSize: "60px",
      fontWeight: "bold",
    },
    subTitle: {
      marginTop: "20px",
      fontSize: "30px",
    },
    backToHome: {
      marginTop: "40px",
      textDecoration: "none",
      fontSize: "25px",
      color: "#007BFF",
      transition: "color 0.3s",
    },
  };

  return (
    <div style={styles.container}>
      <div style={styles.title}>Error</div>
      <div style={styles.subTitle}>Error Page</div>
      <a href={import.meta.env.VITE_BASENAME} style={styles.backToHome}>
        Return to Home Page
      </a>
    </div>
  );
};

export default ErrorPage;
