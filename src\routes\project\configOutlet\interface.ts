import { AbstractEntityInterface } from "@/common/interface";

export interface ProjectBoothInterface extends AbstractEntityInterface {
  name: string;
  description: string;
}

export interface ApiBoothResponseInterface {
  entities: ProjectBoothInterface[];
  count: number;
}

export enum ProjectConfigOutletBoothActionEnum {
  INACTIVE = "INACTIVE",
  ACTIVE = "ACTIVE",
  DELETE = "DELETE",
  EDIT = "EDIT",
}
