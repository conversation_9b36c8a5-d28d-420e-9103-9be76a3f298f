import { AbstractFilterInterface } from "@/common/interface.ts";
import { useApp } from "@/UseApp.tsx";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useCallback } from "react";
import { ComponentFeatureInterface } from "../component/feature/interface.ts";
import { ProjectOutletInterface } from "../outlet/interface.ts";
import { RecordAttendanceInterface } from "../report/types/attendanceClocking/interface.ts";

export const useFeaturesByAttendanceAQuery = (
  projectId?: number,
  attendanceId?: number,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["getAttendanceFeatures", projectId, attendanceId],
    queryFn: () =>
      axiosGet<ComponentFeatureInterface[], unknown>(
        `/tools/projects/${projectId}/attendances/${attendanceId}/features`,
      ),
    enabled: !!projectId && !!attendanceId,
  });
};

export const useAttendanceFeatureDetailQuery = (
  projectId: number,
  attendanceId?: number,
  componentFeatureId?: number,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: [
      "attendanceFeatureDetail",
      projectId,
      attendanceId,
      componentFeatureId,
    ],
    queryFn: () =>
      axiosGet<ComponentFeatureInterface, unknown>(
        `/tools/projects/${projectId}/attendances/${attendanceId}/features/${componentFeatureId}`,
      ),
    enabled: !!projectId && !!attendanceId && !!componentFeatureId,
    // enabled: false
  });
};

export const useAttendanceQuery = (
  projectId: number,
  attendanceId?: number,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["getAttendance", projectId, attendanceId],
    queryFn: () =>
      axiosGet<RecordAttendanceInterface, unknown>(
        `/tools/projects/${projectId}/attendances/${attendanceId}`,
      ),
    enabled: !!projectId && !!attendanceId,
  });
};

export const useAttendancesQuery = (
  projectId: number,
  filter?: AbstractFilterInterface & {
    projectOutletId?: number;
    projectBoothId?: number;
    employeeId?: number;
    leaderId?: number;
    startDate?: Date;
    endDate?: Date;
  },
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["attendances", projectId, filter],
    queryFn: () =>
      axiosGet<
        { entities: RecordAttendanceInterface[]; count: number },
        unknown
      >(`/tools/projects/${projectId}/attendances`, filter),
  });
};

export const useUpdateAttendanceMutation = (
  projectId: number,
  attendanceId: number,
) => {
  const { axiosPut, showNotification } = useApp();

  return useMutation({
    mutationKey: ["updateAttendance", projectId, attendanceId],
    mutationFn: (data: {
      in?: {
        latitude: number;
        longitude: number;
        imageId?: number | null;
        deviceId?: string | null;
        deviceTime: string;
      };
      out?: {
        latitude: number;
        longitude: number;
        imageId?: number;
        deviceId?: string | null;
        deviceTime: string;
      };
    }) =>
      axiosPut<RecordAttendanceInterface, unknown>(
        `/tools/projects/${projectId}/attendances/${attendanceId}`,
        data,
      ),
    onSuccess: () => {
      showNotification({
        type: "success",
        message: "Cập nhật thành công",
      });
    },
  });
};

export const useDeleteAttendanceMutation = (projectId: number) => {
  const { axiosDelete, showNotification } = useApp();

  return useMutation({
    mutationKey: ["deleteAttendance", projectId],
    mutationFn: (attendanceId: number) =>
      axiosDelete(`/tools/projects/${projectId}/attendances/${attendanceId}`),
    onSuccess: () => {
      showNotification({
        type: "success",
        message: "Xóa thành công",
      });
    },
  });
};

export const useAttendanceFeaturesQuery = ({
  projectId,
  projectBoothId,
  enabled,
  roleId,
  at,
}: {
  projectId: number;
  projectBoothId?: number;
  roleId?: number;
  enabled?: boolean;
  at?: string;
}) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["attendanceFeatures", projectId, projectBoothId, roleId, at],
    queryFn: () =>
      axiosGet<ComponentFeatureInterface[], unknown>(
        `/tools/projects/${projectId}/attendances/features`,
        {
          projectBoothId,
          roleId,
          at,
        },
      ),
    enabled,
  });
};

export const useCreateAttendanceMutation = (projectId: number) => {
  const { axiosPost, showNotification } = useApp();

  return useMutation({
    mutationKey: ["createAttendance", projectId],
    mutationFn: (data: {
      projectAgencyId: number;
      projectOutletId: number;
      projectBoothId: number;
      projectEmployeeUserId: number;
      in?: {
        latitude: number;
        longitude: number;
        imageId?: number;
        deviceId?: string | null;
        deviceTime: string;
      };
      out?: {
        latitude: number;
        longitude: number;
        imageId?: number;
        deviceId?: string | null;
        deviceTime: string;
      };
    }) =>
      axiosPost<RecordAttendanceInterface, unknown>(
        `/tools/projects/${projectId}/attendances`,
        data,
      ),
    onSuccess: () => {
      showNotification({
        type: "success",
        message: "Thêm chấm công thành công",
      });
    },
  });
};

export const useGetEmployeeAccessibleOutletsMutation = ({
  projectId,
  id,
}: {
  projectId: number;
  id: number;
}) => {
  const { axiosGet } = useApp();

  return useMutation({
    mutationKey: ["employeeAccessibleOutlets", projectId, id],
    mutationFn: (
      filter?: AbstractFilterInterface & {
        provinceId?: number;
        districtId?: number;
        wardId?: number;
      },
    ) =>
      axiosGet<{ entities: ProjectOutletInterface[]; count: number }, unknown>(
        `/projects/${projectId}/employee-users/${id}/accessible-outlets`,
        filter,
      ),
  });
};
export const useFetchuEmployeeAccessibleOutletsMutation = ({
  projectId,
  filter,
  enabled,
  id,
}: {
  projectId: number;
  filter?: AbstractFilterInterface & {
    provinceId?: number;
    districtId?: number;
    wardId?: number;
  };
  enabled?: boolean;
  id: number;
}) => {
  const getEmployeeAccessibleOutletsMutation =
    useGetEmployeeAccessibleOutletsMutation({
      projectId,
      id,
    });

  const fetchFunction = useCallback(
    async (keyword?: string) => {
      if (!enabled) {
        return [];
      }
      const { entities: projectOutlets } =
        await getEmployeeAccessibleOutletsMutation.mutateAsync({
          keyword,
          take: 10,
          skip: 0,
          ...filter,
        });
      return projectOutlets.map((projectOutlet) => ({
        label: projectOutlet.name,
        value: projectOutlet.id,
      }));
    },
    [enabled, filter, getEmployeeAccessibleOutletsMutation],
  );

  return fetchFunction;
};
