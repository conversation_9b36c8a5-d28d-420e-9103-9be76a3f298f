import { AbstractEntityInterface } from "@/common/interface";
import {
  DistrictInterface,
  ProvinceInterface,
  WardInterface,
} from "@/routes/location/interface.ts";
import { SubChannelInterface } from "@/routes/subChannel/interface";
import { UnitInterface } from "@/routes/unit/UnitPage.tsx";
import { ProjectAgencyChannelInterface } from "@project/interface.ts";
import { ProjectOutletInterface } from "@project/outlet/interface.ts";
import { ProjectProductInterface } from "@project/product/interface.ts";

export interface FeatureSamplingGroupInterface extends AbstractEntityInterface {
  name: string;
  featureSamplingGroupOutletsCount: number;
}

export interface FeatureSamplingInterface extends AbstractEntityInterface {
  numerator: number;
  denominator: number;
  projectProduct: ProjectProductInterface;
  ordinal: number;
  unit: UnitInterface;
}

export interface FeatureSamplingGroupOutletInterface
  extends AbstractEntityInterface {
  featureSamplingGroup: FeatureSamplingGroupInterface;
}

export interface FeatureSamplingOutletAvailabilityInterface
  extends AbstractEntityInterface {
  code: string;
  name: string;
  projectAgencyChannel: ProjectAgencyChannelInterface;
  province: ProvinceInterface;
  district: DistrictInterface;
  ward: WardInterface;
  streetName: string;
  houseNumber: string;
  subChannel: SubChannelInterface;
  featureSamplingGroupOutlets: FeatureSamplingGroupOutletInterface[];
  isAvailable: boolean;
}

export interface SamplingGroupOutletInterface extends AbstractEntityInterface {
  projectOutlet: ProjectOutletInterface;
}
