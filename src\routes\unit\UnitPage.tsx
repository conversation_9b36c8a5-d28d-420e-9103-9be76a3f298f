import { CURD, SELECT_ALL, SELECT_ALL_LABEL } from "@/common/constant";
import { filterOption } from "@/common/helper.ts";
import { AbstractEntityInterface } from "@/common/interface.ts";
import CustomTable from "@/components/CustomTable/CustomTable.tsx";
import FilterComponent from "@/components/FilterComponent.tsx";
import InnerContainer from "@/components/InnerContainer/InnerContainer.tsx";
import { renderTableCell } from "@/components/table-cell";
import { renderTableOptionCell } from "@/components/table-option-cell";
import { useUrlFiltersWithQuery } from "@/hooks/useUrlFiltersWithQuery.ts";
import { useApp } from "@/UseApp.tsx";
import { CloseOutlined } from "@ant-design/icons";
import { Button, Form, Input, Modal, Select } from "antd";
import { ColumnsType } from "antd/es/table";
import _ from "lodash";
import React, { useCallback, useMemo, useState } from "react";
import { ClientInterface } from "../client/interface.ts";
import { useClientsQuery } from "../client/service.ts";
import {
  useCreateUnitMutation,
  useDeleteUnitMutation,
  useUnitsQuery,
  useUpdateUnitMutation,
} from "./service.ts";

export interface UnitInterface extends AbstractEntityInterface {
  name: string;
  description?: string;
  client: ClientInterface;
}

export interface ApiUnitResponseInterface {
  entities: UnitInterface[];
  count: number;
}

const UnitPage: React.FC = () => {
  const { showNotification, openDeleteModal } = useApp();

  const [searchForm] = Form.useForm();
  const [form] = Form.useForm();
  const [isModalAddOrUpdateOpen, setIsModalAddOrUpdateOpen] = useState(false);
  const [formAction, setFormAction] = useState<CURD | null>(null);
  const [modalTitle, setModalTitle] = useState("");
  const [modal, contextHolder] = Modal.useModal();
  const [selectedUnit, setSelectedUnit] = useState<UnitInterface | undefined>(
    undefined,
  );
  const {
    query: {
      data,
      isLoading: unitsQueryIsLoading,
      refetch,
      isFetching,
      isRefetching,
    },
    handleSearch,
    getPaginationProps,
  } = useUrlFiltersWithQuery<UnitInterface>({
    formInstance: searchForm,
    useQueryHook: useUnitsQuery,
    options: {
      defaultFilter: {
        getInActive: true,
      },
    },
  });

  const clientsQuery = useClientsQuery({
    take: 0,
    skip: 0,
  });

  const deleteUnitMutation = useDeleteUnitMutation();
  const createUnitMutation = useCreateUnitMutation();
  const updateUnitMutation = useUpdateUnitMutation();

  const loading = useMemo(
    () =>
      isFetching ||
      isRefetching ||
      unitsQueryIsLoading ||
      deleteUnitMutation.isPending ||
      updateUnitMutation.isPending ||
      createUnitMutation.isPending,
    [
      createUnitMutation.isPending,
      deleteUnitMutation.isPending,
      isFetching,
      isRefetching,
      unitsQueryIsLoading,
      updateUnitMutation.isPending,
    ],
  );

  const handleBtnEditClick = useCallback(
    (record: UnitInterface) => {
      setSelectedUnit(record);
      setIsModalAddOrUpdateOpen(true);
      setFormAction(CURD.UPDATE);
      setModalTitle("Cập nhật đơn vị tính");
      form.setFieldsValue({ ...record, clientId: record.client.id });
    },
    [form],
  );

  const handleBtnInactiveClick = useCallback(
    (record: UnitInterface) => {
      modal.confirm({
        title: `Ngừng hoạt động unit: ${record.name}`,
        content: "Bạn có chắc chắn muốn ngừng hoạt động unit này?",
        okText: "Ngừng hoạt động",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await updateUnitMutation.mutateAsync({
              id: record.id,
              data: { isActive: false },
            });

            showNotification({
              type: "success",
              message: "Ngừng hoạt động unit thành công",
            });
            await refetch();
          } catch (error) {
            console.error(error);

            showNotification({
              type: "error",
              message: "Ngừng hoạt động unit thất bại",
            });
          }
        },
      });
    },
    [modal, updateUnitMutation, showNotification, refetch],
  );

  const handleBtnActiveClick = useCallback(
    (record: UnitInterface) => {
      modal.confirm({
        title: `Kích hoạt unit: ${record.name}`,
        content: "Bạn có chắc chắn muốn kích hoạt unit này?",
        okText: "Kích hoạt",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await updateUnitMutation.mutateAsync({
              id: record.id,
              data: { isActive: true },
            });

            showNotification({
              type: "success",
              message: "Kích hoạt unit thành công",
            });
          } catch (error) {
            console.error(error);
            showNotification({
              type: "error",
              message: "Kích hoạt unit thất bại",
            });
          }
          await refetch();
        },
      });
    },
    [modal, refetch, updateUnitMutation, showNotification],
  );
  const handleBtnDeleteClick = useCallback(
    (record: UnitInterface) => {
      openDeleteModal({
        content: (
          <>
            <p>
              Đơn vị tính sẽ được xóa khỏi hệ thống vĩnh viễn và không thể khôi
              phục
            </p>
            <p>
              Bạn vẫn muốn xóa đơn vị tính{" "}
              <span className={"font-semibold"}>{record.name}</span> khỏi hệ
              thống?
            </p>
          </>
        ),
        deleteText: "Xác nhận xóa",
        loading: deleteUnitMutation.isPending,
        onCancel(): void {},
        onDelete: async () => {
          await deleteUnitMutation.mutateAsync(record.id);
          showNotification({
            type: "success",
            message: "Xóa đơn vị tính thành công",
          });
          await refetch();
        },
        title: `Xóa đơn vị tính`,
        titleError: "Không thể xóa đơn vị tính",
        contentHeader: (
          <>
            Không thể xóa đơn vị tính{" "}
            <span className="font-semibold">{record.name}</span> bởi vì:
          </>
        ),
      });
    },
    [deleteUnitMutation, openDeleteModal, refetch, showNotification],
  );

  const columns: ColumnsType<UnitInterface> = [
    {
      title: "Tên đơn vị tính",
      key: "name",
      dataIndex: "name",
      className: "min-w-[100px]",
      render: renderTableCell,
    },
    {
      title: "Mô tả",
      className: "min-w-[100px]",
      key: "description",
      dataIndex: "description",
      render: renderTableCell,
    },
    {
      title: "Client",
      key: "client",
      dataIndex: "client",
      className: "min-w-[100px]",
      render: renderTableCell,
    },
    {
      key: "isActive",
      title: "Tình trạng",
      className: "min-w-[100px]",
      dataIndex: "isActive",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "isActive");
      },
    },
    {
      key: "createdAt",
      title: "Thời gian tạo",
      className: "min-w-[100px]",
      dataIndex: "createdAt",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "datetime");
      },
    },
    {
      key: "createdByUser.name",
      title: "Người tạo",
      dataIndex: "createdByUser",
      className: "min-w-[100px]",
      render: renderTableCell,
    },
    {
      key: "updatedAt",
      title: "Thời gian cập nhật",
      dataIndex: "updatedAt",
      className: "min-w-[100px]",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "datetime");
      },
    },
    {
      key: "updatedByUser.name",
      title: "Người cập nhật",
      dataIndex: "updatedByUser",
      className: "min-w-[100px]",
      render: renderTableCell,
    },
    {
      key: "actions",
      render: (_, record) => {
        return renderTableOptionCell(
          record,
          handleBtnEditClick,
          handleBtnInactiveClick,
          handleBtnActiveClick,
          handleBtnDeleteClick,
        );
      },
      fixed: "right",
      width: 100,
    },
  ];

  const filterOptions = columns
    .filter(
      (item) =>
        ![
          "isActive",
          "updatedAt",
          "createdAt",
          "actions",
          "createdByUser.name",
          "updatedByUser.name",
          "client",
        ].includes(item.key as string),
    )
    .map((column) => {
      return {
        label: column.title as string,
        value: column.key as string | number,
      };
    });

  filterOptions.unshift({ label: SELECT_ALL_LABEL, value: SELECT_ALL });

  const onCancel = useCallback(() => {
    setIsModalAddOrUpdateOpen(false);
    form.resetFields();
    setSelectedUnit(undefined);
    setModalTitle("");
  }, [form]);

  const handleAddOrUpdateFormSubmit = useCallback(async () => {
    try {
      await form.validateFields();
      const data = form.getFieldsValue();
      const id = form.getFieldValue("id");

      switch (formAction) {
        case CURD.CREATE:
          await createUnitMutation.mutateAsync(data);

          showNotification({
            type: "success",
            message: "Thêm unit thành công",
          });
          break;
        case CURD.UPDATE:
          await updateUnitMutation.mutateAsync({ id, data });

          showNotification({
            type: "success",
            message: "Cập nhật unit thành công",
          });
          break;
        default:
          return;
      }

      onCancel();
      await refetch();

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      const data: { message: { field: string; message: string }[] } =
        error?.response?.data;
      const { message } = data;

      form.setFields([
        { name: message[0].field, errors: [message[0].message] },
      ]);
    }
  }, [
    createUnitMutation,
    form,
    formAction,
    onCancel,
    refetch,
    showNotification,
    updateUnitMutation,
  ]);

  const handleAddButtonClick = () => {
    setIsModalAddOrUpdateOpen(true);
    setFormAction(CURD.CREATE);
    setModalTitle("Thêm đơn vị tính");
  };

  const pagination = useMemo(() => getPaginationProps(), [getPaginationProps]);

  return (
    <div>
      <h2>Đơn vị tính</h2>
      <InnerContainer>
        <FilterComponent
          filterOptions={filterOptions}
          searchHandler={handleSearch}
          handleAddButtonClick={handleAddButtonClick}
          searchForm={searchForm}
          hasClient
          btnLoading={loading}
        />

        <CustomTable<UnitInterface>
          dataSource={data?.entities}
          columns={columns}
          scroll={{
            x: "max-content",
            y: pagination.total ? "80vh" : undefined,
          }}
          pagination={pagination}
          loading={loading}
        />
      </InnerContainer>
      <Modal
        open={isModalAddOrUpdateOpen}
        footer={null}
        closeIcon={null}
        styles={{ content: { padding: 0 } }}
      >
        <div className={"pl-10 pr-10"}>
          <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
            <h2 className="text-neutral-700 text-2xl font-semibold ">
              {modalTitle}
            </h2>
            <div className="pt-5">
              <Button
                type="link"
                onClick={onCancel}
                size="large"
                icon={<CloseOutlined />}
              />
            </div>
          </div>
        </div>

        <Form
          name="unitForm"
          onFinish={handleAddOrUpdateFormSubmit}
          layout={"vertical"}
          form={form}
        >
          <div className={"pl-10 pr-10"}>
            <Form.Item name={"id"} hidden={true}></Form.Item>
            <Form.Item
              name="name"
              label={"Tên đơn vị tính"}
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <Input />
            </Form.Item>

            <Form.Item name="description" label={"Mô tả"}>
              <Input.TextArea />
            </Form.Item>

            {(() => {
              const clientActiveOptions =
                clientsQuery.data?.entities.map((client) => ({
                  label: client.name,
                  value: client.id,
                })) ?? [];

              const { client } = selectedUnit ?? {};
              const selectedOptions = client
                ? [{ label: client?.name, value: client?.id }]
                : [];

              const clientOptions = _.unionBy(
                _.concat(clientActiveOptions, selectedOptions),
                (o) => o.value,
              );

              return (
                <Form.Item
                  name="clientId"
                  label={"Client"}
                  rules={[{ required: true }]}
                >
                  <Select
                    options={clientOptions}
                    showSearch
                    optionFilterProp="children"
                    filterOption={filterOption}
                    disabled={formAction === CURD.UPDATE}
                  />
                </Form.Item>
              );
            })()}
          </div>
          <div className="flex justify-end gap-4 py-4 rounded max-md:max-w-full max-md:flex-wrap pb-4 pl-10 pr-10 bg-[#F7F8FA]">
            <Button htmlType="button" onClick={onCancel}>
              Đóng
            </Button>
            <Button htmlType="submit" type={"primary"} loading={loading}>
              {formAction === CURD.CREATE ? "Thêm" : "Cập nhật"}
            </Button>
          </div>
        </Form>
      </Modal>
      {contextHolder}
    </div>
  );
};

export default UnitPage;
