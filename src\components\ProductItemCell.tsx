import { Space } from "antd";
import { getImageVariants } from "../common/image.helper.ts";
import ImageItem from "./ImageItem.tsx";
import renderStatusOnTopCell from "./renderStatusOnTopCell.tsx";

interface ProductItemCellProps {
  variants: string[];
  name: string;
  isAvailable?: boolean;
  isActive?: boolean;
  isAvailableFalseText?: string;
  status?: string;
  statusColor?: string;
  statusBg?: string;
}

const ProductItemCell = ({
  variants,
  name,
  isAvailable,
  isActive,
  isAvailableFalseText = "Đã thêm vào dự án",
  status,
  statusBg,
  statusColor,
}: ProductItemCellProps) => {
  return (
    <Space size={0}>
      <ImageItem
        thumbnail={getImageVariants(variants, "thumbnail")}
        preview={getImageVariants(variants, "public")}
      />
      <div className={"align-middle ml-4"}>
        {isAvailable !== undefined &&
          !isAvailable &&
          renderStatusOnTopCell(isAvailableFalseText, "#008916", "#E5F5E7")}

        {status !== undefined &&
          renderStatusOnTopCell(status, statusColor, statusBg)}

        {isActive !== undefined &&
          !isActive &&
          renderStatusOnTopCell("Ngừng hoạt động", "#DF3C3C", "#FFEEEE")}

        <p className={"m-0 p-0"}>{name}</p>
      </div>
    </Space>
  );
};

export default ProductItemCell;
