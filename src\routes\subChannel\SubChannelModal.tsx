import { useApp } from "@/UseApp.tsx";
import { CURD } from "@/common/constant.ts";
import { filterOption } from "@/common/helper.ts";
import { CloseOutlined } from "@ant-design/icons";
import { Button, Form, FormInstance, Input, Modal, Select } from "antd";
import { useCallback, useState } from "react";
import { useChannelsQuery } from "../channel/services.ts";
import { useClientsQuery } from "../client/service.ts";
import {
  useCreateSubChannelMutation,
  useUpdateSubChannelMutation,
} from "./service.ts";

interface SubChannelModalInterface {
  isOpen: boolean;
  modalTitle: string;
  setIsOpen: (isOpen: boolean) => void;
  form: FormInstance;
  disabledSelectChannel?: boolean;
  formAction: CURD | null;
  callback?: () => void;
  setModalTitle?: (modalTitle: string) => void;
}

const SubChannelModal = (props: SubChannelModalInterface) => {
  const {
    isOpen,
    modalTitle,
    setIsOpen,
    form,
    disabledSelectChannel,
    formAction,
    callback,
    setModalTitle,
  } = props;

  const { setLoading, loading, showNotification } = useApp();

  const [selectedClientId, setSelectedClientId] = useState<number | undefined>(
    undefined,
  );

  const clientsQuery = useClientsQuery({
    take: 0,
    skip: 0,
    getInActive: true,
  });
  const channelsQuery = useChannelsQuery({
    take: 0,
    skip: 0,
    clientId: selectedClientId ?? form.getFieldValue("clientId"),
    getInActive: true,
  });

  const createSubChannelMutation = useCreateSubChannelMutation();
  const updateSubChannelMutation = useUpdateSubChannelMutation();

  const handleFormSubmit = useCallback(async () => {
    try {
      await form.validateFields();
      const data = form.getFieldsValue();
      const id = form.getFieldValue("id");
      setLoading(true);

      switch (formAction) {
        case CURD.CREATE:
          await createSubChannelMutation.mutateAsync(data);

          showNotification({
            type: "success",
            message: "Thêm nhóm thành công",
          });
          break;
        case CURD.UPDATE:
          await updateSubChannelMutation.mutateAsync({ id, ...data });

          showNotification({
            type: "success",
            message: "Cập nhật nhóm thành công",
          });
          break;
        default:
          return;
      }

      form.resetFields();
      setIsOpen(false);
      if (setModalTitle) setModalTitle("");
      if (callback) callback();

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      const data: { message: { field: string; message: string }[] } =
        error?.response?.data;
      const { message } = data;
      message.forEach((item: { field: string; message: string }) => {
        form.setFields([{ name: item.field, errors: [item.message] }]);
      });
    } finally {
      setLoading(false);
    }
  }, [
    callback,
    createSubChannelMutation,
    form,
    formAction,
    setIsOpen,
    setLoading,
    setModalTitle,
    showNotification,
    updateSubChannelMutation,
  ]);

  return (
    <Modal
      open={isOpen}
      footer={null}
      closeIcon={null}
      styles={{ content: { padding: 0 } }}
    >
      <div className={"pl-10 pr-10"}>
        <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
          <h2 className="text-neutral-700 text-2xl font-semibold ">
            {modalTitle}
          </h2>
          <div className="pt-5">
            <Button
              type="link"
              onClick={() => {
                setIsOpen(false);
                form.resetFields();
              }}
              size="large"
              icon={<CloseOutlined />}
            />
          </div>
        </div>
      </div>

      <Form
        name="subchannelForm"
        onFinish={handleFormSubmit}
        layout={"vertical"}
        form={form}
      >
        <div className={"pl-10 pr-10"}>
          <Form.Item name={"id"} hidden={true}></Form.Item>
          <Form.Item
            name="name"
            label={"Tên nhóm"}
            rules={[
              {
                required: true,
                message: "Tên nhóm thực hiện không được để trống",
              },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="code"
            label={"Mã nhóm"}
            rules={[{ required: true, message: "Mã nhóm không được để trống" }]}
          >
            <Input />
          </Form.Item>

          {(() => {
            const clientOptions = clientsQuery.data?.entities
              .filter(
                (client) =>
                  client.isActive ||
                  client.id === selectedClientId ||
                  client.id === form.getFieldValue("clientId"),
              )
              .map((client) => ({
                label: client.name,
                value: client.id,
              }));
            return (
              <Form.Item
                name="clientId"
                label={"Client"}
                rules={[{ required: true }]}
              >
                <Select
                  options={clientOptions}
                  disabled={formAction === CURD.UPDATE || disabledSelectChannel}
                  onSelect={(value) => setSelectedClientId(value)}
                  onChange={() => form.resetFields(["channelId"])}
                  optionFilterProp="children"
                  filterOption={filterOption}
                  showSearch
                />
              </Form.Item>
            );
          })()}
          <Form.Item noStyle dependencies={["clientId"]}>
            {() => {
              const channelOptions = !form.getFieldValue("clientId")
                ? []
                : channelsQuery.data?.entities
                    .filter(
                      (channel) =>
                        channel.isActive ||
                        channel.id === form.getFieldValue("channelId"),
                    )
                    .map((channel) => ({
                      label: channel.name,
                      value: channel.id,
                    }));

              return (
                <Form.Item
                  name="channelId"
                  label="Kênh thực hiện"
                  rules={[
                    {
                      required: true,
                      message: "Kênh thực hiện không được để trống",
                    },
                  ]}
                >
                  <Select
                    disabled={
                      formAction === CURD.UPDATE || disabledSelectChannel
                    }
                    showSearch
                    placeholder="Chọn kênh thực hiện"
                    optionFilterProp="children"
                    filterOption={filterOption}
                    options={channelOptions}
                  />
                </Form.Item>
              );
            }}
          </Form.Item>
        </div>
        <div className="flex justify-end gap-4 py-4 rounded max-md:max-w-full max-md:flex-wrap pb-4 pl-10 pr-10 bg-[#F7F8FA]">
          <Button
            htmlType="button"
            onClick={() => {
              setIsOpen(false);
              form.resetFields();
            }}
          >
            Đóng
          </Button>
          <Button htmlType="submit" type={"primary"} loading={loading}>
            {formAction === CURD.CREATE ? "Thêm" : "Cập nhật"}
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

export default SubChannelModal;
