import { useApp } from "@/UseApp";
import { useQuery } from "@tanstack/react-query";
import { ChartDataInterface, StatisticsTypeEnum } from "./interface";

export const useProvinceStatisticsQuery = (
  projectId: number,
  type: StatisticsTypeEnum,
  filter?: { provinceIds?: number[]; channelIds?: number[] },
  enabled?: boolean,
) => {
  const { axiosGet } = useApp();

  const { provinceIds, channelIds } = filter ?? {};

  return useQuery({
    queryKey: ["provinceStatistics", projectId, type, provinceIds, channelIds],
    queryFn: () =>
      axiosGet<
        ChartDataInterface[],
        {
          type: StatisticsTypeEnum;
          provinceIds?: number[];
          channelIds?: number[];
        }
      >(`/projects/${projectId}/statistics/provinces`, {
        type,
        provinceIds,
        channelIds,
      }),
    enabled,
  });
};

export const useChannelStatisticsQuery = (
  projectId: number,
  type: StatisticsTypeEnum,
  filter?: { provinceIds?: number[]; channelIds?: number[] },
  enabled?: boolean,
) => {
  const { axiosGet } = useApp();

  const { provinceIds, channelIds } = filter ?? {};

  return useQuery({
    queryKey: ["channelStatistics", projectId, type, provinceIds, channelIds],
    queryFn: () =>
      axiosGet<
        ChartDataInterface[],
        {
          type: StatisticsTypeEnum;
          provinceIds?: number[];
          channelIds?: number[];
        }
      >(`/projects/${projectId}/statistics/channels`, {
        type,
        provinceIds,
        channelIds,
      }),
    enabled,
  });
};

export const useProjectStatisticsQuery = (
  projectId: number,
  type: StatisticsTypeEnum,
  enabled?: boolean,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["projectStatistics", projectId, type],
    queryFn: () =>
      axiosGet<
        ChartDataInterface[],
        {
          type: StatisticsTypeEnum;
        }
      >(`/projects/${projectId}/statistics`, {
        type,
      }),
    enabled,
  });
};
