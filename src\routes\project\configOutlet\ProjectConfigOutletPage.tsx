import InnerContainer from "@/components/InnerContainer/InnerContainer.tsx";
import { Tabs } from "antd";
import { Tab } from "rc-tabs/lib/interface";
import { useParams } from "react-router-dom";
import BoothTab from "./tabs/BoothTab.tsx";

export default function ProjectConfigOutletPage() {
  const projectId = parseInt(useParams().id ?? "0");
  const tabItems: Tab[] = [
    {
      key: "BOOTH",
      label: "Cấu hình booth bên trong outlet",
      children: <BoothTab projectId={projectId} />,
    },
    {
      key: "OUTLET_PROPERTIES",
      label: "Cấu hình thuộc tính outlet",
      children: <></>,
      disabled: true,
    },
  ];
  return (
    <div>
      <h2>Cấu hình outlet</h2>
      <InnerContainer>
        <Tabs defaultActiveKey="1" type="card" items={tabItems} />
      </InnerContainer>
    </div>
  );
}
