import { AbstractFilterInterface } from "@/common/interface";
import { useApp } from "@/UseApp";
import { useMutation, useQuery } from "@tanstack/react-query";
import { QuantityEntityInterface } from "../multipleEntitiesQuantityCapturing/interface";
import {
  FeatureNumericAttributeInterface,
  FeatureNumericAttributeTypeEnum,
  FeatureNumericSheetInterface,
  FeatureNumericSheetOutletAvailableInterface,
  FeatureNumericSheetOutletInterface,
  NumericSheetNumericInterface,
} from "./interface";

export const useCreateNumericSheetMutation = (featureId: number) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createNumericSheet", featureId],
    mutationFn: (data: { name: string }) =>
      axiosPost(`/features/${featureId}/numeric-sheets`, data),
  });
};

export const useNumericSheetsQuery = (
  featureId: number,
  filter?: AbstractFilterInterface & object,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["numericSheets", featureId, filter],
    queryFn: () =>
      axiosGet<
        { entities: FeatureNumericSheetInterface[]; count: number },
        unknown
      >(`/features/${featureId}/numeric-sheets`, filter),
  });
};

export const useNumericSheetNumericsQuery = (
  featureId: number,
  numericSheetId: number,
  filter?: AbstractFilterInterface & object,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["numericSheetNumerics", featureId, numericSheetId, filter],
    queryFn: () =>
      axiosGet<
        { count: number; entities: NumericSheetNumericInterface[] },
        unknown
      >(
        `/features/${featureId}/numeric-sheets/${numericSheetId}/numerics`,
        filter,
      ),
  });
};

export const useArrangeNumericSheetNumericMutation = (
  featureId: number,
  numericSheetId: number,
) => {
  const { axiosPut } = useApp();

  return useMutation({
    mutationKey: ["arrangeNumericSheetNumeric", featureId, numericSheetId],
    mutationFn: ({ overId, id }: { overId: number; id: number }) =>
      axiosPut(
        `/features/${featureId}/numeric-sheets/${numericSheetId}/numerics/${id}/arrangement`,
        {
          overFeatureNumericId: overId,
        },
      ),
  });
};

export const useNumericSheetNumericAvailablesQuery = (
  featureId: number,
  numericSheetId: number,
  filter?: AbstractFilterInterface & object,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: [
      "numericSheetNumericAvailables",
      featureId,
      numericSheetId,
      filter,
    ],
    queryFn: () =>
      axiosGet<{ count: number; entities: QuantityEntityInterface[] }, unknown>(
        `/features/${featureId}/numeric-sheets/${numericSheetId}/numerics/availables`,
        filter,
      ),
  });
};

export const useCreateNumericSheetNumericMutation = (
  featureId: number,
  numericSheetId: number,
) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createNumericSheetNumeric", featureId, numericSheetId],
    mutationFn: (
      data: { projectProductId: number | null; projectItemId: number | null }[],
    ) =>
      axiosPost(
        `/features/${featureId}/numeric-sheets/${numericSheetId}/numerics`,
        data,
      ),
  });
};

export const useUpdateNumericSheetNumericMutation = (
  featureId: number,
  numericSheetId: number,
) => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["updateNumericSheetNumeric", featureId, numericSheetId],
    mutationFn: (data: { id: number; isActive?: boolean }) =>
      axiosPatch(
        `/features/${featureId}/numeric-sheets/${numericSheetId}/numerics/${data.id}`,
        data,
      ),
  });
};

export const useDeleteNumericSheetNumericMutation = (
  featureId: number,
  numericSheetId: number,
) => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deleteNumericSheetNumeric", featureId, numericSheetId],
    mutationFn: (id: number) =>
      axiosDelete(
        `/features/${featureId}/numeric-sheets/${numericSheetId}/numerics/${id}`,
      ),
  });
};

export const useUpdateNumericSheetMutation = (featureId: number) => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["updateNumericSheet", featureId],
    mutationFn: (data: { id: number; name?: string; isActive?: boolean }) =>
      axiosPatch(`/features/${featureId}/numeric-sheets/${data.id}`, data),
  });
};

export const useNumericAttributesQuery = (
  featureId: number,
  filter?: AbstractFilterInterface & object,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["numericAttributes", featureId, filter],
    queryFn: () =>
      axiosGet<
        {
          count: number;
          entities: FeatureNumericAttributeInterface[];
        },
        unknown
      >(`/features/${featureId}/numeric-attributes`, filter),
  });
};

export const useCreateNumericAttributeMutation = (featureId: number) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createNumericAttribute", featureId],
    mutationFn: (data: {
      name: string;
      type: FeatureNumericAttributeTypeEnum;
      minimum: number;
      maximum: number;
    }) => axiosPost(`/features/${featureId}/numeric-attributes`, data),
  });
};

export const useUpdateNumericAttributeMutation = (featureId: number) => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["updateNumericAttribute", featureId],
    mutationFn: (data: {
      id: number;
      name?: string;
      type?: FeatureNumericAttributeTypeEnum;
      minimum?: number;
      maximum?: number;
      isActive?: boolean;
    }) =>
      axiosPatch(`/features/${featureId}/numeric-attributes/${data.id}`, data),
  });
};

export const useDeleteNumericAttributeMutation = (featureId: number) => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deleteNumericAttribute", featureId],
    mutationFn: (id: number) =>
      axiosDelete(`/features/${featureId}/numeric-attributes/${id}`),
  });
};

export const useArrangeNumericAttributeMutation = (featureId: number) => {
  const { axiosPut } = useApp();

  return useMutation({
    mutationKey: ["arrangeNumericAttribute", featureId],
    mutationFn: ({ id, overId }: { id: number; overId: number }) =>
      axiosPut(`/features/${featureId}/numeric-attributes/${id}/arrangement`, {
        overFeatureNumericAttributeId: overId,
      }),
  });
};

export const useNumericSheetOutletAvailablesQuery = (
  featureId: number,
  filter?: AbstractFilterInterface & object,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["numericSheetOutletAvailables", featureId, filter],
    queryFn: () =>
      axiosGet<
        {
          count: number;
          entities: FeatureNumericSheetOutletAvailableInterface[];
        },
        unknown
      >(`/features/${featureId}/numeric-sheets/outlet-availables`, filter),
  });
};

export const useAddNumericSheetOutletMutation = (
  featureId: number,
  id: number,
) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["addNumericSheetOutlet", featureId, id],
    mutationFn: (data: { projectOutletIds: number[] }) =>
      axiosPost(`/features/${featureId}/numeric-sheets/${id}/outlets`, data),
  });
};

export const useNumericSheetOutletsQuery = (
  featureId: number,
  id: number,
  filter?: AbstractFilterInterface & object,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["numericSheetOutlets", featureId, id, filter],
    queryFn: () =>
      axiosGet<
        {
          count: number;
          entities: FeatureNumericSheetOutletInterface[];
        },
        unknown
      >(`/features/${featureId}/numeric-sheets/${id}/outlets`, filter),
  });
};

export const useDeleteNumericSheetOutletMutation = (
  featureId: number,
  id: number,
) => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deleteNumericSheetOutlet", featureId, id],
    mutationFn: ({
      featureNumericSheetOutletIds,
    }: {
      featureNumericSheetOutletIds: number[];
    }) =>
      axiosDelete(`/features/${featureId}/numeric-sheets/${id}/outlets`, {
        featureNumericSheetOutletIds,
      }),
  });
};
