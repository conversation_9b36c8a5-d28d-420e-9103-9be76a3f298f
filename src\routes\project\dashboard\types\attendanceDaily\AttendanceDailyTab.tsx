import { getDatesInRange } from "@/common/helper";
import ChartContanier from "@/routes/project/chart/ChartContanier";
import { Form } from "antd";
import dayjs from "dayjs";
import _ from "lodash";
import { useCallback, useMemo, useState } from "react";
import ReactApexChart from "react-apexcharts";
import DashboardFilterZone from "../../DashboardFilterZone";
import { DashboardFilterInterface } from "../../interface";
import { useAttendanceDailyFrequencyQuery } from "./service";

interface AttendanceDailyTabProps {
  projectId: number;
  dashboardId: number;
}
const AttendanceDailyTab = ({
  projectId,
  dashboardId,
}: AttendanceDailyTabProps) => {
  const [form] = Form.useForm();
  const [filter, setFilter] = useState<DashboardFilterInterface>({});

  const attendanceDailyFrequencyQuery = useAttendanceDailyFrequencyQuery(
    projectId,
    dashboardId,
    filter,
  );

  const handleApply = useCallback(() => {
    const values = form.getFieldsValue();

    const {
      date,
      projectRegionIds,
      projectOutletIds,
      channelIds,
      provinceIds,
      leaderIds,
    } = values;

    const filterValue = {
      startDate: date?.[0] ? date[0].format("YYYY-MM-DD") : null,
      endDate: date?.[1] ? date[1].format("YYYY-MM-DD") : null,
      projectRegionIds,
      projectOutletIds,
      channelIds,
      provinceIds,
      leaderIds: leaderIds?.map((item: { value: number }) => item.value),
    };

    if (_.isEqual(filter, filterValue)) {
      attendanceDailyFrequencyQuery.refetch();
    } else {
      setFilter(filterValue);
    }
  }, [attendanceDailyFrequencyQuery, filter, form]);

  const dates = useMemo(() => {
    return getDatesInRange(
      dayjs(filter.startDate).toDate(),
      dayjs(filter.endDate).toDate(),
    );
  }, [filter.endDate, filter.startDate]);

  const labels = useMemo(() => {
    return dates.map((date) => dayjs(date).format("DD/MM"));
  }, [dates]);

  const planData = useMemo(() => {
    return dates.map((date) => {
      const value =
        attendanceDailyFrequencyQuery.data?.find(
          (item) => item.date === dayjs(date).format("YYYY-MM-DD"),
        )?.totalKpi ?? 0;

      return value;
    });
  }, [attendanceDailyFrequencyQuery.data, dates]);

  const actualDate = useMemo(() => {
    return dates.map((date) => {
      const value =
        attendanceDailyFrequencyQuery.data?.find(
          (item) => item.date === dayjs(date).format("YYYY-MM-DD"),
        )?.totalActual ?? 0;

      return value;
    });
  }, [attendanceDailyFrequencyQuery.data, dates]);

  const achieveData = useMemo(() => {
    return dates.map((_, index) => {
      const plan = planData[index];
      const actual = actualDate[index];

      return Math.round((actual / (plan ? plan : 1)) * 100);
    });
  }, [actualDate, dates, planData]);

  const series = useMemo(
    () => [
      {
        name: "Users have work schedules",
        type: "bar",
        data: planData,
        color: "#4187F6",
      },
      {
        name: "Users actual",
        type: "bar",
        data: actualDate,
        color: "#28A745",
      },
      {
        name: "Achive %",
        type: "line",
        data: achieveData,
        color: "#BE0712",
      },
    ],
    [achieveData, actualDate, planData],
  );

  const options = useMemo(() => {
    return {
      chart: {
        toolbar: {
          show: false,
        },
        type: "line" as const,
        zoom: {
          enabled: false,
        },
      },

      stroke: {
        width: [0, 0, 4],
        curve: "smooth" as const,
      },
      title: {
        text: "PA attendance by day",
        style: {
          fontSize: "16px",
          fontWeight: "bold",
        },
      },
      subtitle: {
        text: "Lần chấm công của PA",
        style: {
          fontSize: "12px",
          fontWeight: "normal",
        },
      },
      dataLabels: {
        enabled: true,
        formatter: function (val: number, opts: { seriesIndex: number }) {
          if (opts.seriesIndex === 2) return `${val}%`;
          return val;
        },
      },
      labels: labels,
      yaxis: [
        {
          seriesName: "Users have work schedules",
          show: true,
          decimalsInFloat: 0,
        },
        {
          seriesName: "Users have work schedules",
          show: false,
          decimalsInFloat: 0,
        },
        {
          seriesName: "Achive %",
          show: true,
          opposite: true,
          title: {
            text: "%",
            style: {
              fontSize: "12px",
              fontWeight: "bold",
            },
          },
          decimalsInFloat: 0,
          min: 0,
          max: Math.max(...achieveData) + 20,
        },
      ],
    };
  }, [achieveData, labels]);

  return (
    <>
      <DashboardFilterZone
        handleApply={handleApply}
        fields={["date", "region", "province", "chain", "leader", "outlet"]}
        form={form}
        projectId={projectId}
        setFilter={setFilter}
        key={"attendanceDaily"}
      />

      <ChartContanier>
        <ReactApexChart
          options={options}
          series={series}
          height={500}
          type="line"
        />
      </ChartContanier>
    </>
  );
};

export default AttendanceDailyTab;
