import { CURD, SELECT_ALL, SELECT_ALL_LABEL } from "@/common/constant";
import CustomTable from "@/components/CustomTable/CustomTable.tsx";
import FilterComponent from "@/components/FilterComponent.tsx";
import InnerContainer from "@/components/InnerContainer/InnerContainer.tsx";
import { renderTableCell } from "@/components/table-cell";
import { renderTableOptionCell } from "@/components/table-option-cell";
import { useUrlFiltersWithQuery } from "@/hooks/useUrlFiltersWithQuery.ts";
import { useApp } from "@/UseApp.tsx";
import { CloseOutlined } from "@ant-design/icons";
import { Button, Form, Input, Modal } from "antd";
import { ColumnsType } from "antd/es/table";
import React, { useCallback, useMemo, useState } from "react";
import { AgencyInterface } from "./interface.ts";
import {
  useAgenciesQuery,
  useCreateAgencyMutation,
  useDeleteAgencyMutation,
  useUpdateAgencyMutation,
} from "./services.ts";

const AgencyPage: React.FC = () => {
  const { showNotification, openDeleteModal } = useApp();

  const [searchForm] = Form.useForm();
  const [form] = Form.useForm();
  const [isModalAddOrUpdateOpen, setIsModalAddOrUpdateOpen] = useState(false);
  const [formAction, setFormAction] = useState<CURD | null>(null);
  const [modalTitle, setModalTitle] = useState("");
  const [modal, contextHolder] = Modal.useModal();
  const {
    query: { data, refetch, isFetching, isRefetching },
    handleSearch,
    getPaginationProps,
  } = useUrlFiltersWithQuery<AgencyInterface>({
    formInstance: searchForm,
    useQueryHook: useAgenciesQuery,
  });

  const createAgencyMutation = useCreateAgencyMutation();
  const updateAgencyMutation = useUpdateAgencyMutation();
  const deleteAgencyMutation = useDeleteAgencyMutation();

  const handleBtnEditClick = useCallback(
    (record: AgencyInterface) => {
      setIsModalAddOrUpdateOpen(true);
      setFormAction(CURD.UPDATE);
      setModalTitle("Cập nhật agency");
      form.setFieldsValue(record);
    },
    [form],
  );

  const handleBtnInactiveClick = useCallback(
    (record: AgencyInterface) => {
      modal.confirm({
        title: `Ngừng hoạt động agency: ${record.name}`,
        content: "Bạn có chắc chắn muốn ngừng hoạt động agency này?",
        okText: "Ngừng hoạt động",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await updateAgencyMutation.mutateAsync({
              id: record.id,
              isActive: false,
            });

            showNotification({
              type: "success",
              message: "Ngừng hoạt động agency thành công",
            });

            refetch();
          } catch (error) {
            console.error(error);
            showNotification({
              type: "error",
              message: "Ngừng hoạt động agency thất bại",
            });
          }
        },
      });
    },
    [modal, refetch, showNotification, updateAgencyMutation],
  );

  const handleBtnActiveClick = useCallback(
    (record: AgencyInterface) => {
      modal.confirm({
        title: `Kích hoạt agency: ${record.name}`,
        content: "Bạn có chắc chắn muốn kích hoạt agency này?",
        okText: "Kích hoạt",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await updateAgencyMutation.mutateAsync({
              id: record.id,
              isActive: true,
            });
            showNotification({
              type: "success",
              message: "Kích hoạt agency thành công",
            });
          } catch (error) {
            console.error(error);
            showNotification({
              type: "error",
              message: "Kích hoạt agency thất bại",
            });
          }
          refetch();
        },
      });
    },
    [modal, refetch, showNotification, updateAgencyMutation],
  );

  const handleDeleteClick = useCallback(
    (record: AgencyInterface) => {
      openDeleteModal({
        content: (
          <>
            <p>
              Agency sẽ được xóa khỏi hệ thống vĩnh viễn và không thể khôi phục
            </p>
            <p>
              Bạn vẫn muốn xóa agency{" "}
              <span className={"font-semibold"}>{record.name}</span> khỏi hệ
              thống?
            </p>
          </>
        ),
        deleteText: "Xác nhận xóa",
        loading: deleteAgencyMutation.isPending,
        onCancel(): void {},
        onDelete: async () => {
          await deleteAgencyMutation.mutateAsync(record.id);
          showNotification({
            type: "success",
            message: "Xóa agency thành công",
          });

          refetch();
        },
        title: `Xóa agency`,
        titleError: "Không thể xóa agency",
        contentHeader: (
          <>
            Không thể xóa agency{" "}
            <span className="font-semibold">{record.name}</span> bởi vì:
          </>
        ),
      });
    },
    [openDeleteModal, deleteAgencyMutation, showNotification, refetch],
  );

  const columns: ColumnsType<AgencyInterface> = [
    {
      title: "Tên viết tắt",
      key: "name",
      dataIndex: "name",
      className: "min-w-[100px]",
      render: renderTableCell,
    },
    {
      title: "Tên đầy đủ",
      key: "legalName",
      dataIndex: "legalName",
      className: "min-w-[100px]",
      render: renderTableCell,
    },
    {
      title: "Email",
      key: "email",
      dataIndex: "email",
      className: "min-w-[100px]",
      render: renderTableCell,
    },
    {
      title: "Số điện thoại",
      key: "phone",
      dataIndex: "phone",
      className: "min-w-[100px]",
      render: renderTableCell,
    },
    {
      key: "isActive",
      title: "Tình trạng",
      className: "min-w-[100px]",
      dataIndex: "isActive",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "isActive");
      },
    },
    {
      key: "createdAt",
      title: "Thời gian tạo",
      className: "min-w-[100px]",
      dataIndex: "createdAt",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "datetime");
      },
    },
    {
      key: "createdByUser.name",
      title: "Người tạo",
      dataIndex: "createdByUser",
      className: "min-w-[100px]",
      render: renderTableCell,
    },
    {
      key: "updatedAt",
      title: "Thời gian cập nhật",
      dataIndex: "updatedAt",
      className: "min-w-[100px]",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "datetime");
      },
    },
    {
      key: "updatedByUser.name",
      title: "Người cập nhật",
      dataIndex: "updatedByUser",
      className: "min-w-[100px]",
      render: renderTableCell,
    },
    {
      key: "actions",
      render: (_, record) => {
        return renderTableOptionCell(
          record,
          handleBtnEditClick,
          handleBtnInactiveClick,
          handleBtnActiveClick,
          handleDeleteClick,
        );
      },
      fixed: "right",
      align: "center",
      width: 100,
    },
  ];

  const filterOptions = columns
    .filter(
      (item) =>
        !["isActive", "updatedAt", "createdAt", "actions"].includes(
          item.key as string,
        ),
    )
    .map((column) => {
      return {
        label: column.title as string,
        value: column.key as string | number,
      };
    });

  filterOptions.unshift({ label: SELECT_ALL_LABEL, value: SELECT_ALL });

  const handleAddOrUpdateFormSubmit = useCallback(async () => {
    try {
      await form.validateFields();
      const data = form.getFieldsValue();
      const id = form.getFieldValue("id");

      switch (formAction) {
        case CURD.CREATE:
          await createAgencyMutation.mutateAsync(data);

          showNotification({
            type: "success",
            message: "Thêm agency thành công",
          });
          break;
        case CURD.UPDATE:
          await updateAgencyMutation.mutateAsync({ id, ...data });

          showNotification({
            type: "success",
            message: "Cập nhật agency thành công",
          });
          break;
        default:
          return;
      }

      form.resetFields();
      setIsModalAddOrUpdateOpen(false);
      setModalTitle("");
      refetch();

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      const data: { message: { field: string; message: string }[] } =
        error?.response?.data;
      const { message } = data;
      form.setFields([
        { name: message[0].field, errors: [message[0].message] },
      ]);
    }
  }, [
    form,
    formAction,
    refetch,
    createAgencyMutation,
    showNotification,
    updateAgencyMutation,
  ]);

  const handleAddButtonClick = () => {
    setIsModalAddOrUpdateOpen(true);
    setFormAction(CURD.CREATE);
    setModalTitle("Thêm agency");
  };
  const pagination = useMemo(() => getPaginationProps(), [getPaginationProps]);

  const loading = useMemo(
    () =>
      isFetching ||
      isRefetching ||
      createAgencyMutation.isPending ||
      updateAgencyMutation.isPending ||
      deleteAgencyMutation.isPending,
    [
      isFetching,
      isRefetching,
      createAgencyMutation.isPending,
      updateAgencyMutation.isPending,
      deleteAgencyMutation.isPending,
    ],
  );

  return (
    <div>
      <h2>Agency</h2>
      <InnerContainer>
        <FilterComponent
          filterOptions={filterOptions}
          searchHandler={handleSearch}
          handleAddButtonClick={handleAddButtonClick}
          searchForm={searchForm}
          btnLoading={loading}
          className="mb-4"
        />

        <CustomTable<AgencyInterface>
          dataSource={data?.entities}
          columns={columns}
          scroll={{
            x: "max-content",
            y: pagination.total ? "80vh" : undefined,
          }}
          pagination={pagination}
          loading={loading}
        />
      </InnerContainer>

      <Modal
        open={isModalAddOrUpdateOpen}
        footer={null}
        closeIcon={null}
        styles={{ content: { padding: 0 } }}
      >
        <div className={"pl-10 pr-10"}>
          <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
            <h2 className="text-neutral-700 text-2xl font-semibold ">
              {modalTitle}
            </h2>
            <div className="pt-5">
              <Button
                type="link"
                onClick={() => {
                  setIsModalAddOrUpdateOpen(false);
                  form.resetFields();
                }}
                size="large"
                icon={<CloseOutlined />}
              />
            </div>
          </div>
        </div>

        <Form
          name="agencyForm"
          onFinish={handleAddOrUpdateFormSubmit}
          layout={"vertical"}
          form={form}
        >
          <div className={"pl-10 pr-10"}>
            <Form.Item name={"id"} hidden={true}></Form.Item>
            <Form.Item
              rules={[{ required: true }]}
              name="name"
              label={"Tên Agency viết tắt"}
            >
              <Input />
            </Form.Item>
            <Form.Item name="legalName" label={"Tên Agency đầy đủ"}>
              <Input />
            </Form.Item>
            <Form.Item name="email" label={"Email"} rules={[{ type: "email" }]}>
              <Input />
            </Form.Item>
            <Form.Item
              name="phone"
              label={"Số điện thoại"}
              rules={[
                {
                  pattern: new RegExp(/^0\d+$/),
                  message: "Vui lòng nhập đúng định dạng số điện thoại",
                },
                {
                  len: 10,
                },
              ]}
            >
              <Input />
            </Form.Item>
          </div>
          <div className="flex justify-end gap-4 py-4 rounded max-md:max-w-full max-md:flex-wrap pb-4 pl-10 pr-10 bg-[#F7F8FA]">
            <Button
              htmlType="button"
              onClick={() => {
                setIsModalAddOrUpdateOpen(false);
                form.resetFields();
              }}
            >
              Đóng
            </Button>
            <Button htmlType="submit" type={"primary"} loading={loading}>
              {formAction === CURD.CREATE ? "Thêm mới" : "Cập nhật"}
            </Button>
          </div>
        </Form>
      </Modal>
      {contextHolder}
    </div>
  );
};

export default AgencyPage;
