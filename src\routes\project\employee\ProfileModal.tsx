import {
  CloseOutlined,
  DeleteOutlined,
  LoadingOutlined,
  PlusOutlined,
} from "@ant-design/icons";

import { DATE_FORMAT } from "@/common/constant";
import { filterOption } from "@/common/helper";
import { uploadImage } from "@/common/upload-image.helper.ts";
import FormImageUploadComponent from "@/components/formImageUploadComponent/FormImageUploadComponent.tsx";
import { useFormPhotosStore } from "@/components/formImageUploadComponent/state.ts";
import { useApp } from "@/UseApp.tsx";
import {
  useDistrictsQuery,
  useProvincesQuery,
  useWardsQuery,
} from "@location/service";
import {
  Button,
  Col,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Menu,
  Modal,
  Radio,
  Row,
  Select,
} from "antd";
import dayjs from "dayjs";
import { useCallback, useEffect, useRef, useState } from "react";
import { ProjectEmployeeUserInterface } from "./interface.ts";
import "./profile-modal.style.css";
import {
  useBanksQuery,
  useCreateProfileEmployeeMutation,
  useProfileQuery,
} from "./service";

interface ProfileModalProps {
  isOpen: boolean;
  onCloseCb: () => void;
  projectEmployeeUser?: ProjectEmployeeUserInterface;
  projectId: number;
}

const ProfileModal = (props: ProfileModalProps) => {
  const { isOpen, onCloseCb, projectEmployeeUser, projectId } = props;
  const { axiosPost, showNotification } = useApp();

  const [form] = Form.useForm();
  const targetRefs = useRef<HTMLElement[]>([]);
  const [selectedKey, setSelectedKey] = useState("");
  const [selectedProvinceId, setSelectedProvinceId] = useState<number | null>(
    null,
  );
  const [selectedDistrictId, setSelectedDistrictId] = useState<number | null>(
    null,
  );
  const [selectedPermanentProvinceId, setSelectedPermanentProvinceId] =
    useState<number | null>(null);
  const [selectedPermanentDistrictId, setSelectedPermanentDistrictId] =
    useState<number | null>(null);
  const [loading, setLoading] = useState(false);
  const { formPhotos: photos, setFormPhotos: setPhotos } = useFormPhotosStore();

  const provincesQuery = useProvincesQuery();
  const districtsQuery = useDistrictsQuery(selectedProvinceId);
  const wardsQuery = useWardsQuery(selectedProvinceId, selectedDistrictId);
  const permanentDistrictsQuery = useDistrictsQuery(
    selectedPermanentProvinceId,
  );
  const permanentWardsQuery = useWardsQuery(
    selectedPermanentProvinceId,
    selectedPermanentDistrictId,
  );
  const profileQuery = useProfileQuery(projectId, projectEmployeeUser?.id);
  const banksQuery = useBanksQuery(isOpen);

  const createProfileEmployeeMutation = useCreateProfileEmployeeMutation(
    projectId,
    projectEmployeeUser?.id,
  );

  const goToHash = useCallback(async (hash: string) => {
    if (hash) {
      const element = document.getElementById(hash);
      element?.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  }, []);

  targetRefs.current = [];
  const addToRefs = (el: HTMLElement | null) => {
    if (el && !targetRefs.current.includes(el)) {
      targetRefs.current.push(el);
    }
  };

  useEffect(() => {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          setSelectedKey(entry.target.id);
        }
      });
    });

    targetRefs.current.forEach((ref) => {
      if (ref) {
        observer.observe(ref);
      }
    });

    // Cleanup observer on unmount
    return () => {
      targetRefs.current.forEach((ref) => {
        if (ref) {
          observer.unobserve(ref);
        }
      });
    };
  }, [profileQuery.data]);

  useEffect(() => {
    form.scrollToField("fullName");
  }, [form, isOpen]);

  const onClose = useCallback(() => {
    form.resetFields();
    setPhotos([]);
    onCloseCb();
  }, [form, onCloseCb, setPhotos]);

  const onSubmit = useCallback(async () => {
    setLoading(true);
    try {
      await form.validateFields();

      const data = form.getFieldsValue();
      const { photoFiles } = data;

      /**
       * Validate photo required: 'PORTRAIT'
       */
      const portraitPhotos = photos.filter(
        (photo) => photo.type === "PORTRAIT",
      );

      if (
        (!photoFiles["PORTRAIT"] ||
          photoFiles["PORTRAIT"]?.fileList?.length === 0) &&
        portraitPhotos.length === 0
      ) {
        form.setFields([
          {
            name: ["photoFiles", "PORTRAIT"],
            errors: ["Ảnh chân dung không được để trống"],
          },
        ]);
        return Promise.reject(Error("Error in PORTRAIT photo"));
      }

      data.photos = [];

      for (const key in photoFiles) {
        const { fileList } = photoFiles[key] ?? { fileList: [] };

        for (const file of fileList) {
          if (file.status !== "done") {
            const uploadResult = await uploadImage(
              axiosPost,
              file.originFileObj,
            );
            data.photos.push({ type: key, imageId: uploadResult?.id });
          }
        }
      }
      delete data.photoFiles;

      for (const photo of photos) {
        data.photos.push({ type: photo.type, imageId: photo.image.id });
      }

      await createProfileEmployeeMutation.mutateAsync(data);
      showNotification({
        type: "success",
        message: "Cập nhật profile thành công",
      });
      onClose();
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      showNotification({
        type: "error",
        message: (error.message as unknown as string) ?? "Có lỗi xảy ra",
      });
      console.error(error);
    } finally {
      setLoading(false);
    }
  }, [
    axiosPost,
    createProfileEmployeeMutation,
    form,
    onClose,
    photos,
    showNotification,
  ]);

  useEffect(() => {
    const {
      birthdate,
      userProfileExperiences,
      province,
      district,
      ward,
      permanentProvince,
      permanentDistrict,
      permanentWard,
      bank,
    } = profileQuery.data ?? {};
    form.setFieldsValue({
      ...profileQuery.data,
      birthdate: birthdate ? dayjs(birthdate) : undefined,
      experiences: userProfileExperiences
        ? userProfileExperiences.map((experience) => ({
            ...experience,
            startedAt: experience.startedAt
              ? dayjs(experience.startedAt)
              : undefined,
            endedAt: experience.endedAt ? dayjs(experience.endedAt) : undefined,
          }))
        : [{}],
      provinceId: province?.id,
      districtId: district?.id,
      wardId: ward?.id,
      permanentProvinceId: permanentProvince?.id,
      permanentDistrictId: permanentDistrict?.id,
      permanentWardId: permanentWard?.id,
      bankId: bank?.id,
    });
    if (province?.id) {
      setSelectedProvinceId(province.id);
    }
    if (district?.id) {
      setSelectedDistrictId(district.id);
    }
    if (permanentProvince?.id) {
      setSelectedPermanentProvinceId(permanentProvince.id);
    }
    if (permanentDistrict?.id) {
      setSelectedPermanentDistrictId(permanentDistrict.id);
    }
    setPhotos(profileQuery.data?.userProfilePhotos ?? []);
  }, [form, profileQuery.data, setPhotos]);

  return (
    <Modal
      open={isOpen}
      footer={false}
      closeIcon={null}
      styles={{ content: { padding: 0 } }}
      width={1000}
    >
      <Row justify={"space-between"} className="">
        <Col md={7} className="bg-[#FAFAFA] pr-6 pl-6 rounded pt-5">
          <h3 className="ml-3">Profile</h3>
          <Menu
            className="bg-[#FAFAFA] profile-menu"
            mode="inline"
            defaultSelectedKeys={["personal"]}
            style={{ borderRight: 0, borderInline: "none", color: "red" }}
            selectedKeys={[selectedKey]}
            items={[
              {
                label: "• Thông tin cá nhân",
                key: "personal",
              },
              {
                label: "• Tài khoản ngân hàng",
                key: "bank",
              },
              {
                label: "• Nơi ở hiện tại",
                key: "address",
              },
              {
                label: "• Địa chỉ trên hộ khẩu",
                key: "permanen",
              },
              {
                label: "• Tình trạng hôn nhân",
                key: "maritalStatus",
              },
              {
                label: "• Người thân liên hệ trong trường hợp khẩn cấp",
                key: "emergency",
              },
              {
                label: "• Ngoại hình",
                key: "body",
              },
              {
                label: "• Vị trí làm việc mong muốn",
                key: "desired",
              },
              {
                label: "• Kinh nghiệm làm việc",
                key: "experiences",
              },
              {
                label: "• Hình ảnh",
                key: "photoFiles",
              },
            ]}
            onClick={(e) => {
              goToHash(e.key);
            }}
          />
        </Col>
        <Col md={17} className="pl-10 pt-5">
          <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap mr-10">
            <h2 className="text-neutral-700 text-2xl font-semibold ">
              Profile nhân viên
            </h2>
            <div className="pt-5">
              <Button
                loading={loading}
                type="link"
                onClick={onClose}
                size="large"
                icon={<CloseOutlined />}
              />
            </div>
          </div>
          <div className="overflow-y-scroll overflow-x-hidden max-h-[650px] pr-10">
            {(() => {
              if (profileQuery.isFetching) {
                return <LoadingOutlined />;
              }

              return (
                <Form
                  layout="vertical"
                  form={form}
                  initialValues={{ experiences: [{}], gender: "FEMALE" }}
                >
                  <p
                    className="text-blue font-semibold text-lg"
                    id="personal"
                    ref={addToRefs}
                  >
                    Thông tin cá nhân
                  </p>

                  <Form.Item
                    label="Họ và tên"
                    rules={[{ required: true }]}
                    name="fullName"
                  >
                    <Input />
                  </Form.Item>

                  <Row gutter={16}>
                    <Col md={12}>
                      <Form.Item
                        label="Số điện thoại"
                        name="phoneNumber"
                        rules={[
                          {
                            required: true,
                          },
                          {
                            pattern: new RegExp(/^0\d+$/),
                            message:
                              "Vui lòng nhập đúng định dạng số điện thoại",
                          },
                          {
                            len: 10,
                          },
                        ]}
                      >
                        <Input />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={16}>
                    <Col md={12}>
                      <Form.Item
                        label="CMND/CCCD"
                        rules={[
                          { required: true },
                          {
                            pattern: /^.{9}$|^.{12}$/,
                            message: "Giá trị phải có độ dài 9 hoặc 12 ký tự.",
                          },
                          {
                            pattern: /^\d+$/,
                            message: "Dữ liệu nhập chỉ được chứa các ký tự số!",
                          },
                        ]}
                        name={"identityCardNumber"}
                      >
                        <Input />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={16}>
                    <Col md={12}>
                      <Form.Item
                        label="Email"
                        rules={[
                          { required: true },
                          {
                            type: "email",
                          },
                        ]}
                        name="email"
                      >
                        <Input />
                      </Form.Item>
                    </Col>
                  </Row>

                  <FormImageUploadComponent
                    label="Ảnh chân dung (1 ảnh)"
                    fieldName={["photoFiles", "PORTRAIT"]}
                    max={1}
                    required
                    imagesFile={profileQuery.data?.userProfilePhotos
                      ?.filter((photo) => photo.type === "PORTRAIT")
                      .map((photo) => photo.image)}
                  />

                  <Form.Item label="Giới tính" name={"gender"}>
                    <Radio.Group>
                      <Radio value="MALE">Nam</Radio>
                      <Radio value="FEMALE">Nữ</Radio>
                    </Radio.Group>
                  </Form.Item>

                  <Row justify={"space-between"} gutter={16}>
                    <Col md={12}>
                      <Form.Item label="Ngày sinh" name={"birthdate"}>
                        <DatePicker
                          format={DATE_FORMAT}
                          className="w-full"
                          placeholder=""
                        />
                      </Form.Item>
                    </Col>
                    <Col md={12}>
                      <Form.Item label="Nơi sinh" name={"birthplace"}>
                        <Input />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={16}>
                    <Col md={12}>
                      <Form.Item
                        label="Trình độ học vấn"
                        name={"educationLevel"}
                      >
                        <Select
                          options={[
                            { label: "Tiểu học", value: "PRIMARY_SCHOOL" },
                            {
                              label: "Trung học cơ sở",
                              value: "MIDDLE_SCHOOL",
                            },
                            {
                              label: "Trung học phổ thông",
                              value: "HIGH_SCHOOL",
                            },
                            {
                              label: "Trung cấp",
                              value: "VOCATIONAL_TRAINING",
                            },
                            {
                              label: "Cao đẳng",
                              value: "ASSOCIATE_DEGREE",
                            },
                            {
                              label: "Đại học",
                              value: "BACHELOR_DEGREE",
                            },
                            { label: "Thạc sĩ", value: "MASTER_DEGREE" },
                            { label: "Tiến sĩ", value: "DOCTORAL_DEGREE" },
                            { label: "Sau tiến sĩ", value: "POSTDOCTORAL" },
                            { label: "Khác", value: "OTHER" },
                          ]}
                          showSearch
                          filterOption={filterOption}
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={16}>
                    <Col md={12}>
                      <Form.Item
                        label="Mã số thuế"
                        name={"personalTaxCode"}
                        rules={[
                          {
                            pattern: /^\d+$/,
                            message: "Dữ liệu nhập chỉ được chứa các ký tự số!",
                          },
                          {
                            max: 20,
                          },
                        ]}
                      >
                        <Input />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={16}>
                    <Col md={12}>
                      <Form.Item
                        label="Số BHXH"
                        name={"socialInsuranceNumber"}
                        rules={[
                          {
                            pattern: /^\d+$/,
                            message: "Dữ liệu nhập chỉ được chứa các ký tự số!",
                          },
                          {
                            max: 20,
                          },
                        ]}
                      >
                        <Input />
                      </Form.Item>
                    </Col>
                  </Row>

                  <p
                    ref={addToRefs}
                    className="text-blue font-semibold text-lg mt-7"
                    id="bank"
                  >
                    Tài khoản ngân hàng
                  </p>

                  <Form.Item label="Ngân hàng" name={"bankId"}>
                    <Select
                      allowClear
                      showSearch
                      optionFilterProp="children"
                      filterOption={filterOption}
                      options={banksQuery.data?.map((bank) => ({
                        label: bank.name,
                        value: bank.id,
                      }))}
                      popupMatchSelectWidth={false}
                    />
                  </Form.Item>

                  <Form.Item label={"Chi nhánh ngân hàng"} name={"bankBranch"}>
                    <Input />
                  </Form.Item>

                  <Form.Item label={"Số tài khoản"} name={"bankAccountNumber"}>
                    <Input />
                  </Form.Item>

                  <Form.Item
                    label={"Tên chủ tài khoản"}
                    name={"bankAccountName"}
                  >
                    <Input />
                  </Form.Item>

                  <p
                    ref={addToRefs}
                    className="text-blue font-semibold text-lg mt-7"
                    id="address"
                  >
                    Nơi ở hiện tại
                  </p>

                  <Form.Item label="Tỉnh/Thành phố" name={"provinceId"}>
                    <Select
                      allowClear
                      showSearch
                      optionFilterProp="children"
                      filterOption={filterOption}
                      options={provincesQuery.data?.map((province) => ({
                        label: province.name,
                        value: province.id,
                      }))}
                      popupMatchSelectWidth={false}
                      onChange={(value: number) => {
                        setSelectedProvinceId(value);
                        form.resetFields(["districtId", "wardId"]);
                      }}
                    />
                  </Form.Item>

                  <Form.Item label="Quận/Huyện" name={"districtId"}>
                    <Select
                      allowClear
                      showSearch
                      optionFilterProp="children"
                      filterOption={filterOption}
                      options={districtsQuery.data?.map((district) => ({
                        label: district.name,
                        value: district.id,
                      }))}
                      popupMatchSelectWidth={false}
                      onChange={(value: number) => {
                        setSelectedDistrictId(value);
                        form.resetFields(["wardId"]);
                      }}
                    />
                  </Form.Item>

                  <Form.Item label="Phường/Xã" name={"wardId"}>
                    <Select
                      allowClear
                      showSearch
                      optionFilterProp="children"
                      filterOption={filterOption}
                      options={wardsQuery.data?.map((ward) => ({
                        label: ward.name,
                        value: ward.id,
                      }))}
                      popupMatchSelectWidth={false}
                    />
                  </Form.Item>

                  <Form.Item label="Số nhà, tên đường" name={"address"}>
                    <Input />
                  </Form.Item>

                  <p
                    ref={addToRefs}
                    className="text-blue font-semibold text-lg mt-[48px]"
                    id="permanen"
                  >
                    Địa chỉ trên hộ khẩu
                  </p>

                  <Form.Item
                    label="Tỉnh/Thành phố"
                    name={"permanentProvinceId"}
                  >
                    <Select
                      allowClear
                      showSearch
                      optionFilterProp="children"
                      filterOption={filterOption}
                      options={provincesQuery.data?.map((province) => ({
                        label: province.name,
                        value: province.id,
                      }))}
                      popupMatchSelectWidth={false}
                      onChange={(value: number) => {
                        setSelectedPermanentProvinceId(value);
                        form.resetFields([
                          "permanentDistrictId",
                          "permanentWardId",
                        ]);
                      }}
                    />
                  </Form.Item>

                  <Form.Item label="Quận/Huyện" name={"permanentDistrictId"}>
                    <Select
                      allowClear
                      showSearch
                      optionFilterProp="children"
                      filterOption={filterOption}
                      options={permanentDistrictsQuery.data?.map(
                        (district) => ({
                          label: district.name,
                          value: district.id,
                        }),
                      )}
                      popupMatchSelectWidth={false}
                      onChange={(value: number) => {
                        setSelectedPermanentDistrictId(value);
                        form.resetFields(["permanentWardId"]);
                      }}
                    />
                  </Form.Item>

                  <Form.Item label="Phường/Xã" name={"permanentWardId"}>
                    <Select
                      allowClear
                      showSearch
                      optionFilterProp="children"
                      filterOption={filterOption}
                      options={permanentWardsQuery.data?.map((ward) => ({
                        label: ward.name,
                        value: ward.id,
                      }))}
                      popupMatchSelectWidth={false}
                    />
                  </Form.Item>

                  <Form.Item
                    label="Số nhà, tên đường"
                    name={"permanentAddress"}
                  >
                    <Input />
                  </Form.Item>

                  <p
                    ref={addToRefs}
                    className="text-blue font-semibold text-lg mt-[48px]"
                    id="maritalStatus"
                  >
                    Tình trạng hôn nhân
                  </p>

                  <Form.Item label="Tình trạng hôn nhân" name={"maritalStatus"}>
                    <Radio.Group defaultValue={"single"}>
                      <Radio value="SINGLE">Độc thân</Radio>
                      <Radio value="MARRIED">Kết hôn</Radio>
                      <Radio value="DIVORCED"> Ly dị</Radio>
                    </Radio.Group>
                  </Form.Item>
                  <Row gutter={16}>
                    <Col md={12}>
                      <Form.Item
                        label="Số con (nếu có)"
                        name={"numberOfChildren"}
                        rules={[
                          () => ({
                            validator(_, value) {
                              if (value > 100) {
                                return Promise.reject(
                                  new Error("Số con không được lớn hơn 100."),
                                );
                              }
                              return Promise.resolve();
                            },
                          }),
                        ]}
                      >
                        <InputNumber
                          controls={false}
                          type="number"
                          step={0}
                          min={0}
                          className="w-full"
                          max={100}
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <p
                    ref={addToRefs}
                    className="text-blue font-semibold text-lg mt-7"
                    id="emergency"
                  >
                    Người thân liên hệ trong trường hợp khẩn
                  </p>

                  <Form.Item label="Họ và tên" name={"emergencyContactName"}>
                    <Input />
                  </Form.Item>

                  <Row gutter={16}>
                    <Col md={12}>
                      <Form.Item
                        label="Quan hệ"
                        name={"emergencyContactRelationship"}
                      >
                        <Input />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={16}>
                    <Col md={12}>
                      <Form.Item
                        label="Số điện thoại"
                        name={"emergencyContactPhoneNumber"}
                        rules={[
                          {
                            pattern: new RegExp(/^0\d+$/),
                            message:
                              "Vui lòng nhập đúng định dạng số điện thoại",
                          },
                          {
                            len: 10,
                          },
                        ]}
                      >
                        <Input />
                      </Form.Item>
                    </Col>
                  </Row>

                  <p
                    ref={addToRefs}
                    className="text-blue font-semibold text-lg mt-7"
                    id="body"
                  >
                    Ngoại hình
                  </p>

                  <Row gutter={16}>
                    <Col md={12}>
                      <Form.Item label="Chiều cao (cm)" name={"bodyHeight"}>
                        <InputNumber
                          controls={false}
                          step={0}
                          min={0}
                          max={255}
                          className="w-full"
                        />
                      </Form.Item>
                    </Col>

                    <Col md={12}>
                      <Form.Item label="Cân nặng (kg)" name={"bodyWeight"}>
                        <InputNumber
                          controls={false}
                          step={0}
                          min={0}
                          max={255}
                          className="w-full"
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={16}>
                    <Col md={12}>
                      <Form.Item label="Size áo" name={"shirtSize"}>
                        <Select
                          options={[
                            {
                              label: "XS",
                              value: "XS",
                            },
                            {
                              label: "S",
                              value: "S",
                            },
                            {
                              label: "M",
                              value: "M",
                            },
                            {
                              label: "L",
                              value: "L",
                            },
                            {
                              label: "XL",
                              value: "XL",
                            },
                            {
                              label: "XXL",
                              value: "XXL",
                            },
                            {
                              label: "XXXL",
                              value: "XXXL",
                            },
                          ]}
                          filterOption={filterOption}
                          showSearch
                        />
                      </Form.Item>
                    </Col>

                    <Col md={12}>
                      <Form.Item label="Size quần/ váy" name={"pantsSize"}>
                        <Select
                          options={[
                            {
                              label: "XS",
                              value: "XS",
                            },
                            {
                              label: "S",
                              value: "S",
                            },
                            {
                              label: "M",
                              value: "M",
                            },
                            {
                              label: "L",
                              value: "L",
                            },
                            {
                              label: "XL",
                              value: "XL",
                            },
                            {
                              label: "XXL",
                              value: "XXL",
                            },
                            {
                              label: "XXXL",
                              value: "XXXL",
                            },
                          ]}
                          filterOption={filterOption}
                          showSearch
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={16}>
                    <Col md={12}>
                      <Form.Item label="Size đầm" name={"dressSize"}>
                        <Select
                          options={[
                            {
                              label: "XS",
                              value: "XS",
                            },
                            {
                              label: "S",
                              value: "S",
                            },
                            {
                              label: "M",
                              value: "M",
                            },
                            {
                              label: "L",
                              value: "L",
                            },
                            {
                              label: "XL",
                              value: "XL",
                            },
                            {
                              label: "XXL",
                              value: "XXL",
                            },
                            {
                              label: "XXXL",
                              value: "XXXL",
                            },
                          ]}
                          filterOption={filterOption}
                          showSearch
                        />
                      </Form.Item>
                    </Col>

                    <Col md={12}>
                      {(() => {
                        const options = [...Array(14).keys()]
                          .map((x) => x + 34)
                          .map((i) => ({
                            label: i.toString(),
                            value: i.toString(),
                          }));
                        return (
                          <Form.Item label="Size giày" name={"shoeSize"}>
                            <Select
                              options={options}
                              filterOption={filterOption}
                              showSearch
                            />
                          </Form.Item>
                        );
                      })()}
                    </Col>
                  </Row>
                  <Row gutter={16}>
                    <Col md={8}>
                      <Form.Item label="Vòng 1" name={"bodyBust"}>
                        <InputNumber
                          controls={false}
                          step={0}
                          min={0}
                          max={200}
                          className="w-full"
                        />
                      </Form.Item>
                    </Col>

                    <Col md={8}>
                      <Form.Item label="Vòng 2" name={"bodyWaist"}>
                        <InputNumber
                          controls={false}
                          step={0}
                          min={0}
                          max={200}
                          className="w-full"
                        />
                      </Form.Item>
                    </Col>

                    <Col md={8}>
                      <Form.Item label="Vòng 3" name={"bodyHips"}>
                        <InputNumber
                          controls={false}
                          step={0}
                          min={0}
                          max={200}
                          className="w-full"
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <p
                    ref={addToRefs}
                    className="text-blue font-semibold text-lg mt-7"
                    id="desired"
                  >
                    Vị trí làm việc mong muốn
                  </p>

                  <Form.Item label="Vị trí" name={"desiredPosition"}>
                    <Select
                      options={[
                        { label: "PG/PB", value: "PG/PB" },
                        { label: "Helper", value: "HELPER" },
                        { label: "SUP", value: "SUP" },
                      ]}
                      showSearch
                      filterOption={filterOption}
                    />
                  </Form.Item>

                  <Form.Item
                    label="Địa bàn làm việc mong muốn"
                    name={"desiredLocation"}
                  >
                    <Input />
                  </Form.Item>

                  <Form.Item
                    label="Nguồn tuyển dụng"
                    name={"recruitmentSource"}
                  >
                    <Select
                      options={[
                        {
                          value: "FACEBOOK",
                          label: "Facebook",
                        },
                        {
                          value: "LINKEDIN",
                          label: "LinkedIn",
                        },
                        {
                          value: "ZALO",
                          label: "Zalo",
                        },
                        {
                          value: "TOP_CV",
                          label: "TopCV",
                        },
                        {
                          value: "VIETNAM_WORKS",
                          label: "Vietnamworks",
                        },
                        {
                          value: "REFERRAL",
                          label: "Được giới thiệu",
                        },
                        {
                          value: "OTHER",
                          label: "Khác",
                        },
                      ]}
                      showSearch
                      filterOption={filterOption}
                    />
                  </Form.Item>

                  <p
                    ref={addToRefs}
                    className="text-blue font-semibold text-lg mt-[48px]"
                    id="experiences"
                  >
                    Kinh nghiệm làm việc
                  </p>

                  <Form.List name="experiences">
                    {(fields, { add, remove }) => (
                      <>
                        {fields.map(({ key, name, ...restField }, index) => (
                          <>
                            {index !== 0 && (
                              <hr className="border-dashed border-t-0 border-[#DDE1EA] mb-3" />
                            )}
                            <Row gutter={16} key={key}>
                              <Col md={22}>
                                <Row gutter={16}>
                                  <Col md={12}>
                                    <Form.Item
                                      label="Thời gian bắt đầu"
                                      {...restField}
                                      name={[name, "startedAt"]}
                                      rules={[{ required: true }]}
                                    >
                                      <DatePicker
                                        format={DATE_FORMAT}
                                        className="w-full"
                                        placeholder=""
                                      />
                                    </Form.Item>
                                  </Col>

                                  <Col md={12}>
                                    <Form.Item
                                      label="Thời gian kết thúc"
                                      {...restField}
                                      name={[name, "endedAt"]}
                                    >
                                      <DatePicker
                                        format={DATE_FORMAT}
                                        className="w-full"
                                        placeholder=""
                                      />
                                    </Form.Item>
                                  </Col>
                                </Row>

                                <Row gutter={16}>
                                  <Col md={12}>
                                    <Form.Item
                                      label="Tên công ty"
                                      {...restField}
                                      name={[name, "companyName"]}
                                      rules={[{ required: true }]}
                                    >
                                      <Input />
                                    </Form.Item>
                                  </Col>

                                  <Col md={12}>
                                    <Form.Item
                                      label="Ngành nghề kinh doanh"
                                      {...restField}
                                      name={[name, "businessLine"]}
                                    >
                                      <Input />
                                    </Form.Item>
                                  </Col>
                                </Row>

                                <Row gutter={11}>
                                  <Col md={12}>
                                    <Form.Item
                                      label="Vị trí"
                                      {...restField}
                                      name={[name, "title"]}
                                      rules={[{ required: true }]}
                                    >
                                      <Input />
                                    </Form.Item>
                                  </Col>

                                  <Col md={12}>
                                    <Form.Item
                                      label="Lý do từ chức"
                                      {...restField}
                                      name={[name, "leaveReason"]}
                                    >
                                      <Input />
                                    </Form.Item>
                                  </Col>
                                </Row>
                              </Col>
                              <Col
                                md={2}
                                className="flex justify-center items-center mt-[25px] mb-5"
                              >
                                <Button
                                  type="default"
                                  onClick={() => remove(name)}
                                  block
                                  icon={<DeleteOutlined />}
                                  className="btn-delete-experience"
                                />
                              </Col>
                            </Row>
                          </>
                        ))}

                        <Button
                          type="link"
                          onClick={() => add()}
                          icon={<PlusOutlined />}
                          style={{
                            color: "#1D8EE6",
                            padding: "0px",
                            marginTop: "-5px",
                          }}
                        >
                          Thêm mới
                        </Button>
                      </>
                    )}
                  </Form.List>

                  <p
                    ref={addToRefs}
                    className="text-blue font-semibold text-lg pb-0 mb-0 mt-[40px]"
                    id="photoFiles"
                  >
                    Hình ảnh
                  </p>
                  <p className="font-normal text-xs text-hint mt-0 pt-0 mb-5">
                    Vui lòng sử dụng ảnh có định dạng .png, .jpg, .jpeg và có
                    dung lượng &lt;= 5mb
                  </p>

                  <FormImageUploadComponent
                    label="Ảnh toàn thân (1-3 ảnh)"
                    fieldName={["photoFiles", "FULLBODY"]}
                    max={3}
                    imagesFile={profileQuery.data?.userProfilePhotos
                      ?.filter((photo) => photo.type === "FULLBODY")
                      .map((photo) => photo.image)}
                  />

                  <Row gutter={16}>
                    <Col md={12}>
                      <FormImageUploadComponent
                        label="Ảnh CMND/CCCD mặt trước"
                        fieldName={["photoFiles", "IDFRONT"]}
                        max={1}
                        imagesFile={profileQuery.data?.userProfilePhotos
                          ?.filter((photo) => photo.type === "IDFRONT")
                          .map((photo) => photo.image)}
                      />
                    </Col>

                    <Col md={12}>
                      <FormImageUploadComponent
                        label="Ảnh CMND/CCCD mặt sau"
                        fieldName={["photoFiles", "IDBACK"]}
                        max={1}
                        imagesFile={profileQuery.data?.userProfilePhotos
                          ?.filter((photo) => photo.type === "IDBACK")
                          .map((photo) => photo.image)}
                      />
                    </Col>
                  </Row>

                  <FormImageUploadComponent
                    label="Ảnh sơ yếu lý lịch (1-5 ảnh)"
                    fieldName={["photoFiles", "CV"]}
                    max={5}
                    imagesFile={profileQuery.data?.userProfilePhotos
                      ?.filter((photo) => photo.type === "CV")
                      .map((photo) => photo.image)}
                  />
                </Form>
              );
            })()}
          </div>
        </Col>
      </Row>
      <div className="flex justify-end gap-4 py-4 rounded-b max-md:max-w-full max-md:flex-wrap pl-10 bg-[#F7F8FA] pr-10 pb-4 border-t-[1.5px] border-[#DDE1EA] border-solid border-l-0 border-r-0 border-b-0">
        <Button type="default" onClick={onClose} style={{}} loading={loading}>
          Đóng
        </Button>
        <Button
          htmlType="submit"
          type={"primary"}
          onClick={onSubmit}
          loading={loading}
        >
          Cập nhật
        </Button>
      </div>
    </Modal>
  );
};

export default ProfileModal;
