import { Column } from "@ant-design/charts";
import { useCallback } from "react";
import DashboardFilterZone from "../../DashboardFilterZone";

const CheckInOutTab = () => {
  const handleApply = useCallback(() => {}, []);

  const data = [
    { date: "20/12", shift: "Ca sáng", value: 6.5 },
    { date: "20/12", shift: "Ca chiều", value: 6.3 },
    { date: "21/12", shift: "Ca sáng", value: 6.5 },
    { date: "21/12", shift: "Ca chiều", value: 6.3 },
    { date: "22/12", shift: "Ca sáng", value: 6.4 },
    { date: "22/12", shift: "Ca chiều", value: 6.3 },
    { date: "23/12", shift: "Ca sáng", value: 6.4 },
    { date: "23/12", shift: "Ca chiều", value: 6.1 },
    { date: "24/12", shift: "Ca sáng", value: 6.5 },
    { date: "24/12", shift: "Ca chiều", value: 6.3 },
    { date: "25/12", shift: "Ca sáng", value: 6.7 },
    { date: "25/12", shift: "Ca chiều", value: 6.7 },
    { date: "26/12", shift: "Ca sáng", value: 6.7 },
    { date: "26/12", shift: "Ca chiều", value: 6.5 },
    { date: "27/12", shift: "Ca sáng", value: 6.1 },
    { date: "27/12", shift: "Ca chiều", value: 6.3 },
    { date: "28/12", shift: "Ca sáng", value: 6.7 },
    { date: "28/12", shift: "Ca chiều", value: 6.6 },
    { date: "29/12", shift: "Ca sáng", value: 6.8 },
    { date: "29/12", shift: "Ca chiều", value: 6.7 },
    { date: "30/12", shift: "Ca sáng", value: 6.7 },
    { date: "30/12", shift: "Ca chiều", value: 6.1 },
    { date: "31/12", shift: "Ca sáng", value: 6.8 },
    { date: "31/12", shift: "Ca chiều", value: 6.3 },
    { date: "1/1", shift: "Ca sáng", value: 6.7 },
    { date: "1/1", shift: "Ca chiều", value: 6.3 },
    { date: "2/1", shift: "Ca sáng", value: 6.1 },
    { date: "2/1", shift: "Ca chiều", value: 6.5 },
    { date: "3/1", shift: "Ca sáng", value: 6.7 },
    { date: "3/1", shift: "Ca chiều", value: 6.3 },
  ];

  const config = {
    data,
    isGroup: true,
    xField: "date",
    yField: "value",
    seriesField: "shift",
    columnStyle: {
      radius: [2, 2, 0, 0],
    },
    color: ["#4CAF50", "#2196F3"],
    colorField: "shift",
    label: false,
    yAxis: {
      min: 0,
      max: 10,
      grid: {
        line: {
          style: {
            stroke: "#E5E7EB",
            lineWidth: 1,
            lineDash: [4, 4],
          },
        },
      },
    },
    xAxis: {
      label: {
        style: {
          fontSize: 12,
        },
      },
    },
    legend: {
      position: "bottom",
    },
    annotations: [
      {
        type: "lineY",
        yField: 6,
        style: { stroke: "#F4664A", strokeOpacity: 1, lineWidth: 2 },
      },
    ],
    title: {
      title: "Check In/Out",
      subtitle: "Trung bình giờ làm mỗi ca",
    },
  };

  return (
    <>
      <DashboardFilterZone handleApply={handleApply} />

      <Column {...config} height={400} />
    </>
  );
};

export default CheckInOutTab;
