import { Image, Space } from "antd";
import React, { Fragment, useMemo } from "react";
import ImageItem from "./ImageItem";

interface ImageData {
  preview?: string;
  thumbnail?: string;
  alt?: string;
}

interface ImagesGridProps {
  images: ImageData[];
  maxImagesPerRow?: number;
  height?: number;
  width?: number;
}

const ImagesGrid: React.FC<ImagesGridProps> = ({
  images,
  maxImagesPerRow = 5,
  height,
  width,
}) => {
  const rows = useMemo(() => {
    const rows: React.ReactNode[] = [];
    let currentRow: React.ReactNode[] = [];
    const totalImages = images.length;

    images.forEach((image, index) => {
      currentRow.push(
        <ImageItem
          key={index}
          preview={image.preview}
          thumbnail={image.thumbnail}
          height={height}
          width={width}
        />,
      );

      const isLastRow =
        currentRow.length === totalImages || index === totalImages - 1;
      const shouldCreateNewRow =
        currentRow.length === maxImagesPerRow || isLastRow;

      if (shouldCreateNewRow) {
        rows.push(
          <Fragment key={`row ${rows.length}`}>
            <Space key={`space ${rows.length}`} wrap>
              {currentRow}
            </Space>
            <br />
          </Fragment>,
        );
        currentRow = [];
      }
    });

    return rows;
  }, [height, images, maxImagesPerRow, width]);

  return <Image.PreviewGroup>{rows}</Image.PreviewGroup>;
};

export default ImagesGrid;
