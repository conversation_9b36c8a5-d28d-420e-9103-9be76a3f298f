import { filterOption } from "@/common/helper.ts";
import CustomModal from "@/components/CustomModal.tsx";
import ProductItemCell from "@/components/ProductItemCell.tsx";
import { useUrlFiltersWithQuery } from "@/hooks/useUrlFiltersWithQuery.ts";
import {
  useOosMergedProductProductsQuery,
  useRemoveOosMergedProductMutation,
} from "@project/component/feature/config/types/outOfStockStatus/steps/mergedProduct/service.ts";
import { useProjectBrandsQuery } from "@project/general/services.ts";
import { Button, Form, Input, Select, Space, Table } from "antd";
import React, { useCallback, useMemo, useState } from "react";

import { useApp } from "@/UseApp";
import {
  OosMergedProductInterface,
  OosProductInterface,
} from "../../interface";

interface OutOfStockStatusOutletMergedProductProductModalProps {
  oosMergedProduct: OosMergedProductInterface | null;
  componentFeatureId: number;
  projectId: number;
  cb: () => void;
  onClose: () => void;
}

const OutOfStockStatusOutletMergedProductProductModal = ({
  oosMergedProduct,
  componentFeatureId,
  projectId,
  cb,
  onClose,
}: OutOfStockStatusOutletMergedProductProductModalProps) => {
  const { showNotification } = useApp();

  const [searchForm] = Form.useForm();
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);

  const {
    query: { data, refetch, isFetching },
    handleSearch,
    getPaginationProps,
  } = useUrlFiltersWithQuery<OosProductInterface>({
    formInstance: searchForm,
    useQueryHook: useOosMergedProductProductsQuery,
    queryParams: [componentFeatureId, oosMergedProduct?.id ?? 0],
    options: {
      urlSync: {
        enabled: false,
      },
    },
  });

  const projectBrandsQuery = useProjectBrandsQuery(projectId);

  const removeOosMergedProductMutation = useRemoveOosMergedProductMutation(
    componentFeatureId,
    oosMergedProduct?.id ?? 0,
  );

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys: selectedKeys,
    onChange: onSelectChange,
  };

  const pagination = useMemo(() => getPaginationProps(), [getPaginationProps]);

  const content = (
    <>
      <p>
        Sản phẩm gộp:{" "}
        <span className={"text-blue font-semibold"}>
          {oosMergedProduct?.productName}
        </span>
      </p>

      <Form layout="vertical" onFinish={handleSearch} form={searchForm}>
        <Space>
          <Form.Item label="Nhãn hàng" name={"projectBrandId"}>
            <Select
              style={{ width: "200px" }}
              placeholder={"Tất cả"}
              allowClear
              options={projectBrandsQuery.data?.map((projectBrand) => ({
                value: projectBrand.id,
                label: projectBrand.brand.name,
              }))}
              showSearch
              filterOption={filterOption}
            />
          </Form.Item>
          <Form.Item label="Tên sản phẩm" name={"keyword"}>
            <Input placeholder="Nhập tên hoặc mã" allowClear />
          </Form.Item>
          <Form.Item label=" ">
            <Button htmlType="submit">Tìm kiếm</Button>
          </Form.Item>
        </Space>
      </Form>

      <Table
        scroll={{
          y: pagination.total ? "70vh" : undefined,
          x: "max-content",
        }}
        dataSource={data?.entities ?? []}
        loading={isFetching}
        columns={[
          {
            title: "Tên sản phẩm",
            className: "min-w-[100px]",
            render: (_, record: OosProductInterface) => {
              const { projectProduct } = record ?? {};

              if (projectProduct) {
                return (
                  <ProductItemCell
                    name={projectProduct?.product.name}
                    variants={projectProduct.product.image?.variants ?? []}
                  />
                );
              }
            },
          },
          {
            title: "Mã sản phẩm",
            className: "min-w-[100px]",
            render: (_, record: OosProductInterface) => {
              return record.projectProduct?.product.code;
            },
          },
          {
            title: "Nhãn hàng",
            className: "min-w-[100px]",
            render: (_, record: OosProductInterface) => {
              return record.projectProduct?.product?.brand?.name;
            },
          },
          {
            title: "Quy cách",
            className: "min-w-[100px]",
            render: (_, record: OosProductInterface) => {
              return record.projectProduct?.productPackaging?.unit?.name;
            },
          },
        ]}
        rowSelection={rowSelection}
        rowKey={(record) => record.id}
        pagination={pagination}
      />
    </>
  );

  const confirm = useCallback(async () => {
    if (selectedKeys.length > 0) {
      await removeOosMergedProductMutation.mutateAsync(
        selectedKeys.map((item) => item as number),
      );

      showNotification({
        type: "success",
        message: "Xóa sản phẩm gộp thành công",
      });

      refetch();
      cb();
    }
  }, [
    cb,
    refetch,
    removeOosMergedProductMutation,
    selectedKeys,
    showNotification,
  ]);

  return (
    <CustomModal
      title={"Sản phẩm bên trong sản phẩm gộp"}
      isOpen={true}
      content={content}
      onCancel={onClose}
      width={900}
      confirmText={`Xóa ${selectedKeys.length} sản phẩm`}
      onConfirm={confirm}
      confirmDisable={selectedKeys.length === 0}
    />
  );
};

export default OutOfStockStatusOutletMergedProductProductModal;
