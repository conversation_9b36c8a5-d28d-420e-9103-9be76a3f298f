import FormNumberInput from "@/components/FormNumberInput";
import InnerContainer from "@/components/InnerContainer/InnerContainer";
import { useApp } from "@/UseApp";
import { Button, Col, Form, Row, Switch } from "antd";
import { useCallback, useEffect, useState } from "react";
import {
  useCreateProjectSitecheckConfigMutation,
  useDeleteProjectSitecheckConfigMutation,
  useProjectSitecheckConfigQuery,
  useUpdateProjectSitecheckConfigMutation,
} from "./service";

interface ConfigSitecheckSectionProps {
  projectId: number;
}

const ConfigSitecheckSection = ({ projectId }: ConfigSitecheckSectionProps) => {
  const { showNotification } = useApp();
  const projectSitecheckConfigQuery = useProjectSitecheckConfigQuery(projectId);

  const [isConfigSitecheck, setIsConfigSitecheck] = useState(
    !!projectSitecheckConfigQuery.data?.id,
  );

  const createProjectSitecheckConfigMutation =
    useCreateProjectSitecheckConfigMutation(projectId);
  const deleteProjectSitecheckConfigMutation =
    useDeleteProjectSitecheckConfigMutation(projectId);
  const updateProjectSitecheckConfigMutation =
    useUpdateProjectSitecheckConfigMutation(projectId);

  const onConfigSitecheckChange = useCallback(
    async (value: boolean) => {
      if (!value) {
        await deleteProjectSitecheckConfigMutation.mutateAsync();
        projectSitecheckConfigQuery.refetch();
      }
      setIsConfigSitecheck(value);
    },
    [deleteProjectSitecheckConfigMutation, projectSitecheckConfigQuery],
  );

  const onFinish = useCallback(
    async (values: {
      sitecheckCompanyId: number;
      sitecheckProjectId: number;
    }) => {
      if (projectSitecheckConfigQuery.data?.id) {
        await updateProjectSitecheckConfigMutation.mutateAsync(values);
      } else {
        await createProjectSitecheckConfigMutation.mutateAsync(values);
        projectSitecheckConfigQuery.refetch();
      }
      showNotification({
        type: "success",
        message: "Cập nhật cấu hình sitecheck thành công",
      });
    },
    [
      createProjectSitecheckConfigMutation,
      projectSitecheckConfigQuery,
      showNotification,
      updateProjectSitecheckConfigMutation,
    ],
  );

  useEffect(() => {
    if (projectSitecheckConfigQuery.data?.id) {
      setIsConfigSitecheck(true);
    }
  }, [projectSitecheckConfigQuery.data?.id]);

  return (
    <InnerContainer className="mt-5">
      <Row>
        <Col md={8}>
          <h4>Sitecheck</h4>
          <p className="text-gray-400 max-w-[350px]">
            Cho phép liên kết với dự án bên hệ thống sitecheck để hiển thị chart
            trên FMS
          </p>
        </Col>

        <Col md={16}>
          <p>
            Liên kết sitecheck
            <Switch
              className="ml-5"
              value={isConfigSitecheck}
              onChange={onConfigSitecheckChange}
              loading={
                createProjectSitecheckConfigMutation.isPending ||
                deleteProjectSitecheckConfigMutation.isPending
              }
            />
          </p>

          {isConfigSitecheck && (
            <Form
              layout="vertical"
              className="mt-8"
              onFinish={onFinish}
              initialValues={projectSitecheckConfigQuery.data}
            >
              <Row>
                <Col md={10} xs={24}>
                  <Form.Item
                    label="ID company"
                    name={"sitecheckCompanyId"}
                    rules={[
                      {
                        required: true,
                      },
                    ]}
                  >
                    <FormNumberInput className="w-full" />
                  </Form.Item>
                </Col>
              </Row>

              <Row>
                <Col md={10} xs={24}>
                  <Form.Item
                    label="ID dự án"
                    name={"sitecheckProjectId"}
                    rules={[
                      {
                        required: true,
                      },
                    ]}
                  >
                    <FormNumberInput className="w-full" />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item>
                <Button htmlType="submit" type="primary">
                  Lưu
                </Button>
              </Form.Item>
            </Form>
          )}
        </Col>
      </Row>
    </InnerContainer>
  );
};

export default ConfigSitecheckSection;
