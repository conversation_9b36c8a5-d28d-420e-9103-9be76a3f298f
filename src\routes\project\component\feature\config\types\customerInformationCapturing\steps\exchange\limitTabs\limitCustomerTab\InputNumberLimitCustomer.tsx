import InputNumberInCell from "@/components/InputNumberInCell";
import { LoadingOutlined } from "@ant-design/icons";
import { Space } from "antd";
import { useUpdateFeatureOrderLimitMutation } from "../../../../service";

interface InputNumberLimitCustomerProps {
  initValue: number | null;
  componentFeatureId: number;
  id: number;
  fieldName: "maximum" | "periodDays";
}

const InputNumberLimitCustomer = ({
  initValue,
  componentFeatureId,
  id,
  fieldName,
}: InputNumberLimitCustomerProps) => {
  const updateFeatureOrderLimitMutation =
    useUpdateFeatureOrderLimitMutation(componentFeatureId);

  return (
    <Space>
      <InputNumberInCell
        initValue={initValue}
        onSubmit={async (value) => {
          await updateFeatureOrderLimitMutation.mutateAsync({
            id,
            [fieldName]: value ?? null,
          });
        }}
      />
      {updateFeatureOrderLimitMutation.isPending && <LoadingOutlined />}
    </Space>
  );
};

export default InputNumberLimitCustomer;
