import { CURD } from "@/common/constant.ts";
import { filterOption } from "@/common/helper.ts";
import { useUploadImageMutation } from "@/common/upload-image.helper.ts";
import FormImageUploadComponent from "@/components/formImageUploadComponent/FormImageUploadComponent.tsx";
import { useFormPhotosStore } from "@/components/formImageUploadComponent/state.ts";
import { RecordAttendanceInterface } from "@/routes/project/report/types/attendanceClocking/interface.ts";
import { useApp } from "@/UseApp.tsx";
import { CloseOutlined, DeleteOutlined } from "@ant-design/icons";
import {
  ExchangeProceedInterface,
  OrderProductInterface,
} from "@project/component/feature/config/types/customerInformationCapturing/interface.ts";
import { ComponentFeatureInterface } from "@project/component/feature/interface.ts";
import {
  <PERSON><PERSON>,
  Col,
  Form,
  InputNumber,
  Modal,
  Row,
  Select,
  TimePicker,
} from "antd";
import { Input, UploadFile } from "antd/lib/index";
import dayjs, { Dayjs } from "dayjs";
import _ from "lodash";
import { Fragment, useCallback, useEffect, useMemo, useState } from "react";
import { v4 as uuidv4 } from "uuid";
import CustomerInput from "./CustomerInput.tsx";
import { RecordType } from "./interface.ts";
import {
  useCreateCustomerInformationMutation,
  useEditOrderQuery,
  useUpdateCustomerInformationMutation,
} from "./service.ts";

interface EditCustomerInformationCapturingModalProps {
  action: CURD | undefined;
  isOpen: boolean;
  componentFeatureId: number;
  projectId: number;
  attendanceId: number;
  cancelCb: () => void;
  componentFeature?: ComponentFeatureInterface;
  orderId?: number;
  attendance?: RecordAttendanceInterface;
}

const EditCustomerInformationCapturingModal = ({
  action,
  isOpen,
  componentFeatureId,
  projectId,
  attendanceId,
  cancelCb,
  componentFeature,
  orderId,
  attendance,
}: EditCustomerInformationCapturingModalProps) => {
  const { showNotification } = useApp();

  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [selectedOrderProducts, setSelectedOrderProducts] = useState<
    OrderProductInterface[]
  >([]);

  const { formPhotos: photos, setFormPhotos: setPhotos } = useFormPhotosStore();

  const editOrderQuery = useEditOrderQuery(
    projectId,
    attendanceId,
    componentFeatureId,
    orderId,
  );

  const createCustomerInformationMutation =
    useCreateCustomerInformationMutation(
      projectId,
      attendanceId,
      componentFeatureId,
    );
  const updateCustomerInformationMutation =
    useUpdateCustomerInformationMutation(
      projectId,
      attendanceId,
      componentFeatureId,
    );

  const uploadImageMutation = useUploadImageMutation();

  const onClose = useCallback(() => {
    cancelCb();
    setPhotos([]);
    form.resetFields();
  }, [cancelCb, form, setPhotos]);

  const featureCustomers = useMemo(
    () => componentFeature?.featureCustomers ?? [],
    [componentFeature?.featureCustomers],
  );

  const featureOrder = useMemo(
    () => componentFeature?.featureOrder,
    [componentFeature?.featureOrder],
  );

  const featureOrderProducts = useMemo(
    () => featureOrder?.featureOrderProducts ?? [],
    [featureOrder?.featureOrderProducts],
  );

  const featureSchemes = useMemo(
    () => componentFeature?.featureSchemes ?? [],
    [componentFeature?.featureSchemes],
  );

  const featurePhotos = useMemo(
    () => componentFeature?.featurePhotos ?? [],
    [componentFeature?.featurePhotos],
  );

  const isIdentity = useMemo(() => {
    return (
      componentFeature?.featureCustomers?.find((item) => item.isIdentity)
        ?.isIdentity ?? false
    );
  }, [componentFeature?.featureCustomers]);

  const handleOrderData = useCallback(() => {
    const {
      recordOrderCustomers,
      recordOrderPurchases,
      recordOrderExchanges,
      recordOrderPhotos,
      dataTimestamp,
    } = editOrderQuery.data ?? {};

    const customers: Record<string, RecordType> = {};
    let otpCode: undefined | string = undefined;

    for (const recordOrderCustomer of recordOrderCustomers ?? []) {
      const { featureCustomerId, value, recordCustomerOptions, otpDelivery } =
        recordOrderCustomer.recordCustomer;
      if (otpDelivery) {
        otpCode = otpDelivery.otpCode;
      }

      if (value) {
        customers[`${featureCustomerId}`] = value;
      }
      if (recordCustomerOptions.length > 0) {
        customers[`${featureCustomerId}`] =
          recordCustomerOptions[0].featureCustomerOptionId;
      }
    }

    const selectedOrderProducts: OrderProductInterface[] = [];
    const purchases: Record<string, RecordType> = {};
    for (const recordOrderPurchase of recordOrderPurchases ?? []) {
      const { featureOrderProductId, quantity } = recordOrderPurchase;

      const orderProduct = featureOrderProducts.find(
        ({ id }) => id === featureOrderProductId,
      );
      if (orderProduct) {
        selectedOrderProducts.push(orderProduct);
      }

      purchases[`${featureOrderProductId}`] = quantity;
    }
    setSelectedOrderProducts(selectedOrderProducts);

    const exchanges: Record<string, string | number | null> = {};
    for (const recordOrderExchange of recordOrderExchanges ?? []) {
      const { featureSchemeExchangeId, quantity } = recordOrderExchange;
      exchanges[`${featureSchemeExchangeId}`] = quantity;
    }

    setPhotos(
      recordOrderPhotos?.map((recordOrderPhoto) => ({
        type: recordOrderPhoto.recordPhoto.featurePhotoId.toString(),
        image: recordOrderPhoto.recordPhoto.image,
        dataUuid: recordOrderPhoto.recordPhoto.dataUuid,
        dataTimestamp: recordOrderPhoto.recordPhoto.dataTimestamp,
      })) ?? [],
    );

    form.setFieldsValue({
      customers,
      purchases,
      exchanges,
      time: dayjs(dataTimestamp),
      otpCode,
    });
  }, [editOrderQuery.data, featureOrderProducts, form, setPhotos]);

  useEffect(() => {
    if (!editOrderQuery.data) {
      return;
    }

    handleOrderData();
  }, [editOrderQuery.data, featureOrderProducts, form, handleOrderData]);

  const onPurchaseSelected = useCallback(
    (orderProductId: number) => {
      const orderProduct = featureOrderProducts.find(
        ({ id }) => id === orderProductId,
      );
      if (orderProduct) {
        setSelectedOrderProducts((prevState) =>
          _.uniqBy([...prevState, orderProduct], (o) => o.id),
        );
      }
      form.resetFields(["orderProduct"]);
    },
    [featureOrderProducts, form],
  );

  const deleteOrderProduct = useCallback((id: number) => {
    setSelectedOrderProducts((prevState) =>
      prevState.filter((orderProduct) => orderProduct.id !== id),
    );
  }, []);

  const renderedExchangeProceeds = useCallback(
    (exchangeProceeds: ExchangeProceedInterface[]) => {
      return exchangeProceeds.map((exchangeProceed, index) => {
        const { projectItem, projectProduct, quantity } = exchangeProceed;
        const itemName =
          projectItem?.item?.name ?? projectProduct?.product?.name;
        const unitName =
          projectProduct?.productPackaging?.unit?.name ??
          projectItem?.item.unit?.name;
        const code = projectProduct?.product?.code ?? projectItem?.item?.code;

        return (
          <p
            key={`${index}-${exchangeProceed.id}-${projectProduct?.id}-${projectItem?.id}`}
            className="mt-0 mb-0"
          >
            x{quantity} {unitName} - {code} - {itemName}
          </p>
        );
      });
    },
    [],
  );

  const selectedExchangeItems = useMemo(
    () =>
      featureSchemes.flatMap((featureScheme) => {
        const { featureSchemeExchanges } = featureScheme;

        return featureSchemeExchanges
          .filter((item) => !item.luckyDrawId)
          .flatMap((featureSchemeExchange) => {
            const { logical, exchangeConditions, id, exchangeProceeds, name } =
              featureSchemeExchange;

            if (exchangeConditions.length === 0) {
              return {
                item: (
                  <Form.Item
                    name={id}
                    label={
                      <div>{renderedExchangeProceeds(exchangeProceeds)}</div>
                    }
                  >
                    <InputNumber className="w-full" controls={false} />
                  </Form.Item>
                ),
                schemeName: name,
                id: id,
              };
            }

            if (logical === "or") {
              const hasProjectProduct = exchangeConditions.some(
                (exchangeCondition) =>
                  selectedOrderProducts.some(
                    (orderProduct) =>
                      orderProduct.projectProductId ===
                      exchangeCondition.projectProductId,
                  ),
              );

              if (hasProjectProduct) {
                return {
                  item: (
                    <Form.Item
                      name={id}
                      label={
                        <div>{renderedExchangeProceeds(exchangeProceeds)}</div>
                      }
                    >
                      <InputNumber className="w-full" controls={false} />
                    </Form.Item>
                  ),
                  schemeName: name,
                  id: id,
                };
              }
            }

            if (logical === "and") {
              const selectedOrderProductsProjectProductId =
                selectedOrderProducts
                  .map((item) => item.projectProductId)
                  .sort((a, b) => a - b);
              const exchangeConditionsProjectProductId = exchangeConditions
                .map((item) => item.projectProductId)
                .sort((a, b) => a - b);

              if (
                _.intersection(
                  selectedOrderProductsProjectProductId,
                  exchangeConditionsProjectProductId,
                ).length === exchangeConditionsProjectProductId.length
              ) {
                return {
                  item: (
                    <Form.Item
                      name={id}
                      label={
                        <div>{renderedExchangeProceeds(exchangeProceeds)}</div>
                      }
                    >
                      <InputNumber className="w-full" controls={false} />
                    </Form.Item>
                  ),
                  schemeName: name,
                  id: id,
                };
              }
            }
          });
      }),
    [featureSchemes, renderedExchangeProceeds, selectedOrderProducts],
  );

  const onSubmit = useCallback(async () => {
    setLoading(true);

    try {
      const data = await form.validateFields();

      const { customers, time, purchases, exchanges } = data;
      const photoFiles: { fileList: UploadFile[] }[] = data.photoFiles ?? {};

      /**
       * Validate photos
       */
      for (const [featurePhotoId, file] of Object.entries(photoFiles ?? {})) {
        const featurePhoto = featurePhotos.find(
          (featurePhoto) => featurePhoto.id === Number(featurePhotoId),
        );
        if (!featurePhoto) {
          throw new Error(`Feature photo ${featurePhotoId} not found`);
        }

        const alreadyUploadedPhotos = photos.filter(
          (photo) => photo.type === featurePhotoId,
        );

        const length =
          (file?.fileList?.filter((file) => file.status !== "done").length ??
            0) + alreadyUploadedPhotos.length;

        if (length < featurePhoto.minimum || length > featurePhoto.maximum) {
          throw new Error(
            `${featurePhoto.name} phải có số lượng giữa ${featurePhoto.minimum} và ${featurePhoto.maximum}`,
          );
        }
      }

      const photosData = [];
      for (const [featurePhotoId, photoFile] of Object.entries(
        photoFiles ?? {},
      )) {
        if (photoFile) {
          const { fileList } = photoFile;
          for (const file of fileList) {
            if (file.originFileObj) {
              const result = await uploadImageMutation.mutateAsync(
                file.originFileObj,
              );
              if (result?.id)
                photosData.push({
                  featurePhotoId: Number(featurePhotoId),
                  imageId: result.id,
                  dataUuid: uuidv4(),
                  dataTimestamp: time.toISOString(),
                });
            }
          }
        }
      }
      for (const photo of photos) {
        const { image, dataUuid, dataTimestamp } = photo;

        photosData.push({
          featurePhotoId: Number(photo.type),
          imageId: image.id,
          dataUuid: dataUuid ?? "",
          dataTimestamp: dataTimestamp ?? "",
        });
      }

      const customersData = [];
      for (const [featureCustomerId, value] of Object.entries(
        customers ?? {},
      )) {
        if (typeof value === "number") {
          customersData.push({
            featureCustomerId: Number(featureCustomerId),
            featureCustomerOptionIds: [value],
          });
        }
        if (typeof value === "string") {
          customersData.push({
            featureCustomerId: Number(featureCustomerId),
            value: String(value),
          });
        }
      }
      if (customersData.length !== featureCustomers.length) {
        for (const featureCustomer of featureCustomers) {
          if (
            !customersData.find(
              (customer) => customer.featureCustomerId === featureCustomer.id,
            )
          ) {
            customersData.push({
              featureCustomerId: featureCustomer.id,
              value: null,
            });
          }
        }
      }

      const purchasesData = [];
      for (const [featureOrderProductId, quantity] of Object.entries(
        purchases ?? {},
      )) {
        if (Number(quantity)) {
          const selectedOrderProduct = selectedOrderProducts.find(
            (selectedOrderProduct) =>
              selectedOrderProduct.id === Number(featureOrderProductId),
          );

          if (selectedOrderProduct) {
            purchasesData.push({
              featureOrderProductId: Number(featureOrderProductId),
              quantity: Number(quantity),
            });
          }
        }
      }

      const exchangesData = [];
      for (const [featureSchemeExchangeId, quantity] of Object.entries(
        exchanges ?? {},
      )) {
        if (Number(quantity)) {
          const groupedItem = selectedExchangeItems.find(
            (groupedItem) =>
              groupedItem?.id === Number(featureSchemeExchangeId),
          );
          if (groupedItem) {
            exchangesData.push({
              featureSchemeExchangeId: Number(featureSchemeExchangeId),
              quantity: Number(quantity),
            });
          }
        }
      }

      switch (action) {
        case CURD.CREATE:
          await createCustomerInformationMutation.mutateAsync({
            customers: customersData,
            dataTimestamp: dayjs(time)
              .add(_.random(59 - dayjs(time).second()), "second")
              .toISOString(),
            exchanges: exchangesData,
            purchases: purchasesData,
            photos: photosData,
            dataUuid: uuidv4(),
          });

          showNotification({
            type: "success",
            message: "Tạo đơn hàng thành công.",
          });

          break;
        case CURD.UPDATE:
          await updateCustomerInformationMutation.mutateAsync({
            id: editOrderQuery.data?.id ?? 0,
            data: {
              customers: customersData,
              dataTimestamp: time.toISOString(),
              exchanges: exchangesData,
              purchases: purchasesData,
              photos: photosData,
            },
          });
          showNotification({
            type: "success",
            message: "Cập nhật đơn hàng thành công.",
          });
          break;
      }

      onClose();

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      showNotification({
        type: "error",
        message: (error.message as unknown as string) ?? "Có lỗi xảy ra",
      });
      console.error(error);
    } finally {
      setLoading(false);
    }
  }, [
    form,
    featureCustomers,
    action,
    onClose,
    featurePhotos,
    photos,
    uploadImageMutation,
    selectedOrderProducts,
    selectedExchangeItems,
    createCustomerInformationMutation,
    showNotification,
    updateCustomerInformationMutation,
    editOrderQuery.data?.id,
  ]);

  return (
    <Modal
      open={isOpen}
      footer={false}
      closeIcon={null}
      styles={{ content: { padding: 0 } }}
      width={708}
    >
      <Row justify={"space-between"} className="">
        <Col md={24} className="pl-10 pt-5">
          <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap mr-10">
            <h2 className="text-neutral-700 text-2xl font-semibold ">
              {action === CURD.CREATE
                ? "Thêm mới đơn hàng"
                : "Chỉnh sửa đơn hàng"}
            </h2>
            <div className="pt-5">
              <Button
                loading={loading}
                type="link"
                onClick={onClose}
                size="large"
                icon={<CloseOutlined />}
              />
            </div>
          </div>
          <div className="overflow-y-scroll overflow-x-hidden max-h-[650px] pr-10">
            <Form
              layout="vertical"
              form={form}
              initialValues={{
                time: dayjs(attendance?.createdAt).add(1, "minute"),
              }}
            >
              {featureOrder?.hasCustomer && (
                <>
                  <p
                    className="text-primary font-semibold text-lg"
                    id="customers"
                  >
                    Thông tin khách hàng
                  </p>

                  <Form.List name={"customers"}>
                    {() => {
                      return featureCustomers.map((featureCustomer) => (
                        <CustomerInput
                          key={featureCustomer.id}
                          featureCustomer={featureCustomer}
                        />
                      ));
                    }}
                  </Form.List>
                </>
              )}

              {action === CURD.UPDATE && isIdentity && (
                <>
                  <p
                    className="text-primary font-semibold text-lg"
                    id="customers"
                  >
                    Xác thực
                  </p>

                  <Form.Item label={"OTP"} required name={"otpCode"}>
                    <Input disabled />
                  </Form.Item>
                </>
              )}

              {featureOrder?.hasPurchase && (
                <>
                  <p
                    className="text-primary font-semibold text-lg mt-7"
                    id="purchase"
                  >
                    Sản phẩm đã mua
                  </p>

                  <Form.List name={"purchases"}>
                    {() => {
                      return selectedOrderProducts.map((orderProduct) => {
                        const { projectProduct, id } = orderProduct;
                        const unitName =
                          projectProduct.productPackaging?.unit.name;
                        const { code, name } = projectProduct.product;

                        return (
                          <div
                            className="flex items-center mb-4 w-full"
                            key={`selectedOrderProducts${id}`}
                          >
                            <Form.Item
                              label={`${unitName} - ${code} - ${name}`}
                              name={`${id}`}
                              className={"w-full"}
                            >
                              <InputNumber
                                className={"w-full"}
                                controls={false}
                              />
                            </Form.Item>

                            <Button
                              type="text"
                              icon={<DeleteOutlined />}
                              className="ml-2"
                              onClick={() => {
                                deleteOrderProduct(id);
                              }}
                            />
                          </div>
                        );
                      });
                    }}
                  </Form.List>

                  <Form.Item name={"orderProduct"}>
                    <Select
                      showSearch={true}
                      placeholder={"Chọn sản phẩm cần thêm vào đơn hàng"}
                      filterOption={filterOption}
                      options={featureOrderProducts
                        .filter(
                          (orderProduct) =>
                            !selectedOrderProducts
                              .map((item) => item.id)
                              .includes(orderProduct.id),
                        )
                        .map((orderProduct) => ({
                          label: `${orderProduct.projectProduct.productPackaging?.unit.name} - ${orderProduct.projectProduct.product.code} - ${orderProduct.projectProduct.product.name}`,
                          value: orderProduct.id,
                        }))}
                      onSelect={onPurchaseSelected}
                    />
                  </Form.Item>
                </>
              )}

              {featureOrder?.hasExchange && (
                <>
                  <p
                    className="text-primary font-semibold text-lg mt-7"
                    id="gift"
                  >
                    Quà đã nhận
                  </p>
                  <Form.Item noStyle dependencies={["purchases"]}>
                    {() => (
                      <Form.List name="exchanges">
                        {() => {
                          const filteredItems = selectedExchangeItems.filter(
                            (item) => item,
                          );
                          const groupedBySchemeName = _.groupBy(
                            filteredItems,
                            (item) => item?.schemeName,
                          );

                          return Object.entries(groupedBySchemeName).map(
                            ([schemeName, items]) => {
                              if (schemeName && items.length > 0) {
                                return (
                                  <Fragment key={schemeName}>
                                    <p className="text-blue font-semibold">
                                      {schemeName}
                                    </p>
                                    {items.map((item) => (
                                      <Fragment key={item?.id}>
                                        {item?.item}
                                      </Fragment>
                                    ))}
                                  </Fragment>
                                );
                              }

                              return <></>;
                            },
                          );
                        }}
                      </Form.List>
                    )}
                  </Form.Item>
                </>
              )}

              {featureOrder?.hasPhoto && (
                <>
                  <p
                    className="text-primary font-semibold text-lg pb-0 mb-0 mt-[40px]"
                    id="photoFiles"
                  >
                    Hình ảnh
                  </p>
                  <p className="font-normal text-xs text-hint mt-0 pt-0 mb-5">
                    Vui lòng sử dụng ảnh có định dạng .png, .jpg, .jpeg và có
                    dung lượng &lt;= 5mb
                  </p>

                  {featurePhotos.map((featurePhoto, index) => (
                    <FormImageUploadComponent
                      key={index}
                      label={featurePhoto.name}
                      fieldName={["photoFiles", `${featurePhoto.id}`]}
                      max={featurePhoto.maximum}
                      required={featurePhoto.minimum > 0}
                      imagesFile={editOrderQuery.data?.recordOrderPhotos
                        ?.filter(
                          (recordOrderPhoto) =>
                            recordOrderPhoto.recordPhoto.featurePhotoId ===
                            featurePhoto.id,
                        )
                        .map(
                          (recordOrderPhoto) =>
                            recordOrderPhoto.recordPhoto.image,
                        )}
                    />
                  ))}
                </>
              )}

              <div hidden={action === CURD.UPDATE}>
                <p
                  className="text-primary font-semibold text-lg pb-0 mb-0 mt-[40px]"
                  id="time"
                >
                  Thời gian ghi đơn
                </p>

                <Form.Item
                  label={"Thời gian ghi đơn (giờ 24)"}
                  name={"time"}
                  rules={[{ required: true }]}
                >
                  <TimePicker
                    format={"HH:mm"}
                    className={"w-[50%]"}
                    showNow={false}
                    disabledTime={(date: Dayjs) => {
                      const hours = date.get("hours");

                      const startArrayHours: number[] = [];
                      const startTime = dayjs(attendance?.createdAt);
                      startArrayHours.length = startTime.get("hours");

                      const endTime = dayjs(attendance?.updatedAt);
                      const endArrayHours: number[] = [];
                      endArrayHours.length =
                        24 -
                        (endTime.get("hours") === 0
                          ? 23
                          : endTime.get("hours"));

                      return {
                        disabledHours: () => [
                          ...startArrayHours.fill(0).map((_, index) => index),
                          ...endArrayHours
                            .fill(0)
                            .map((_, index) => 24 - index),
                        ],
                        disabledMinutes: () => {
                          const disabledMinutes: number[] = [];

                          if (hours === startTime.get("hours")) {
                            const startArrayMinutes: number[] = [];
                            startArrayMinutes.length =
                              startTime.get("minutes") + 1; // +1 bắt đầu
                            disabledMinutes.push(
                              ...startArrayMinutes
                                .fill(0)
                                .map((_, index) => index),
                            );
                          }

                          if (hours === endTime.get("hours")) {
                            const endArrayMinutes: number[] = [];
                            endArrayMinutes.length =
                              60 - endTime.get("minutes") + 1; // -1 kết thúc
                            disabledMinutes.push(
                              ...endArrayMinutes
                                .fill(0)
                                .map((_, index) => 60 - index),
                            );
                          }

                          return disabledMinutes;
                        },
                      };
                    }}
                  />
                </Form.Item>
              </div>
            </Form>
          </div>
        </Col>
      </Row>

      <div className="flex justify-end gap-4 py-4 rounded-b max-md:max-w-full max-md:flex-wrap pl-10 bg-[#F7F8FA] pr-10 pb-4 border-t-[1.5px] border-[#DDE1EA] border-solid border-l-0 border-r-0 border-b-0">
        <Button type="default" onClick={onClose} style={{}} loading={loading}>
          Đóng
        </Button>
        <Button
          htmlType="submit"
          type={"primary"}
          onClick={onSubmit}
          loading={loading}
        >
          {action === CURD.CREATE ? "Thêm mới" : "Cập nhật"}
        </Button>
      </div>
    </Modal>
  );
};

export default EditCustomerInformationCapturingModal;
