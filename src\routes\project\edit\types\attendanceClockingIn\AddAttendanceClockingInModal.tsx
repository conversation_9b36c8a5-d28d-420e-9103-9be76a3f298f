import {DATETIME_FORMAT} from "@/common/constant";
import {useUploadImageMutation} from "@/common/upload-image.helper";
import CustomModal from "@/components/CustomModal";
import DebounceSelect from "@/components/DebounceSelectComponent";
import FormImageUploadComponent from "@/components/formImageUploadComponent/FormImageUploadComponent";
import UserOptionComponent from "@/components/UserOptionComponent";
import {FeatureTypeEnum} from "@/routes/project/component/feature/interface";
import {useProjectBoothsQuery} from "@/routes/project/configOutlet/service";
import {ProjectEmployeeUserInterface} from "@/routes/project/employee/interface";
import {useGetEmployeeMutation} from "@/routes/project/employee/service";
import {useProjectAgenciesQuery} from "@/routes/project/general/services";
import {CloseCircleOutlined} from "@ant-design/icons";
import {DatePicker, Form, Input, Select, Tag} from "antd";
import {UploadFile} from "antd/lib";
import dayjs from "dayjs";
import {useCallback, useEffect, useMemo, useState} from "react";
import {useParams} from "react-router-dom";
import {
  useAttendanceFeaturesQuery,
  useCreateAttendanceMutation,
  useFetchuEmployeeAccessibleOutletsMutation,
} from "../../service";

const locationRegex = /^([-+]?\d{1,2}(?:\.\d+)?),\s*([-+]?\d{1,3}(?:\.\d+)?)$/;
const locationRegexMessage = 'Nhập định dạng "latitude, longitude"';

interface AddAttendanceClockingInModalProps {
  setOpen: (isOpen: boolean) => void;
  cb: () => void;
}
const AddAttendanceClockingInModal = ({
  setOpen,
  cb,
}: AddAttendanceClockingInModalProps) => {
  const projectId = parseInt(useParams().id ?? "0");

  const [form] = Form.useForm();
  const [selectedValue, setSelectedValue] =
    useState<null | ProjectEmployeeUserInterface>(null);
  const [options, setOptions] = useState<ProjectEmployeeUserInterface[]>([]);
  const projectBoothId = Form.useWatch("projectBoothId", form);
  const projectAgencyId = Form.useWatch("projectAgenyId", form);
  const inTime = Form.useWatch("inTime", form);
  const [loading, setLoading] = useState(false);

  const projectAgenciesQuery = useProjectAgenciesQuery(projectId);
  const projectBoothsQuery = useProjectBoothsQuery(projectId);
  const attendanceFeaturesQuery = useAttendanceFeaturesQuery({
    projectId,
    projectBoothId,
    at: dayjs(inTime).toISOString(),
    roleId: selectedValue ? selectedValue.role.id : undefined,
    enabled: !!projectBoothId && !!inTime && !!selectedValue,
  });
  const getEmployeeMutation = useGetEmployeeMutation(projectId);
  const uploadImageMutation = useUploadImageMutation();
  const createAttendanceMutation = useCreateAttendanceMutation(projectId);

  const configAttendanceClockingIn = useMemo(
    () =>
      attendanceFeaturesQuery.data?.find(
        (item) => item.type === FeatureTypeEnum.AttendanceClockingIn,
      ),
    [attendanceFeaturesQuery.data],
  );
  const configAttendanceClockingOut = useMemo(
    () =>
      attendanceFeaturesQuery.data?.find(
        (item) => item.type === FeatureTypeEnum.AttendanceClockingOut,
      ),
    [attendanceFeaturesQuery.data],
  );

  const fetchProjectOutletOptions = useFetchuEmployeeAccessibleOutletsMutation({
    projectId,
    enabled: !!projectAgencyId && !!selectedValue,
    id: selectedValue?.id ?? 0,
  });

  const fetchEmployeeOptions = useCallback(
    async (keyword?: string) => {
      if (keyword?.length === 0) {
        return [];
      }

      const result = await getEmployeeMutation.mutateAsync({
        keyword,
        take: 10,
        projectAgencyId,
      });

      setOptions(result.entities);

      return result.entities.map((item) => ({
        value: item.id,
        label: item.user.username,
        user: item.user,
        isAvailable: true,
      }));
    },
    [getEmployeeMutation, projectAgencyId],
  );

  const content = (
    <Form form={form} layout="vertical">
      <h3 className="text-primary">Thông tin outlet, nhân viên</h3>
      <Form.Item
        label="Agency quản lý"
        name={"projectAgenyId"}
        rules={[
          {
            required: true,
          },
        ]}
      >
        <Select
          options={projectAgenciesQuery.data?.map((item) => ({
            label: item.agency.name,
            value: item.id,
          }))}
        />
      </Form.Item>

      <Form.Item
        label="Nhân viên chấm công"
        name={"projectUserId"}
        rules={[
          {
            required: true,
          },
        ]}
      >
        {selectedValue ? (
          <Tag
            closable
            onClose={() => {
              setSelectedValue(null);
              form.resetFields(["projectUserId"]);
            }}
            className="w-full justify-between flex pt-2 pb-2 pl-3 pr-3"
            closeIcon={<CloseCircleOutlined style={{ fontSize: 14 }} />}
            style={{
              fontSize: 14,
              backgroundColor:
                typeof selectedValue === "string" ? "" : "#F0F8FF",
              borderColor: typeof selectedValue === "string" ? "" : "#C4D6FF",
            }}
          >
            {typeof selectedValue === "string" ? (
              <p>{selectedValue}</p>
            ) : (
              <UserOptionComponent
                avatarUrl={selectedValue?.user?.picture}
                name={selectedValue?.user.name}
                phone={selectedValue?.user.phone}
                email={selectedValue?.user.email}
              />
            )}
          </Tag>
        ) : (
          <DebounceSelect
            disabled={!projectAgencyId}
            autoClearSearchValue
            showSearch
            fetchOptions={fetchEmployeeOptions}
            style={{ width: "100%" }}
            optionRender={(option) => {
              if (option.data.user) {
                return (
                  <UserOptionComponent
                    avatarUrl={option.data.user?.imageUrl}
                    name={option.data.user?.name}
                    phone={option.data.user?.phone}
                    email={option.data.user?.email}
                  />
                );
              }
              return option.label;
            }}
            onSelect={({ value }) => {
              if (typeof value === "number") {
                const option = options.find((item) => item.id === value);
                if (option) {
                  setSelectedValue(option);
                }
              } else setSelectedValue(value);
            }}
          />
        )}
      </Form.Item>

      <Form.Item
        label="Outlet"
        name={"projectOutletId"}
        rules={[
          {
            required: true,
          },
        ]}
      >
        <DebounceSelect
          fetchOptions={fetchProjectOutletOptions}
          showSearch
          labelInValue={false}
          placeholder={"Mã/ Tên outlet"}
          disabled={!projectAgencyId}
        />
      </Form.Item>

      <Form.Item
        label="Booth"
        name={"projectBoothId"}
        rules={[
          {
            required: true,
          },
        ]}
      >
        <Select
          options={projectBoothsQuery.data?.entities?.map((item) => ({
            label: item.name,
            value: item.id,
          }))}
        />
      </Form.Item>

      <h3 className="text-primary">Chấm công vào</h3>
      <Form.Item
        label={"Thời gian vào"}
        rules={[{ required: true }]}
        name={"inTime"}
      >
        <DatePicker
          showTime
          format={DATETIME_FORMAT}
          style={{ width: "100%" }}
          allowClear={false}
        />
      </Form.Item>

      {configAttendanceClockingIn?.featureAttendance.isLocationRequired && (
        <Form.Item
          label="Vị trí"
          name={"locationIn"}
          rules={[
            { required: true },
            { pattern: locationRegex, message: locationRegexMessage },
          ]}
        >
          <Input />
        </Form.Item>
      )}

      {configAttendanceClockingIn?.featureAttendance.isPhotoRequired && (
        <FormImageUploadComponent
          label="Hình chấm công vào"
          fieldName={["photoFiles", `in`]}
          max={1}
          required={true}
          requiredOnForm
        />
      )}

      <h3 className="text-primary">Chấm công ra</h3>
      <Form.Item
        label={"Thời gian ra"}
        rules={[
          { required: true },
          ({ getFieldValue }) => ({
            validator(_, value) {
              const startDate = getFieldValue("inTime");
              if (!startDate) {
                return Promise.reject(new Error("Chưa có thời gian vào."));
              }
              if (value && dayjs(value) >= dayjs(startDate)) {
                return Promise.resolve();
              }

              return Promise.reject(
                new Error("Thời gian ra phải lớn hơn thời gian vào."),
              );
            },
          }),
        ]}
        name={"outTime"}
      >
        <DatePicker
          showTime
          format={DATETIME_FORMAT}
          style={{ width: "100%" }}
          allowClear={false}
          minDate={inTime}
          maxDate={inTime}
        />
      </Form.Item>

      {configAttendanceClockingOut?.featureAttendance.isLocationRequired && (
        <Form.Item
          label="Vị trí"
          name={"locationOut"}
          rules={[
            { required: true },
            { pattern: locationRegex, message: locationRegexMessage },
          ]}
        >
          <Input />
        </Form.Item>
      )}

      {configAttendanceClockingOut?.featureAttendance.isPhotoRequired && (
        <FormImageUploadComponent
          label="Hình chấm công ra"
          fieldName={["photoFiles", `out`]}
          max={1}
          required={true}
          requiredOnForm
        />
      )}
    </Form>
  );

  useEffect(() => {
    if (attendanceFeaturesQuery.isFetching) {
      form.resetFields([
        ["photoFiles", "in"],
        ["photoFiles", "out"],
      ]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [attendanceFeaturesQuery.isFetching]);

  const onCancel = useCallback(() => {
    setOpen(false);
  }, [setOpen]);

  const onConfim = useCallback(async () => {
    try {
      setLoading(true);

      const data = await form.validateFields();
      const { projectOutletId, locationIn, locationOut, outTime } = data;

      const photoFiles: { fileList: UploadFile[] }[] = data.photoFiles ?? {};

      const photosData = [];
      for (const [type, photoFile] of Object.entries(photoFiles ?? {})) {
        if (photoFile) {
          const { fileList } = photoFile;
          for (const file of fileList) {
            if (file.originFileObj) {
              const result = await uploadImageMutation.mutateAsync(
                file.originFileObj,
              );
              if (result?.id)
                photosData.push({
                  type,
                  imageId: result.id,
                });
            }
          }
        }
      }
      const [latitudeIn, longitudeIn] = locationIn.split(",");
      const [latitudeOut, longitudeOut] = locationOut.split(",");

      await createAttendanceMutation.mutateAsync({
        projectAgencyId,
        projectBoothId,
        projectOutletId,
        projectEmployeeUserId: selectedValue?.id ?? 0,
        in: {
          latitude: latitudeIn,
          longitude: longitudeIn,
          imageId:
            photosData.find((item) => item.type === "in")?.imageId ?? undefined,
          deviceTime: dayjs(inTime).toISOString(),
        },
        out: {
          latitude: latitudeOut,
          longitude: longitudeOut,
          imageId:
            photosData.find((item) => item.type === "out")?.imageId ??
            undefined,
          deviceTime: dayjs(outTime).toISOString(),
        },
      });

      cb();
      onCancel();
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (e: any) {
      console.error(e);
    } finally {
      setLoading(false);
    }
  }, [
    cb,
    createAttendanceMutation,
    form,
    inTime,
    onCancel,
    projectAgencyId,
    projectBoothId,
    selectedValue?.id,
    uploadImageMutation,
  ]);

  return (
    <CustomModal
      title={"Thêm chấm công"}
      isOpen={true}
      content={content}
      onCancel={onCancel}
      onConfirm={onConfim}
      confirmLoading={loading}
    />
  );
};

export default AddAttendanceClockingInModal;
