import { useApp } from "@/UseApp";
import { RoleInterface } from "@project/role/interface";
import { Switch } from "antd";
import { useCallback } from "react";
import { useUpdateRolesOnComponentFeaturesMutation } from "./service";

interface RoleSwitchProps {
  role: RoleInterface;
  projectId: number;
  projectComponentId: number;
  componentFeatureId: number;
  employeeRoles: RoleInterface[];
  cb: () => void;
}
const RoleSwitch = ({
  role,
  projectId,
  projectComponentId,
  componentFeatureId,
  employeeRoles,
  cb,
}: RoleSwitchProps) => {
  const { showNotification } = useApp();

  const updateRolesOnComponentFeaturesMutation =
    useUpdateRolesOnComponentFeaturesMutation(
      projectId,
      projectComponentId,
      componentFeatureId,
    );

  const handleSwitchChange = useCallback(
    async (role: RoleInterface, value: boolean) => {
      try {
        await updateRolesOnComponentFeaturesMutation.mutateAsync(
          employeeRoles
            ?.filter((item) => (item.id === role.id ? value : item.isEnabled))
            .map((item) => item.id) ?? [],
        );

        showNotification({
          type: "success",
          message: "Cập nhật nhân viên sử dụng chức năng thành công.",
        });

        cb();
      } catch (e) {
        console.error(e);

        showNotification({
          type: "error",
          message: "Cập nhật nhân viên sử dụng chức năng thất bại.",
        });

        cb();
      }
    },
    [
      cb,
      employeeRoles,
      showNotification,
      updateRolesOnComponentFeaturesMutation,
    ],
  );

  return (
    <Switch
      value={role.isEnabled}
      onChange={async (value: boolean) => {
        await handleSwitchChange(role, value);
      }}
      disabled={role.isNotAllowed}
      title={role.isNotAllowed ? "Chức năng chỉ áp dụng cho trưởng nhóm" : ""}
      loading={updateRolesOnComponentFeaturesMutation.isPending}
    />
  );
};

export default RoleSwitch;
