import InnerContainer from "@/components/InnerContainer/InnerContainer";
import { LoadingOutlined } from "@ant-design/icons";
import { Col, Row, Tabs } from "antd";
import { useProjectAgenciesQuery } from "../general/services";
import ToolEditConfigTab from "./ToolEditConfigTab";

interface ConfigToolSectionProps {
  projectId: number;
}

const ConfigToolSection = ({ projectId }: ConfigToolSectionProps) => {
  const projectAgenciesQuery = useProjectAgenciesQuery(projectId);

  return (
    <InnerContainer className="mt-5">
      <Row>
        <Col md={8}>
          <h4>Tool chỉnh dữ liệu</h4>
          <p className="text-gray-400 max-w-[350px]">
            Cho phép kích hoạt và giới hạn thời gian sử dụng tool chỉnh sửa dữ
            liệu
          </p>
        </Col>

        <Col md={16}>
          {(projectAgenciesQuery.isLoading ||
            projectAgenciesQuery.isFetching) && <LoadingOutlined />}
          <Tabs
            items={projectAgenciesQuery.data?.map((projectAgency) => ({
              key: projectAgency.id.toString(),
              label: projectAgency.agency.name,
              children: (
                <ToolEditConfigTab
                  key={projectAgency.id}
                  projectId={projectId}
                  projectAgencyId={projectAgency.id}
                />
              ),
            }))}
          />
        </Col>
      </Row>
    </InnerContainer>
  );
};

export default ConfigToolSection;
