import { useApp } from "@/UseApp";
import {
  DeleteOutlined,
  EllipsisOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import { Al<PERSON>, Button, Dropdown, Table } from "antd";
import { useEffect, useState } from "react";
import {
  getOrderLimitRestrictionLabel,
  OrderLimitRestrictionEnum,
  OrderLimitRestrictionEnumToLabel,
  OrderLimitTypeEnum,
  OrderLimitTypeEnumToLabel,
} from "../../../../interface";
import {
  useDeleteFeatureOrderLimitMutation,
  useFeatureOrderLimitsQuery,
} from "../../../../service";
import InputNumberLimitCustomer from "./InputNumberLimitCustomer";
import LimitCustomerModal from "./LimitCustomerModal";

interface LimitCustomerTabProps {
  componentFeatureId: number;
  type: OrderLimitTypeEnum;
  activeKey: string;
}

const LimitCustomerTab = ({
  componentFeatureId,
  type,
  activeKey,
}: LimitCustomerTabProps) => {
  const { showNotification, openDeleteModal } = useApp();

  const [open, setOpen] = useState(false);

  const featureOrderLimitsQuery = useFeatureOrderLimitsQuery(
    componentFeatureId,
    { take: 50, type },
  );

  const deleteFeatureOrderLimitMutation =
    useDeleteFeatureOrderLimitMutation(componentFeatureId);

  useEffect(() => {
    if (activeKey === type.toString()) {
      featureOrderLimitsQuery.refetch();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeKey]);

  return (
    <>
      <div className="inline-block">
        <Alert
          showIcon
          type="warning"
          message={`App cần ghi đơn online để sử dụng chức năng giới hạn ${OrderLimitTypeEnumToLabel[type]} theo khách chính xác nhất`}
        />
      </div>

      <Table
        loading={
          featureOrderLimitsQuery.isFetching ||
          featureOrderLimitsQuery.isLoading ||
          featureOrderLimitsQuery.isFetching
        }
        pagination={false}
        className="mt-6"
        dataSource={featureOrderLimitsQuery.data?.entities ?? []}
        rowKey={"id"}
        columns={[
          {
            title: "Loại giới hạn",
            dataIndex: "restriction",
            render: (restriction: OrderLimitRestrictionEnum) =>
              getOrderLimitRestrictionLabel(type, restriction),
          },
          {
            title: "Giới hạn ngày",
            dataIndex: "periodDays",
            render: (periodDays: number, { id }) => (
              <InputNumberLimitCustomer
                initValue={periodDays}
                componentFeatureId={componentFeatureId}
                id={id}
                fieldName={"periodDays"}
              />
            ),
            align: "right",
          },
          {
            title: `Giới hạn ${OrderLimitTypeEnumToLabel[type]}`,
            dataIndex: "maximum",
            render: (maximum: number, { id }) => (
              <InputNumberLimitCustomer
                initValue={maximum}
                componentFeatureId={componentFeatureId}
                id={id}
                fieldName={"maximum"}
              />
            ),
            align: "right",
          },
          {
            title: "",
            className: "min-w-[200px]",
            align: "right",
            render: (_, record) => (
              <Dropdown
                menu={{
                  items: [
                    {
                      key: "delete",
                      icon: <DeleteOutlined />,
                      label: "Xóa",
                      onClick: () => {
                        openDeleteModal({
                          title: `Xóa giới hạn ${OrderLimitTypeEnumToLabel[type]} theo khách`,
                          content: (
                            <>
                              <div>
                                <span className="font-semibold">
                                  Xóa loại giới hạn:
                                </span>{" "}
                                {
                                  OrderLimitRestrictionEnumToLabel[
                                    record.restriction
                                  ]
                                }
                              </div>
                              <div>
                                <span className="font-semibold">
                                  Giới hạn ngày:
                                </span>{" "}
                                {record.periodDays}
                              </div>
                              <div>
                                <span className="font-semibold">
                                  Giới hạn quà:
                                </span>{" "}
                                {record.maximum}
                              </div>
                            </>
                          ),
                          onDelete: async () => {
                            await deleteFeatureOrderLimitMutation.mutateAsync(
                              record.id,
                            );

                            showNotification({
                              type: "success",
                              message: `Xóa giới hạn ${OrderLimitTypeEnumToLabel[type]} thành công`,
                            });

                            featureOrderLimitsQuery.refetch();
                          },
                          deleteText: "Xóa",
                          onCancel: () => {},
                        });
                      },
                    },
                  ],
                }}
              >
                <Button type="link">
                  <EllipsisOutlined />
                </Button>
              </Dropdown>
            ),
          },
        ]}
      />

      <p
        onClick={() => setOpen(true)}
        className={
          "text-[#1D8EE6] cursor-pointer p-0 m-0 mt-4 font-semibold inline-block"
        }
      >
        <PlusOutlined color="#1D8EE6" /> Thêm loại giới hạn
      </p>

      {open && (
        <LimitCustomerModal
          open={open}
          componentFeatureId={componentFeatureId}
          cb={() => {
            setOpen(false);
            showNotification({
              type: "success",
              message: `Thêm loại giới hạn ${OrderLimitTypeEnumToLabel[type]} thành công`,
            });
            featureOrderLimitsQuery.refetch();
          }}
          onCancel={() => setOpen(false)}
          type={type}
        />
      )}
    </>
  );
};

export default LimitCustomerTab;
