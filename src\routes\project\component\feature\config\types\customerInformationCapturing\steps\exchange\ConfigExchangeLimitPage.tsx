import { Tabs } from "antd";
import { useParams } from "react-router-dom";

import React, { useState } from "react";
import { OrderLimitTypeEnum } from "../../interface";

const LimitOrderTab = React.lazy(
  () => import("./limitTabs/limitOrderTab/LimitOrdertTab"),
);

const LimitCustomerTab = React.lazy(
  () => import("./limitTabs/limitCustomerTab/LimitCustomerTab"),
);

const LimitLuckyDrawTab = React.lazy(
  () => import("./limitTabs/limitLuckyDrawTab/LimitLuckyDrawTab"),
);
/**
 * Cấu hình giới hạn đổi quà của scheme
 * @constructor
 */
export default function ConfigExchangeLimitPage() {
  const projectId = parseInt(useParams().id ?? "0");
  const componentFeatureId = parseInt(useParams().componentFeatureId ?? "0");
  const [activeKey, setActiveKey] = useState("order");

  return (
    <Tabs
      type="card"
      activeKey={activeKey}
      onChange={setActiveKey}
      items={[
        {
          key: "order",
          label: "Giới hạn quà theo đơn",
          children: (
            <LimitOrderTab
              componentFeatureId={componentFeatureId}
              activeKey={activeKey}
            />
          ),
        },
        {
          key: OrderLimitTypeEnum.EXCHANGE,
          label: "Giới hạn quà theo khách",
          children: (
            <LimitCustomerTab
              componentFeatureId={componentFeatureId}
              type={OrderLimitTypeEnum.EXCHANGE}
              activeKey={activeKey}
            />
          ),
        },
        {
          key: OrderLimitTypeEnum.LUCKY_DRAW,
          label: "Giới hạn vòng quay",
          children: (
            <LimitLuckyDrawTab
              componentFeatureId={componentFeatureId}
              activeKey={activeKey}
              projectId={projectId}
            />
          ),
        },
      ]}
    />
  );
}
