import CustomModal from "@/components/CustomModal";
import FilterClassicComponent from "@/components/FilterClassicComponent";
import { ProjectOutletInterface } from "@project/outlet/interface.ts";
import { OosGroupInterface } from "../../interface";

import OutletSearchFilterContent from "@/components/outletSearchFilterContent/OutletSearchFilterContent";
import renderStatusOnTopCell from "@/components/renderStatusOnTopCell";
import { useUrlFiltersWithQuery } from "@/hooks/useUrlFiltersWithQuery";
import { ProjectAgencyChannelInterface } from "@/routes/project/interface";
import { SubChannelInterface } from "@/routes/subChannel/interface";
import { useApp } from "@/UseApp";
import { Form, Table } from "antd";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import {
  useAddOosGroupOutletMutation,
  useOosGroupOutletAvailablesQuery,
} from "./service";

interface OutOfStockStatusOutletAddModalProps {
  isOpen: boolean;
  oosGroup: OosGroupInterface | undefined;
  projectId: number;
  componentFeatureId: number;
  onCancelCb: () => void;
  cb: () => void;
}

const OutOfStockStatusOutletAddModal = ({
  isOpen,
  oosGroup,
  projectId,
  componentFeatureId,
  onCancelCb,
  cb,
}: OutOfStockStatusOutletAddModalProps) => {
  const { showNotification } = useApp();

  const [searchForm] = Form.useForm();

  const [selectedOutletKeys, setSelectedOutletKeys] = useState<React.Key[]>([]);
  const {
    query: { data, isFetching },
    handleSearch,
    getPaginationProps,
    handleReset,
  } = useUrlFiltersWithQuery<ProjectOutletInterface>({
    formInstance: searchForm,
    useQueryHook: useOosGroupOutletAvailablesQuery,
    queryParams: [componentFeatureId],
    options: {
      urlSync: {
        enabled: false,
      },
    },
  });

  const addOosGroupOutletMutation = useAddOosGroupOutletMutation(
    componentFeatureId,
    oosGroup?.id ?? 0,
  );

  const pagination = useMemo(() => getPaginationProps(), [getPaginationProps]);
  const rowSelection = {
    selectedRowKeys: selectedOutletKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedOutletKeys(newSelectedRowKeys);
    },
    getCheckboxProps: (record: ProjectOutletInterface) => {
      return {
        disabled: !record.isAvailable || !record.isActive,
      };
    },
  };

  const content = (
    <>
      <p>
        Nhóm sản phẩm đang phân bổ outlet:{" "}
        <span className={"text-primary font-semibold"}>{oosGroup?.name}</span>
      </p>

      <FilterClassicComponent
        searchHandler={handleSearch}
        searchForm={searchForm}
        content={
          <OutletSearchFilterContent
            open={isOpen}
            searchForm={searchForm}
            projectId={projectId}
          />
        }
        className={"mb-5"}
      />

      <Table
        scroll={{ x: "max-content", y: "70vh" }}
        rowKey={(o) => o.id}
        dataSource={data?.entities}
        pagination={pagination}
        loading={isFetching}
        rowSelection={rowSelection}
        columns={[
          {
            title: "Mã outlet",
            dataIndex: "code",
            className: "min-w-[100px]",
          },
          {
            title: "Tên outlet",
            dataIndex: "name",
            className: "min-w-[100px]",
            render: (name: string, record: ProjectOutletInterface) => {
              const { isActive, isAvailable, featureOosGroupOutlets } = record;
              let isInCurrentScheme = false;

              if (
                featureOosGroupOutlets &&
                featureOosGroupOutlets.length > 0 &&
                featureOosGroupOutlets[0].featureOosGroup.id === oosGroup?.id
              ) {
                isInCurrentScheme = true;
              }

              return (
                <>
                  {isInCurrentScheme &&
                    renderStatusOnTopCell(
                      "Đã thêm vào nhóm hiện tại",
                      "#008916",
                      "#E5F5E7",
                    )}

                  {!isAvailable &&
                    !isInCurrentScheme &&
                    renderStatusOnTopCell(
                      "Đã thêm vào nhóm khác",
                      "#393939",
                      "#F5F5F5",
                    )}

                  {!isActive &&
                    renderStatusOnTopCell(
                      "Ngừng hoạt động",
                      "#DF3C3C",
                      "#FFEEEE",
                    )}
                  <p>{name}</p>
                </>
              );
            },
          },
          {
            title: "Số nhà",
            dataIndex: "houseNumber",
            className: "min-w-[100px]",
          },
          {
            title: "Tên đường",
            dataIndex: "streetName",
            className: "min-w-[100px]",
          },
          {
            title: "Tỉnh/ TP",
            dataIndex: "province",
            render: (province) => province?.name,
            className: "min-w-[100px]",
          },
          {
            title: "Quận/ Huyện",
            dataIndex: "district",
            className: "min-w-[100px]",
            render: (district) => district?.name,
          },
          {
            title: "Phường/ Xã",
            dataIndex: "ward",
            className: "min-w-[100px]",
            render: (ward) => ward?.name,
          },
          {
            title: "Kênh",
            dataIndex: "projectAgencyChannel",
            className: "min-w-[100px]",
            render: (projectAgencyChannel: ProjectAgencyChannelInterface) =>
              projectAgencyChannel?.channel.name,
          },
          {
            title: "Nhóm",
            className: "min-w-[100px]",
            dataIndex: "subChannel",
            render: (subChannel: SubChannelInterface) => subChannel?.name,
          },
          {
            title: "Tên nhóm item",
            className: "min-w-[100px]",
            render: (_, record: ProjectOutletInterface) => {
              const { featureOosGroupOutlets } = record;
              if (featureOosGroupOutlets?.[0]?.featureOosGroup.name) {
                return featureOosGroupOutlets[0].featureOosGroup.name;
              }
              return "";
            },
          },
        ]}
      />
    </>
  );

  const onCancel = useCallback(async () => {
    setSelectedOutletKeys([]);
    searchForm.resetFields();
    handleReset();
    onCancelCb();
  }, [handleReset, onCancelCb, searchForm]);

  const newAvailableSelectedOutletKeys = useMemo(() => {
    return (
      data?.entities
        .filter(
          (featureNumericSheetOutletAvailable) =>
            selectedOutletKeys.includes(
              featureNumericSheetOutletAvailable.id,
            ) && featureNumericSheetOutletAvailable.isAvailable,
        )
        .map((item) => item.id) ?? []
    );
  }, [selectedOutletKeys, data?.entities]);

  const confirm = useCallback(async () => {
    if (oosGroup) {
      await addOosGroupOutletMutation.mutateAsync({
        projectOutletIds: newAvailableSelectedOutletKeys,
      });

      showNotification({
        message: "Thêm outlet vào nhóm item thành công",
        type: "success",
      });
      cb();
      onCancel();
    }
  }, [
    addOosGroupOutletMutation,
    cb,
    newAvailableSelectedOutletKeys,
    onCancel,
    oosGroup,
    showNotification,
  ]);

  useEffect(() => {
    setSelectedOutletKeys(
      data?.entities
        .filter(
          (projectOutlet) =>
            projectOutlet.featureOosGroupOutlets?.[0]?.featureOosGroup?.id ===
            oosGroup?.id,
        )
        .map((item) => item.id) ?? [],
    );
  }, [data?.entities, oosGroup?.id]);

  return (
    <CustomModal
      title={"Thêm outlet vào nhóm sản phẩm"}
      isOpen={isOpen}
      content={content}
      width={1200}
      confirmText={`Thêm ${newAvailableSelectedOutletKeys.length} outlet vào nhóm sản phẩm`}
      confirmDisable={newAvailableSelectedOutletKeys.length === 0}
      onConfirm={confirm}
      onCancel={onCancel}
    />
  );
};

export default OutOfStockStatusOutletAddModal;
