import { useMutation, useQuery } from "@tanstack/react-query";
import { useApp } from "@/UseApp";
import { AbstractFilterInterface } from "@/common/interface";
import { AppContextInterface } from "@/interface";
import { FeatureCustomerInterface } from "@project/component/feature/config/types/customerInformationCapturing/interface";
import { RecordOrderInterface } from "./interface";

export const getReportOrders = (
  axiosGet: AppContextInterface["axiosGet"],
  projectId: number,
  componentFeatureId: number,
  filter?: object & AbstractFilterInterface,
) =>
  axiosGet<
    {
      entities: RecordOrderInterface[];
      count: number;
    },
    unknown
  >(
    `/projects/${projectId}/report/features/${componentFeatureId}/orders`,
    filter,
  );

export const useReportOrdersQuery = (
  projectId: number,
  componentFeatureId: number,
  filter?: object & AbstractFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["reportOrders", projectId, componentFeatureId, filter],
    queryFn: () =>
      getReportOrders(axiosGet, projectId, componentFeatureId, filter),
  });
};

export const getFeatureCustomers = (
  axiosGet: AppContextInterface["axiosGet"],
  projectId: number,
  componentFeatureId: number,
) =>
  axiosGet<FeatureCustomerInterface[], unknown>(
    `/projects/${projectId}/report/features/${componentFeatureId}/orders/feature-customers`,
  );

export const useGetReportOrdersMutation = (
  projectId: number,
  componentFeatureId: number,
) => {
  const { axiosGet } = useApp();

  return useMutation({
    mutationKey: ["getReportOrders", projectId, componentFeatureId],
    mutationFn: (filter: object & AbstractFilterInterface) =>
      getReportOrders(axiosGet, projectId, componentFeatureId, filter),
  });
};

export const useGetFeatureCustomersMutation = (
  projectId: number,
  componentFeatureId: number,
) => {
  const { axiosGet } = useApp();

  return useMutation({
    mutationKey: ["getFeatureCustomers", projectId, componentFeatureId],
    mutationFn: () =>
      getFeatureCustomers(axiosGet, projectId, componentFeatureId),
  });
};
