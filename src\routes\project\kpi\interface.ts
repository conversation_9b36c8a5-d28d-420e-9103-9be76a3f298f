import { AbstractEntityInterface } from "@/common/interface";
import { ProvinceInterface } from "@location/interface";
import { ChannelInterface } from "../../channel/interface";
import { ProjectInterface } from "../interface";
import { ProjectOutletInterface } from "../outlet/interface";

export enum KPIScopeEnum {
  CHANNEL = "channel",
  AGENCY = "agecny",
  PROVINCE = "province",
  OUTLET = "outlet",
}

export enum ItemKpiTypeEnum {
  SAMPLING = "sampling",
  GAME = "game",
  PRODUCT = "product",
  GIFT = "gift",
  HIT = "hit",
  ORDER = "order",
  SESSION = "session",
  SALES_VOLUME = "salesVolume",
  SALES_REVENUE = "salesRevenue",
}

export const ITEM_KPI_TYPE_LABEL = {
  [ItemKpiTypeEnum.SAMPLING]: "KPI sampling (dry)",
  [ItemKpiTypeEnum.GAME]: "Quà tặng",
  [ItemKpiTypeEnum.PRODUCT]: "<PERSON><PERSON> sản phẩm",
  [ItemKpiTypeEnum.GIFT]: "Quà tặng",
};

export interface ProjectKPIItemInterface {
  projectProductId?: number;
  projectItemId?: number;
  type: ItemKpiTypeEnum;
  kpi: string;
}

export interface KPIInterface extends AbstractEntityInterface {
  project?: ProjectInterface;
  channel?: ChannelInterface;
  province?: ProvinceInterface;
  hit: string;
  order: string;
  session: string;
  salesRevenue: string;
  salesVolume: string;
  projectKpiItems?: ProjectKPIItemInterface[];
  projectOutlet?: ProjectOutletInterface;
}

export enum PropagationModalType {
  CHANNEL = "channel",
  PROVINCE = "province",
  OUTLET = "outlet",
}

export const PROPAGATION_MODAL_TYPE_LABELS = {
  [PropagationModalType.CHANNEL]: "Kênh",
  [PropagationModalType.PROVINCE]: "Tỉnh",
  [PropagationModalType.OUTLET]: "Cửa hàng",
};

export interface KpiRollingInterface extends AbstractEntityInterface {
  province: ProvinceInterface;
  channel: ChannelInterface;
  type: ItemKpiTypeEnum.HIT | ItemKpiTypeEnum.SESSION;
  targetName: string;
  targetDate: string;
  targetKpi: number;
}

export enum PeriodTypeEnum {
  DAILY = "daily",
  WEEKLY = "weekly",
  MONTHLY = "monthly",
  QUARTERLY = "quarterly",
  YEARLY = "yearly",
  PROJECT_LIFECYCLE = "project_lifecycle",
}
