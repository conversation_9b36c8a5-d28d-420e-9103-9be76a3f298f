import CustomModal from "@/components/CustomModal";
import FilterClassicComponent from "@/components/FilterClassicComponent";
import OutletSearchFilterContent from "@/components/outletSearchFilterContent/OutletSearchFilterContent";
import { useUrlFiltersWithQuery } from "@/hooks/useUrlFiltersWithQuery";
import { useApp } from "@/UseApp";
import { Form, Table } from "antd";
import { useCallback, useMemo, useState } from "react";
import {
  FeatureNumericSheetInterface,
  FeatureNumericSheetOutletInterface,
} from "../../interface";
import {
  useDeleteNumericSheetOutletMutation,
  useNumericSheetOutletsQuery,
} from "../../service";

interface NumericSheetOutletViewModalProps {
  isOpen: boolean;
  projectId: number;
  componentFeatureId: number;
  selectedFeatureNumericSheet: FeatureNumericSheetInterface | undefined;
  onCancelCb: () => void;
}

const NumericSheetOutletViewModal = ({
  isOpen,
  projectId,
  componentFeatureId,
  selectedFeatureNumericSheet,
  onCancelCb,
}: NumericSheetOutletViewModalProps) => {
  const { showNotification } = useApp();

  const [searchForm] = Form.useForm();

  const [selectedOutletKeys, setSelectedOutletKeys] = useState<React.Key[]>([]);
  const {
    query: { data, isFetching },
    handleSearch,
    getPaginationProps,
    handleReset,
  } = useUrlFiltersWithQuery<FeatureNumericSheetOutletInterface>({
    formInstance: searchForm,
    useQueryHook: useNumericSheetOutletsQuery,
    queryParams: [componentFeatureId, selectedFeatureNumericSheet?.id ?? 0],
    options: {
      urlSync: {
        enabled: false,
      },
    },
  });

  const deleteNumericSheetOutletMutation = useDeleteNumericSheetOutletMutation(
    componentFeatureId,
    selectedFeatureNumericSheet?.id ?? 0,
  );

  const pagination = useMemo(() => getPaginationProps(), [getPaginationProps]);
  const rowSelection = {
    selectedRowKeys: selectedOutletKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedOutletKeys(newSelectedRowKeys);
    },
  };

  const content = (
    <>
      <p>
        Nhóm item đang phân bổ outlet:{" "}
        <span className={"text-primary font-semibold"}>
          {selectedFeatureNumericSheet?.name}
        </span>
      </p>

      <FilterClassicComponent
        searchHandler={handleSearch}
        searchForm={searchForm}
        content={
          <OutletSearchFilterContent
            open={isOpen}
            searchForm={searchForm}
            projectId={projectId}
          />
        }
        className={"mb-5"}
      />

      <Table
        scroll={{ x: "max-content", y: "70vh" }}
        rowKey={(o) => o.id}
        dataSource={data?.entities}
        pagination={pagination}
        loading={isFetching}
        rowSelection={rowSelection}
        columns={[
          {
            title: "Mã outlet",
            className: "min-w-[100px]",
            render: (_, record) => record.projectOutlet.code,
          },
          {
            title: "Tên outlet",
            className: "min-w-[100px]",
            render: (_, record) => record.projectOutlet.name,
          },
          {
            title: "Số nhà",
            className: "min-w-[100px]",
            render: (_, record) => record.projectOutlet.houseNumber,
          },
          {
            title: "Tên đường",
            render: (_, record) => record.projectOutlet.streetName,
            className: "min-w-[100px]",
          },
          {
            title: "Tỉnh/ TP",
            render: (_, record) => record.projectOutlet?.province?.name,
            className: "min-w-[100px]",
          },
          {
            title: "Quận/ Huyện",
            render: (_, record) => record.projectOutlet?.district?.name,
            className: "min-w-[100px]",
          },
          {
            title: "Phường/ Xã",
            className: "min-w-[100px]",
            render: (_, record) => record.projectOutlet?.ward?.name,
          },
          {
            title: "Kênh",
            className: "min-w-[100px]",
            render: (_, record) =>
              record.projectOutlet?.projectAgencyChannel?.channel?.name,
          },
          {
            title: "Nhóm",
            className: "min-w-[100px]",
            render: (_, record) => record.projectOutlet?.subChannel?.name,
          },
        ]}
      />
    </>
  );

  const onCancel = useCallback(async () => {
    setSelectedOutletKeys([]);
    searchForm.resetFields();
    handleReset();
    onCancelCb();
  }, [handleReset, onCancelCb, searchForm]);

  const confirm = useCallback(async () => {
    if (selectedFeatureNumericSheet) {
      await deleteNumericSheetOutletMutation.mutateAsync({
        featureNumericSheetOutletIds: selectedOutletKeys.map(
          (key) => key as number,
        ),
      });

      showNotification({
        message: "Xóa outlet khỏi nhóm item thành công",
        type: "success",
      });
      onCancel();
    }
  }, [
    deleteNumericSheetOutletMutation,
    onCancel,
    selectedFeatureNumericSheet,
    selectedOutletKeys,
    showNotification,
  ]);

  return (
    <CustomModal
      title={"Danh sách outlet bên trong nhóm item"}
      isOpen={isOpen}
      content={content}
      width={1200}
      confirmText={`Xóa ${selectedOutletKeys.length} outlet khỏi nhóm item`}
      confirmDisable={selectedOutletKeys.length === 0}
      onConfirm={confirm}
      onCancel={onCancel}
    />
  );
};

export default NumericSheetOutletViewModal;
