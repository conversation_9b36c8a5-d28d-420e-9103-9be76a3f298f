import useDeviceType from "@/hooks/useDeviceType";
import { CloseOutlined } from "@ant-design/icons";
import { Button, Modal, ModalProps } from "antd";
import React, { useCallback, useMemo } from "react";
import { useApp } from "../UseApp";

interface CustomModalProps extends ModalProps {
  readonly title: string | React.ReactNode;
  readonly isOpen: boolean;
  readonly setIsOpen?: (setIsOpen: boolean) => void;
  readonly content: React.ReactNode;
  readonly onConfirm?: () => void;
  readonly onCancel?: () => void;
  readonly btnCloseHandler?: () => void;
  readonly confirmDisable?: boolean;
  readonly confirmText?: string;
  readonly cancelText?: string;
  readonly hideConfirm?: boolean;
  readonly confirmLoading?: boolean;
}
const CustomModal = ({
  isOpen,
  setIsOpen,
  title,
  content,
  onConfirm,
  onCancel: onCancelCb,
  confirmDisable,
  confirmText = "Thêm",
  cancelText = "Đóng",
  hideConfirm = false,
  confirmLoading,
  ...props
}: Readonly<CustomModalProps>) => {
  const { loading: appLoading } = useApp();
  const isMobile = useDeviceType();

  const onClose = useCallback(() => {
    if (setIsOpen) {
      setIsOpen(false);
    }
    if (onCancelCb) {
      onCancelCb();
    }
  }, [onCancelCb, setIsOpen]);

  const loading = useMemo(
    () => appLoading || confirmLoading,
    [appLoading, confirmLoading],
  );

  return (
    <Modal
      open={isOpen}
      footer={null}
      closeIcon={null}
      styles={{ content: { padding: 0 } }}
      {...props}
    >
      <div className={isMobile ? "px-3 pt-2" : `pl-10 pr-10 pt-3`}>
        <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
          <h2 className="text-2xl font-semibold mb-5">{title}</h2>
          <div className="pt-5">
            <Button
              type="text"
              onClick={onClose}
              size="large"
              icon={<CloseOutlined />}
              className={"text-[#393939]"}
              loading={loading}
            />
          </div>
        </div>

        {content}
      </div>

      <div className="flex justify-end gap-4 py-4 rounded max-md:max-w-full max-md:flex-wrap pl-10 bg-[#F7F8FA] pr-10 pb-4">
        <Button htmlType="button" loading={loading} onClick={onClose}>
          {cancelText}
        </Button>
        {!hideConfirm && (
          <Button
            htmlType="submit"
            type={"primary"}
            loading={loading}
            disabled={confirmDisable}
            onClick={onConfirm}
          >
            {confirmText}
          </Button>
        )}
      </div>
    </Modal>
  );
};
export default CustomModal;
