import { useApp } from "@/UseApp";
import { useQuery } from "@tanstack/react-query";
import { DashboardFilterInterface } from "../../interface";
import { InventoryDataInterface } from "./interface";

export const useInventoryQuery = (
  projectId: number,
  dashboardId: number,
  filter?: DashboardFilterInterface & {
    controlBy: "region" | "leader" | "province" | "channel" | "brand";
  },
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["inventory", projectId, dashboardId, filter],
    queryFn: () =>
      axiosGet<InventoryDataInterface[], unknown>(
        `/projects/${projectId}/dashboards/${dashboardId}/charts/inventory`,
        filter,
      ),
  });
};

export const useInventoryDailyQuery = (
  projectId: number,
  dashboardId: number,
  filter?: DashboardFilterInterface & {
    controlBy: "region" | "leader" | "province" | "channel" | "brand";
  },
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["inventoryDaily", projectId, dashboardId, filter],
    queryFn: () =>
      axiosGet<
        {
          date: string;
          total: number;
        }[],
        unknown
      >(
        `/projects/${projectId}/dashboards/${dashboardId}/charts/inventory-daily`,
        filter,
      ),
  });
};
