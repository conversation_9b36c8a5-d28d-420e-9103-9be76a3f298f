import { CURD } from "@/common/constant";
import { filterOption } from "@/common/helper.ts";
import DebounceSelect from "@/components/DebounceSelectComponent.tsx";
import ModalCURD from "@/components/ModalCURD";
import UserOptionComponent from "@/components/UserOptionComponent.tsx";
import { useApp } from "@/UseApp.tsx";
import { CloseCircleOutlined } from "@ant-design/icons";
import {
  useDistrictsQuery,
  useProvincesQuery,
  useWardsQuery,
} from "@location/service.ts";
import { ProjectEmployeeUserInterface } from "@project/employee/interface.ts";
import { useGetAgencyEmployeeLeadersMutation } from "@project/employee/service.ts";
import {
  HAS_OVERNIGHT_SHIFT_FALSE_LABEL,
  HAS_OVERNIGHT_SHIFT_TRUE_LABEL,
  ProjectOutletInterface,
} from "@project/outlet/interface.ts";

import {
  useCreateOutletMutation,
  useProjectChannelAgenciesQuery,
  useProjectChannelQuery,
  useProjectChannelsQuery,
  useUpdateOutletMutation,
} from "@project/outlet/service.ts";
import { RoleInterface } from "@project/role/interface.ts";
import { Col, Form, Input, Row, Select, Tag, TreeSelect } from "antd";
import { useCallback, useEffect, useMemo, useState } from "react";
import { RegionInterface, RegionOption } from "../region/interface";
import { useProjectRegionTreesQuery } from "../region/service";

interface ProjectOutletModalProps {
  action: CURD | null;
  projectId: number;
  cb: () => void;
  leaderRole: RoleInterface;
  cancelCb: () => void;
  selectedOutlet?: ProjectOutletInterface;
}

const ProjectOutletModal = ({
  action,
  projectId,
  cb,
  leaderRole,
  cancelCb,
  selectedOutlet,
}: ProjectOutletModalProps) => {
  const { showNotification } = useApp();
  const [form] = Form.useForm();
  const [channelIdSelected, setChannelIdSelected] = useState<number | null>(
    null,
  );
  const [selectedValue, setSelectedValue] = useState<
    string | null | ProjectEmployeeUserInterface | undefined
  >(null);
  const [selectedProvinceId, setSelectedProvinceId] = useState<number | null>(
    null,
  );
  const [selectedDistrictId, setSelectedDistrictId] = useState<number | null>(
    null,
  );
  const [options, setOptions] = useState<ProjectEmployeeUserInterface[]>([]);

  const projectChannelAgenciesQuery = useProjectChannelAgenciesQuery(
    projectId,
    channelIdSelected,
  );
  const provincesQuery = useProvincesQuery();
  const districtsQuery = useDistrictsQuery(selectedProvinceId);
  const wardsQuery = useWardsQuery(selectedProvinceId, selectedDistrictId);
  const projectChannelsQuery = useProjectChannelsQuery(projectId);
  const projectChannelQuery = useProjectChannelQuery(
    projectId,
    channelIdSelected,
  );
  const projectRegionTreesQuery = useProjectRegionTreesQuery(projectId);

  const createOutletMutation = useCreateOutletMutation(projectId, form);
  const updateOutletMutation = useUpdateOutletMutation(projectId, form);
  const getAgencyEmployeeLeadersMutation =
    useGetAgencyEmployeeLeadersMutation(projectId);

  const handleSelectProvinceChange = useCallback(
    (value: number) => {
      setSelectedProvinceId(value);
      form.resetFields(["districtId", "wardId"]);
    },
    [form],
  );

  const handleSelectDistrictChange = useCallback(
    (value: number) => {
      setSelectedDistrictId(value);
      form.resetFields(["wardId"]);
    },
    [form],
  );

  const regionOptions = useMemo(() => {
    const getRegions = (regions: RegionInterface[]): RegionOption[] => {
      return regions.map((region: RegionInterface) => ({
        value: region.id,
        label: region.name,
        children: getRegions(region.children || []),
      }));
    };

    return getRegions(projectRegionTreesQuery.data || []);
  }, [projectRegionTreesQuery.data]);
  const fetchLeaderOptions = useCallback(
    async (keyword?: string) => {
      if (keyword?.length === 0) {
        return [];
      }

      const projectAgencyId = form.getFieldValue("projectAgencyId");
      if (!projectAgencyId) {
        return [];
      }

      const leadersResponse =
        await getAgencyEmployeeLeadersMutation.mutateAsync({
          keyword,
          take: 10,
          projectAgencyId,
        });

      setOptions(leadersResponse.entities);

      return leadersResponse.entities.map((item) => ({
        value: item.id,
        label: item.user.username,
        user: item.user,
        isAvailable: true,
      }));
    },
    [form, getAgencyEmployeeLeadersMutation],
  );
  const formContent = (
    <>
      <Form.Item name="id" hidden>
        <Input hidden />
      </Form.Item>

      <Form.Item
        name="code"
        label="Mã outlet"
        extra="Nếu để trống hệ thống sẽ tự sinh mã theo cấu trúc [Mã kênh-Số thứ tự]"
        rules={[
          {
            required: action === CURD.UPDATE,
          },
        ]}
      >
        <Input />
      </Form.Item>

      <Form.Item
        name="name"
        label="Tên outlet"
        rules={[
          {
            required: true,
            message: "Tên outlet không được để trống.",
          },
        ]}
      >
        <Input />
      </Form.Item>

      <Row justify={"space-between"} gutter={16}>
        <Col md={12} xs={12}>
          <Form.Item
            label="Latitude"
            name="latitude"
            rules={[
              {
                pattern: /^-?([0-8]?[0-9]|90)(\.[0-9]{1,20})?$/,
                message: "Vui lòng nhập latitude hợp lệ (-90 đến 90)",
              },
            ]}
          >
            <Input placeholder="Ví dụ: 10.762622" />
          </Form.Item>
        </Col>
        <Col md={12} xs={12}>
          <Form.Item
            label="Longitude"
            name="longitude"
            rules={[
              {
                pattern: /^-?([0-9]{1,2}|1[0-7][0-9]|180)(\.[0-9]{1,20})?$/,
                message: "Vui lòng nhập longitude hợp lệ (-180 đến 180)",
              },
            ]}
          >
            <Input placeholder="Ví dụ: 106.660172" />
          </Form.Item>
        </Col>
      </Row>

      <Form.Item
        name="provinceId"
        label="Tỉnh/ TP"
        rules={[
          {
            required: true,
            message: "Tỉnh/ TP không được để trống.",
          },
        ]}
      >
        <Select
          placeholder="Tỉnh/ TP"
          showSearch
          optionFilterProp="children"
          filterOption={filterOption}
          options={provincesQuery.data?.map((province) => ({
            label: province.name,
            value: province.id,
          }))}
          popupMatchSelectWidth={false}
          onChange={handleSelectProvinceChange}
        />
      </Form.Item>

      <Form.Item
        name="districtId"
        label="Quận/ Huyện"
        rules={[
          {
            required: true,
            message: "Quận/ Huyện không được để trống.",
          },
        ]}
      >
        <Select
          placeholder="Quận/ Huyện"
          showSearch
          optionFilterProp="children"
          filterOption={filterOption}
          options={districtsQuery.data?.map((district) => ({
            label: district.name,
            value: district.id,
          }))}
          popupMatchSelectWidth={false}
          onChange={handleSelectDistrictChange}
        />
      </Form.Item>

      <Form.Item name="wardId" label="Phường/ Xã">
        <Select
          showSearch
          allowClear
          placeholder="Chọn Phường/Xã"
          optionFilterProp="children"
          filterOption={filterOption}
          popupMatchSelectWidth={false}
          options={wardsQuery.data?.map((ward) => ({
            label: ward.name,
            value: ward.id,
          }))}
        />
      </Form.Item>

      <Row justify={"space-between"} gutter={16}>
        <Col md={5} xs={8}>
          <Form.Item label="Số nhà" name="houseNumber">
            <Input />
          </Form.Item>
        </Col>
        <Col md={18} xs={16}>
          <Form.Item label="Tên đường" name="streetName">
            <Input />
          </Form.Item>
        </Col>
      </Row>

      <Form.Item label="Phân vùng" name={"projectRegionId"}>
        <TreeSelect
          treeData={regionOptions}
          treeDefaultExpandAll
          popupMatchSelectWidth={false}
        />
      </Form.Item>

      <Form.Item
        label={"Loại ca làm việc"}
        rules={[
          {
            required: true,
          },
        ]}
        name="hasOvernightShift"
      >
        <Select
          optionFilterProp="children"
          options={[
            {
              label: HAS_OVERNIGHT_SHIFT_FALSE_LABEL,
              value: false,
            },
            {
              label: HAS_OVERNIGHT_SHIFT_TRUE_LABEL,
              value: true,
            },
          ]}
        />
      </Form.Item>

      <Form.Item
        name="channelId"
        label="Kênh"
        rules={[
          {
            required: true,
            message: "Kênh không được để trống.",
          },
        ]}
      >
        <Select
          showSearch
          optionFilterProp="children"
          filterOption={filterOption}
          options={projectChannelsQuery.data?.map((channel) => ({
            value: channel.id,
            label: channel.name,
          }))}
          onSelect={(id) => setChannelIdSelected(id)}
          onChange={() => {
            form.resetFields(["subChannelId"]);
          }}
          disabled={action === CURD.UPDATE}
        />
      </Form.Item>

      <Form.Item name="subChannelId" label="Nhóm">
        <Select
          showSearch
          allowClear
          optionFilterProp="children"
          filterOption={filterOption}
          popupMatchSelectWidth={false}
          options={projectChannelQuery.data?.subChannels
            .filter((subChannel) => subChannel.isActive)
            .map((subChannel) => ({
              value: subChannel.id,
              label: subChannel.name,
            }))}
        />
      </Form.Item>

      <hr color="#DDE1EA" />

      <Form.Item
        name="projectAgencyId"
        label="Agency quản lý outlet này"
        rules={[
          {
            required: true,
            message: "Agency quản lý outlet không được để trống.",
          },
        ]}
      >
        <Select
          showSearch
          optionFilterProp="children"
          filterOption={filterOption}
          options={projectChannelAgenciesQuery.data?.map((projectAgency) => ({
            value: projectAgency.id,
            label: projectAgency.agency.name,
          }))}
          disabled={action === CURD.UPDATE}
        />
      </Form.Item>

      <Form.Item
        label={`Trưởng nhóm (${leaderRole?.name}) quản lý outlet này`}
        name="leaderId"
        extra={`Có thể phân bổ trưởng nhóm (${leaderRole?.name}) quản lý sau nếu không tìm được`}
      >
        {selectedValue ? (
          <Tag
            closable
            onClose={() => {
              setSelectedValue(null);
              form.setFieldValue("leaderId", []);
            }}
            className="w-full justify-between flex pt-2 pb-2 pl-3 pr-3"
            closeIcon={<CloseCircleOutlined style={{ fontSize: 14 }} />}
            style={{
              fontSize: 14,
              backgroundColor:
                typeof selectedValue === "string" ? "" : "#F0F8FF",
              borderColor: typeof selectedValue === "string" ? "" : "#C4D6FF",
            }}
          >
            {typeof selectedValue === "string" ? (
              <p>{selectedValue}</p>
            ) : (
              <UserOptionComponent
                avatarUrl={selectedValue?.user?.picture}
                name={selectedValue?.user.name}
                phone={selectedValue?.user.phone}
                email={selectedValue?.user.email}
              />
            )}
          </Tag>
        ) : (
          <DebounceSelect
            allowClear
            autoClearSearchValue
            showSearch
            fetchOptions={fetchLeaderOptions}
            style={{ width: "100%" }}
            optionRender={(option) => {
              if (option.data.user) {
                return (
                  <UserOptionComponent
                    avatarUrl={option.data.user?.imageUrl}
                    name={option.data.user?.name}
                    phone={option.data.user?.phone}
                    email={option.data.user?.email}
                  />
                );
              }
              return option.label;
            }}
            onSelect={({ value }) => {
              if (typeof value === "number") {
                const option = options.find((item) => item.id === value);
                if (option) {
                  setSelectedValue(option);
                }
              } else setSelectedValue(value);
            }}
          />
        )}
      </Form.Item>
    </>
  );

  const handleFormSubmit = async () => {
    if (action === CURD.CREATE) {
      const formValues = form.getFieldsValue();
      await createOutletMutation.mutateAsync({
        ...formValues,
        leaderId:
          typeof selectedValue === "object" ? selectedValue?.id : undefined,
        projectAgencyChannelId: projectChannelAgenciesQuery.data?.find(
          (projectAgency) => projectAgency.id === formValues["projectAgencyId"],
        )?.projectAgencyChannels[0]?.id,
        latitude: formValues["latitude"]
          ? Number(formValues["latitude"])
          : null,
        longitude: formValues["longitude"]
          ? Number(formValues["longitude"])
          : null,
      });
      showNotification({
        type: "success",
        message: `Thêm mới outlet (${formValues["name"]}) thành công`,
      });

      form.resetFields();
      setSelectedValue(null);
      setChannelIdSelected(null);

      cb();
    }

    if (action === CURD.UPDATE) {
      const formValues = form.getFieldsValue();
      await updateOutletMutation.mutateAsync({
        ...formValues,
        leaderId:
          typeof selectedValue === "object" ? (selectedValue?.id ?? 0) : 0,
        projectAgencyChannelId: projectChannelAgenciesQuery.data?.find(
          (projectAgency) => projectAgency.id === formValues["projectAgencyId"],
        )?.projectAgencyChannels[0]?.id,
        subChannelId: formValues["subChannelId"] ?? null,
        latitude: formValues["latitude"]
          ? Number(formValues["latitude"])
          : null,
        longitude: formValues["longitude"]
          ? Number(formValues["longitude"])
          : null,
      });
      showNotification({
        type: "success",
        message: `Cập nhật outlet (${formValues["name"]}) thành công`,
      });

      form.resetFields();
      setSelectedValue(null);

      cb();
    }
  };

  useEffect(() => {
    if (action === CURD.UPDATE && selectedOutlet) {
      setChannelIdSelected(selectedOutlet?.projectAgencyChannel?.channelId);
      form.setFieldsValue({
        ...selectedOutlet,
        provinceId: selectedOutlet.province?.id,
        districtId: selectedOutlet.district?.id,
        wardId: selectedOutlet.ward?.id,
        subChannelId: selectedOutlet.subChannel?.id,
        projectAgencyId: selectedOutlet.projectAgency?.id,
        channelId: selectedOutlet.projectAgencyChannel?.channelId,
        projectRegionId: selectedOutlet.projectRegion?.id,
      });

      setSelectedValue(selectedOutlet.projectEmployeeUser);
      setSelectedProvinceId(selectedOutlet.province?.id);
      setSelectedDistrictId(selectedOutlet.district?.id);
    }

    if (action === CURD.CREATE) {
      form.setFieldsValue({
        hasOvernightShift: false,
      });
    }
  }, [action, form, selectedOutlet]);

  return (
    <ModalCURD
      width={550}
      title={action === CURD.CREATE ? "Thêm outlet" : "Outlet"}
      isOpen={!!action}
      formContent={formContent}
      form={form}
      onFinish={handleFormSubmit}
      action={action}
      btnConfirmLoading={
        createOutletMutation.isPending || updateOutletMutation.isPending
      }
      onCancelCb={() => {
        setSelectedValue(null);
        form.resetFields();

        cancelCb();
      }}
    />
  );
};

export default ProjectOutletModal;
