import ChartContanier from "@/routes/project/chart/ChartContanier";
import { Pie } from "@ant-design/charts";
import { Col, Row } from "antd";
import { useCallback } from "react";
import DashboardFilterZone from "../../DashboardFilterZone";

const CompetitorGiftingBoothPitstopTab = () => {
  const handleApply = useCallback(() => {}, []);

  const config = {
    appendPadding: 10,
    data: [
      { type: "Carlsberg", value: 28.22 },
      { type: "Heineken", value: 23.31 },
      { type: "Tiger", value: 17.18 },
      { type: "Larue", value: 15.95 },
      { type: "Sài G n Chill", value: 15.34 },
    ],
    angleField: "value",
    colorField: "type",
    radius: 0.8,
    interactions: [
      {
        type: "element-active",
      },
    ],
    color: ["#7986CB", "#FF8A65", "#4DD0E1", "#FFB74D", "#4FC3F7"],
    label: {
      text: (d: { type: string; value: number }) => `${d.type}\n ${d.value}`,
      position: "spider",
    },
    legend: false,
    title: {
      title: "Competitor Gifting Booth",
      subtitle: "Tỷ l<PERSON> gift booth Sabeco và đối thủ",
    },
    tooltip: {
      field: "value",
      title: (d: { type: string }) => d.type,
      value: (d: { value: number }) => d.value,
    },
  };

  return (
    <>
      <DashboardFilterZone handleApply={handleApply} />
      <Row gutter={[16, { xs: 16, sm: 16 }]}>
        <Col md={12} xs={24}>
          <ChartContanier>
            <Pie {...config} />
          </ChartContanier>
        </Col>

        <Col md={12} xs={24}>
          <ChartContanier>
            <Pie {...config} />
          </ChartContanier>
        </Col>
      </Row>
    </>
  );
};

export default CompetitorGiftingBoothPitstopTab;
