import DragSortRowComponent from "@/components/DragSortRowComponent";
import ProductItemCell from "@/components/ProductItemCell";
import { renderTableCell } from "@/components/table-cell";
import { renderTableOptionCell } from "@/components/table-option-cell";
import { useCanPermission } from "@/layouts/MainLayout/hook";
import { DndContext, DragEndEvent } from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
  arrayMove,
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { Button, Modal, Table } from "antd";
import { useCallback, useEffect, useState } from "react";
import { PermissionEnum } from "../../interface";
import { ProjectItemInterface } from "../../item/interface";
import { ProjectLuckDrawItemInterface } from "../interface";
import {
  useDeleteLuckyDrawItemMutation,
  useLuckyDrawItemArrangementMutation,
  useLuckyDrawItemsQuery,
  usePatchLuckyDrawItemMutation,
} from "../service";
import ProjectConfigLuckyWheelItemsAvailablesModal from "./ProjectConfigLuckyWheelItemsAvailablesModal";

interface ProjectConfigLuckyWheelCollapseChildProps {
  projectId: number;
  luckyDrawId: number;
}
const ProjectConfigLuckyWheelCollapseChild = ({
  projectId,
  luckyDrawId,
}: ProjectConfigLuckyWheelCollapseChildProps) => {
  const [open, setOpen] = useState(false);
  const [modal, contentHolder] = Modal.useModal();

  const { canPermissionFunction } = useCanPermission(projectId);

  const luckyDrawItemsQuery = useLuckyDrawItemsQuery(projectId, luckyDrawId);

  const patchLuckyDrawItemMutation = usePatchLuckyDrawItemMutation(
    projectId,
    luckyDrawId,
  );
  const deleteLuckyDrawItemMutation = useDeleteLuckyDrawItemMutation(
    projectId,
    luckyDrawId,
  );
  const luckyDrawItemArrangementMutation = useLuckyDrawItemArrangementMutation(
    projectId,
    luckyDrawId,
  );

  const [dataSource, setDataSource] = useState(
    luckyDrawItemsQuery.data?.entities ?? [],
  );

  const onDragEnd = useCallback(
    async ({ active, over }: DragEndEvent) => {
      if (!over) {
        return;
      }
      if (active.id !== over?.id) {
        setDataSource((previous) => {
          const activeIndex = previous?.findIndex((i) => i.id === active.id);
          const overIndex = previous?.findIndex((i) => i.id === over?.id);
          return arrayMove(previous, activeIndex, overIndex);
        });

        await luckyDrawItemArrangementMutation.mutateAsync({
          id: active.id as number,
          arrangement: over?.id as number,
        });
      }
    },
    [luckyDrawItemArrangementMutation],
  );

  const onEdit = useCallback(() => {}, []);

  const onInActive = useCallback(
    (record: ProjectLuckDrawItemInterface) => {
      modal.confirm({
        title: `Ngừng hoạt động vật phẩm: ${record.projectItem.item.name}`,
        content: "Bạn có chắc chắn muốn ngừng hoạt động vật phẩm này?",
        okText: "Ngừng hoạt động",
        cancelText: "Hủy",
        onOk: async () => {
          await patchLuckyDrawItemMutation.mutateAsync({
            id: record.id,
            isActive: false,
          });
          luckyDrawItemsQuery.refetch();
        },
      });
    },
    [luckyDrawItemsQuery, modal, patchLuckyDrawItemMutation],
  );

  const onActive = useCallback(
    (record: ProjectLuckDrawItemInterface) => {
      modal.confirm({
        title: `Kích hoạt vật phẩm: ${record.projectItem.item.name}`,
        content: "Bạn có chắc chắn muốn kích hoạt vật phẩm này?",
        okText: "Kích hoạt",
        cancelText: "Hủy",
        onOk: async () => {
          await patchLuckyDrawItemMutation.mutateAsync({
            id: record.id,
            isActive: true,
          });
          luckyDrawItemsQuery.refetch();
        },
      });
    },
    [luckyDrawItemsQuery, modal, patchLuckyDrawItemMutation],
  );

  const onDelete = useCallback(
    (record: ProjectLuckDrawItemInterface) => {
      modal.confirm({
        title: `Xóa vật phẩm: ${record.projectItem.item.name}`,
        content: "Bạn có chắc chắn muốn xóa vật phẩm này?",
        okText: "Xóa",
        cancelText: "Hủy",
        onOk: async () => {
          await deleteLuckyDrawItemMutation.mutateAsync(record.id);
          luckyDrawItemsQuery.refetch();
        },
      });
    },
    [deleteLuckyDrawItemMutation, luckyDrawItemsQuery, modal],
  );

  useEffect(() => {
    setDataSource(luckyDrawItemsQuery.data?.entities ?? []);
  }, [luckyDrawItemsQuery.data?.entities]);

  return (
    <>
      {canPermissionFunction(PermissionEnum.NONE_ACCESS) && (
        <div className="flex justify-end">
          <Button
            type="link"
            className="text-blue"
            onClick={() => setOpen(true)}
          >
            Thêm quà
          </Button>
        </div>
      )}

      <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
        <SortableContext
          items={dataSource.map((i) => i.id)}
          strategy={verticalListSortingStrategy}
        >
          <Table
            rowKey={(o) => o.id}
            dataSource={dataSource}
            components={{
              body: {
                row: DragSortRowComponent,
              },
            }}
            pagination={false}
            columns={[
              {
                key: "sort",
              },
              {
                title: "Tên quà",
                className: "min-w-[100px]",
                dataIndex: "projectItem",
                render: (projectItem: ProjectItemInterface) => {
                  return (
                    <ProductItemCell
                      variants={projectItem?.item?.image?.variants ?? []}
                      name={projectItem.item.name}
                    />
                  );
                },
              },
              {
                title: "Mã",
                dataIndex: "projectItem",
                render: (projectItem: ProjectItemInterface) => {
                  return projectItem.item.code;
                },
              },
              {
                title: "Loại",
                dataIndex: "projectItem",
                render: (projectItem: ProjectItemInterface) => {
                  return projectItem.item.itemType?.name;
                },
              },
              {
                title: "Đơn vị tính",
                dataIndex: "projectItem",
                render: (projectItem: ProjectItemInterface) => {
                  return projectItem.item.unit?.name;
                },
              },
              {
                title: "Tình trạng",
                dataIndex: "isActive",
                render: (value, record, index) => {
                  return renderTableCell(value, record, index, "isActive");
                },
              },
              {
                key: "actions",
                render: (_, record) => {
                  return renderTableOptionCell(
                    record,
                    onEdit,
                    onInActive,
                    onActive,
                    onDelete,
                  );
                },
                width: 100,
                hidden: !canPermissionFunction(PermissionEnum.NONE_ACCESS),
              },
            ]}
          />
        </SortableContext>
      </DndContext>

      {open && (
        <ProjectConfigLuckyWheelItemsAvailablesModal
          cb={function (): void {
            luckyDrawItemsQuery.refetch();
          }}
          onModalClose={function (): void {
            setOpen(false);
          }}
          projectId={projectId}
          luckyDrawId={luckyDrawId}
        />
      )}

      {contentHolder}
    </>
  );
};

export default ProjectConfigLuckyWheelCollapseChild;
