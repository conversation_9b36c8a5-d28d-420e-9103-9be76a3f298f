import { AbstractFilterInterface } from "@/common/interface";
import { AppContextInterface } from "@/interface.ts";
import { useApp } from "@/UseApp";
import { useMutation, useQuery } from "@tanstack/react-query";
import { AxiosError } from "axios";
import {
  ApiProjectEmployeeUserResponseInterface,
  BankInterface,
  ProfileInterface,
  ProjectEmployeeUserInterface,
} from "./interface";

export const getEmployee = async (
  axiosGet: AppContextInterface["axiosGet"],
  projectId: number,
  filter: {
    roleId?: number;
    projectAgencyId?: number;
    keyword?: string;
    phone?: string;
  } & AbstractFilterInterface,
) => {
  return await axiosGet<ApiProjectEmployeeUserResponseInterface, unknown>(
    `/projects/${projectId}/employee-users`,
    filter,
  );
};

export interface EmployeeFilterInterface extends AbstractFilterInterface {
  roleId?: number;
  projectAgencyId?: number;
  keyword?: string;
  phone?: string;
}

/**
 * <PERSON>h sách nhân viên dựa trên ID của dự án và bộ lọc tùy chọn
 *
 * @param {number} projectId - ID của dự án cần lấy thông tin nhân viên.
 * @param {object & AbstractFilterInterface} [filter] - Đối tượng bộ lọc tùy chọn theo AbstractFilterInterface.
 *
 */
export const useEmployeesQuery = (
  projectId: number,
  filter?: EmployeeFilterInterface,
) => {
  const { axiosGet } = useApp();
  return useQuery({
    queryKey: ["employees", projectId, filter],
    queryFn: () =>
      axiosGet<ApiProjectEmployeeUserResponseInterface, unknown>(
        `/projects/${projectId}/employee-users`,
        filter,
      ),
  });
};

/**
 * Tạo nhân viên trong dự án.
 * @param  {number} projectId
 * @returns
 */
export const useCreateEmployeeMutation = (projectId: number) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createEmployee", projectId],
    mutationFn: (data: {
      projectAgencyId?: number;
      roleId: number;
      userId: number;
      leaderId?: number;
    }) => axiosPost(`/projects/${projectId}/employee-users`, data),
  });
};

/**
 * Cập nhật nhân viên trong dự án
 * @param {number} projectId
 * @returns
 */
export const useUpdateEmployeeMutation = (projectId: number) => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["updateEmployee", projectId],
    mutationFn: (data: {
      id: number;
      projectAgencyId?: number;
      roleId?: number;
      userId?: number;
      leaderId?: number;
      isActive?: boolean;
    }) => axiosPatch(`/projects/${projectId}/employee-users/${data.id}`, data),
  });
};

export const useGetEmployeesAvailablesMutation = (projectId: number) => {
  const { axiosGet } = useApp();

  return useMutation({
    mutationKey: ["getEmployeesAvailables", projectId],
    mutationFn: (filter: { keyword?: string } & AbstractFilterInterface) =>
      axiosGet<ApiProjectEmployeeUserResponseInterface, unknown>(
        `/projects/${projectId}/employee-users/availables`,
        filter,
      ),
  });
};

export const useDeleteProjectEmployeeOutletMutation = (
  projectId: number,
  leaderId: number | undefined,
) => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deleteProjectEmployeeOutlet", projectId, leaderId],
    mutationFn: (id: number) =>
      axiosDelete(
        `/projects/${projectId}/employee-users/${leaderId}/outlets/${id}`,
      ),
  });
};

/**
 * Chi tiết nhân viên trong dự án
 * @param projectId
 * @param employeeId
 * @returns
 */
export const useEmployeeQuery = (projectId: number, employeeId: number) => {
  const { axiosGet } = useApp();
  return useQuery({
    queryKey: ["employee", projectId, employeeId],
    queryFn: () =>
      axiosGet<ProjectEmployeeUserInterface, unknown>(
        `/projects/${projectId}/employee-users/${employeeId}`,
      ),
  });
};

export const useAddProjectOutletToLeaderMutation = (
  projectId: number,
  leaderId: number | undefined,
) => {
  const { axiosPost } = useApp();
  return useMutation({
    mutationKey: ["addProjectOutletToLeader", projectId, leaderId],
    mutationFn: (outletIds: number[]) =>
      axiosPost(`/projects/${projectId}/employee-users/${leaderId}/outlets`, {
        projectOutletIds: outletIds,
      }),
  });
};

/**
 * Thêm nhân viên vào leader
 * @param projectId
 * @param leaderId
 * @returns
 */
export const useAddEmployeeToLeaderMutation = (
  projectId: number,
  leaderId?: number,
) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["addEmployeeToLeader", projectId, leaderId],
    mutationFn: (data: number[]) =>
      axiosPost(`/projects/${projectId}/employee-users/${leaderId}/members`, {
        projectEmployeeUserIds: data,
      }),
  });
};

export const getLeadersInProjectAgency = async (
  axiosGet: AppContextInterface["axiosGet"],
  projectId: number,
  projectAgencyId: number,
  filter?: object & AbstractFilterInterface,
) =>
  axiosGet<ApiProjectEmployeeUserResponseInterface, unknown>(
    `/projects/${projectId}/agencies/${projectAgencyId}/leaders`,
    filter,
  );

export const findLeaderIdByPhoneByProjectAgencyId = async (
  axiosGet: AppContextInterface["axiosGet"],
  projectId: number,
  leaders: React.MutableRefObject<ProjectEmployeeUserInterface[]>,
  phone: string,
  projectAgencyId: number,
) => {
  const leader = leaders.current.find(
    (item) =>
      phone.toString().toLowerCase() ===
      item.user.phone.toString().toLowerCase(),
  );
  if (leader) {
    return leader.id;
  }

  try {
    const responseLeader = await getLeadersInProjectAgency(
      axiosGet,
      projectId,
      projectAgencyId,
      {
        keyword: phone,
      },
    );

    leaders.current = [...leaders.current, ...responseLeader.entities];
    return responseLeader.entities.find(
      (projectEmployeeUser) => projectEmployeeUser.user.phone === phone,
    )?.id;
  } catch (error) {
    if (error instanceof AxiosError) {
      throw error;
    }
  }
};

export const useDeleteEmployeeMutation = (projectId: number) => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deleteEmployee", projectId],
    mutationFn: (id: number) =>
      axiosDelete(`/projects/${projectId}/employee-users/${id}`),
  });
};

export const useCreateProfileEmployeeMutation = (
  projectId: number,
  id?: number,
) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createEmployeeProfile", projectId, id],
    mutationFn: (data: object) =>
      axiosPost(`/projects/${projectId}/employee-users/${id}/profile`, data),
  });
};

export const useProfileQuery = (projectId: number, id?: number) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["profile", projectId, id],
    queryFn: () =>
      axiosGet<ProfileInterface, unknown>(
        `/projects/${projectId}/employee-users/${id}/profile`,
      ),
    enabled: !!id,
  });
};

export const useGetEmployeeMutation = (projectId: number) => {
  const { axiosGet } = useApp();

  return useMutation({
    mutationKey: ["getEmployee", projectId],
    mutationFn: (
      filter: AbstractFilterInterface & {
        roleId?: number;
        projectAgencyId?: number;
        keyword?: string;
        phone?: string;
      },
    ) => getEmployee(axiosGet, projectId, filter),
  });
};

export const useGetEmployeeLeaderMutation = (projectId: number) => {
  const { axiosGet } = useApp();

  return useMutation({
    mutationKey: ["getEmployeeLeader", projectId],
    mutationFn: (filter: AbstractFilterInterface) =>
      axiosGet<ApiProjectEmployeeUserResponseInterface, unknown>(
        `/projects/${projectId}/employee-users/leaders`,
        filter,
      ),
  });
};

export const useBanksQuery = (enabled?: boolean) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["banks"],
    queryFn: () => axiosGet<BankInterface[], unknown>(`/banks`),
    enabled,
  });
};

export const useGetAgencyEmployeeLeadersMutation = (projectId: number) => {
  const { axiosGet } = useApp();

  return useMutation({
    mutationKey: ["getAgencyEmployeeLeaders", projectId],
    mutationFn: async (
      filter: {
        roleId?: number;
        keyword?: string;
      } & AbstractFilterInterface & { projectAgencyId?: number },
    ) =>
      await axiosGet<ApiProjectEmployeeUserResponseInterface, unknown>(
        `/projects/${projectId}/agencies/${filter.projectAgencyId}/leaders`,
        filter,
      ),
  });
};

export const useLeaderEmployeeMembersQuery = (
  projectId: number,
  employeeId?: number,
  filter?: object & AbstractFilterInterface,
  enabled?: boolean,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["leaderEmployeeMembers", projectId, employeeId, filter],
    queryFn: () =>
      axiosGet<ApiProjectEmployeeUserResponseInterface, unknown>(
        `/projects/${projectId}/employee-users/${employeeId}/members`,
        filter,
      ),
    enabled,
  });
};

export const useDeleteLeaderEmployeeMemberMutation = (
  projectId: number,
  leaderId: number,
) => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deleteLeaderEmployeeMember", projectId, leaderId],
    mutationFn: (id: number) =>
      axiosDelete(
        `/projects/${projectId}/employee-users/${leaderId}/members/${id}`,
      ),
  });
};

export const useGetLeaderEmployeeMembersMutation = (
  projectId: number,
  employeeId?: number,
) => {
  const { axiosGet } = useApp();

  return useMutation({
    mutationKey: ["leaderEmployeeMembers", projectId, employeeId],
    mutationFn: (filter?: object & AbstractFilterInterface) =>
      axiosGet<ApiProjectEmployeeUserResponseInterface, unknown>(
        `/projects/${projectId}/employee-users/${employeeId}/members`,
        filter,
      ),
  });
};
