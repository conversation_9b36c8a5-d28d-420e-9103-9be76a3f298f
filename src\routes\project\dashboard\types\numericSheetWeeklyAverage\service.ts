import { useApp } from "@/UseApp";
import { useQuery } from "@tanstack/react-query";
import { DashboardFilterInterface } from "../../interface";

export const useNumericSheetWeeklyAverageQuery = (
  projectId: number,
  dashboardId: number,
  filter?: DashboardFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["numericSheetWeeklyAverage", projectId, dashboardId, filter],
    queryFn: () =>
      axiosGet<
        {
          name: string;
          code: string;
          attribute: {
            name: string;
            backgroundColor: string;
            foregroundColor: string;
          };
          week: number;
          average: number;
          backgroundColor: string;
          foregroundColor: string;
          startDate: string;
        }[],
        unknown
      >(
        `/projects/${projectId}/dashboards/${dashboardId}/charts/numeric-sheet-weekly-average`,
        filter,
      ),
  });
};
