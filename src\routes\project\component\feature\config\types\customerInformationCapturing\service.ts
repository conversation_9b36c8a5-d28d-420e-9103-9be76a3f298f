import { AbstractFilterInterface } from "@/common/interface";
import { useApp } from "@/UseApp";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  OrderInterface,
  OrderLimitRestrictionEnum,
  OrderLimitTypeEnum,
} from "./interface";

export const useUpdateStepStatusMutation = (componentFeatureId: number) => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["updateStepStatus", componentFeatureId],
    mutationFn: (data: {
      hasPurchase?: boolean;
      hasExchange?: boolean;
      hasCustomer?: boolean;
      hasPhoto?: boolean;
      hasSampling?: boolean;
      isCustomerRequired?: boolean;
      isPurchaseRequired?: boolean;
      isExchangeRequired?: boolean;
      isSamplingRequired?: boolean;
      isPhotoRequired?: boolean;
    }) => axiosPatch(`/features/${componentFeatureId}/order`, data),
  });
};

export const useOrderQuery = (componentFeatureId: number | null) => {
  const { axiosGet } = useApp();
  return useQuery({
    queryKey: ["orderLatest", componentFeatureId],
    queryFn: () =>
      axiosGet<OrderInterface, unknown>(
        `/features/${componentFeatureId}/order`,
      ),
    enabled: !!componentFeatureId,
  });
};

export const useCreateFeatureOrderLimitMutation = (
  componentFeatureId: number,
) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createFeatureOrderLimit", componentFeatureId],
    mutationFn: (data: {
      type: OrderLimitTypeEnum;
      maximum: number; //Giới hạn quà
      restriction: OrderLimitRestrictionEnum; //Chọn loại giới hạn
      periodDays?: number; //Giới hạn ngày
    }) => axiosPost(`/features/${componentFeatureId}/order/limits`, data),
  });
};

export const useFeatureOrderLimitsQuery = (
  componentFeatureId: number,
  filter?: AbstractFilterInterface & { type: OrderLimitTypeEnum },
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["featureOrderLimits", componentFeatureId, filter],
    queryFn: () =>
      axiosGet<
        {
          entities: {
            id: number;
            type: OrderLimitTypeEnum;
            maximum: number;
            restriction: OrderLimitRestrictionEnum;
            periodDays: number;
          }[];
          count: number;
        },
        unknown
      >(`/features/${componentFeatureId}/order/limits`, filter),
  });
};

export const useDeleteFeatureOrderLimitMutation = (
  componentFeatureId: number,
) => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deleteFeatureOrderLimit", componentFeatureId],
    mutationFn: (id: number) =>
      axiosDelete(`/features/${componentFeatureId}/order/limits/${id}`),
  });
};

export const useUpdateFeatureOrderLimitMutation = (
  componentFeatureId: number,
) => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["updateFeatureOrderLimit", componentFeatureId],
    mutationFn: (data: {
      id: number;
      maximum?: number;
      periodDays?: number;
      isActive?: boolean;
    }) =>
      axiosPatch(
        `/features/${componentFeatureId}/order/limits/${data.id}`,
        data,
      ),
  });
};
