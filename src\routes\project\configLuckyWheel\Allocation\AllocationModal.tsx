import { DATE_FORMAT } from "@/common/constant";
import CustomModal from "@/components/CustomModal";
import FormNumberInput from "@/components/FormNumberInput";
import { Col, Form, Input, Row } from "antd";
import dayjs from "dayjs";
import _ from "lodash";
import { useCallback, useEffect, useMemo } from "react";
import { SelectedProjectOuutletLuckyDrawItemInterface } from "../interface";
import { useCreateLuckyDrawAllocationMutation } from "../service";

interface AllocationModalProps {
  projectId: number;
  luckyDrawId: number;
  selectedProjectOuutletLuckyDrawItem?: SelectedProjectOuutletLuckyDrawItemInterface;
  onClose?: () => void;
  cb?: () => void;
  type?: "total" | "date";
  selectedDate?: string;
}
const AllocationModal = ({
  projectId,
  luckyDrawId,
  selectedProjectOuutletLuckyDrawItem,
  onClose,
  cb,
  selectedDate,
  type,
}: AllocationModalProps) => {
  const [form] = Form.useForm();

  const createLuckyDrawAllocationMutation =
    useCreateLuckyDrawAllocationMutation(projectId, luckyDrawId);

  const allocatedQuantity = useMemo(() => {
    if (type === "total") {
      return selectedProjectOuutletLuckyDrawItem?.total?.allocatedQuantity;
    }
    if (type === "date") {
      return selectedProjectOuutletLuckyDrawItem?.projectOutlet?.projectLuckyDrawAllocations?.find(
        (item) =>
          dayjs(item.allocatedDate, "YYYY-MM-DD").isSame(
            dayjs(selectedDate, DATE_FORMAT),
            "date",
          ) &&
          item.projectLuckyDrawItem.projectItem.id ===
            selectedProjectOuutletLuckyDrawItem.projectItem.id,
      )?.allocatedQuantity;
    }
    return null;
  }, [
    selectedDate,
    selectedProjectOuutletLuckyDrawItem?.projectItem.id,
    selectedProjectOuutletLuckyDrawItem?.projectOutlet
      ?.projectLuckyDrawAllocations,
    selectedProjectOuutletLuckyDrawItem?.total?.allocatedQuantity,
    type,
  ]);

  const issuedQuantity = useMemo(() => {
    if (type === "total") {
      return selectedProjectOuutletLuckyDrawItem?.total?.issuedQuantity;
    }
    if (type === "date") {
      return selectedProjectOuutletLuckyDrawItem?.projectOutlet?.projectLuckyDrawAllocations?.find(
        (item) =>
          dayjs(item.allocatedDate, "YYYY-MM-DD").isSame(
            dayjs(selectedDate, DATE_FORMAT),
            "date",
          ) &&
          item.projectLuckyDrawItem.projectItem.id ===
            selectedProjectOuutletLuckyDrawItem.projectItem.id,
      )?.issuedQuantity;
    }
    return null;
  }, [
    selectedDate,
    selectedProjectOuutletLuckyDrawItem?.projectItem.id,
    selectedProjectOuutletLuckyDrawItem?.projectOutlet
      ?.projectLuckyDrawAllocations,
    selectedProjectOuutletLuckyDrawItem?.total?.issuedQuantity,
    type,
  ]);

  const min = useMemo(() => {
    if (type === "date") {
      return _.isNumber(issuedQuantity) ? issuedQuantity : undefined;
    }
    if (type === "total") {
      const projectLuckyDrawAllocations =
        selectedProjectOuutletLuckyDrawItem?.projectOutlet
          ?.projectLuckyDrawAllocations;
      const total = projectLuckyDrawAllocations
        ?.filter(
          (item) =>
            item.projectLuckyDrawItem.id ===
            selectedProjectOuutletLuckyDrawItem?.projectLuckyDrawItemId,
        )
        .filter((item) => item.allocatedDate !== null)
        ?.reduce((acc, curr) => acc + curr.allocatedQuantity, 0);

      return _.isNumber(total) ? total : undefined;
    }
  }, [
    issuedQuantity,
    selectedProjectOuutletLuckyDrawItem?.projectLuckyDrawItemId,
    selectedProjectOuutletLuckyDrawItem?.projectOutlet
      ?.projectLuckyDrawAllocations,
    type,
  ]);

  const content = (
    <>
      <Row justify={"space-between"}>
        <Col>
          <div>
            <span className="text-hint">Outlet: </span>
            <span className="text-blue">
              {selectedProjectOuutletLuckyDrawItem?.projectOutlet?.name}
            </span>
          </div>

          {type === "total" && (
            <div>
              <span className="text-hint">Tỉnh: </span>
              <span className="text-blue">
                {
                  selectedProjectOuutletLuckyDrawItem?.projectOutlet?.province
                    .name
                }
              </span>
            </div>
          )}

          {type === "date" && (
            <div>
              <span className="text-hint">Ngày: </span>
              <span className="text-blue">{selectedDate}</span>
            </div>
          )}
        </Col>
        <Col className="text-end">
          <div>
            <span className="text-hint">Đã dùng: </span>
            <span className="text-blue">
              {_.isNumber(issuedQuantity) ? issuedQuantity : "_"}
            </span>
          </div>
          <div>
            <span className="text-hint">Còn lại: </span>
            <span className="text-blue">
              {_.isNumber(allocatedQuantity) && _.isNumber(issuedQuantity)
                ? allocatedQuantity - issuedQuantity
                : "_"}
            </span>
          </div>
        </Col>
      </Row>

      <Form form={form} layout="vertical" className="mt-5">
        <Row gutter={16}>
          <Col md={16}>
            <Form.Item
              label={"Tên quà"}
              name={"name"}
              rules={[{ required: true }]}
            >
              <Input disabled />
            </Form.Item>
          </Col>
          <Col md={8}>
            <Form.Item
              label={"Số lượng phân bổ"}
              name={"allocatedQuantity"}
              rules={[
                { required: true },
                {
                  min: min,
                  type: "number",
                },
              ]}
            >
              <FormNumberInput className="w-full" />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </>
  );

  useEffect(() => {
    if (selectedProjectOuutletLuckyDrawItem) {
      if (type === "total") {
        form.setFieldsValue({
          name: selectedProjectOuutletLuckyDrawItem.projectItem.item.name,
          allocatedQuantity:
            selectedProjectOuutletLuckyDrawItem.total?.allocatedQuantity,
        });
      }
      if (type === "date") {
        const allocatedQuantity =
          selectedProjectOuutletLuckyDrawItem?.projectOutlet?.projectLuckyDrawAllocations?.find(
            (item) =>
              dayjs(item.allocatedDate, "YYYY-MM-DD").isSame(
                dayjs(selectedDate, DATE_FORMAT),
                "date",
              ) &&
              item.projectLuckyDrawItem.projectItem.id ===
                selectedProjectOuutletLuckyDrawItem.projectItem.id,
          )?.allocatedQuantity;

        form.setFieldsValue({
          name: selectedProjectOuutletLuckyDrawItem.projectItem.item.name,
          allocatedQuantity,
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onFinish = useCallback(async () => {
    await form.validateFields();

    const values = form.getFieldsValue();

    if (type === "total") {
      await createLuckyDrawAllocationMutation.mutateAsync({
        projectOutletId:
          selectedProjectOuutletLuckyDrawItem?.projectOutlet.id ?? 0,
        projectLuckyDrawItemId:
          selectedProjectOuutletLuckyDrawItem?.projectLuckyDrawItemId ?? 0,
        allocatedQuantity: values.allocatedQuantity,
        allocatedDate: null,
      });
    }

    if (type === "date" && selectedDate) {
      await createLuckyDrawAllocationMutation.mutateAsync({
        projectOutletId:
          selectedProjectOuutletLuckyDrawItem?.projectOutlet.id ?? 0,
        projectLuckyDrawItemId:
          selectedProjectOuutletLuckyDrawItem?.projectLuckyDrawItemId ?? 0,
        allocatedQuantity: values.allocatedQuantity,
        allocatedDate: dayjs(selectedDate, DATE_FORMAT).toISOString(),
      });
    }

    cb?.();
    onClose?.();
  }, [
    cb,
    createLuckyDrawAllocationMutation,
    form,
    onClose,
    selectedDate,
    selectedProjectOuutletLuckyDrawItem?.projectLuckyDrawItemId,
    selectedProjectOuutletLuckyDrawItem?.projectOutlet.id,
    type,
  ]);

  return (
    <CustomModal
      title={"Phân bổ quà theo siêu thị"}
      isOpen={true}
      content={content}
      onCancel={onClose}
      onConfirm={onFinish}
      confirmText="Phân bổ"
    />
  );
};

export default AllocationModal;
