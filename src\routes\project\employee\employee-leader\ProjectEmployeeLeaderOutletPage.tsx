import { useApp } from "@/UseApp";
import { DEFAULT_CURRENT_PAGE, DEFAULT_PAGE_SIZE } from "@/common/constant";
import { filterOption } from "@/common/helper";
import FilterClassicComponent from "@/components/FilterClassicComponent";
import TableActionCell from "@/components/TableActionCell";
import UserOptionComponent from "@/components/UserOptionComponent";
import { renderTableCell } from "@/components/table-cell";
import {
  ArrowLeftOutlined,
  DeleteOutlined,
  PauseCircleOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import {
  useDistrictsQuery,
  useProvincesQuery,
  useWardsQuery,
} from "@location/service";
import { ProjectOutletInterface } from "@project/outlet/interface";
import {
  useProjectEmployeeLeaderOutletsQuery,
  useUpdateOutletMutation,
} from "@project/outlet/service";
import { Form, Input, Modal, Select, Table } from "antd";
import { ColumnsType } from "antd/es/table";
import _ from "lodash";
import { useCallback, useEffect, useMemo, useState } from "react";
import { Link, useLocation, useParams } from "react-router-dom";
import { EmployeeActionEnum, ProjectEmployeeUserInterface } from "../interface";
import {
  useDeleteProjectEmployeeOutletMutation,
  useEmployeeQuery,
} from "../service";
import ProjectEmployeeLeaderOutletModal from "./modal/ProjectEmployeeLeaderOutletModal";

export default function ProjectEmployeeLeaderOutletPage() {
  const projectId = parseInt(useParams().id ?? "0");
  const employeeId = parseInt(useParams().employeeId ?? "0");

  const location = useLocation();

  const { loading, showNotification } = useApp();

  const [searchForm] = Form.useForm();
  const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [isOpen, setIsOpen] = useState(false);
  const [modal, contextHolder] = Modal.useModal();
  const [filter, setFilter] = useState({});

  const employeeQuery = useEmployeeQuery(projectId, employeeId);

  const [selectedProvinceId, setSelectedProvinceId] = useState<number | null>(
    null,
  );
  const [selectedDistrictId, setSelectedDistrictId] = useState<number | null>(
    null,
  );

  const leader: ProjectEmployeeUserInterface =
    location.state || employeeQuery.data;

  const provincesQuery = useProvincesQuery();
  const districtsQuery = useDistrictsQuery(selectedProvinceId);
  const wardsQuery = useWardsQuery(selectedProvinceId, selectedDistrictId);

  const projectEmployeeLeaderOutletsQuery =
    useProjectEmployeeLeaderOutletsQuery(projectId, employeeId, {
      ...filter,
      take: pageSize,
      skip: (currentPage - 1) * pageSize,
    });

  const deleteProjectEmployeeOutletMutation =
    useDeleteProjectEmployeeOutletMutation(projectId, leader.id);

  const updateOutletMutation = useUpdateOutletMutation(projectId);

  const handleSelectProvinceChange = useCallback(
    (value: number) => {
      setSelectedProvinceId(value);
      searchForm.resetFields(["districtId", "wardId"]);
    },
    [searchForm],
  );

  const handleSelectDistrictChange = useCallback(
    (value: number) => {
      setSelectedDistrictId(value);
      searchForm.resetFields(["wardId"]);
    },
    [searchForm],
  );

  const searchContent = (
    <>
      <Form.Item name="keyword">
        <Input
          placeholder="Tìm theo mã, tên outlet, số nhà, tên đường"
          allowClear
          prefix={<SearchOutlined />}
        />
      </Form.Item>

      <Form.Item name="provinceId">
        <Select
          allowClear
          placeholder="Tỉnh/ TP"
          showSearch
          optionFilterProp="children"
          filterOption={filterOption}
          options={provincesQuery.data?.map((province) => ({
            label: province.name,
            value: province.id,
          }))}
          popupMatchSelectWidth={false}
          onChange={handleSelectProvinceChange}
        />
      </Form.Item>

      <Form.Item name="districtId">
        <Select
          allowClear
          placeholder="Quận/ Huyện"
          showSearch
          optionFilterProp="children"
          filterOption={filterOption}
          options={districtsQuery.data?.map((district) => ({
            label: district.name,
            value: district.id,
          }))}
          popupMatchSelectWidth={false}
          onChange={handleSelectDistrictChange}
        />
      </Form.Item>

      <Form.Item name="wardId">
        <Select
          allowClear
          placeholder="Phường/ Xã"
          showSearch
          optionFilterProp="children"
          filterOption={filterOption}
          options={wardsQuery.data?.map((ward) => ({
            label: ward.name,
            value: ward.id,
          }))}
          popupMatchSelectWidth={false}
        />
      </Form.Item>
    </>
  );

  const handlePaginationChange = useCallback(
    (page: number) => {
      setCurrentPage(page);
    },
    [setCurrentPage],
  );

  const handlePageSizeChange = useCallback(
    (_current: number, size: number) => {
      setPageSize(size);
      setCurrentPage(DEFAULT_CURRENT_PAGE);
    },
    [setPageSize, setCurrentPage],
  );

  const pagination = useMemo(() => {
    return {
      current: currentPage,
      total: projectEmployeeLeaderOutletsQuery.data?.count ?? 0,
      pageSize: pageSize,
      onChange: handlePaginationChange,
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: handlePageSizeChange,
    };
  }, [
    currentPage,
    projectEmployeeLeaderOutletsQuery.data?.count,
    pageSize,
    handlePaginationChange,
    handlePageSizeChange,
  ]);

  const searchHandler = useCallback(() => {
    const values = searchForm.getFieldsValue();
    setCurrentPage(DEFAULT_CURRENT_PAGE);
    if (_.isEqual(values, filter)) {
      projectEmployeeLeaderOutletsQuery.refetch();
    }
    setFilter(values);
  }, [filter, projectEmployeeLeaderOutletsQuery, searchForm]);

  const ACTION_ACTIVE = [
    EmployeeActionEnum.INACTIVE,
    EmployeeActionEnum.DELETE,
  ];
  const ACTION_INACTIVE = [
    EmployeeActionEnum.ACTIVE,
    EmployeeActionEnum.DELETE,
  ];

  const actionItems = [
    {
      key: EmployeeActionEnum.INACTIVE,
      label: (
        <>
          <PauseCircleOutlined /> Ngừng hoạt động
        </>
      ),
    },
    {
      key: EmployeeActionEnum.ACTIVE,
      label: (
        <>
          <PauseCircleOutlined /> Khôi phục
        </>
      ),
    },
    {
      key: EmployeeActionEnum.DELETE,
      label: (
        <>
          <DeleteOutlined /> Xóa khỏi quản lý của trưởng nhóm
        </>
      ),
    },
  ];

  const handleActionInActiveClick = (record: ProjectOutletInterface) => {
    modal.confirm({
      title: `Ngừng hoạt động outlet: ${record.name}`,
      content: `Bạn có chắc chắn muốn ngừng hoạt động outlet ${record.name} này?`,
      okText: "Ngừng hoạt động",
      cancelText: "Hủy",
      onOk: async () => {
        try {
          await updateOutletMutation.mutateAsync({
            id: record.id,
            isActive: false,
          });

          showNotification({
            type: "success",
            message: `Ngừng hoạt động outlet ${record.name} thành công`,
          });

          projectEmployeeLeaderOutletsQuery.refetch();
        } catch (error) {
          console.error(error);

          showNotification({
            type: "error",
            message: `Ngừng hoạt động outlet ${record.name} thất bại`,
          });
        }
      },
    });
  };

  const handleActionActiveClick = (record: ProjectOutletInterface) => {
    modal.confirm({
      title: `Kích hoạt outlet ${record.name}`,
      content: `Bạn có chắc chắn muốn kích hoạt outlet ${record.name} này?`,
      okText: "Kích hoạt",
      cancelText: "Hủy",
      onOk: async () => {
        try {
          await updateOutletMutation.mutateAsync({
            id: record.id,
            isActive: true,
          });

          showNotification({
            type: "success",
            message: `Kích hoạt outlet ${record.name} thành công`,
          });

          projectEmployeeLeaderOutletsQuery.refetch();
        } catch (error) {
          console.error(error);

          showNotification({
            type: "error",
            message: `Kích hoạt outlet ${record.name} thất bại`,
          });
        }
      },
    });
  };

  const handleActionDeleteClick = (record: ProjectOutletInterface) => {
    modal.confirm({
      title: `Xóa outlet: ${record.name}`,
      content: `Bạn có chắc chắn muốn xóa outlet (${record.name}) khỏi quản lý của ${leader.user.name}?`,
      okText: "Xóa",
      cancelText: "Hủy",
      onOk: async () => {
        try {
          await deleteProjectEmployeeOutletMutation.mutateAsync(record.id);

          showNotification({
            type: "success",
            message: `Xóa outlet  (${record.name}) thành công`,
          });

          projectEmployeeLeaderOutletsQuery.refetch();
        } catch (error) {
          console.error(error);

          showNotification({
            type: "error",
            message: `Xóa outlet (${record.name}) thất bại`,
          });
        }
      },
    });
  };

  const actionActions = [
    {
      key: EmployeeActionEnum.DELETE,
      action: handleActionDeleteClick,
    },
    {
      key: EmployeeActionEnum.INACTIVE,
      action: handleActionInActiveClick,
    },
    {
      key: EmployeeActionEnum.ACTIVE,
      action: handleActionActiveClick,
    },
  ];

  const columns: ColumnsType<ProjectOutletInterface> = [
    {
      title: "Mã outlet",
      key: "code",
      dataIndex: "code",
      className: "min-w-[150px]",
    },
    {
      title: "Tên outlet",
      key: "name",
      dataIndex: "name",
      className: "min-w-[150px]",
    },
    {
      title: "Số nhà",
      key: "houseNumber",
      dataIndex: "houseNumber",
      className: "min-w-[150px]",
      render: renderTableCell,
    },
    {
      title: "Tên đường",
      key: "streetName",
      dataIndex: "streetName",
      className: "min-w-[150px]",
      render: renderTableCell,
    },
    {
      title: "Tỉnh/ TP",
      key: "province",
      dataIndex: "province",
      className: "min-w-[150px]",
      render: renderTableCell,
    },
    {
      title: "Quận/ Huyện",
      key: "district",
      dataIndex: "district",
      className: "min-w-[150px]",
      render: renderTableCell,
    },
    {
      title: "Phường/ Xã",
      key: "ward",
      dataIndex: "ward",
      className: "min-w-[150px]",
      render: renderTableCell,
    },
    {
      title: "Tình trạng",
      dataIndex: "isActive",
      className: "min-w-[150px]",
      key: "isActive",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "isActive");
      },
    },
    {
      key: "actions",
      render: (_, record: ProjectOutletInterface) => {
        const actionKeys = record.isActive ? ACTION_ACTIVE : ACTION_INACTIVE;
        const items = actionItems.filter((item) =>
          actionKeys.includes(item.key),
        );
        return (
          <TableActionCell
            actions={actionActions}
            items={items}
            record={record}
          />
        );
      },
      fixed: "right",
      width: 100,
    },
  ];

  useEffect(() => {
    if (
      projectEmployeeLeaderOutletsQuery.data?.entities.length === 0 &&
      currentPage > 1
    ) {
      setCurrentPage(currentPage - 1);
    }
  }, [currentPage, projectEmployeeLeaderOutletsQuery.data?.entities.length]);

  return (
    <>
      <Link to={`/project/${projectId}/employee?tab=${leader.role.name}`}>
        <p className={"text-hint mt-[34px]"}>
          <ArrowLeftOutlined /> Quay lại
        </p>
      </Link>
      <h2>Outlet được trưởng nhóm quản lý</h2>
      <div className="bg-white pt-3 pl-10 pr-10 rounded pb-5">
        <h3>Trưởng nhóm</h3>
        <div className="table">
          <div className="border-solid border-2 border-[#C4D6FF] p-2 table-cell bg-[#F0F8FF] rounded">
            <UserOptionComponent
              avatarUrl={leader.user.picture}
              name={leader.user.name}
              phone={leader.user.phone}
              email={leader.user.email}
              roleName={leader.role.name}
            />
          </div>
          &nbsp; &nbsp; &nbsp;
          <div className="border-solid border-2 border-[#C4D6FF] p-2 table-cell bg-[#F0F8FF] rounded pl-5 pr-10">
            <p className="text-[#8C8C8D] m-0">Trực thuộc Agency</p>
            <p className="text-[#393939] m-0">
              {leader.projectAgency?.agency.name}
            </p>
          </div>
        </div>
        <h3>Outlet được trưởng nhóm quản lý</h3>
        <FilterClassicComponent
          showAddButton={true}
          searchHandler={searchHandler}
          searchForm={searchForm}
          content={searchContent}
          className="mb-6"
          handleAddButtonClick={() => {
            setIsOpen(true);
          }}
          btnLoading={projectEmployeeLeaderOutletsQuery.isFetching}
        />

        <Table
          dataSource={projectEmployeeLeaderOutletsQuery?.data?.entities}
          columns={columns}
          rowKey={(record) => record.id}
          scroll={{
            x: "max-content",
            y: pagination.total ? "80vh" : undefined,
          }}
          pagination={pagination}
          loading={loading}
        />

        <ProjectEmployeeLeaderOutletModal
          isOpen={isOpen}
          setIsOpen={setIsOpen}
          leader={leader}
          projectId={projectId}
          cb={searchHandler}
        />
      </div>
      {contextHolder}
    </>
  );
}
