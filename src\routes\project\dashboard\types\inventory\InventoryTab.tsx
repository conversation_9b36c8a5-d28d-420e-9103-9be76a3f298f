import { CHART_COLOR_PALETTE } from "@/common/constant";
import { randomColor } from "@/common/helper";
import ChartContanier from "@/routes/project/chart/ChartContanier";
import { Pie } from "@ant-design/charts";
import { Col, Form, Row } from "antd";
import _ from "lodash";
import { useCallback, useMemo, useState } from "react";
import DashboardFilterZone from "../../DashboardFilterZone";
import { DashboardFilterInterface } from "../../interface";
import { InventoryDataInterface } from "./interface";
import { useInventoryQuery } from "./service";

interface InventoryTabProps {
  projectId: number;
  dashboardId: number;
}
const InventoryTab = ({ projectId, dashboardId }: InventoryTabProps) => {
  const [form] = Form.useForm();
  const [filter, setFilter] = useState<DashboardFilterInterface>({});

  const inventoryBrandQuery = useInventoryQuery(projectId, dashboardId, {
    ...filter,
    controlBy: "brand",
  });
  const inventoryRegionQuery = useInventoryQuery(projectId, dashboardId, {
    ...filter,
    controlBy: "region",
  });
  const inventoryChannelQuery = useInventoryQuery(projectId, dashboardId, {
    ...filter,
    controlBy: "channel",
  });
  const inventoryLeaderQuery = useInventoryQuery(projectId, dashboardId, {
    ...filter,
    controlBy: "leader",
  });

  const handleApply = useCallback(() => {
    const values = form.getFieldsValue();

    const {
      projectRegionIds,
      projectOutletIds,
      channelIds,
      provinceIds,
      dateSingle,
      leaderIds,
    } = values;

    const filterValue = {
      startDate: dateSingle.format("YYYY-MM-DD"),
      endDate: dateSingle.format("YYYY-MM-DD"),
      projectRegionIds,
      projectOutletIds,
      channelIds,
      provinceIds,
      leaderIds: leaderIds?.map((item: { value: number }) => item.value),
    };
    if (_.isEqual(filter, filterValue)) {
      inventoryBrandQuery.refetch();
      inventoryRegionQuery.refetch();
      inventoryChannelQuery.refetch();
      inventoryLeaderQuery.refetch();
    } else {
      setFilter(filterValue);
    }
  }, [
    filter,
    form,
    inventoryBrandQuery,
    inventoryChannelQuery,
    inventoryLeaderQuery,
    inventoryRegionQuery,
  ]);

  const getPieData = useCallback((data: InventoryDataInterface[]) => {
    const group = _.groupBy(data, "control.name");
    const result = [];

    for (const [name, values] of Object.entries(group)) {
      const total = values.reduce((sum, item) => sum + item.quantity, 0);
      result.push({
        name: name,
        quantity: total,
        backgroundColor: values[0].control.backgroundColor,
      });
    }

    return result;
  }, []);

  const pieBrandData = useMemo(() => {
    return getPieData(inventoryBrandQuery.data ?? []);
  }, [getPieData, inventoryBrandQuery.data]);
  const totalBrand = useMemo(
    () => pieBrandData.reduce((sum, item) => sum + item.quantity, 0),
    [pieBrandData],
  );
  const pieBrandConfig = useMemo(
    () => ({
      appendPadding: 10,
      data: pieBrandData,
      angleField: "quantity",
      colorField: "name",
      radius: 0.8,
      interactions: [
        {
          type: "element-active",
        },
      ],
      label: {
        text: (d: { quantity: number; name: string }) =>
          `${d.quantity}  (${Math.round((d.quantity / totalBrand) * 100)}%)`,
        position: "spider",
        fontSize: 12,
        fontWeight: "bold",
      },
      legend: {
        color: {
          position: "bottom",
          layout: {
            justifyContent: "center",
          },
        },
      },
      title: {
        title: "Dashboard stock by brand",
        subtitle: "Tính theo thùng",
      },
      tooltip: {
        field: "quantity",
        title: (d: { name: string }) => d.name,
        value: (d: { quantity: number }) => d.quantity,
      },
      scale: {
        color: {
          palette: pieBrandData.map(
            (item) => item.backgroundColor ?? randomColor(),
          ),
        },
      },
    }),
    [pieBrandData, totalBrand],
  );

  const pieRegionData = useMemo(() => {
    return getPieData(inventoryRegionQuery.data ?? []);
  }, [getPieData, inventoryRegionQuery.data]);
  const totalRegion = useMemo(
    () => pieRegionData.reduce((sum, item) => sum + item.quantity, 0),
    [pieRegionData],
  );
  const pieRegionConfig = useMemo(
    () => ({
      appendPadding: 10,
      data: pieRegionData,
      angleField: "quantity",
      colorField: "name",
      radius: 0.8,
      interactions: [
        {
          type: "element-active",
        },
      ],
      label: {
        text: (d: { quantity: number; name: string }) =>
          `${d.quantity}  (${Math.round((d.quantity / totalRegion) * 100)}%)`,
        position: "spider",
        fontSize: 12,
        fontWeight: "bold",
      },
      legend: {
        color: {
          position: "bottom",
          layout: {
            justifyContent: "center",
          },
        },
      },
      title: {
        title: "Dashboard stocks by area",
        subtitle: "Tính theo thùng",
      },
      tooltip: {
        field: "quantity",
        title: (d: { name: string }) => d.name,
        value: (d: { quantity: number }) => d.quantity,
      },
      scale: {
        color: {
          palette: CHART_COLOR_PALETTE,
        },
      },
    }),
    [pieRegionData, totalRegion],
  );

  const pieChannelData = useMemo(
    () => getPieData(inventoryChannelQuery.data ?? []),
    [getPieData, inventoryChannelQuery.data],
  );
  const totalChannel = useMemo(
    () => pieChannelData.reduce((sum, item) => sum + item.quantity, 0),
    [pieChannelData],
  );
  const pieChannelConfig = useMemo(
    () => ({
      appendPadding: 10,
      data: pieChannelData,
      angleField: "quantity",
      colorField: "name",
      radius: 0.8,
      interactions: [
        {
          type: "element-active",
        },
      ],
      label: {
        text: (d: { quantity: number; name: string }) =>
          `${d.quantity}  (${Math.round((d.quantity / totalChannel) * 100)}%)`,
        position: "spider",
        fontSize: 12,
        fontWeight: "bold",
      },
      legend: {
        color: {
          position: "bottom",
          layout: {
            justifyContent: "center",
          },
        },
      },
      title: {
        title: "Dashboard stocks by chain",
        subtitle: "Tính theo thùng",
      },
      tooltip: {
        field: "quantity",
        title: (d: { name: string }) => d.name,
        value: (d: { quantity: number }) => d.quantity,
      },
      scale: {
        color: {
          palette: pieChannelData.map(
            (item) => item.backgroundColor ?? randomColor(),
          ),
        },
      },
    }),
    [pieChannelData, totalChannel],
  );

  const pieLeaderData = useMemo(
    () => getPieData(inventoryLeaderQuery.data ?? []),
    [getPieData, inventoryLeaderQuery.data],
  );
  const totalLeader = useMemo(
    () => pieLeaderData.reduce((sum, item) => sum + item.quantity, 0),
    [pieLeaderData],
  );
  const pieLeaderConfig = useMemo(
    () => ({
      appendPadding: 10,
      data: pieLeaderData,
      angleField: "quantity",
      colorField: "name",
      radius: 0.8,
      interactions: [
        {
          type: "element-active",
        },
      ],
      label: {
        text: (d: { quantity: number; name: string }) =>
          `${d.quantity}  (${Math.round((d.quantity / totalLeader) * 100)}%)`,
        position: "spider",
        fontSize: 12,
        fontWeight: "bold",
      },
      legend: {
        color: {
          position: "bottom",
          layout: {
            justifyContent: "center",
          },
        },
      },
      title: {
        title: "Dashboard stocks by sup",
        subtitle: "Tính theo thùng",
      },
      tooltip: {
        field: "quantity",
        title: (d: { name: string }) => d.name,
        value: (d: { quantity: number }) => d.quantity,
      },
      scale: {
        color: {
          palette: CHART_COLOR_PALETTE,
        },
      },
    }),
    [pieLeaderData, totalLeader],
  );

  return (
    <>
      <DashboardFilterZone
        handleApply={handleApply}
        fields={[
          "date.single",
          "region",
          "province",
          "chain",
          "leader",
          "outlet",
        ]}
        form={form}
        projectId={projectId}
        setFilter={setFilter}
        key={"inventory"}
      />
      <Row gutter={[16, { xs: 16, sm: 16 }]}>
        <Col md={12} xs={24}>
          <ChartContanier>
            <Pie {...pieRegionConfig} />
          </ChartContanier>
        </Col>

        <Col md={12} xs={24}>
          <ChartContanier>
            <Pie {...pieChannelConfig} />
          </ChartContanier>
        </Col>

        <Col md={12} xs={24}>
          <ChartContanier>
            <Pie {...pieLeaderConfig} />
          </ChartContanier>
        </Col>

        <Col md={12} xs={24}>
          <ChartContanier>
            <Pie {...pieBrandConfig} />
          </ChartContanier>
        </Col>
      </Row>
    </>
  );
};

export default InventoryTab;
