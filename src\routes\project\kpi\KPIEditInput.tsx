import InputNumberInCell from "@/components/InputNumberInCell";
import { useApp } from "@/UseApp";
import { LoadingOutlined } from "@ant-design/icons";
import { Space } from "antd";
import { useCallback } from "react";
import { ItemKpiTypeEnum, PeriodTypeEnum } from "./interface";
import { useCreateProjectKPIMutation } from "./service";
import "./style.css";

interface KPIEditInputProps {
  kpi: number | null;
  cb: () => void;
  projectId: number;
  type?: "salesRevenue" | "salesVolume" | "hit" | "order" | "session";
  channelId?: number;
  provinceId?: number;
  projectProductId?: number;
  projectItemId?: number;
  itemKpiType?: ItemKpiTypeEnum;
  projectOutletId?: number;
  periodType?: PeriodTypeEnum;
}
const KPIEditInput = ({
  kpi,
  cb,
  projectId,
  type,
  channelId,
  provinceId,
  projectProductId,
  projectItemId,
  itemKpiType,
  projectOutletId,
  periodType,
}: KPIEditInputProps) => {
  const { showNotification } = useApp();

  const createProjectKPIMutation = useCreateProjectKPIMutation(projectId);

  const onSubmit = useCallback(
    async (value: number | null) => {
      if (value !== kpi) {
        try {
          const data = { channelId, provinceId, projectOutletId, periodType };
          if (type) {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            (data as any)[type] = value;
          }

          if (itemKpiType && (projectProductId || projectItemId)) {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            (data as any)["items"] = [
              {
                projectProductId,
                projectItemId,
                type: itemKpiType,
                kpi: value,
              },
            ];
          }

          await createProjectKPIMutation.mutateAsync(data);

          cb();

          showNotification({
            type: "success",
            message: `Cập nhật thành công`,
          });
        } catch (e) {
          console.error(e);

          showNotification({
            type: "error",
            message: `Cập nhật thất bại`,
          });
        }
      }
    },
    [
      cb,
      channelId,
      createProjectKPIMutation,
      itemKpiType,
      kpi,
      projectItemId,
      projectProductId,
      provinceId,
      projectOutletId,
      periodType,
      showNotification,
      type,
    ],
  );

  return (
    <Space>
      <InputNumberInCell initValue={kpi} onSubmit={onSubmit} />
      {createProjectKPIMutation.isPending && <LoadingOutlined />}
    </Space>
  );
};

export default KPIEditInput;
