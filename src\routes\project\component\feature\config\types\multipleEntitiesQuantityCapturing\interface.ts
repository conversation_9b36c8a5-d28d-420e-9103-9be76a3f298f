import { AbstractEntityInterface, ImageInterface } from "@/common/interface";
import { ProductPackagingInterface } from "@/routes/product/interface";
import { ProjectItemInterface } from "@project/item/interface";
import { ProjectProductInterface } from "@project/product/interface";

export interface QuantityEntityInterface extends AbstractEntityInterface {
  name: string;
  code: string;
  unitName: string;
  itemTypeId: number;
  itemTypeName: string;
  productId?: number;
  projectProductId?: number;
  itemId?: number;
  projectItemId?: number;
  projectProduct?: ProjectProductInterface;
  productPackaging?: ProductPackagingInterface;
  projectItem?: ProjectItemInterface;
  imageUrl?: string;
  projectFeatureId?: number | null;
  image?: ImageInterface;
  isAvailable?: boolean;
  ordinal: number;
}

export enum QuantityActionEnum {
  INACTIVE = "INACTIVE",
  ACTIVE = "ACTIVE",
  DELETE = "DELETE",
}
