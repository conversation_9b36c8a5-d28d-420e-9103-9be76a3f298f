import { DATE_FORMAT } from "@/common/constant";
import { useProjectQuery } from "@/routes/project/services";
import dayjs from "dayjs";
import { useEffect, useMemo } from "react";
import { useParams } from "react-router-dom";
import { StatisticsTypeEnum } from "../../interface";
import { useProjectStatisticsQuery } from "../../service";
import StatisticCell from "./StatisticCell";

const OverallTab = ({ activeKey }: { activeKey: string }) => {
  const projectId = parseInt(useParams().id ?? "0");

  const projectQuery = useProjectQuery(projectId);
  const projectStatisticsHitQuery = useProjectStatisticsQuery(
    projectId,
    StatisticsTypeEnum.HIT,
  );
  const projectStatisticsSessionQuery = useProjectStatisticsQuery(
    projectId,
    StatisticsTypeEnum.SESSION,
  );
  const projectStatisticsSalesRevenueQuery = useProjectStatisticsQuery(
    projectId,
    StatisticsTypeEnum.SALES_REVENUE,
  );
  const projectStatisticsGiftQuery = useProjectStatisticsQuery(
    projectId,
    StatisticsTypeEnum.GIFT,
  );
  const projectStatisticsGameQuery = useProjectStatisticsQuery(
    projectId,
    StatisticsTypeEnum.GAME,
  );

  const topValueGitfAndGame = useMemo(() => {
    const gift =
      projectStatisticsGiftQuery.data?.reduce(
        (previous, current) => previous + current?.kpi,
        0,
      ) ?? 0;
    const game =
      projectStatisticsGameQuery.data?.reduce(
        (previous, current) => previous + current?.kpi,
        0,
      ) ?? 0;

    return gift + game;
  }, [projectStatisticsGameQuery.data, projectStatisticsGiftQuery.data]);

  const bottomValueGitfAndGame = useMemo(() => {
    const gift =
      projectStatisticsGiftQuery.data?.reduce(
        (previous, current) => previous + current?.totalValue,
        0,
      ) ?? 0;
    const game =
      projectStatisticsGameQuery.data?.reduce(
        (previous, current) => previous + current?.totalValue,
        0,
      ) ?? 0;

    return gift + game;
  }, [projectStatisticsGameQuery.data, projectStatisticsGiftQuery.data]);

  useEffect(() => {
    if (activeKey === "OVERALL") {
      projectStatisticsHitQuery.refetch();
      projectStatisticsSessionQuery.refetch();
      projectStatisticsSalesRevenueQuery.refetch();
      projectStatisticsGiftQuery.refetch();
      projectStatisticsGameQuery.refetch();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeKey]);

  return (
    <>
      <p className="text-blue text-[18px]">
        Project: {projectQuery.data?.name}
      </p>

      <p className="mb-2">
        Time: {dayjs(projectQuery.data?.startDate).format(DATE_FORMAT)} -{" "}
        {dayjs(projectQuery.data?.endDate).format(DATE_FORMAT)}
      </p>
      <p className="mt-2">Geo coverage: _</p>

      <p className="text-blue text-[18px]">Overall KPI</p>

      <StatisticCell
        key={"Target hits"}
        topLabel={"Target hits"}
        topValue={projectStatisticsHitQuery.data?.[0]?.kpi}
        bottomLabel={"UTD results"}
        bottomValue={projectStatisticsHitQuery.data?.[0]?.totalValue ?? 0}
        className="mb-5"
      />

      <StatisticCell
        key={"Target sessions"}
        topLabel={"Target sessions"}
        topValue={projectStatisticsSessionQuery.data?.[0]?.kpi}
        bottomLabel={"UTD results"}
        bottomValue={projectStatisticsSessionQuery.data?.[0]?.totalValue ?? 0}
        className="mb-5"
      />

      <StatisticCell
        key={"Target Incentives & gift"}
        topLabel={"Target Incentives & gift"}
        topValue={topValueGitfAndGame}
        bottomLabel={"UTD deliveries"}
        bottomValue={bottomValueGitfAndGame}
        className="mb-5"
      />

      <StatisticCell
        key={"Target sales (VNĐ)"}
        topLabel={"Target sales (VNĐ)"}
        topValue={projectStatisticsSalesRevenueQuery.data?.[0]?.kpi}
        bottomLabel={"UTD results"}
        bottomValue={
          projectStatisticsSalesRevenueQuery.data?.[0]?.totalValue ?? 0
        }
        className="mb-5"
      />
    </>
  );
};

export default OverallTab;
