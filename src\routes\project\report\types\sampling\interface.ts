import { AbstractEntityInterface } from "@/common/interface.ts";
import { FeatureSamplingInterface } from "@project/component/feature/config/types/sampling/interface.ts";
import { ProjectRecordFeatureInterface } from "../../interface.ts";

export interface RecordSamplingValueInterface extends AbstractEntityInterface {
  featureSamplingId: number;
  featureSampling?: FeatureSamplingInterface;
  conversionRate: number;
  conversionValue: number;
  value: number;
}

export interface RecordSamplingInterface extends AbstractEntityInterface {
  dataUuid: string;
  dataTimestamp: string;
  projectRecordFeature: ProjectRecordFeatureInterface;
  recordSamplingValues: RecordSamplingValueInterface[];
}
