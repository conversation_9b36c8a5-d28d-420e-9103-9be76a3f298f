import {
  AppState,
  Auth0Provider,
  Auth0ProviderOptions,
} from "@auth0/auth0-react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ConfigProvider } from "antd";
import viVN from "antd/locale/vi_VN";
import dayjs from "dayjs";
import "dayjs/locale/vi";
import timezone from "dayjs/plugin/timezone";
import utc from "dayjs/plugin/utc";
import React, { PropsWithChildren } from "react";
import ReactDOM from "react-dom/client";
import App from "./App";
import { AppProvider } from "./AppProvider";
import { HOME_PAGE } from "./common/url.helper";
import "./index.css";

// eslint-disable-next-line react-refresh/only-export-components
const Auth0ProviderWithRedirectCallback = ({
  children,
  ...props
}: PropsWithChildren<Auth0ProviderOptions>) => {
  const onRedirectCallback = (appState?: AppState) => {
    location.replace(appState?.returnTo ?? HOME_PAGE);
  };

  return (
    <Auth0Provider onRedirectCallback={onRedirectCallback} {...props}>
      {children}
    </Auth0Provider>
  );
};

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
    },
  },
});

dayjs.locale("vi");
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.tz.setDefault("Asia/Bangkok");

ReactDOM.createRoot(document.getElementById("root") as HTMLElement).render(
  <React.StrictMode>
    <Auth0ProviderWithRedirectCallback
      domain={import.meta.env.VITE_AUTH0_DOMAIN}
      clientId={import.meta.env.VITE_AUTH0_CLIENT_ID}
      authorizationParams={{
        redirect_uri: HOME_PAGE,
        audience: import.meta.env.VITE_AUTH0_AUDIENCE,
        scope: "openid profile email offline_access",
      }}
      cacheLocation="localstorage"
    >
      <ConfigProvider
        locale={viVN}
        theme={{
          token: {
            colorInfoBorder: "#EBF1FF",
            colorInfoBg: "#EBF1FF",
            colorLink: "#000000",
            colorTextPlaceholder: "#8C8C8D",
          },
          components: {
            Layout: {
              siderBg: "#fff",
              headerBg: "#fff",
            },
            Menu: {
              itemBg: "#fff",
              itemSelectedBg: "#34E1D1",
              itemSelectedColor: "#fff",
              groupTitleColor: "#393939",
              groupTitleFontSize: 14,
              groupTitleLineHeight: "22px",
              subMenuItemBg: "",
              borderRadius: 4,
              itemBorderRadius: 4,
            },
            Button: {
              borderRadius: 4,
              fontWeight: 600,
              colorLink: "#000",
              colorLinkHover: "#000",
              colorPrimaryBg: "#34E1D1",
              colorPrimary: "#34E1D1",
              colorPrimaryHover: "#E35E5E",
              colorPrimaryActive: "#B52424",
              defaultBg: "#FFFFFF",
              defaultColor: "#393939",
              defaultBorderColor: "#ECEDEF",
              defaultHoverBg: "#FFFFFF",
              defaultHoverColor: "#393939",
              defaultActiveBg: "#E3E4E7",
              defaultActiveColor: "#393939",
              defaultHoverBorderColor: "#D9D9D9",
              defaultActiveBorderColor: "#ECEDEF",
            },
            Select: {
              borderRadius: 4,
              controlHeight: 40,
            },
            Input: {
              borderRadius: 4,
              controlHeight: 40,
            },
            InputNumber: {
              controlHeight: 40,
              borderRadius: 4,
            },
            Table: {
              headerBg: "#E8F3FC",
              headerColor: "#504646",
              borderColor: "#ECEDEF",
              colorBgContainer: "#fff",
              headerBorderRadius: 4,
              borderRadius: 4,
            },
            Pagination: {
              colorPrimary: "#34E1D1",
              colorPrimaryHover: "#34E1D1",
              borderRadius: 4,
              controlHeight: 40,
            },
            Modal: {
              borderRadius: 4,
              borderRadiusLG: 4,
            },
            Switch: {
              colorBgBase: "#27AE60",
              colorBgContainer: "#27AE60",
              colorPrimary: "#27AE60",
              colorPrimaryHover: "#27AE60",
            },
            DatePicker: {
              controlHeight: 40,
            },
            Form: {
              itemMarginBottom: 20,
              verticalLabelMargin: "0 0 4px",
              verticalLabelPadding: 0,
            },
            Tabs: {
              horizontalMargin: "0 0 24px 0",
            },
            Collapse: {
              contentPadding: "0 0 0 0",
              headerPadding: "30px 0 0 0",
            },
          },
        }}
      >
        <AppProvider>
          <QueryClientProvider client={queryClient}>
            <App />
            {/* <ReactQueryDevtools buttonPosition="bottom-right" /> */}
          </QueryClientProvider>
        </AppProvider>
      </ConfigProvider>
    </Auth0ProviderWithRedirectCallback>
  </React.StrictMode>,
);
