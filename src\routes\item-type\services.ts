import { useApp } from "@/UseApp";
import { AbstractFilterInterface } from "@/common/interface";
import { AppContextInterface } from "@/interface.ts";
import { useMutation, useQuery } from "@tanstack/react-query";
import { ApiItemTypeResponseInterface } from "./interface";

export const getItemTypes = async (
  axiosGet: AppContextInterface["axiosGet"],
  filter: unknown,
) => {
  return await axiosGet<ApiItemTypeResponseInterface, unknown>(
    "/item-types",
    filter,
  );
};

export const useItemTypesQuery = (
  filter: {
    clientId?: number;
    getInActive?: boolean;
  } & AbstractFilterInterface,
  enabled?: boolean,
) => {
  const { axiosGet } = useApp();
  const { getInActive, ...restFilter } = filter;
  const queryFilter = {
    ...(getInActive ? {} : { isActive: true }),
    ...restFilter,
  };

  return useQuery({
    queryKey: ["itemTypes", queryFilter],
    queryFn: () =>
      axiosGet<ApiItemTypeResponseInterface, unknown>(
        "/item-types",
        queryFilter,
      ),
    enabled,
  });
};

export const useDeleteItemTypeMutation = () => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deleteItemType"],
    mutationFn: (id: number) => axiosDelete(`/item-types/${id}`),
  });
};
