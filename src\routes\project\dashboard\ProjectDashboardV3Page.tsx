import InnerContainer from "@/components/InnerContainer/InnerContainer";
import SuspenseComponent from "@/components/SuspenseComponent";
import { Tabs, type TabsProps } from "antd";
import React, { useEffect, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import { ProjectDashboardTypeEnum } from "../configDashboard/interface";
import { useGetProjectDashboards } from "../configDashboard/service";

const AttendanceDailyTab = React.lazy(
  () => import("./types/attendanceDaily/AttendanceDailyTab"),
);
const UrgencyTab = React.lazy(() => import("./types/urgency/UrgencyTab"));
const QuantityDailyTab = React.lazy(
  () => import("./types/quantityDaily/QuantityDailyTab"),
);
const QuantityAverageTab = React.lazy(
  () => import("./types/quantityAverage/QuantityAverageTab"),
);
const NumericSheetWeeklyAverageTab = React.lazy(
  () =>
    import("./types/numericSheetWeeklyAverage/NumericSheetWeeklyAverageTab"),
);
const NumericSheetTab = React.lazy(
  () => import("./types/numericSheet/NumericSheetTab"),
);
const InventoryTab = React.lazy(() => import("./types/inventory/InventoryTab"));
const OosStoreLevelTab = React.lazy(
  () => import("./types/oosStoreLevel/OosStoreLevelTab"),
);
const OosTab = React.lazy(() => import("./types/oos/OosTab"));
const NumericSheetEachAttributeTab = React.lazy(
  () =>
    import("./types/numericSheetEachAttribute/NumericSheetEachAttributeTab"),
);

const ProjectDashboardV3Page = () => {
  const projectId = parseInt(useParams().id ?? "0");

  const { data } = useGetProjectDashboards(projectId);

  const [activeKey, setActiveKey] = useState<string>();

  const items: TabsProps["items"] = useMemo(
    () =>
      data?.entities
        ?.filter((dashboard) =>
          [
            ProjectDashboardTypeEnum.ATTENDANCE_DAILY,
            ProjectDashboardTypeEnum.URGENCY,
            ProjectDashboardTypeEnum.QUANTITY_DAILY,
            ProjectDashboardTypeEnum.QUANTITY_AVERAGE,
            ProjectDashboardTypeEnum.NUMERIC_SHEET_WEEKLY_AVERAGE,
            ProjectDashboardTypeEnum.NUMERIC_SHEET,
            ProjectDashboardTypeEnum.INVENTORY,
            ProjectDashboardTypeEnum.OOS_STORE_LEVEL,
            ProjectDashboardTypeEnum.OOS,
            ProjectDashboardTypeEnum.NUMERIC_SHEET_EACH_ATTRIBUTE,
          ].includes(dashboard.type),
        )
        .map((dashboard) => {
          switch (dashboard.type) {
            case ProjectDashboardTypeEnum.ATTENDANCE_DAILY:
              return {
                key: dashboard.id.toString(),
                label: dashboard.name,
                children: (
                  <SuspenseComponent>
                    {dashboard.id.toString() === activeKey && (
                      <AttendanceDailyTab
                        projectId={projectId}
                        dashboardId={dashboard.id}
                      />
                    )}
                  </SuspenseComponent>
                ),
              };
            case ProjectDashboardTypeEnum.URGENCY:
              return {
                key: dashboard.id.toString(),
                label: dashboard.name,
                children: (
                  <SuspenseComponent>
                    <UrgencyTab
                      projectId={projectId}
                      dashboardId={dashboard.id}
                    />
                  </SuspenseComponent>
                ),
              };
            case ProjectDashboardTypeEnum.QUANTITY_DAILY:
              return {
                key: dashboard.id.toString(),
                label: dashboard.name,
                children: (
                  <SuspenseComponent>
                    <QuantityDailyTab
                      projectId={projectId}
                      dashboardId={dashboard.id}
                    />
                  </SuspenseComponent>
                ),
              };
            case ProjectDashboardTypeEnum.QUANTITY_AVERAGE:
              return {
                key: dashboard.id.toString(),
                label: dashboard.name,
                children: (
                  <SuspenseComponent>
                    <QuantityAverageTab
                      projectId={projectId}
                      dashboardId={dashboard.id}
                    />
                  </SuspenseComponent>
                ),
              };
            case ProjectDashboardTypeEnum.NUMERIC_SHEET_WEEKLY_AVERAGE:
              return {
                key: dashboard.id.toString(),
                label: dashboard.name,
                children: (
                  <SuspenseComponent>
                    <NumericSheetWeeklyAverageTab
                      projectId={projectId}
                      dashboardId={dashboard.id}
                    />
                  </SuspenseComponent>
                ),
              };
            case ProjectDashboardTypeEnum.NUMERIC_SHEET:
              return {
                key: dashboard.id.toString(),
                label: dashboard.name,
                children: (
                  <SuspenseComponent>
                    <NumericSheetTab
                      projectId={projectId}
                      dashboardId={dashboard.id}
                    />
                  </SuspenseComponent>
                ),
              };
            case ProjectDashboardTypeEnum.INVENTORY:
              return {
                key: dashboard.id.toString(),
                label: dashboard.name,
                children: (
                  <SuspenseComponent>
                    <InventoryTab
                      projectId={projectId}
                      dashboardId={dashboard.id}
                    />
                  </SuspenseComponent>
                ),
              };
            case ProjectDashboardTypeEnum.OOS_STORE_LEVEL:
              return {
                key: dashboard.id.toString(),
                label: dashboard.name,
                children: (
                  <SuspenseComponent>
                    <OosStoreLevelTab
                      projectId={projectId}
                      dashboardId={dashboard.id}
                    />
                  </SuspenseComponent>
                ),
              };
            case ProjectDashboardTypeEnum.OOS:
              return {
                key: dashboard.id.toString(),
                label: dashboard.name,
                children: (
                  <SuspenseComponent>
                    <OosTab projectId={projectId} dashboardId={dashboard.id} />
                  </SuspenseComponent>
                ),
              };
            case ProjectDashboardTypeEnum.NUMERIC_SHEET_EACH_ATTRIBUTE:
              return {
                key: dashboard.id.toString(),
                label: dashboard.name,
                children: (
                  <SuspenseComponent>
                    <NumericSheetEachAttributeTab
                      projectId={projectId}
                      dashboardId={dashboard.id}
                    />
                  </SuspenseComponent>
                ),
              };
            default:
              return {
                key: dashboard.id.toString(),
                label: dashboard.name,
                children: <>NOT SUPPORTED</>,
              };
          }
        }) ?? [],
    [activeKey, data?.entities, projectId],
  );

  useEffect(() => {
    if (items.length > 0) {
      setActiveKey(data?.entities[0].id.toString());
    }
  }, [data?.entities, items.length]);

  return (
    <>
      <h2>Dashboard</h2>
      <InnerContainer>
        <Tabs items={items} activeKey={activeKey} onChange={setActiveKey} />
      </InnerContainer>
    </>
  );
};

export default ProjectDashboardV3Page;
