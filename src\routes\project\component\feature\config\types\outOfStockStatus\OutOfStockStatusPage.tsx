import CustomMenu from "@/components/customMenu/CustomMenu";
import { Col, Row, Skeleton } from "antd";
import { Suspense, useEffect, useMemo, useState } from "react";
import { Link, Outlet } from "react-router-dom";
import { OutOfStockStatusStepEnum } from "./interface";

const OutOfStockStatusPage = () => {
  const [activeKey, setActiveKey] = useState<string>(
    OutOfStockStatusStepEnum.STATUS,
  );

  const items = useMemo(() => {
    return [
      {
        key: OutOfStockStatusStepEnum.STATUS,
        label: (
          <Link to={OutOfStockStatusStepEnum.STATUS}>
            • Đ<PERSON><PERSON> nghĩa trạng thái
          </Link>
        ),
      },
      {
        key: OutOfStockStatusStepEnum.ZONE,
        label: (
          <Link to={OutOfStockStatusStepEnum.ZONE}>
            • <PERSON><PERSON> b<PERSON><PERSON> vị trí ghi nhận
          </Link>
        ),
      },
      {
        key: OutOfStockStatusStepEnum.GROUP,
        label: <Link to={OutOfStockStatusStepEnum.GROUP}>• Nhóm sản phẩm</Link>,
      },
      {
        key: OutOfStockStatusStepEnum.OUTLET,
        label: (
          <Link to={OutOfStockStatusStepEnum.OUTLET}>
            • Phân bổ outlet vào nhóm sản phẩm
          </Link>
        ),
      },
      {
        key: OutOfStockStatusStepEnum.MERGED_PRODUCT,
        label: (
          <Link to={OutOfStockStatusStepEnum.MERGED_PRODUCT}>
            • Gộp sản phẩm
          </Link>
        ),
      },
      {
        key: OutOfStockStatusStepEnum.THRESHOLD_PRODUCT,
        label: (
          <Link to={OutOfStockStatusStepEnum.THRESHOLD_PRODUCT}>
            • Ràng buộc trạng thái sản phẩm thường
          </Link>
        ),
      },
      {
        key: OutOfStockStatusStepEnum.THRESHOLD_MERGED_PRODUCT,
        label: (
          <Link to={OutOfStockStatusStepEnum.THRESHOLD_MERGED_PRODUCT}>
            • Ràng buộc trạng thái sản phẩm gộp
          </Link>
        ),
      },
    ];
  }, []);

  useEffect(() => {
    const activeItem = items.find((item) =>
      location.pathname.endsWith(item.key),
    );
    if (activeItem) {
      setActiveKey(activeItem.key.toString());
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location.pathname]);

  return (
    <div className="bg-white pt-5 pl-10 rounded pb-5 mt-5 pr-10">
      <Row className="" justify={"space-between"}>
        <Col span={4}>
          <CustomMenu
            items={items}
            mode="inline"
            onClick={({ key }) => {
              setActiveKey(key);
            }}
            selectedKeys={[activeKey]}
          />
        </Col>
        <Col span={19}>
          <Suspense fallback={<Skeleton loading />}>
            <Outlet />
          </Suspense>
        </Col>
      </Row>
    </div>
  );
};

export default OutOfStockStatusPage;
