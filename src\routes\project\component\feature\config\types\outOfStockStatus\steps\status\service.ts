import { AbstractFilterInterface } from "@/common/interface.ts";
import { useApp } from "@/UseApp.tsx";
import { OosLevelInterface } from "@project/component/feature/config/types/outOfStockStatus/interface.ts";
import { useMutation, useQuery } from "@tanstack/react-query";

export const useOosLevelsQuery = (
  featureId: number,
  filter?: AbstractFilterInterface & object,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["oosLevels", featureId, filter],
    queryFn: () =>
      axiosGet<{ entities: OosLevelInterface[]; count: number }, unknown>(
        `/features/${featureId}/oos-levels`,
        filter,
      ),
  });
};

export const useCreateOosLevelMutation = (featureId: number) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createOosLevel", featureId],
    mutationFn: (data: {
      name: string;
      backgroundColor: string;
      foregroundColor: string;
      isStandard: boolean;
    }) => axiosPost(`/features/${featureId}/oos-levels`, data),
  });
};

export const useUpdateOosLevelMutation = (featureId: number) => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["updateOosLevel", featureId],
    mutationFn: (data: {
      id: number;
      name?: string;
      backgroundColor?: string;
      foregroundColor?: string;
      isStandard?: boolean;
      isActive?: boolean;
    }) => axiosPatch(`/features/${featureId}/oos-levels/${data.id}`, data),
  });
};

export const useDeleteOosLevelMutation = (featureId: number) => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deleteOosLevel", featureId],
    mutationFn: (id: number) =>
      axiosDelete(`/features/${featureId}/oos-levels/${id}`),
  });
};

export const useArrangeOosLevelMutation = (featureId: number) => {
  const { axiosPut } = useApp();

  return useMutation({
    mutationKey: ["arrangeOosLevel", featureId],
    mutationFn: ({
      id,
      overFeatureOosLevelId,
    }: {
      id: number;
      overFeatureOosLevelId: number;
    }) =>
      axiosPut(`/features/${featureId}/oos-levels/${id}/arrangement`, {
        overFeatureOosLevelId,
      }),
  });
};
