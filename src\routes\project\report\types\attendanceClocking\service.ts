import { useApp } from "@/UseApp.tsx";
import { AbstractFilterInterface } from "@/common/interface.ts";
import { AppContextInterface } from "@/interface.ts";
import { useMutation, useQuery } from "@tanstack/react-query";
import { ProjectRecordInterface } from "../../interface";
import { RecordAttendanceInterface } from "./interface";

export const getReportAttendances = (
  axiosGet: AppContextInterface["axiosGet"],
  projectId: number,
  componentFeatureId: number,
  filter?: object & AbstractFilterInterface,
) =>
  axiosGet<
    {
      entities: RecordAttendanceInterface[];
      count: number;
    },
    unknown
  >(
    `/projects/${projectId}/report/features/${componentFeatureId}/attendances`,
    filter,
  );

export const useReportAttendanceQuery = (
  projectId: number,
  componentFeatureId: number,
  filter?: object & AbstractFilterInterface,
  isEnabled?: boolean,
) => {
  const { axiosGet } = useApp();
  const isEnabledValue = isEnabled ?? true;

  return useQuery({
    queryKey: ["reportAttendance", projectId, componentFeatureId, filter],
    queryFn: () =>
      getReportAttendances(axiosGet, projectId, componentFeatureId, filter),
    enabled: isEnabledValue,
  });
};

export const useReportAttendanceBoothsQuery = (
  projectId: number,
  componentFeatureId: number,
  filter?: object & AbstractFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["reportAttendanceBooths", projectId, componentFeatureId, filter],
    queryFn: () =>
      axiosGet<
        {
          entities: ProjectRecordInterface[];
          count: number;
        },
        unknown
      >(
        `/projects/${projectId}/report/features/${componentFeatureId}/attendances/booths`,
        filter,
      ),
  });
};

export const useGetReportAttendancesMutation = (
  projectId: number,
  componentFeatureId: number,
) => {
  const { axiosGet } = useApp();

  return useMutation({
    mutationKey: ["getReportAttendances", projectId, componentFeatureId],
    mutationFn: (filter?: object & AbstractFilterInterface) =>
      getReportAttendances(axiosGet, projectId, componentFeatureId, filter),
  });
};
