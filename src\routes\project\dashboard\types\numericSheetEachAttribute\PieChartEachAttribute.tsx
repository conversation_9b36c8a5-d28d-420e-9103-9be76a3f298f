import { randomColor } from "@/common/helper";
import Chart<PERSON>ontanier from "@/routes/project/chart/ChartContanier";
import { Pie } from "@ant-design/charts";
import { useMemo } from "react";

interface PieChartEachAttributeProps {
  data: {
    name: string;
    code: string;
    attribute: {
      name: string;
      backgroundColor: string | null;
      foregroundColor: string | null;
    };
    total: number;
    backgroundColor: string | null;
    foregroundColor: string | null;
  }[];
  name: string;
}
const PieChartEachAttribute = ({ data, name }: PieChartEachAttributeProps) => {
  const pieData = useMemo(
    () =>
      data.map((item) => ({
        name: item.name,
        total: item.total,
        backgroundColor: item.backgroundColor,
      })),
    [data],
  );

  const total = useMemo(
    () => pieData.reduce((sum, item) => sum + item.total, 0),
    [pieData],
  );

  const pieConfig = useMemo(
    () => ({
      appendPadding: 10,
      data: pieData,
      angleField: "total",
      colorField: "name",
      radius: 0.8,
      interactions: [
        {
          type: "element-active",
        },
      ],
      label: {
        text: (d: { total: number; name: string }) =>
          `${d.total}  (${Math.round((d.total / total) * 100)}%)`,
        position: "spider",
        fontSize: 12,
        fontWeight: "bold",
      },
      legend: {
        color: {
          position: "bottom",
          layout: {
            justifyContent: "center",
          },
        },
      },
      title: {
        title: `Dashboard ${name} instore total`,
        subtitle: `Tỷ lệ ${name} giữa Sabeco và đối thủ`,
      },
      tooltip: {
        field: "value",
        title: (d: { name: string }) => d.name,
        value: (d: { total: number }) => d.total,
      },
      scale: {
        color: {
          palette: pieData.map((item) => item.backgroundColor ?? randomColor()),
        },
      },
    }),
    [name, pieData, total],
  );

  return (
    <ChartContanier>
      <Pie {...pieConfig} height={500} />
    </ChartContanier>
  );
};

export default PieChartEachAttribute;
