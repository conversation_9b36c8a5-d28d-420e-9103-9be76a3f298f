import {
  createFileAndDownLoad,
  createTemplateAndDownload,
} from "@/common/export-excel.helper";
import {
  getErrorMessageFromAxiosError,
  validateAndClearVietnamPhoneNumber,
} from "@/common/helper";
import {
  setExcelError,
  validateExcelData,
} from "@/common/import-excel.helper.ts";
import { ImportStatus } from "@/common/interface";
import { GENDER } from "@/routes/user/interface";
import { useFindOrCreateUserMutation } from "@/routes/user/services";
import {
  CheckCircleTwoTone,
  CloseCircleTwoTone,
  CloseOutlined,
  DownloadOutlined,
  InboxOutlined,
  LoadingOutlined,
} from "@ant-design/icons";
import { ProjectAgencyInterface } from "@project/interface";
import { RoleInterface } from "@project/role/interface";
import { Alert, Button, Form, Modal, Space, Table, Upload } from "antd";
import { UploadFile } from "antd/lib";
import { AxiosError } from "axios";
import dayjs from "dayjs";
import Excel from "exceljs";
import Joi from "joi";
import React, { useCallback, useState } from "react";
import { Importer } from "xlsx-import/lib/Importer";
import { useCreateEmployeeMutation } from "../../service";

const { Dragger } = Upload;

interface EmployeeLeaderImportDataInterface {
  name: string;
  phone: string;
  email: string;
  gender: "Nam" | "Nữ" | "Khác";
  agency: string;
  id: number;
  status?: ImportStatus;
  errorMessage?: string;
  projectAgencyId: number;
}

const employeeLeaderImportDataScheme = Joi.object({
  name: Joi.string().trim().required(),
  phone: Joi.string().trim().required(),
  email: Joi.string().trim().required(),
  gender: Joi.string().trim().valid("Nam", "Nữ", "Khác").required(),
  agency: Joi.string().trim().required(),
  id: Joi.number().required(),
  status: Joi.string()
    .valid("pending", "success", "error")
    .allow("")
    .optional(),
  errorMessage: Joi.string().allow("").optional(),
});

const headers = [
  { index: 1, header: "Họ và tên", key: "name" },
  { index: 2, header: "Số điện thoại", key: "phone" },
  { index: 3, header: "Email", key: "email" },
  { index: 4, header: "Giới tính", key: "gender" },
  { index: 5, header: "Agency phụ trách", key: "agency" },
];

export default function ImportEmployeeLeaderModal({
  isOpen,
  role,
  projectAgencies,
  setIsOpen,
  projectId,
}: Readonly<{
  isOpen: boolean;
  role: RoleInterface;
  projectAgencies: ProjectAgencyInterface[];
  setIsOpen: (arg0: boolean) => void;
  projectId: number;
  leaderRole?: RoleInterface;
}>) {
  const [importData, setImportData] = useState<
    EmployeeLeaderImportDataInterface[]
  >([]);
  const [isDataVisible, setIsDataVisible] = useState(false);
  const [displayData, setDisplayData] = useState<
    EmployeeLeaderImportDataInterface[]
  >([]);
  const [successfulImports, setSuccessfulImports] = useState<
    EmployeeLeaderImportDataInterface[]
  >([]);
  const [failedImports, setFailedImports] = useState<
    EmployeeLeaderImportDataInterface[]
  >([]);
  const [isImporting, setIsImporting] = useState(false);
  const tableRef: Parameters<typeof Table>[0]["ref"] = React.useRef(null);
  const [uploadForm] = Form.useForm();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [isFileError, setIsFileError] = useState(false);
  const [excelErrorMessage, setExcelErrorMessage] = useState<string>("");

  const findOrCreateUserMutation = useFindOrCreateUserMutation();
  const createEmployeeMutation = useCreateEmployeeMutation(projectId);

  const downloadTemplate = useCallback(async () => {
    const filename = `Import truong nhom ${dayjs().format("DDMMYY")}`;
    await createTemplateAndDownload(
      headers,
      filename,
      [
        { index: 4, list: ["Nam", "Nữ", "Khác"] },
        {
          index: 5,
          list: projectAgencies.map(
            (projectAgency) => projectAgency.agency.name,
          ),
        },
      ],
      [
        {
          color: "Ff0000",
          message: "Bắt buộc điền",
        },
        {
          color: "Ff0000",
          message: "Bắt buộc điền",
        },
        {
          color: "Ff0000",
          message: "Bắt buộc điền",
        },
        {
          color: "Ff0000",
          message: "Bắt buộc điền",
        },
        {
          color: "Ff0000",
          message: "Bắt buộc điền",
        },
      ],
    );
  }, [projectAgencies]);

  const processExcelFile = useCallback(async (file: File) => {
    const buffer = await file.arrayBuffer();
    const workbook = new Excel.Workbook();
    await workbook.xlsx.load(buffer);

    const config = {
      worksheet: "Data",
      type: "list",
      rowOffset: 0,
      columns: headers,
    };

    const importer = new Importer(workbook);
    const data =
      importer.getAllItems<EmployeeLeaderImportDataInterface>(config);

    validateExcelData(data, headers);

    setDisplayData(data.map((item, index) => ({ ...item, id: index })));
    setImportData(data.map((item, index) => ({ ...item, id: index })));
  }, []);

  const beforeUploadHandler = useCallback(
    (file: File) => {
      if (file) {
        processExcelFile(file).catch(({ message }: { message: string }) => {
          setExcelError(
            message,
            setFileList,
            setIsFileError,
            setExcelErrorMessage,
          );
        });
      }

      return false;
    },
    [processExcelFile],
  );

  const importHandler = useCallback(async () => {
    const validatePhone = async (
      value: Promise<EmployeeLeaderImportDataInterface>,
    ) => {
      const item = await value;
      const { phone } = item;
      const phoneValidateResult = validateAndClearVietnamPhoneNumber(phone);

      if (!phoneValidateResult.isValid) {
        throw new Error(`Số điện thoại không hợp lệ`);
      }
      item.phone = phoneValidateResult.cleanedPhoneNumber;

      return item;
    };

    const validateAgency = async (
      value: Promise<EmployeeLeaderImportDataInterface>,
    ) => {
      const item = await value;
      const { agency } = item;

      const projectAgencyId = projectAgencies.find(
        (projectAgency) =>
          projectAgency.agency.name.toString().toLocaleLowerCase() ===
          agency.toString()?.toLocaleLowerCase(),
      )?.id;

      if (!projectAgencyId) {
        throw new Error(`Không tìm thấy agency ${agency}`);
      }
      item.projectAgencyId = projectAgencyId;

      return item;
    };

    const processItem = async (item: EmployeeLeaderImportDataInterface) => {
      const itemIndex = displayData.findIndex(
        (dataItem) => dataItem.id === item.id,
      );

      if (itemIndex === -1) {
        console.warn(`Item with email ${item.id} not found in dataShow`);
        return;
      }
      await new Promise((resolve) => setTimeout(resolve, 200));

      if (tableRef.current) {
        tableRef.current?.scrollTo({ index: itemIndex });
      }
      displayData[itemIndex].status = "doing";
      setDisplayData([...displayData]);

      await employeeLeaderImportDataScheme
        .custom(validatePhone)
        .custom(validateAgency)
        .validateAsync(item)
        .then(async (item: EmployeeLeaderImportDataInterface) => {
          try {
            const user = await findOrCreateUserMutation.mutateAsync({
              phone: item.phone,
              email: item.email,
              name: item.name,
              password: "123456",
              type: "EMPLOYEE",
              username: item.phone,
              gender: GENDER[item.gender],
            });

            await createEmployeeMutation.mutateAsync({
              projectAgencyId: item.projectAgencyId,
              roleId: role.id,
              userId: user.id,
            });

            await new Promise((resolve) => setTimeout(resolve, 500));

            displayData[itemIndex].status = "success";
            setDisplayData([...displayData]);
            setSuccessfulImports((importSuccess) => [...importSuccess, item]);
          } catch (error) {
            if (error instanceof AxiosError) {
              displayData[itemIndex].status = "error";
              displayData[itemIndex].errorMessage =
                getErrorMessageFromAxiosError(error);
              setDisplayData([...displayData]);
              setFailedImports((importError) => [
                ...importError,
                displayData[itemIndex],
              ]);
            }
          }
        })
        .catch(async (error: Error) => {
          displayData[itemIndex].status = "error";
          displayData[itemIndex].errorMessage = error.message;
          setDisplayData([...displayData]);
          setFailedImports((importError) => [
            ...importError,
            displayData[itemIndex],
          ]);
        });
    };

    const processItems = async (items: EmployeeLeaderImportDataInterface[]) => {
      setIsImporting(true);
      for (const item of items) {
        await processItem(item);
      }
      setIsImporting(false);
    };

    await processItems(importData);
  }, [
    importData,
    projectAgencies,
    displayData,
    findOrCreateUserMutation,
    createEmployeeMutation,
    role.id,
  ]);

  const handleImportErrorClick = useCallback(async () => {
    const fileName = `Import ${role.name} loi `;
    const importErrorStrings = failedImports.map((item) => [
      ...headers.map(
        (header) => item[header.key as keyof typeof item]?.toString() ?? "",
      ),
      item.errorMessage ?? "",
    ]);

    const headerStrings = [...headers.map((header) => header.header), "Lỗi"];

    await createFileAndDownLoad({
      data: importErrorStrings,
      headers: headerStrings,
      fileName,
    });
  }, [failedImports, role.name]);

  const handleModalClose = useCallback(() => {
    setIsOpen(false);
    uploadForm.resetFields();
    setIsDataVisible(false);
    setIsImporting(false);
    setImportData([]);
    setDisplayData([]);
    setSuccessfulImports([]);
    setFailedImports([]);
    setFileList([]);
    setIsFileError(false);
    setExcelErrorMessage("");
  }, [setIsOpen, uploadForm]);

  return (
    <>
      <Modal
        open={isOpen}
        closable={false}
        styles={{ content: { padding: 0 } }}
        footer={false}
      >
        <div className={"pl-10 pr-10"}>
          <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
            <h2 className="text-neutral-700 text-2xl font-semibold ">
              {`Import ${role.name}`}
            </h2>
            <div className="pt-5">
              <Button
                type="link"
                onClick={handleModalClose}
                size="large"
                icon={<CloseOutlined />}
              />
            </div>
          </div>
          <Alert
            message={
              <>
                <span>
                  Để thực hiện thêm mới {role.name} bằng cách import file excel
                  bạn cần sử dụng đúng template.{" "}
                </span>
                <Button
                  className="ml-0 pl-0 text-blue"
                  type="link"
                  onClick={downloadTemplate}
                >
                  Tải file template
                </Button>
              </>
            }
            type="info"
            showIcon
          />
          <br />
          <Form form={uploadForm}>
            <Form.Item name={"upload"}>
              <Dragger
                className={"pt-5"}
                listType={"picture"}
                accept={".xlsx, .xlsm"}
                multiple={false}
                beforeUpload={beforeUploadHandler}
                maxCount={1}
                fileList={fileList}
                onChange={({ fileList }) => {
                  if (fileList.length === 0) {
                    setIsFileError(false);
                  }
                  setFileList(fileList);
                }}
              >
                <p className="ant-upload-drag-icon">
                  <InboxOutlined />
                </p>
                <p className="ant-upload-text">
                  Kéo thả hoặc chọn file để tải lên
                </p>
                <p className="ant-upload-hint">Định dạng hỗ trợ: xlsm, xlsx</p>
              </Dragger>

              {isFileError && (
                <p className={"text-[#F73A3A]"}>{excelErrorMessage}</p>
              )}
            </Form.Item>
          </Form>
        </div>
        <div className="flex justify-end gap-4 py-4 rounded max-md:max-w-full max-md:flex-wrap pb-4 pl-10 pr-10 bg-[#F7F8FA]">
          <Button htmlType="button" onClick={handleModalClose}>
            Đóng
          </Button>
          <Button
            htmlType="submit"
            type={"primary"}
            disabled={fileList.length === 0 || isFileError}
            onClick={() => {
              setIsDataVisible(true);
              setIsOpen(false);
              importHandler();
            }}
          >
            Import
          </Button>
        </div>
      </Modal>

      <Modal
        open={isDataVisible}
        width={"90%"}
        closable={false}
        styles={{ content: { padding: 0 } }}
        closeIcon={null}
        footer={null}
      >
        <div className={"pl-10 pr-10"}>
          <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
            <h2 className="text-neutral-700 text-2xl font-semibold ">
              {`Import ${role.name}`}
            </h2>
            <div className="pt-5">
              <Button
                type="link"
                onClick={handleModalClose}
                size="large"
                icon={<CloseOutlined />}
                disabled={isImporting}
              />
            </div>
          </div>
        </div>
        <div className={"pl-10 pr-10 mb-8"}>
          <p className={"m-0"}>
            Bạn đã tải lên {importData.length} dòng dữ liệu, phía dưới là kết
            quả sau khi import
          </p>

          <Space className="pb-3">
            <Alert
              type="success"
              showIcon
              message={`Import thành công: ${successfulImports.length}`}
            />
            <Alert
              type="error"
              showIcon
              message={`Import thất bại: ${failedImports.length}`}
            />
          </Space>

          <Table
            ref={tableRef}
            rowKey={(record) => record.id as React.Key}
            dataSource={displayData}
            scroll={{ x: "max-content", y: 450 }}
            columns={[
              ...headers.map((header) => ({
                title: header.header,
                dataIndex: header.key,
              })),
              {
                title: "Trạng thái",
                fixed: "right",
                dataIndex: "status",
                className: "min-w-[100px]",
                render: (value: ImportStatus, record) => {
                  switch (value) {
                    case "doing":
                      return (
                        <>
                          <LoadingOutlined /> Đang import
                        </>
                      );
                    case "success":
                      return (
                        <>
                          <CheckCircleTwoTone twoToneColor="#52c41a" /> Đã
                          import
                        </>
                      );
                    case "error":
                      return (
                        <>
                          <CloseCircleTwoTone twoToneColor="#f5222d" />{" "}
                          {record.errorMessage}
                        </>
                      );
                    default:
                      return null;
                  }
                },
              },
            ]}
            pagination={false}
          />
        </div>
        <div className="flex justify-between gap-4 py-4 rounded max-md:max-w-full max-md:flex-wrap pb-4 pl-10 pr-10 bg-[#F7F8FA]">
          {failedImports.length + successfulImports.length <
          importData.length ? (
            <>
              {isImporting && (
                <>
                  <p>
                    <LoadingOutlined /> Đang tiến hành import
                  </p>
                  <p>Vui lòng không tắt popup cho đến khi hoàn thành</p>
                </>
              )}
            </>
          ) : (
            <>
              <p>Hoàn thành quá trình import</p>
              <Space>
                <Button type="default" onClick={handleModalClose}>
                  Đóng
                </Button>
                <Button
                  type="default"
                  icon={<DownloadOutlined />}
                  onClick={handleImportErrorClick}
                  disabled={failedImports.length === 0}
                >
                  Tải data lỗi import
                </Button>
              </Space>
            </>
          )}
        </div>
      </Modal>
    </>
  );
}
