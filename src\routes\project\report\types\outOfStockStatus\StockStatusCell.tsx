import { formatNumber } from "@/common/helper";
import { ProjectOutletStockInterface } from "@/routes/project/outlet/interface";
import { useMemo } from "react";
import {
  NOT_HAVE_VALUE_BG_COLOR,
  NOT_HAVE_VALUE_TEXT_COLOR,
  NOT_SALE_VALUE_BG_COLOR,
  NOT_SALE_VALUE_TEXT_COLOR,
} from "./interface";

interface StockStatusCellProps {
  projectOutletStocks: ProjectOutletStockInterface[];
  projectProductId?: number;
  mergedProductCode?: string;
  isSale?: boolean;
  howToDisplay?: "updatedOnly" | "all";
  notRenderColor?: boolean;
}
const StockStatusCell = ({
  projectOutletStocks,
  projectProductId,
  mergedProductCode,
  isSale,
  howToDisplay,
  notRenderColor = false,
}: StockStatusCellProps) => {
  const projectOutletStockDetail = useMemo(() => {
    if (projectProductId) {
      return projectOutletStocks?.[0]?.projectOutletStockDetails.find(
        (item) => item.featureOosProduct?.projectProductId === projectProductId,
      );
    } else {
      return projectOutletStocks?.[0]?.projectOutletStockDetails.find(
        (item) => item.featureOosMergedProduct?.code === mergedProductCode,
      );
    }
  }, [mergedProductCode, projectOutletStocks, projectProductId]);

  const whenKnownSaleViewAndThisNotUpdateBySale = useMemo(
    () =>
      isSale &&
      howToDisplay === "updatedOnly" &&
      projectOutletStockDetail &&
      !projectOutletStocks?.[0]?.finalized,
    [howToDisplay, isSale, projectOutletStockDetail, projectOutletStocks],
  );

  const value = useMemo(
    () => projectOutletStockDetail?.quantity,
    [projectOutletStockDetail?.quantity],
  );

  const backgroundColor = useMemo(() => {
    if (whenKnownSaleViewAndThisNotUpdateBySale) {
      return NOT_SALE_VALUE_BG_COLOR;
    }

    if (notRenderColor) {
      return NOT_HAVE_VALUE_BG_COLOR;
    }
    const color =
      projectOutletStockDetail?.featureOosThreshold?.featureOosLevel
        ?.backgroundColor ?? NOT_HAVE_VALUE_BG_COLOR;

    return color;
  }, [
    notRenderColor,
    projectOutletStockDetail?.featureOosThreshold?.featureOosLevel
      ?.backgroundColor,
    whenKnownSaleViewAndThisNotUpdateBySale,
  ]);

  const foregroundColor = useMemo(() => {
    if (whenKnownSaleViewAndThisNotUpdateBySale) {
      return NOT_SALE_VALUE_TEXT_COLOR;
    }

    if (notRenderColor) {
      return NOT_HAVE_VALUE_TEXT_COLOR;
    }

    const color =
      projectOutletStockDetail?.featureOosThreshold?.featureOosLevel
        ?.foregroundColor ?? NOT_HAVE_VALUE_TEXT_COLOR;

    return color;
  }, [
    notRenderColor,
    projectOutletStockDetail?.featureOosThreshold?.featureOosLevel
      ?.foregroundColor,
    whenKnownSaleViewAndThisNotUpdateBySale,
  ]);

  return (
    <div
      style={{
        background: backgroundColor,
        color: foregroundColor,
      }}
      className="w-[calc(100% - 2px)] h-full py-4 font-medium"
    >
      {value || value === 0 ? formatNumber(value) : "-"}
    </div>
  );
};

export default StockStatusCell;
