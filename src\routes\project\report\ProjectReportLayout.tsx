import useDeviceType from "@/hooks/useDeviceType.ts";
import { Form } from "antd";
import dayjs from "dayjs";
import { useCallback, useEffect, useState } from "react";
import { Outlet, useLocation, useParams } from "react-router-dom";
import { useComponentFeatureQuery } from "../component/feature/service";
import { useProjectRolesQuery } from "../role/service";
import { useProjectQuery } from "../services.ts";
import AdvancedFilter from "./AdvancedFilter";
import {
  AdvancedFilterFormValueInterface,
  ProjectReportOutletContextType,
} from "./interface";
import { useAdvancedFilterValuesStore } from "./state.ts";
import "./style.css";

export default function ProjectReportLayout() {
  const isMobile = useDeviceType();

  const projectId = parseInt(useParams().id ?? "0");
  const projectComponentId = parseInt(useParams().projectComponentId ?? "0");
  const componentFeatureId = parseInt(useParams().componentFeatureId ?? "0");

  const location = useLocation();

  const [isHideFilter, setIsHideFilter] = useState(true);
  const [advancedFilterForm] = Form.useForm();
  const { setIsFiltered, values, setFilter } = useAdvancedFilterValuesStore();

  const componentFeatureQuery = useComponentFeatureQuery(
    projectId,
    projectComponentId,
    componentFeatureId,
  );
  const projectQuery = useProjectQuery(projectId);

  const projectRolesQuery = useProjectRolesQuery(projectId, {
    type: "EMPLOYEE",
  });

  const onAdvancedFilterFormFinish = useCallback(
    (values: AdvancedFilterFormValueInterface) => {
      if (values.attendance) {
        const [attendanceStartDate, attendanceEndDate] = values.attendance;
        values.attendanceStartDate = attendanceStartDate
          ? dayjs(attendanceStartDate).startOf("date").toDate()
          : undefined;
        values.attendanceEndDate = attendanceEndDate
          ? dayjs(attendanceEndDate).endOf("date").toDate()
          : undefined;

        delete values.attendance;
      }

      setFilter(values);
      setIsFiltered(true);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [location.pathname, setFilter, setIsFiltered],
  );

  useEffect(() => {
    advancedFilterForm.resetFields();
    setIsHideFilter(true);
    setFilter({
      attendanceStartDate: dayjs().startOf("date").toDate(),
      attendanceEndDate: dayjs().endOf("date").toDate(),
    });

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location.pathname]);

  return (
    <div className="flex justify-between overflow-x-hidden m-0 p-0">
      <div
        className={`w-full h-full ml-0 mr-${isMobile ? "0" : "10"} overflow-x-auto`}
      >
        <Outlet
          context={
            {
              isHideFilter,
              setIsHideFilter,
              componentFeatureQuery,
              advancedFilterForm,
              projectId,
              componentFeatureId,
              projectComponentId,
              advancedFilterValues: values,
              roles: projectRolesQuery.data?.entities,
              project: projectQuery.data,
            } satisfies ProjectReportOutletContextType
          }
        />
      </div>
      {!isHideFilter && !isMobile && (
        <div hidden={isHideFilter} className="bg-[#F8FCFF] m-0 text-nowrap">
          <AdvancedFilter
            isHideFilter={isHideFilter}
            form={advancedFilterForm}
            projectId={projectId}
            onFinish={onAdvancedFilterFormFinish}
            roles={projectRolesQuery.data?.entities}
          />
        </div>
      )}
    </div>
  );
}
