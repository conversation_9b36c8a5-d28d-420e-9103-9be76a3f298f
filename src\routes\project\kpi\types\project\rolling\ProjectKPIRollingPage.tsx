import {
  DATE_FORMAT,
  DEFAULT_CURRENT_PAGE,
  DEFAULT_PAGE_SIZE,
} from "@/common/constant";
import { formatNumber } from "@/common/helper";
import { useApp } from "@/UseApp";
import { DeleteOutlined, PlusOutlined } from "@ant-design/icons";
import { Button, Col, Row, Space, Table } from "antd";
import dayjs from "dayjs";
import { useCallback, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import { ItemKpiTypeEnum } from "../../../interface";
import ProjectKPIRollingImportModal from "./ProjectKPIRollingImportModal";
import { useDeleteKpiRollingMutation, useKpiRollingQuery } from "./service";

const ProjectKPIRollingPage = () => {
  const projectId = parseInt(useParams().id ?? "0");
  const { openDeleteModal, showNotification } = useApp();

  const [open, setOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);

  const kpiRollingQuery = useKpiRollingQuery(projectId, {
    type: ItemKpiTypeEnum.HIT,
    take: pageSize,
    skip: (currentPage - 1) * pageSize,
  });

  const deleteKpiRollingMutation = useDeleteKpiRollingMutation(projectId);

  const onDelete = useCallback(async () => {
    openDeleteModal({
      content: (
        <>
          <p>
            Cấu hình timegone theo hit xóa khỏi hệ thống vĩnh viễn và không thể
            khôi phục
          </p>
          <p>Bạn vẫn muốn xóa khỏi hệ thống?</p>
        </>
      ),
      deleteText: "Xác nhận xóa",
      loading: false,
      onCancel(): void {},
      onDelete: async () => {
        await deleteKpiRollingMutation.mutateAsync(ItemKpiTypeEnum.HIT);
        showNotification({
          type: "success",
          message: "Xóa cấu hình timegone theo hit thành công",
        });
        await kpiRollingQuery.refetch();
      },
      title: `Xóa cấu hình timegone theo hit thực hiện`,
      titleError: "Không thể xóa kênh thực hiện",
      contentHeader: <>Không thể xóa cấu hình timegone theo hit bởi vì:</>,
    });
  }, [
    deleteKpiRollingMutation,
    kpiRollingQuery,
    openDeleteModal,
    showNotification,
  ]);

  const pagination = useMemo(() => {
    return {
      current: currentPage,
      total: kpiRollingQuery.data?.count ?? 0,
      pageSize: pageSize,
      onChange: (page: number) => {
        setCurrentPage(page);
      },
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: (_current: number, size: number) => {
        setPageSize(size);
        setCurrentPage(DEFAULT_CURRENT_PAGE);
      },
    };
  }, [currentPage, kpiRollingQuery.data?.count, pageSize]);

  return (
    <>
      <Row justify={"end"}>
        <Col>
          <Space>
            <Button icon={<DeleteOutlined />} onClick={onDelete}>
              Xóa tất cả
            </Button>

            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setOpen(true);
              }}
            >
              Import plan
            </Button>
          </Space>
        </Col>
      </Row>

      <Table
        className="mt-8"
        dataSource={kpiRollingQuery.data?.entities}
        rowKey={"id"}
        columns={[
          {
            title: "Ngày thực hiện",
            dataIndex: "targetDate",
            render: (targetDate) => dayjs(targetDate).format(DATE_FORMAT),
          },
          {
            title: "Kênh thực hiện",
            dataIndex: "channel",
            render: (channel) => channel.name,
          },
          {
            title: "Tên outlet",
            dataIndex: "targetName",
          },
          {
            title: "Tỉnh/ TP",
            dataIndex: "province",
            render: (province) => province.name,
          },
          {
            title: "Target hit",
            dataIndex: "targetKpi",
            align: "right",
            render: (targetKpi) => formatNumber(targetKpi),
          },
        ]}
        pagination={pagination}
      />

      <ProjectKPIRollingImportModal
        open={open}
        setOpen={setOpen}
        projectId={projectId}
        cb={() => {
          kpiRollingQuery.refetch();
        }}
      />
    </>
  );
};

export default ProjectKPIRollingPage;
