import { UseQueryResult } from "@tanstack/react-query";
import { Switch } from "antd";
import { useCallback } from "react";
import { useOutletContext, useParams } from "react-router-dom";
import PhotographyPage from "../../../photography/PhotographyPage";
import StepLockPage from "../../StepLockPage";
import { OrderInterface, StepLockEnum } from "../../interface";
import { useUpdateStepStatusMutation } from "../../service";

export default function ConfigPhotoPage() {
  const [orderLatestQuery]: [UseQueryResult<OrderInterface, unknown>] =
    useOutletContext();
  const componentFeatureId = parseInt(useParams().componentFeatureId ?? "0");

  const updateStepStatusMutation =
    useUpdateStepStatusMutation(componentFeatureId);

  const onRequiredPhotoSwitchChange = useCallback(
    async (value: boolean) => {
      updateStepStatusMutation
        .mutateAsync({
          isPhotoRequired: !value,
        })
        .then(() => {
          orderLatestQuery.refetch();
        });
    },
    [orderLatestQuery, updateStepStatusMutation],
  );

  if (!orderLatestQuery.data?.hasPhoto) {
    return (
      <StepLockPage
        title="Chức năng chụp hình đơn hàng"
        description="Chức năng cho phép cấu hình các loại hình cần chụp khi ghi nhận đơn hàng của khách"
        type={StepLockEnum.Photo}
        orderLatestQuery={orderLatestQuery}
        locked={!orderLatestQuery.data?.hasPhoto}
      />
    );
  }

  return (
    <>
      <div className="">
        <span className="mr-4">Cho phép skip trên app</span>
        <Switch
          value={!orderLatestQuery?.data?.isPhotoRequired}
          loading={updateStepStatusMutation.isPending}
          onChange={onRequiredPhotoSwitchChange}
        />
      </div>
      <PhotographyPage isChild />
    </>
  );
}
