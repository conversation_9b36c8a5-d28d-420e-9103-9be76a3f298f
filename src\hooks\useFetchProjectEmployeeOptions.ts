import { useGetEmployeeMutation } from "@/routes/project/employee/service";
import { useCallback } from "react";

const useFetchProjectEmployeeOptions = (projectId: number) => {
  const findEmployeeMutation = useGetEmployeeMutation(projectId);

  const fetchFuntion = useCallback(
    async (keyword?: string) => {
      const { entities } = await findEmployeeMutation.mutateAsync({
        keyword,
        take: 10,
        skip: 0,
      });

      return entities.map((employee) => ({
        label: employee.user.name,
        value: employee.id,
      }));
    },
    [findEmployeeMutation],
  );

  return fetchFuntion;
};

export default useFetchProjectEmployeeOptions;
