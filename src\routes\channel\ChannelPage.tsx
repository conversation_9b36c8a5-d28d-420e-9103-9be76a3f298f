import { CURD, SELECT_ALL, SELECT_ALL_LABEL } from "@/common/constant";
import { filterOption } from "@/common/helper.ts";
import CustomTable from "@/components/CustomTable/CustomTable.tsx";
import FilterComponent from "@/components/FilterComponent.tsx";
import InnerContainer from "@/components/InnerContainer/InnerContainer.tsx";
import { renderTableCell } from "@/components/table-cell";
import { renderTableOptionCell } from "@/components/table-option-cell";
import { useUrlFilters } from "@/hooks/useUrlFilters.ts";
import { useApp } from "@/UseApp.tsx";
import {
  AlignRightOutlined,
  CloseOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import { Button, ColorPicker, Form, Input, Modal, Select } from "antd";
import { useForm } from "antd/es/form/Form";
import { ColumnsType } from "antd/es/table";
import _ from "lodash";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { createSearchParams, useNavigate } from "react-router-dom";
import { useClientsQuery } from "../client/service.ts";
import SubChannelModal from "../subChannel/SubChannelModal.tsx";
import { ChannelInterface } from "./interface.ts";
import {
  useChannelsQuery,
  useCreateChannelMutation,
  useDeleteChannelMutation,
  useUpdateChannelMutation,
} from "./services.ts";

const ChannelPage: React.FC = () => {
  const { showNotification, openDeleteModal } = useApp();
  const navigate = useNavigate();

  const [searchForm] = Form.useForm();
  const [form] = Form.useForm();
  const [isModalAddOrUpdateOpen, setIsModalAddOrUpdateOpen] = useState(false);
  const [formAction, setFormAction] = useState<CURD | null>(null);
  const [modalTitle, setModalTitle] = useState("");
  const [modal, contextHolder] = Modal.useModal();
  const [subChannelForm] = useForm();
  const [isSubChannelModalOpen, setIsSubChannelModalOpen] = useState(false);
  const [selectedChannel, setSelectedChannel] = useState<
    ChannelInterface | undefined
  >(undefined);
  const { filter, currentPage, pageSize, handleSearch, getPaginationProps } =
    useUrlFilters({
      formInstance: searchForm,
      handleSearchCallback: () => {
        channelsQuery.refetch();
      },
    });

  const clientsQuery = useClientsQuery({
    take: 0,
    skip: 0,
  });
  const channelsQuery = useChannelsQuery({
    ...filter,
    take: pageSize,
    skip: (currentPage - 1) * pageSize,
    getInActive: true,
  });

  const createChannelMutation = useCreateChannelMutation();
  const updateChannelMutation = useUpdateChannelMutation();
  const deleteChannelMutation = useDeleteChannelMutation();

  const handleBtnEditClick = useCallback(
    (record: ChannelInterface) => {
      setSelectedChannel(record);
      setIsModalAddOrUpdateOpen(true);
      setFormAction(CURD.UPDATE);
      setModalTitle("Cập nhật kênh thực hiện");
      form.setFieldsValue({ ...record, clientId: record.client.id });
    },
    [form],
  );

  const handleBtnInactiveClick = useCallback(
    (record: ChannelInterface) => {
      modal.confirm({
        title: `Ngừng hoạt động channel: ${record.name}`,
        content: "Bạn có chắc chắn muốn ngừng hoạt động channel này?",
        okText: "Ngừng hoạt động",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await updateChannelMutation.mutateAsync({
              id: record.id,
              isActive: false,
            });
            showNotification({
              type: "success",
              message: "Ngừng hoạt động channel thành công",
            });

            channelsQuery.refetch();
          } catch (error) {
            console.error(error);

            showNotification({
              type: "error",
              message: "Ngừng hoạt động channel thất bại",
            });
          }
        },
      });
    },
    [channelsQuery, modal, showNotification, updateChannelMutation],
  );

  const handleBtnActiveClick = useCallback(
    (record: ChannelInterface) => {
      modal.confirm({
        title: `Kích hoạt channel: ${record.name}`,
        content: "Bạn có chắc chắn muốn kích hoạt channel này?",
        okText: "Kích hoạt",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await updateChannelMutation.mutateAsync({
              id: record.id,
              isActive: true,
            });
            showNotification({
              type: "success",
              message: "Kích hoạt channel thành công",
            });
          } catch (error) {
            console.error(error);

            showNotification({
              type: "error",
              message: "Kích hoạt channel thất bại",
            });
          }
          channelsQuery.refetch();
        },
      });
    },
    [channelsQuery, modal, showNotification, updateChannelMutation],
  );

  const handleBtnDeleteClick = useCallback(
    (record: ChannelInterface) => {
      openDeleteModal({
        content: (
          <>
            <p>
              Kênh thực hiện sẽ được xóa khỏi hệ thống vĩnh viễn và không thể
              khôi phục
            </p>
            <p>
              Bạn vẫn muốn xóa kênh{" "}
              <span className={"font-semibold"}>{record.name}</span> khỏi hệ
              thống?
            </p>
          </>
        ),
        deleteText: "Xác nhận xóa",
        loading: deleteChannelMutation.isPending,
        onCancel(): void {},
        onDelete: async () => {
          await deleteChannelMutation.mutateAsync(record.id);
          showNotification({
            type: "success",
            message: "Xóa kênh thành công",
          });
          channelsQuery.refetch();
        },
        title: `Xóa kênh thực hiện`,
        titleError: "Không thể xóa kênh thực hiện",
        contentHeader: (
          <>
            Không thể xóa kênh{" "}
            <span className="font-semibold">{record.name}</span> bởi vì:
          </>
        ),
      });
    },
    [channelsQuery, deleteChannelMutation, openDeleteModal, showNotification],
  );

  const handleBtnAddSubChannelClick = (record: ChannelInterface) => {
    setIsSubChannelModalOpen(true);
    subChannelForm.setFields([
      {
        name: "channelId",
        value: record.id,
      },
      {
        name: "clientId",
        value: record.client.id,
      },
    ]);
  };

  const handleBtnViewSubChannelClick = (record: ChannelInterface) => {
    navigate({
      pathname: "/sub-channel",
      search: createSearchParams({
        filterValue: record.name,
        filterField: "channel.name",
      }).toString(),
    });
  };

  const extraActionInActive = {
    items: [
      {
        key: "VIEW_SUBCHANNEL",
        label: "Xem nhóm",
        icon: <AlignRightOutlined />,
      },
    ],
    actions: [
      {
        key: "VIEW_SUBCHANNEL",
        action: handleBtnViewSubChannelClick,
      },
    ],
  };

  const extraActionActive = {
    items: [
      {
        key: "ADD_SUBCHANNEL",
        label: "Thêm nhóm thực hiện",
        icon: <PlusOutlined />,
      },
      {
        key: "VIEW_SUBCHANNEL",
        label: "Xem nhóm",
        icon: <AlignRightOutlined />,
      },
    ],
    actions: [
      {
        key: "ADD_SUBCHANNEL",
        action: handleBtnAddSubChannelClick,
      },
      {
        key: "VIEW_SUBCHANNEL",
        action: handleBtnViewSubChannelClick,
      },
    ],
  };

  const columns: ColumnsType<ChannelInterface> = [
    {
      title: "Tên kênh",
      key: "name",
      dataIndex: "name",
      className: "min-w-[100px]",
      render: renderTableCell,
    },
    {
      title: "Mã kênh",
      key: "code",
      dataIndex: "code",
      className: "min-w-[100px]",
      render: renderTableCell,
    },
    {
      title: "Nhóm",
      key: "subChannels.name",
      dataIndex: "subChannels",
      className: "min-w-[100px]",
      render: (value, record, index) => {
        return (
          <div className="cursor-pointer">
            {renderTableCell(value, record, index, "array", 3)}
          </div>
        );
      },
      onCell: (record) => {
        return {
          onClick: () => {
            navigate({
              pathname: "/sub-channel",
              search: createSearchParams({
                filterValue: record.name,
                filterField: "channel.name",
                clientId: record.client.id.toString(),
              }).toString(),
            });
          },
        };
      },
    },
    {
      title: "Client",
      key: "client",
      className: "min-w-[100px]",
      dataIndex: "client",
      render: renderTableCell,
    },
    {
      key: "isActive",
      title: "Tình trạng",
      className: "min-w-[100px]",
      dataIndex: "isActive",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "isActive");
      },
    },
    {
      key: "createdAt",
      title: "Thời gian tạo",
      className: "min-w-[100px]",
      dataIndex: "createdAt",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "datetime");
      },
    },
    {
      key: "createdByUser.name",
      title: "Người tạo",
      className: "min-w-[100px]",
      dataIndex: "createdByUser",
      render: renderTableCell,
    },
    {
      key: "updatedAt",
      title: "Thời gian cập nhật",
      dataIndex: "updatedAt",
      className: "min-w-[100px]",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "datetime");
      },
    },
    {
      key: "updatedByUser.name",
      title: "Người cập nhật",
      className: "min-w-[100px]",
      dataIndex: "updatedByUser",
      render: renderTableCell,
    },
    {
      key: "actions",
      render: (_, record) => {
        const extraAction = record.isActive
          ? extraActionActive
          : extraActionInActive;

        return renderTableOptionCell(
          record,
          handleBtnEditClick,
          handleBtnInactiveClick,
          handleBtnActiveClick,
          handleBtnDeleteClick,
          extraAction,
        );
      },
      fixed: "right",
      width: 100,
      align: "center",
    },
  ];

  const filterOptions = columns
    .filter(
      (item) =>
        !["isActive", "updatedAt", "createdAt", "actions", "client"].includes(
          item.key as string,
        ),
    )
    .map((column) => {
      return {
        label: column.title as string,
        value: column.key as string | number,
      };
    });

  filterOptions.unshift({ label: SELECT_ALL_LABEL, value: SELECT_ALL });

  const onCancel = useCallback(() => {
    setIsModalAddOrUpdateOpen(false);
    form.resetFields();
    setModalTitle("");
    setSelectedChannel(undefined);
  }, [form]);

  const handleAddOrUpdateFormSubmit = useCallback(async () => {
    try {
      await form.validateFields();
      const data = form.getFieldsValue();
      const id = form.getFieldValue("id");

      const { backgroundColor, foregroundColor } = data;

      data.backgroundColor =
        typeof backgroundColor === "string"
          ? backgroundColor
          : backgroundColor?.toHexString();

      data.foregroundColor =
        typeof foregroundColor === "string"
          ? foregroundColor
          : foregroundColor?.toHexString();

      switch (formAction) {
        case CURD.CREATE:
          await createChannelMutation.mutateAsync(data);
          showNotification({
            type: "success",
            message: "Thêm channel thành công",
          });
          break;
        case CURD.UPDATE:
          await updateChannelMutation.mutateAsync({ id, ...data });
          showNotification({
            type: "success",
            message: "Cập nhật channel thành công",
          });
          break;
        default:
          return;
      }

      form.resetFields();
      setIsModalAddOrUpdateOpen(false);
      setModalTitle("");
      channelsQuery.refetch();

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      console.error(error);

      const data: { message: { field: string; message: string }[] } =
        error?.response?.data;
      const { message } = data;
      form.setFields([
        { name: message[0].field, errors: [message[0].message] },
      ]);
    }
  }, [
    form,
    formAction,
    channelsQuery,
    createChannelMutation,
    showNotification,
    updateChannelMutation,
  ]);

  const handleAddButtonClick = () => {
    setIsModalAddOrUpdateOpen(true);
    setFormAction(CURD.CREATE);
    setModalTitle("Thêm kênh thực hiện");
  };

  const pagination = useMemo(
    () => getPaginationProps(channelsQuery.data?.count),
    [getPaginationProps, channelsQuery.data?.count],
  );

  const loading = useMemo(
    () =>
      clientsQuery.isLoading ||
      clientsQuery.isFetching ||
      createChannelMutation.isPending ||
      updateChannelMutation.isPending ||
      deleteChannelMutation.isPending,
    [
      clientsQuery.isLoading,
      clientsQuery.isFetching,
      createChannelMutation.isPending,
      updateChannelMutation.isPending,
      deleteChannelMutation.isPending,
    ],
  );

  useEffect(() => {
    if (formAction === CURD.CREATE) {
      form.setFieldsValue({
        backgroundColor: "#FFFFFF",
        foregroundColor: "#000000",
      });
    }
  }, [form, formAction]);

  return (
    <div>
      <h2>Kênh thực hiện</h2>
      <InnerContainer>
        <FilterComponent
          filterOptions={filterOptions}
          searchHandler={handleSearch}
          handleAddButtonClick={handleAddButtonClick}
          searchForm={searchForm}
          hasClient
        />

        <CustomTable<ChannelInterface>
          dataSource={channelsQuery.data?.entities}
          columns={columns}
          scroll={{
            x: "max-content",
            y: pagination.total ? "80vh" : undefined,
          }}
          pagination={pagination}
          loading={loading}
        />
      </InnerContainer>

      <Modal
        open={isModalAddOrUpdateOpen}
        footer={null}
        closeIcon={null}
        styles={{ content: { padding: 0 } }}
      >
        <div className={"pl-10 pr-10"}>
          <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
            <h2 className="text-neutral-700 text-2xl font-semibold ">
              {modalTitle}
            </h2>
            <div className="pt-5">
              <Button
                type="link"
                onClick={onCancel}
                size="large"
                icon={<CloseOutlined />}
              />
            </div>
          </div>
        </div>

        <Form
          name="channelForm"
          onFinish={handleAddOrUpdateFormSubmit}
          layout={"vertical"}
          form={form}
        >
          <div className={"pl-10 pr-10"}>
            <Form.Item name={"id"} hidden={true}></Form.Item>
            <Form.Item
              name="name"
              label={"Tên kênh"}
              rules={[
                {
                  required: true,
                  message: "Tên kênh không được để trống",
                },
              ]}
            >
              <Input />
            </Form.Item>
            <Form.Item
              name="code"
              label={"Mã kênh"}
              rules={[
                {
                  required: true,
                  message: "Mã kênh không được để trống",
                },
              ]}
              extra={
                <p className={"mt-1"}>
                  Mã kênh sẽ được dùng khi outlet được sinh mã tự động, vd:
                  MT-10012
                </p>
              }
            >
              <Input />
            </Form.Item>

            {(() => {
              const clientActiveOptions =
                clientsQuery.data?.entities.map((client) => ({
                  label: client.name,
                  value: client.id,
                })) ?? [];

              const { client } = selectedChannel ?? {};
              const selectedOptions = client
                ? [{ label: client?.name, value: client?.id }]
                : [];

              const clientOptions = _.unionBy(
                _.concat(clientActiveOptions, selectedOptions),
                (o) => o.value,
              );

              return (
                <Form.Item
                  name="clientId"
                  label={"Client"}
                  rules={[{ required: true }]}
                >
                  <Select
                    options={clientOptions}
                    showSearch
                    optionFilterProp="children"
                    filterOption={filterOption}
                    disabled={formAction === CURD.UPDATE}
                  />
                </Form.Item>
              );
            })()}

            <Form.Item
              name={"backgroundColor"}
              label={"Màu nền"}
              layout={"horizontal"}
            >
              <ColorPicker
                showText
                defaultValue={"#FFFFFF"}
                mode={"single"}
                disabledAlpha
                format="hex"
              />
            </Form.Item>

            <Form.Item
              name={"foregroundColor"}
              label={"Màu chữ"}
              layout={"horizontal"}
            >
              <ColorPicker
                showText
                defaultValue={"#000000"}
                disabledAlpha
                format="hex"
              />
            </Form.Item>
          </div>
          <div className="flex justify-end gap-4 py-4 rounded max-md:max-w-full max-md:flex-wrap pb-4 pl-10 pr-10 bg-[#F7F8FA]">
            <Button htmlType="button" onClick={onCancel}>
              Đóng
            </Button>
            <Button htmlType="submit" type={"primary"} loading={loading}>
              {formAction === CURD.CREATE ? "Thêm" : "Cập nhật"}
            </Button>
          </div>
        </Form>
      </Modal>

      <SubChannelModal
        form={subChannelForm}
        modalTitle="Nhóm thực hiện"
        isOpen={isSubChannelModalOpen}
        setIsOpen={setIsSubChannelModalOpen}
        disabledSelectChannel={true}
        formAction={CURD.CREATE}
        callback={() => channelsQuery.refetch()}
      />

      {contextHolder}
    </div>
  );
};

export default ChannelPage;
