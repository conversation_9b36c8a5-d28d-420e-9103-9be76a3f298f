import { AbstractFilterInterface } from "@/common/interface";
import { useApp } from "@/UseApp";
import { useMutation, useQuery } from "@tanstack/react-query";
import { FeatureTypeEnum } from "../component/feature/interface";
import { ToolSettingSettingTypeEnum } from "./interface";

export const useProjectToolAgencyQuery = (
  projectId: number,
  filter?: AbstractFilterInterface & { projectAgencyId: number },
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["projectTool", projectId, filter],
    queryFn: () =>
      axiosGet<
        {
          entities: {
            id: number;
            allowedDays: number;
          };
          count: number;
        }[],
        unknown
      >(`/tools/projects/${projectId}`, filter),
  });
};

export const useCreateProjectToolAgencyMutation = (projectId: number) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createProjectTool", projectId],
    mutationFn: (data: { projectAgencyId: number; allowedDays: number }) =>
      axiosPost(
        `/tools/projects/${projectId}/agencies/${data.projectAgencyId}`,
        data,
      ),
  });
};

export const useUpdateProjectToolAgencyMutation = (projectId: number) => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["updateProjectTool", projectId],
    mutationFn: (data: { projectAgencyId: number; allowedDays: number }) =>
      axiosPatch(
        `/tools/projects/${projectId}/agencies/${data.projectAgencyId}`,
        data,
      ),
  });
};

export const useProjectToolAgencyDetailQuery = (
  projectId: number,
  projectAgencyId?: number,
  enabled?: boolean,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["projectToolDetail", projectId, projectAgencyId],
    queryFn: () =>
      axiosGet<
        {
          id: number;
          allowedDays: number;
        },
        unknown
      >(`/tools/projects/${projectId}/agencies/${projectAgencyId}`),
    enabled: enabled && !!projectAgencyId,
  });
};

export const useDeleteProjectToolAgencyMutation = (projectId: number) => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deleteProjectTool", projectId],
    mutationFn: (id: number) =>
      axiosDelete(`/tools/projects/${projectId}/agencies/${id}`),
  });
};

export const useToolPermittedDatesQuery = (
  toolId: number,
  filter?: AbstractFilterInterface,
  enabled?: boolean,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["toolPermittedDates", toolId, filter],
    queryFn: () =>
      axiosGet<
        {
          entities: { id: number; startDate: string; endDate: string }[];
          count: number;
        },
        unknown
      >(`/tools/${toolId}/permitted-dates`, filter),
    enabled: !!toolId && enabled,
  });
};

export const useCreateToolPermittedDateMutation = (toolId?: number) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createToolPermittedDate", toolId],
    mutationFn: (data: { startDate: string; endDate: string }) =>
      axiosPost(`/tools/${toolId}/permitted-dates`, data),
  });
};

export const useDeleteToolPermittedDateMutation = (toolId?: number) => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deleteToolPermittedDate", toolId],
    mutationFn: (id: number) =>
      axiosDelete(`/tools/${toolId}/permitted-dates/${id}`),
  });
};

export const useProjectSitecheckConfigQuery = (projectId: number) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["projectSitecheckConfig", projectId],
    queryFn: () =>
      axiosGet<
        {
          id: number;
          sitecheckCompanyId: number;
          sitecheckProjectId: number;
        },
        unknown
      >(`/projects/${projectId}/sitecheck`),
    enabled: !!projectId,
  });
};

export const useCreateProjectSitecheckConfigMutation = (projectId: number) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createProjectSitecheckConfig", projectId],
    mutationFn: (data: {
      sitecheckCompanyId: number;
      sitecheckProjectId: number;
    }) => axiosPost(`/projects/${projectId}/sitecheck`, data),
  });
};

export const useDeleteProjectSitecheckConfigMutation = (projectId: number) => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deleteProjectSitecheckConfig", projectId],
    mutationFn: () => axiosDelete(`/projects/${projectId}/sitecheck`),
  });
};

export const useUpdateProjectSitecheckConfigMutation = (projectId: number) => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["updateProjectSitecheckConfig", projectId],
    mutationFn: (data: {
      sitecheckCompanyId: number;
      sitecheckProjectId: number;
    }) => axiosPatch(`/projects/${projectId}/sitecheck`, data),
  });
};

export const useToolSettingsQuery = (toolId?: number) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["toolSettings", toolId],
    queryFn: () =>
      axiosGet<
        {
          id: number;
          featureType: FeatureTypeEnum;
          settingType: ToolSettingSettingTypeEnum;
          enabled: boolean;
        }[],
        unknown
      >(`/tools/${toolId}/settings`),
    enabled: !!toolId,
  });
};

export const useCreateToolSettingMutation = (toolId?: number) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createToolSetting", toolId],
    mutationFn: (data: {
      featureType: FeatureTypeEnum;
      settingType: ToolSettingSettingTypeEnum;
      enabled: boolean;
    }) => axiosPost(`/tools/${toolId}/settings`, data),
  });
};

export const useDeleteToolSettingMutation = (toolId?: number) => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deleteToolSetting", toolId],
    mutationFn: (id: number) => axiosDelete(`/tools/${toolId}/settings/${id}`),
  });
};

export const useProjectOtpsQuery = (projectId: number) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["projectOtps", projectId],
    queryFn: () =>
      axiosGet<
        {
          id: number;
          primaryType: "sms";
          provider: "tingting";
          clientId: string;
          clientSecret: string;
          otpLength: number;
          otpResendTimeout: number;
          otpTimeout: number;
          otpSmsTemplate: {
            senderName: string;
            content: string;
            contentType?: "ascii";
          };
        },
        unknown
      >(`/projects/${projectId}/otps`),
    enabled: !!projectId,
  });
};

export const useCreateProjectOtpMutation = (projectId: number) => {
  const { axiosPost, showNotification } = useApp();

  return useMutation({
    mutationKey: ["createProjectOtp", projectId],
    mutationFn: (data: {
      primaryType: "sms";
      provider: string;
      clientId: string;
      clientSecret: string;
      otpLength: number;
      otpResendTimeout: number;
      otpTimeout: number;
      otpSmsTemplate: {
        senderName: string;
        content: string;
        contentType?: string;
      };
    }) => axiosPost(`/projects/${projectId}/otps`, data),
    onSuccess: () => {
      showNotification({
        type: "success",
        message: "Cập nhật cấu hình SMS OTP thành công.",
      });
    },
  });
};

export const useUpdateProjectOtpMutation = (projectId: number) => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["updateProjectOtp", projectId],
    mutationFn: (data: {
      id: number;
      primaryType: "sms";
      provider: string;
      clientId: string;
      clientSecret: string;
      otpLength: number;
      otpResendTimeout: number;
      otpTimeout: number;
      otpSmsTemplate: {
        senderName: string;
        content: string;
        contentType?: string;
      };
    }) => axiosPatch(`/projects/${projectId}/otps/${data.id}`, data),
  });
};

export const useDeleteProjectOtpMutation = (projectId: number) => {
  const { axiosDelete, showNotification } = useApp();

  return useMutation({
    mutationKey: ["deleteProjectOtp", projectId],
    mutationFn: (id?: number) =>
      axiosDelete(`/projects/${projectId}/otps/${id}`),
    onSuccess: () => {
      showNotification({
        type: "success",
        message: "Xóa cấu hình SMS OTP thành công.",
      });
    },
  });
};

export const usePatchProjectOtpMutation = (projectId: number) => {
  const { axiosPatch, showNotification } = useApp();

  return useMutation({
    mutationKey: ["patchProjectOtp", projectId],
    mutationFn: (data: {
      id: number;
      primaryType: "sms";
      provider: string;
      clientId: string;
      clientSecret: string;
      otpLength: number;
      otpResendTimeout: number;
      otpTimeout: number;
      otpSmsTemplate: {
        senderName: string;
        content: string;
        contentType?: string;
      };
    }) => axiosPatch(`/projects/${projectId}/otps/${data.id}`, data),
    onSuccess: () => {
      showNotification({
        type: "success",
        message: "Cập nhật cấu hình SMS OTP thành công.",
      });
    },
  });
};
