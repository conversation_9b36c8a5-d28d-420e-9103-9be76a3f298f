import FormNumberInput from "@/components/FormNumberInput";
import { Button, Checkbox, Col, Form, Row } from "antd";

const NumericSheetBindingPage = () => {
  return (
    <>
      <p>Loại data cho phép nhập</p>
      <Form layout="vertical">
        <Form.Item>
          <Checkbox><PERSON><PERSON> nguyên</Checkbox>
        </Form.Item>

        <Form.Item>
          <Checkbox>Số thập phân</Checkbox>
        </Form.Item>

        <Row>
          <Col>
            <Form.Item
              label="Giá trị tối thiểu"
              rules={[{ required: true }]}
              name={"min"}
            >
              <FormNumberInput />
            </Form.Item>
          </Col>

          <Col>
            <Form.Item
              label="Giá trị tối đa"
              rules={[{ required: true }]}
              name={"max"}
            >
              <FormNumberInput />
            </Form.Item>
          </Col>
        </Row>
        <Form.Item>
          <Button type="primary">L<PERSON><PERSON></Button>
        </Form.Item>
      </Form>
    </>
  );
};

export default NumericSheetBindingPage;
