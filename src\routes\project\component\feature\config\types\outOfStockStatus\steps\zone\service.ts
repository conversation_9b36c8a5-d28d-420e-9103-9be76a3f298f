import { AbstractFilterInterface } from "@/common/interface";
import { useApp } from "@/UseApp";
import { useMutation, useQuery } from "@tanstack/react-query";
import { OosZoneInterface } from "../../interface";

export const useOosZonesQuery = (
  featureId: number,
  filter?: AbstractFilterInterface & object,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["oosZones", featureId, filter],
    queryFn: () =>
      axiosGet<
        {
          entities: OosZoneInterface[];
          count: number;
        },
        unknown
      >(`/features/${featureId}/oos-zones`, filter),
  });
};

export const useCreateOosZoneMutation = (featureId: number) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createOosZone", featureId],
    mutationFn: (data: { name: string; description?: string }) =>
      axiosPost(`/features/${featureId}/oos-zones`, data),
  });
};

export const useUpdateOosZoneMutation = (featureId: number) => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["updateOosZone", featureId],
    mutationFn: (data: {
      id: number;
      name?: string;
      description?: string;
      isActive?: boolean;
    }) => axiosPatch(`/features/${featureId}/oos-zones/${data.id}`, data),
  });
};

export const useDeleteOosZoneMutation = (featureId: number) => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deleteOosZone", featureId],
    mutationFn: (id: number) =>
      axiosDelete(`/features/${featureId}/oos-zones/${id}`),
  });
};

export const useArrangeOosZoneMutation = (featureId: number) => {
  const { axiosPut } = useApp();

  return useMutation({
    mutationKey: ["arrangeOosZone", featureId],
    mutationFn: ({
      id,
      overFeatureOosZoneId,
    }: {
      id: number;
      overFeatureOosZoneId: number;
    }) =>
      axiosPut(`/features/${featureId}/oos-zones/${id}/arrangement`, {
        overFeatureOosZoneId,
      }),
  });
};
