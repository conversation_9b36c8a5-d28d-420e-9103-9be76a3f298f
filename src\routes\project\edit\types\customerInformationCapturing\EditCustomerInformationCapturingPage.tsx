import {
  CURD,
  DATETIME_FORMAT,
  DEFAULT_CURRENT_PAGE,
  DEFAULT_PAGE_SIZE,
} from "@/common/constant.ts";
import { formatMoney } from "@/common/helper.ts";
import TableActionCell from "@/components/TableActionCell.tsx";
import { useCanPermission } from "@/layouts/MainLayout/hook.ts";
import { PermissionEnum } from "@/routes/project/interface.ts";
import { UserInterface } from "@/routes/user/interface.ts";
import { useApp } from "@/UseApp.tsx";
import {
  DeleteOutlined,
  EditOutlined,
  EditTwoTone,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import ItemProductQuantity from "@project/report/ItemProductQuantity.tsx";
import CustomerCell from "@project/report/types/customerInformationCapturing/CustomerCell.tsx";
import CustomerExchangesCell from "@project/report/types/customerInformationCapturing/CustomerExchangesCell.tsx";
import CustomerPhotosCell from "@project/report/types/customerInformationCapturing/CustomerPhotosCell.tsx";
import {
  RecordOrderCustomerInterface,
  RecordOrderExchangeInterface,
  RecordOrderPhotoInterface,
  RecordOrderPurchaseInterface,
} from "@project/report/types/customerInformationCapturing/interface.ts";
import { Button, Col, Form, Input, Row, Select, Space, Table } from "antd";
import dayjs from "dayjs";
import _ from "lodash";
import { useCallback, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import AttendanceDetailRow from "../../AttendanceDetailRow.tsx";
import {
  useAttendanceFeatureDetailQuery,
  useAttendanceQuery,
} from "../../service.ts";
import EditCustomerInformationCapturingModal from "./EditCustomerInformationCapturingModal.tsx";
import { EditRecordOrderInterface } from "./interface.ts";
import { useDeleteOrderMutation, useEditOrdersQuery } from "./service.ts";

const EditCustomerInformationCapturingPage = () => {
  const projectId = parseInt(useParams().id ?? "0");
  const componentFeatureId = parseInt(useParams().componentFeatureId ?? "0");
  const attendanceId = parseInt(useParams().attendanceId ?? "0");

  const { openDeleteModal, showNotification } = useApp();
  const { canPermissionFunction } = useCanPermission(projectId);

  const [isOpen, setIsOpen] = useState(false);
  const [action, setAction] = useState<CURD | undefined>(undefined);
  const [filter, setFilter] = useState({});
  const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [searchForm] = Form.useForm();
  const [selectedOrderId, setSelectedOrderId] = useState<number | undefined>(
    undefined,
  );

  const attendanceQuery = useAttendanceQuery(projectId, attendanceId);
  const attendanceFeatureDetailQuery = useAttendanceFeatureDetailQuery(
    projectId,
    attendanceId,
    componentFeatureId,
  );
  const editOrdersQuery = useEditOrdersQuery(
    projectId,
    attendanceId,
    componentFeatureId,
    {
      ...filter,
      sort: "desc",
      take: pageSize,
      skip: (currentPage - 1) * pageSize,
    },
    !!attendanceFeatureDetailQuery.data?.id,
  );
  const deleteOrderMutation = useDeleteOrderMutation(
    projectId,
    attendanceId,
    componentFeatureId,
  );

  const featureCustomers = useMemo(
    () => attendanceFeatureDetailQuery.data?.featureCustomers ?? [],
    [attendanceFeatureDetailQuery.data?.featureCustomers],
  );

  const cancel = useCallback(() => {
    setAction(undefined);
    setIsOpen(false);
    setSelectedOrderId(undefined);
    editOrdersQuery.refetch();
  }, [editOrdersQuery]);

  const pagination = useMemo(() => {
    return {
      current: currentPage,
      total: editOrdersQuery.data?.count,
      pageSize: pageSize,
      onChange: (page: number) => {
        setCurrentPage(page);
      },
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: (_current: number, size: number) => {
        setPageSize(size);
        setCurrentPage(DEFAULT_CURRENT_PAGE);
      },
    };
  }, [currentPage, pageSize, editOrdersQuery.data?.count]);

  const onSearch = useCallback(() => {
    const values = searchForm.getFieldsValue();
    setCurrentPage(DEFAULT_CURRENT_PAGE);
    if (_.isEqual(filter, values)) {
      editOrdersQuery.refetch();
    }
    setFilter(values);
  }, [editOrdersQuery, filter, searchForm]);

  const handleBtnDeleteClick = useCallback(
    (record: EditRecordOrderInterface) => {
      openDeleteModal({
        content: (
          <>
            <p>
              Đơn hàng sẽ được xóa khỏi hệ thống vĩnh viễn và không thể khôi
              phục
            </p>
            <p>
              Bạn vẫn muốn xóa đơn hàng có{" "}
              <span className={"font-semibold"}>ID {record.id}</span> khỏi hệ
              thống?
            </p>
          </>
        ),
        deleteText: "Xác nhận xóa",
        loading: deleteOrderMutation.isPending,
        onCancel(): void {},
        onDelete: async () => {
          await deleteOrderMutation.mutateAsync(record.id);
          showNotification({
            type: "success",
            message: "Xóa đơn hàng thành công",
          });
          await editOrdersQuery.refetch();
        },
        title: `Xóa đơn hàng`,
        titleError: "Không thể xóa đơn hàng",
        contentHeader: (
          <>
            Không thể xóa đơn hàng có{" "}
            <span className="font-semibold">ID {record.id}</span> bởi vì:
          </>
        ),
      });
    },
    [deleteOrderMutation, editOrdersQuery, openDeleteModal, showNotification],
  );

  return (
    <>
      <h2>Đơn hàng đã ghi nhận</h2>
      <div className="bg-white p-10 rounded">
        <AttendanceDetailRow
          projectRecordEmployee={attendanceQuery.data?.projectRecordEmployee}
          createdAt={attendanceQuery.data?.timeIn}
          updatedAt={attendanceQuery.data?.timeOut}
        />

        <Row justify={"space-between"} className={"mt-10"}>
          <Col>
            <Form form={searchForm}>
              <Space>
                <Form.Item name={"keyword"}>
                  <Input
                    placeholder={"Tìm theo ID đơn hàng, thông tin khách"}
                    prefix={<SearchOutlined />}
                    allowClear
                    className={"min-w-[279px]"}
                  />
                </Form.Item>

                {
                  //    <Form.Item name={"usesUpdateTool"}>
                  //    <Select
                  //      placeholder={"Tình trạng đơn hàng"}
                  //      allowClear
                  //      options={[
                  //        {
                  //          label: "Chưa qua chỉnh sửa",
                  //          value: false,
                  //        },
                  //        {
                  //          label: "Được chỉnh lần cuối bởi tool edit",
                  //          value: true,
                  //        },
                  //      ]}
                  //      popupMatchSelectWidth={false}
                  //    />
                  //  </Form.Item>
                }

                <Form.Item name={"usesCreateTool"}>
                  <Select
                    placeholder={"Người tạo đơn hàng"}
                    allowClear
                    options={[
                      {
                        label: "Bản thân nhân viên field",
                        value: false,
                      },
                      {
                        label: "Tool edit",
                        value: true,
                      },
                    ]}
                    popupMatchSelectWidth={false}
                  />
                </Form.Item>
                <Form.Item>
                  <Button htmlType="submit" onClick={onSearch}>
                    Tìm kiếm
                  </Button>
                </Form.Item>
              </Space>
            </Form>
          </Col>

          <Col>
            <Button
              icon={<PlusOutlined />}
              type={"primary"}
              onClick={() => {
                setAction(CURD.CREATE);
                setIsOpen(true);
              }}
            >
              Thêm mới đơn hàng
            </Button>
          </Col>
        </Row>

        <Table
          loading={editOrdersQuery.isLoading}
          dataSource={editOrdersQuery.data?.entities}
          pagination={pagination}
          rowKey={"id"}
          columns={[
            {
              title: "ID đơn hàng",
              dataIndex: "id",
              render: (
                id,
                { usesUpdateTool }: { usesUpdateTool: boolean; id: number },
              ) => {
                return (
                  <>
                    <p className="p-0 m-0">{id}</p>
                    {usesUpdateTool && (
                      <p className="p-0 m-0">
                        <div className="p-1 bg-[#FFE5E5] rounded h-6 w-6 flex justify-center items-center">
                          <EditTwoTone twoToneColor="#DF3C3C" />
                        </div>
                      </p>
                    )}
                  </>
                );
              },
            },
            {
              title: "Thông tin khách",
              dataIndex: "recordOrderCustomers",
              render: (
                recordOrderCustomers: RecordOrderCustomerInterface[],
              ) => {
                for (const recordOrderCustomer of recordOrderCustomers) {
                  recordOrderCustomer.recordCustomer.featureCustomer =
                    featureCustomers.find(
                      (featureCustomer) =>
                        featureCustomer.id ===
                        recordOrderCustomer.recordCustomer.featureCustomerId,
                    );
                }

                return (
                  <CustomerCell
                    orderCustomers={recordOrderCustomers}
                  ></CustomerCell>
                );
              },
            },
            {
              title: "Thời gian ghi nhận",
              dataIndex: "dataTimestamp",
              render: (value) => dayjs(value).format(DATETIME_FORMAT),
            },
            {
              title: "Hình đã chụp",
              className: "content-baseline w-[290px]",
              dataIndex: "recordOrderPhotos",
              render: (orderPhotos: RecordOrderPhotoInterface[]) => (
                <CustomerPhotosCell orderPhotos={orderPhotos} />
              ),
            },
            {
              title: "Thời gian dùng tool edit",
              dataIndex: "editedAt",
              render: (editedAt?: string) =>
                editedAt ? dayjs(editedAt).format(DATETIME_FORMAT) : "",
            },
            {
              title: "User dùng tool edit",
              dataIndex: "editedByUser",
              render: (editedByUser?: UserInterface) => editedByUser?.name,
            },
            {
              title: "Tổng tiền (VNĐ)",
              dataIndex: "recordOrderPurchases",
              align: "right",
              fixed: "right",
              render: (
                recordOrderPurchases: RecordOrderPurchaseInterface[],
              ) => {
                let total = 0;
                for (const recordOrderPurchase of recordOrderPurchases) {
                  const { quantity } = recordOrderPurchase;
                  total +=
                    recordOrderPurchase.featureOrderProduct.price * quantity;
                }
                return formatMoney(total);
              },
            },
            {
              title: "Sản phẩm đã mua",
              dataIndex: "recordOrderPurchases",
              className: "content-baseline",
              fixed: "right",
              render: (orderPurchases: RecordOrderPurchaseInterface[]) => {
                return orderPurchases.map((orderPurchase, index) => (
                  <ItemProductQuantity
                    isFirst={index === 0}
                    key={orderPurchase.id}
                    quantity={orderPurchase.quantity}
                    unitName={
                      orderPurchase.featureOrderProduct?.projectProduct
                        ?.productPackaging?.unit?.name ?? ""
                    }
                    name={
                      orderPurchase?.featureOrderProduct?.projectProduct
                        ?.product?.name
                    }
                    code={
                      orderPurchase?.featureOrderProduct?.projectProduct
                        ?.product?.code
                    }
                  />
                ));
              },
            },
            {
              title: "Quà đã nhận",
              dataIndex: "recordOrderExchanges",
              className: "content-baseline min-w-[180px]",
              fixed: "right",
              render: (orderExchanges: RecordOrderExchangeInterface[]) => (
                <CustomerExchangesCell orderExchanges={orderExchanges} />
              ),
            },
            {
              dataIndex: "id",
              fixed: "right",

              render: (
                id: number,
                record: { id: number; usesUpdateTool: boolean },
              ) => {
                return (
                  <TableActionCell
                    actions={[
                      {
                        key: "DELETE",
                        action: handleBtnDeleteClick,
                      },
                      canPermissionFunction(PermissionEnum.EDIT_ORDER)
                        ? {
                            key: "EDIT",
                            action: () => {
                              setIsOpen(true);
                              setAction(CURD.UPDATE);
                              setSelectedOrderId(id);
                            },
                          }
                        : null,
                    ]}
                    items={[
                      canPermissionFunction(PermissionEnum.EDIT_ORDER)
                        ? {
                            key: "EDIT",
                            label: "Chỉnh sửa",
                            icon: <EditOutlined />,
                          }
                        : null,
                      {
                        key: "DELETE",
                        label: "Xóa đơn hàng",
                        icon: <DeleteOutlined />,
                      },
                    ]}
                    record={record}
                  />
                );
              },
            },
          ]}
        />

        {isOpen && action && (
          <EditCustomerInformationCapturingModal
            action={action}
            isOpen={isOpen}
            componentFeatureId={componentFeatureId}
            projectId={projectId}
            attendanceId={attendanceId}
            cancelCb={cancel}
            componentFeature={attendanceFeatureDetailQuery.data}
            orderId={selectedOrderId}
            attendance={attendanceQuery.data}
          />
        )}
      </div>
    </>
  );
};

export default EditCustomerInformationCapturingPage;
