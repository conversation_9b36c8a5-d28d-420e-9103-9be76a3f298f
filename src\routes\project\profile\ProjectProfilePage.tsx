import {
  DATETIME_FORMAT,
  DATE_FORMAT,
  DEFAULT_CURRENT_PAGE,
  DEFAULT_PAGE_SIZE,
} from "@/common/constant.ts";
import DebounceSelect from "@/components/DebounceSelectComponent.tsx";
import FilterClassicComponent from "@/components/FilterClassicComponent";
import InnerContainer from "@/components/InnerContainer/InnerContainer.tsx";
import TableActionCell from "@/components/TableActionCell.tsx";
import UserOptionComponent from "@/components/UserOptionComponent.tsx";
import { useMainLayoutStore } from "@/state.ts";
import { useApp } from "@/UseApp.tsx";
import { FileSearchOutlined, SearchOutlined } from "@ant-design/icons";
import { Button, DatePicker, Form, Input, Select, Space, Table } from "antd";
import TextArea from "antd/es/input/TextArea";
import { TableProps } from "antd/lib";
import dayjs from "dayjs";
import _ from "lodash";
import React, { useCallback, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import { UserInterface } from "../../user/interface.ts";
import { useGetEmployeeMutation } from "../employee/service.ts";
import { ProfileRequestInterface, ProfileRequestStatus } from "./interface.ts";
import ProjectProfileModal from "./ProjectProfileModal.tsx";
import {
  useApproveUserProfilesMutation,
  useProjectUserProfilesQuery,
  useRejectUserProfilesMutation,
} from "./service.ts";
import "./style.css";

const approvedDot = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="10"
    height="10"
    viewBox="0 0 10 10"
    fill="none"
  >
    <circle cx="5" cy="5" r="5" fill="#00CD21" />
  </svg>
);

const rejectedDot = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="10"
    height="10"
    viewBox="0 0 10 10"
    fill="none"
  >
    <circle cx="5" cy="5" r="5" fill="#F92525" />
  </svg>
);

const pendingDot = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="10"
    height="10"
    viewBox="0 0 10 10"
    fill="none"
  >
    <circle cx="5" cy="5" r="5" fill="#8C8C8D" />
  </svg>
);

const ProjectProfilePage = () => {
  const { openConfirmModal, showNotification } = useApp();
  const projectId = parseInt(useParams().id ?? "0");

  const [searchForm] = Form.useForm();
  const [filter, setFilter] = useState<{
    reviewedAt?: string;
    createdAt?: string;
    userId?: {
      label: string;
      value: string;
      key: string;
    };
    id?: number;
  }>({});
  const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [selectedItemKeys, setSelectedItemKeys] = useState<React.Key[]>([]);
  const [rejectForm] = Form.useForm();
  const [isOpen, setIsOpen] = useState(false);
  const [selectedProfileRequest, setSelectedProfileRequest] = useState<
    ProfileRequestInterface | undefined
  >(undefined);

  const { collapsed } = useMainLayoutStore();

  const projectUserProfilesQuery = useProjectUserProfilesQuery(projectId, {
    ...filter,
    reviewedAt: filter.reviewedAt
      ? dayjs(filter.reviewedAt).toISOString()
      : undefined,
    createdAt: filter.createdAt
      ? dayjs(filter.createdAt).toISOString()
      : undefined,
    userId: filter.userId ? filter.userId.value : undefined,
    id: filter.id ? filter.id : undefined,
    take: pageSize,
    skip: (currentPage - 1) * pageSize,
  });

  const getEmployeeMutation = useGetEmployeeMutation(projectId);
  const approveUserProfileMutation = useApproveUserProfilesMutation(projectId);
  const rejectUserProfilesMutation = useRejectUserProfilesMutation(projectId);

  const fetchEmployeeFilterOptions = useCallback(
    async (keyword?: string) => {
      if (keyword?.length !== 0) {
        const response = await getEmployeeMutation.mutateAsync({
          keyword,
          take: 10,
        });
        return response.entities.map((item) => ({
          value: item.user.id,
          label: item.user.name,
          user: item.user,
          isAvailable: true,
        }));
      }

      return [];
    },
    [getEmployeeMutation],
  );

  const searchFormContent = (
    <>
      <Form.Item name="id">
        <Input
          prefix={<SearchOutlined />}
          placeholder="ID yêu cầu"
          allowClear
          type={"number"}
        />
      </Form.Item>

      <Form.Item name="userId">
        <DebounceSelect
          className={"h-10"}
          popupMatchSelectWidth={false}
          placeholder="Nhân viên"
          allowClear
          showSearch
          fetchOptions={fetchEmployeeFilterOptions}
          optionRender={(option) => {
            if (option.data.user) {
              return (
                <UserOptionComponent
                  avatarUrl={option.data.user?.imageUrl}
                  name={option.data.user?.name}
                  phone={option.data.user?.phone}
                  email={option.data.user?.email}
                  key={option.data.user.id}
                />
              );
            }
            return option.label;
          }}
        />
      </Form.Item>

      <Form.Item name={"createdAt"}>
        <DatePicker
          format={DATE_FORMAT}
          allowClear
          placeholder="Ngày gửi yêu cầu"
        />
      </Form.Item>

      <Form.Item name={"reviewedAt"}>
        <DatePicker
          format={DATE_FORMAT}
          allowClear
          placeholder={"Ngày xét duyệt"}
        />
      </Form.Item>

      <Form.Item name={"status"}>
        <Select
          allowClear
          placeholder={"Tình trạng"}
          popupMatchSelectWidth={false}
          options={[
            {
              label: "Chờ xét duyệt",
              value: "PENDING",
            },
            {
              label: "Từ chối",
              value: "REJECTED",
            },
            {
              label: "Đã duyệt",
              value: "APPROVED",
            },
          ]}
        />
      </Form.Item>
    </>
  );

  const columns: TableProps<ProfileRequestInterface>["columns"] = [
    {
      title: "ID yêu cầu",
      dataIndex: "id",
      className: "min-w-[100px]",
    },
    {
      title: "Nhân viên",
      dataIndex: "user",
      render: (user: UserInterface) => user.name,
      className: "min-w-[100px]",
    },
    {
      title: "Ngày gửi yêu cầu",
      dataIndex: "createdAt",
      render: (createdAt: string) => dayjs(createdAt).format(DATETIME_FORMAT),
      className: "min-w-[100px]",
    },
    {
      title: "Người xét duyệt",
      dataIndex: "reviewedByUser",
      render: (reviewedByUser: UserInterface) => reviewedByUser?.name,
      className: "min-w-[100px]",
    },
    {
      title: "Ngày xét duyệt",
      dataIndex: "reviewedAt",
      render: (reviewedAt: string) =>
        reviewedAt ? dayjs(reviewedAt).format(DATETIME_FORMAT) : "",
      className: "min-w-[100px]",
    },
    {
      title: "Tình trạng ",
      dataIndex: "status",
      render: (status: string, record: ProfileRequestInterface) => {
        switch (status) {
          case "PENDING":
            return <>{pendingDot} Chờ xét duyệt</>;
          case "REJECTED":
            return (
              <>
                <span>{rejectedDot} Từ chối</span> <br />
                <span className={"text-hint bg-[#F5F5F5] "}>
                  {record.reason}
                </span>
              </>
            );
          case "APPROVED":
            return <>{approvedDot} Đã duyệt</>;
          default:
            return "";
        }
      },
      className: "min-w-[100px]",
    },
    {
      render: (_, record) => {
        return (
          <TableActionCell
            actions={[
              {
                key: "view",
                action: () => {
                  setSelectedProfileRequest(record);
                  setIsOpen(true);
                },
              },
            ]}
            items={[
              {
                key: "view",
                label: "Xem chi tiết",
                icon: <FileSearchOutlined />,
              },
            ]}
            record={record}
          />
        );
      },

      width: 100,
    },
  ];

  const pagination = useMemo(() => {
    return {
      current: currentPage,
      total: projectUserProfilesQuery.data?.count ?? 0,
      pageSize: pageSize,
      onChange: (page: number) => {
        setCurrentPage(page);
      },
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: (_current: number, size: number) => {
        setPageSize(size);
        setCurrentPage(DEFAULT_CURRENT_PAGE);
      },
    };
  }, [currentPage, projectUserProfilesQuery.data?.count, pageSize]);

  const searchHandler = useCallback(async () => {
    const values = searchForm.getFieldsValue();
    setCurrentPage(DEFAULT_CURRENT_PAGE);
    if (_.isEqual(filter, values)) {
      await projectUserProfilesQuery.refetch();
    }
    setFilter(values);
  }, [filter, projectUserProfilesQuery, searchForm]);

  const rowSelection = useMemo(
    () => ({
      selectedRowKeys: selectedItemKeys,
      onChange: (newSelectedRowKeys: React.Key[]) => {
        setSelectedItemKeys(newSelectedRowKeys);
      },
      getCheckboxProps: (record: ProfileRequestInterface) => {
        return {
          disabled: record.status !== ProfileRequestStatus.PENDING,
        };
      },
    }),
    [selectedItemKeys],
  );

  const onApprove = useCallback(() => {
    openConfirmModal({
      onConfirm: async () => {
        await approveUserProfileMutation.mutateAsync({
          ids: selectedItemKeys.map((key) => Number(key)),
        });

        showNotification({
          type: "success",
          message: `Duyệt yêu cầu thay đổi thông tin thành công`,
        });

        setSelectedItemKeys([]);
        await projectUserProfilesQuery.refetch();
      },
      onCancel(): void {},
      title: "Duyệt yêu cầu thay đổi thông tin",
      content: (
        <>
          Profile của{" "}
          <span className={"font-semibold"}>
            {selectedItemKeys.length} nhân viên
          </span>{" "}
          sẽ được thay đổi trên hệ thống khi bạn nhấn nút xác nhận. Bạn vẫn muốn
          tiến hành chứ?
        </>
      ),
    });
  }, [
    approveUserProfileMutation,
    openConfirmModal,
    projectUserProfilesQuery,
    selectedItemKeys,
    showNotification,
  ]);

  const onReject = useCallback(() => {
    openConfirmModal({
      onConfirm: async () => {
        await rejectForm.validateFields();

        await rejectUserProfilesMutation.mutateAsync({
          ids: selectedItemKeys.map((key) => Number(key)),
          reason: rejectForm.getFieldValue("reason"),
        });

        showNotification({
          type: "success",
          message: `Từ chối yêu cầu thay đổi thông tin thành công`,
        });

        setSelectedItemKeys([]);
        await projectUserProfilesQuery.refetch();
        rejectForm.resetFields();
      },
      onCancel(): void {},
      title: (
        <span className={"text-primary"}>
          Từ chối yêu cầu thay đổi thông tin
        </span>
      ),
      content: (
        <>
          <span className={"text-hint"}>
            Lý do từ chối sẽ được áp dụng cho tất cả các yêu cầu được chọn
          </span>
          <Form layout={"vertical"} form={rejectForm}>
            <Form.Item
              name={"reason"}
              label={"Lý do từ chối"}
              rules={[{ required: true }]}
            >
              <TextArea showCount maxLength={256} />
            </Form.Item>
          </Form>
        </>
      ),
    });
  }, [
    openConfirmModal,
    projectUserProfilesQuery,
    rejectForm,
    rejectUserProfilesMutation,
    selectedItemKeys,
    showNotification,
  ]);

  return (
    <>
      <div>
        <h2>Yêu cầu cập nhật profile nhân viên</h2>
        <InnerContainer>
          <FilterClassicComponent
            searchHandler={searchHandler}
            searchForm={searchForm}
            content={searchFormContent}
            className="mb-6"
          />

          <Table
            columns={columns}
            dataSource={projectUserProfilesQuery.data?.entities}
            rowKey={(record) => record.id}
            scroll={{
              x: "max-content",
              y: pagination.total ? "80vh" : undefined,
            }}
            loading={projectUserProfilesQuery.isFetching}
            pagination={pagination}
            rowSelection={rowSelection}
          />
        </InnerContainer>
      </div>

      {selectedItemKeys.length > 0 && (
        <div
          className={`fixed bottom-0 h-[80px] ml-[-40px] bg-[#F7F8FA] border-t-[1.5px] border-[#DDE1EA] pl-10 pr-10 ${collapsed ? "footer-with-slider-collapsed" : "footer-with-slider-not-collapsed"}`}
        >
          <div
            className={"justify-between flex h-full align-middle items-center"}
          >
            <p>Đã chọn {selectedItemKeys.length} yêu cầu</p>
            <Space>
              <Button type="default" onClick={onReject}>
                Từ chối yêu cầu
              </Button>
              <Button type="primary" onClick={onApprove}>
                Duyệt yêu cầu
              </Button>
            </Space>
          </div>
        </div>
      )}

      {selectedProfileRequest && (
        <ProjectProfileModal
          isOpen={isOpen}
          profileRequest={selectedProfileRequest}
          projectId={projectId}
          onCloseCb={async () => {
            setIsOpen(false);
            setSelectedProfileRequest(undefined);
            await projectUserProfilesQuery.refetch();
          }}
          setIsOpen={setIsOpen}
        />
      )}
    </>
  );
};

export default ProjectProfilePage;
