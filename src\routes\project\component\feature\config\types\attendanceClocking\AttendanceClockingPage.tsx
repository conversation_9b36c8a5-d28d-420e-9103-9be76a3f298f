import { useApp } from "@/UseApp";
import { compareObjectsByProperties } from "@/common/helper";
import InnerContainer from "@/components/InnerContainer/InnerContainer";
import { Button, Checkbox, Form, InputNumber } from "antd";
import { useCallback, useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import {
  useAttendanceClockingQuery,
  useUpdateAttendanceClockingMutation,
} from "./service";

/**
 * Chấm công vào/ra
 *
 * @return {JSX.Element} The rendered page component
 */
export default function AttendanceClockingPage() {
  const componentFeatureId = parseInt(useParams().componentFeatureId ?? "0");

  const { showNotification } = useApp();

  const [form] = Form.useForm();
  const [isBtnSubmitDisable, setIsBtnSubmitDisable] = useState(true);

  const attendanceClockingQuery =
    useAttendanceClockingQuery(componentFeatureId);

  const updateAttendanceClockingMutation =
    useUpdateAttendanceClockingMutation(componentFeatureId);

  useEffect(() => {
    form.setFieldsValue(attendanceClockingQuery.data);
  }, [attendanceClockingQuery.data, form]);

  const onFormFinish = useCallback(() => {
    const data = form.getFieldsValue();
    updateAttendanceClockingMutation.mutateAsync(data).then(() => {
      attendanceClockingQuery.refetch();
      showNotification({
        type: "success",
        message: "Cập nhật cấu hình thành công.",
      });
      setIsBtnSubmitDisable(true);
    });
  }, [
    attendanceClockingQuery,
    form,
    showNotification,
    updateAttendanceClockingMutation,
  ]);

  const onFormChange = useCallback(() => {
    setIsBtnSubmitDisable(
      compareObjectsByProperties(
        attendanceClockingQuery.data,
        form.getFieldsValue(),
        [
          "isPhotoRequired",
          "isWatermarkRequired",
          "isLocationRequired",
          "mustWithinRadius",
          "isFaceRequired",
        ],
      ),
    );
  }, [attendanceClockingQuery.data, form]);

  return (
    <InnerContainer>
      <Form
        layout="vertical"
        form={form}
        onFinish={onFormFinish}
        onChange={onFormChange}
      >
        <Form.Item
          name="isPhotoRequired"
          valuePropName="checked"
          className={"mb-4"}
        >
          <Checkbox>Chụp hình chấm công</Checkbox>
        </Form.Item>
        <Form.Item noStyle dependencies={["isPhotoRequired"]}>
          {() => (
            <>
              {form.getFieldValue("isPhotoRequired") && (
                <div className="ml-[33px]">
                  <Form.Item
                    name="isFaceRequired"
                    valuePropName="checked"
                    className={"mb-4"}
                  >
                    <Checkbox>Xác thực khuôn mặt khi chấm công</Checkbox>
                  </Form.Item>
                  <Form.Item
                    name="isWatermarkRequired"
                    valuePropName="checked"
                    className={"mb-4"}
                  >
                    <Checkbox>
                      Ghi watermark thời gian và vị trí lên ảnh chụp
                    </Checkbox>
                  </Form.Item>
                </div>
              )}
            </>
          )}
        </Form.Item>

        <Form.Item
          name="isLocationRequired"
          valuePropName="checked"
          className={"mb-4"}
        >
          <Checkbox>Ghi nhận vị trí chấm công (GPS)</Checkbox>
        </Form.Item>

        <Form.Item
          name="mustWithinRadius"
          label={<p className="mb-1 mt-0 pt-0">Bán kính chấm công (m)</p>}
          extra={
            <p className={"mt-1"}>
              Nhân viên chỉ có thể chấm công trong bán kính cho phép từ vị ví
              trí đang đứng so với outlet
            </p>
          }
        >
          <InputNumber controls={false} step={0} style={{ width: "200px" }} />
        </Form.Item>

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            loading={updateAttendanceClockingMutation.isPending}
            disabled={isBtnSubmitDisable}
          >
            Lưu thông tin
          </Button>
        </Form.Item>
      </Form>
    </InnerContainer>
  );
}
