import ChartContanier from "@/routes/project/chart/ChartContanier";
import ReactApexChart from "react-apexcharts";
import DashboardFilterZone from "../../DashboardFilterZone";

const DisplayShareTabV2Tab = () => {
  return (
    <>
      <DashboardFilterZone
        handleApply={function (): void {
          throw new Error("Function not implemented.");
        }}
      />

      <ChartContanier>
        <ReactApexChart
          type="bar"
          height={450}
          options={{
            title: {
              text: "Competitor PA",
              align: "left",
              style: {
                fontSize: "16px",
                fontWeight: "bold",
              },
            },
            subtitle: {
              text: "Order: Heineken, Sabeco, Habeco, Carlsberg",
            },
            chart: {
              type: "bar",
              height: 350,
              // stacked: true,
            },
            xaxis: {
              categories: [
                "20/12",
                "21/12",
                "22/12",
                "23/12",
                "24/12",
                "25/12",
              ],
            },
          }}
          series={[
            {
              name: "PG Inline",
              group: "Heineken",
              data: [23, 21, 2, 23, 34, 11],
            },
            {
              name: "PG Gift touch",
              group: "Heineken",
              data: [10, 12, 5, 14, 15, 6],
            },
            {
              name: "PG kha bia",
              group: "Heineken",
              data: [8, 9, 7, 10, 12, 13],
            },
            {
              name: "PG time",
              group: "Heineken",
              data: [20, 18, 15, 17, 20, 18],
            },
            {
              name: "PG Inline",
              group: "Sabeco",
              data: [15, 13, 10, 12, 15, 11],
            },
            {
              name: "PG Gift touch",
              group: "Sabeco",
              data: [11, 9, 8, 10, 12, 13],
            },
            {
              name: "PG kha bia 2",
              group: "Sabeco",
              data: [18, 16, 14, 17, 19, 18],
            },
            {
              name: "PG time 2",
              group: "Sabeco",
              data: [25, 22, 20, 23, 26, 24],
            },
          ]}
        />
      </ChartContanier>
    </>
  );
};
export default DisplayShareTabV2Tab;
