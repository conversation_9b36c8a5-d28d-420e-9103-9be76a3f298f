import { AbstractFilterInterface } from "@/common/interface";
import { useApp } from "@/UseApp";
import { useQuery } from "@tanstack/react-query";
import { ProjectDeliveryInterface } from "./interface";

export const useProjectDeliveriesQuery = (
  projectId: number,
  filter?: AbstractFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["projectDeliveries", projectId, filter],
    queryFn: () =>
      axiosGet<
        { entities: ProjectDeliveryInterface[]; count: number },
        unknown
      >("/projects/" + projectId + "/otps/deliveries", filter),
    enabled: !!projectId,
  });
};
