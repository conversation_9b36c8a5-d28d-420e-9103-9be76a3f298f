import { DEFAULT_CURRENT_PAGE } from "@/common/constant";
import CustomTable from "@/components/CustomTable/CustomTable";
import FilterClassicComponent from "@/components/FilterClassicComponent";
import InnerContainer from "@/components/InnerContainer/InnerContainer";
import usePagination from "@/hooks/usePagination";
import { Form, Input, Select, TableColumnsType } from "antd";
import _ from "lodash";
import { useCallback, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import { ProjectDeliveryInterface } from "./interface";
import { useProjectDeliveriesQuery } from "./service";

export default function ProjectOtpDeliveryPage() {
  const projectId = parseInt(useParams().id ?? "0");

  const [searchForm] = Form.useForm();

  const [filter, setFilter] = useState({});
  const { getPagination, currentPage, pageSize, setCurrentPage } =
    usePagination({});

  const projectDeliveriesQuery = useProjectDeliveriesQuery(projectId, {
    ...filter,
    take: pageSize,
    skip: (currentPage - 1) * pageSize,
  });

  const pagination = useMemo(
    () => getPagination(projectDeliveriesQuery.data?.count ?? 0),
    [getPagination, projectDeliveriesQuery.data?.count],
  );

  const searchHandler = useCallback(() => {
    setCurrentPage(DEFAULT_CURRENT_PAGE);
    const values = searchForm.getFieldsValue();
    if (_.isEqual(filter, values)) {
      projectDeliveriesQuery.refetch();
    }
    setFilter(values);
  }, [filter, projectDeliveriesQuery, searchForm, setCurrentPage]);

  const columns: TableColumnsType<ProjectDeliveryInterface> = [
    {
      title: "Recipient",
      dataIndex: "recipient",
      key: "recipient",
      className: "min-w-[100px]",
    },
    {
      title: "OTP Code",
      dataIndex: "otpCode",
      key: "otpCode",
      className: "min-w-[100px]",
    },
    {
      title: "Created At",
      dataIndex: "createdAt",
      key: "createdAt",
      render: (value: string) => new Date(value).toLocaleString(),
      className: "min-w-[150px]",
    },
    {
      title: "OTP Expires At",
      dataIndex: "otpExpiresAt",
      key: "otpExpiresAt",
      render: (value: string) => new Date(value).toLocaleString(),
      className: "min-w-[150px]",
    },
    {
      title: "Resend Count",
      dataIndex: "resendCount",
      key: "resendCount",
      className: "min-w-[100px]",
    },
    {
      title: "Attempt Count",
      dataIndex: "attemptCount",
      key: "attemptCount",
      className: "min-w-[100px]",
    },
    {
      title: "Verified",
      dataIndex: "verified",
      key: "verified",
      render: (value: boolean) => (value ? "Yes" : "No"),
      className: "min-w-[100px]",
    },
  ];

  const filterContent = (
    <>
      <Form.Item name={"recipient"}>
        <Input placeholder="recipient" allowClear />
      </Form.Item>
      <Form.Item name={"verified"}>
        <Select
          placeholder="verified"
          options={[{ value: "true" }, { value: "false" }]}
          allowClear
        />
      </Form.Item>
    </>
  );

  return (
    <div>
      <h2>OTP Deliveries</h2>
      <InnerContainer>
        <FilterClassicComponent
          searchHandler={searchHandler}
          searchForm={searchForm}
          className="mb-6"
          content={filterContent}
        />

        <CustomTable
          dataSource={projectDeliveriesQuery?.data?.entities}
          columns={columns}
          scroll={{
            x: "max-content",
            y: pagination.total ? "80vh" : undefined,
          }}
          pagination={pagination}
          loading={projectDeliveriesQuery.isFetching}
        />
      </InnerContainer>
    </div>
  );
}
