import { CURD } from "@/common/constant";
import { formatMoney } from "@/common/helper";
import { renderTableCell } from "@/components/table-cell";
import TableActionCell from "@/components/TableActionCell";
import { ProjectAgencyInterface } from "@/routes/project/interface";
import { useApp } from "@/UseApp";
import {
  DeleteOutlined,
  EditOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import { Button, Form, Modal, Table } from "antd";
import { useCallback, useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import {
  ConfigExchangeTabEnum,
  FeatureSchemeExchangeInterface,
  FeatureSchemeInterface,
} from "../../../interface";
import {
  useDeleteSchemeExchangeMutation,
  useSchemesQuery,
  useUpdateSchemeExchangeMutation,
} from "../service";
import ExchangeTabConditionEditNameModal from "./ExchangeTabConditionEditNameModal";
import ExchangeTabConditionModal from "./ExchangeTabConditionModal";
import ExchangeTabSchemeModal from "./ExchangeTabSchemeModal";

const ExchangeTab = (props: {
  projectAgency: ProjectAgencyInterface;
  componentFeatureId: number;
}) => {
  const { projectAgency, componentFeatureId } = props;

  const { showNotification } = useApp();
  const location = useLocation();

  const [isSchemeOpen, setIsSchemeOpen] = useState(false);
  const [isAddConditionOpen, setIsAddConditionOpen] = useState(false);
  const [selectedScheme, setSelectedScheme] =
    useState<null | FeatureSchemeInterface>(null);
  const [modal, contextHolder] = Modal.useModal();
  const [isEditConditionOpen, setIsEditConditionOpen] = useState(false);
  const [formEditScheme] = Form.useForm();
  const [schemeAction, setSchemeAction] = useState<CURD | null>(null);

  const schemesQuery = useSchemesQuery(componentFeatureId, projectAgency.id);

  const updateSchemeExchangeMutation =
    useUpdateSchemeExchangeMutation(componentFeatureId);
  const deleteSchemeExchangeMutation =
    useDeleteSchemeExchangeMutation(componentFeatureId);

  const goToHash = useCallback(async (hash: string) => {
    if (hash) {
      await new Promise((resolve) => setTimeout(resolve, 1000)).then(() => {
        const element = document.getElementById(hash);
        element?.scrollIntoView({ behavior: "smooth" });
      });
    }
  }, []);

  useEffect(() => {
    if (location.hash) {
      goToHash(location.hash.replace("#", ""));
    }
  }, [goToHash, location]);

  const exchangeConditionsLengthClick = useCallback(
    (record: FeatureSchemeExchangeInterface) => {
      if (record.logical === "or" && record.exchangeConditions.length > 0) {
        modal.info({
          title: "Danh sách sản phẩm quy định",
          icon: <></>,
          width: "70%",
          content: (
            <div>
              <Table
                rowKey={"id"}
                columns={[
                  {
                    title: "Tên sản phẩm cần mua",
                    dataIndex: "name",
                  },
                  {
                    title: "Mã sản phẩm",
                    dataIndex: "code",
                  },
                  {
                    title: "Điều kiện",
                    dataIndex: "quantity",
                  },
                  {
                    title: "Quy cách cần mua",
                    dataIndex: "unitName",
                  },
                  {
                    title: "Nhãn hàng",
                    dataIndex: "brandName",
                  },
                ]}
                pagination={false}
                dataSource={record.exchangeConditions.map(
                  (exchangeCondition) => ({
                    id: exchangeCondition.id,
                    name: exchangeCondition.projectProduct.product.name,
                    code: exchangeCondition.projectProduct.product.code,
                    unitName:
                      exchangeCondition.projectProduct.productPackaging?.unit
                        .name,
                    brandName:
                      exchangeCondition.projectProduct.product?.brand?.name,
                    quantity: exchangeCondition.quantity,
                  }),
                )}
              />
            </div>
          ),
        });
      } else if (
        record.logical === "relaxed" &&
        record.exchangeConditions.length > 0
      ) {
        modal.info({
          title: "Danh sách sản phẩm quy định",
          icon: <></>,
          width: "70%",
          content: (
            <div>
              <Table
                rowKey={"id"}
                columns={[
                  {
                    title: "Tag",
                    dataIndex: "tag",
                  },
                  {
                    title: "Tên sản phẩm cần mua",
                    dataIndex: "name",
                  },
                  {
                    title: "Mã sản phẩm",
                    dataIndex: "code",
                  },
                  {
                    title: "Điều kiện",
                    dataIndex: "quantity",
                  },
                  {
                    title: "Quy cách cần mua",
                    dataIndex: "unitName",
                  },
                  {
                    title: "Nhãn hàng",
                    dataIndex: "brandName",
                  },
                ]}
                pagination={false}
                dataSource={record.exchangeConditions.map(
                  (exchangeCondition) => ({
                    id: exchangeCondition.id,
                    tag: exchangeCondition.tag,
                    name: exchangeCondition.projectProduct.product.name,
                    code: exchangeCondition.projectProduct.product.code,
                    unitName:
                      exchangeCondition.projectProduct.productPackaging?.unit
                        .name,
                    brandName:
                      exchangeCondition.projectProduct.product?.brand?.name,
                    quantity: exchangeCondition.quantity,
                  }),
                )}
              />
            </div>
          ),
        });
      }
    },
    [modal],
  );

  const renderExchangeConditionCell = useCallback(
    (record: FeatureSchemeExchangeInterface) => {
      const { hasPlayedGame, name } = record;

      let condition;

      if (hasPlayedGame) {
        condition = <p>Đã chơi game</p>;
      } else {
        condition = (
          <>
            {record.reachAmount || record.exchangeConditions.length > 0 ? (
              <p className="text-hint mb-0">Mua</p>
            ) : (
              <p className="">Không yêu cầu mua hàng</p>
            )}

            {record.reachAmount && (
              <p className={"m-1"}>
                {formatMoney(record.reachAmount)} VNĐ
                {record.exchangeConditions.length > 0 && (
                  <span className="text-hint"> và</span>
                )}
              </p>
            )}
            {record.logical === "and" &&
              record.exchangeConditions.map((item, index) => {
                return (
                  <p key={item.id} className={"m-1"}>
                    <span className="text-primary font-semibold">
                      x{item.quantity}
                    </span>{" "}
                    {item.projectProduct?.product.code} -{" "}
                    {item.projectProduct?.productPackaging?.unit.name} -{" "}
                    {item.projectProduct?.product.name}
                    {index < record.exchangeConditions.length - 1 && (
                      <span className="text-hint"> và </span>
                    )}
                  </p>
                );
              })}

            {record.logical === "or" && (
              <p className={"m-1"}>
                Sản phẩm bất kỳ thỏa một trong{" "}
                <Button
                  type="link"
                  className="underline cursor-pointer p-0 m-0"
                  onClick={() => exchangeConditionsLengthClick(record)}
                >
                  {record.exchangeConditions.length} điều kiện
                </Button>{" "}
                sau
              </p>
            )}

            {record.logical === "relaxed" && (
              <p className="m-1">
                Tối thiểu {record.reachQuantity} sản phẩm{" "}
                <Button
                  type="link"
                  className="underline cursor-pointer p-0 m-0"
                  onClick={() => exchangeConditionsLengthClick(record)}
                >
                  {record.exchangeConditions.length} điều kiện
                </Button>{" "}
                sau
              </p>
            )}
          </>
        );
      }

      return (
        <>
          {name && (
            <p className="font-semibold bg-input-disabled inline pl-1 pr-1">
              {name}
            </p>
          )}
          {condition}
        </>
      );
    },
    [exchangeConditionsLengthClick],
  );

  const handleActionEditClick = useCallback(
    (record: FeatureSchemeExchangeInterface) => {
      setIsEditConditionOpen(true);
      formEditScheme.setFieldsValue({
        id: record.id,
        name: record.name,
        featureSchemeId: record.featureSchemeId,
      });
    },
    [formEditScheme],
  );

  const handleActionActiveClick = useCallback(
    (record: FeatureSchemeExchangeInterface) => {
      modal.confirm({
        title: `Kích hoạt điều kiện nhận quà`,
        content: (
          <>
            <p>Bạn có chắc chắn muốn kích hoạt điều kiện nhận quà:</p>
            {renderExchangeConditionCell(record)}
          </>
        ),
        okText: "Kích hoạt",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await updateSchemeExchangeMutation.mutateAsync({
              exchangeId: record.id,
              schemeId: record.featureSchemeId,
              isActive: true,
            });

            showNotification({
              type: "success",
              message: `Kích hoạt điều kiện nhận quà thành công`,
            });

            await schemesQuery.refetch();
          } catch (error) {
            console.error(error);
            showNotification({
              type: "error",
              message: `Kích hoạt điều kiện nhận quà thất bại`,
            });
          }
        },
      });
    },
    [
      modal,
      renderExchangeConditionCell,
      schemesQuery,
      showNotification,
      updateSchemeExchangeMutation,
    ],
  );

  const handleActionInActiveClick = useCallback(
    (record: FeatureSchemeExchangeInterface) => {
      modal.confirm({
        title: `Ngừng hoạt động điều kiện nhận quà`,
        content: (
          <>
            <p>Bạn có chắc chắn muốn ngừng hoạt động điều kiện nhận quà:</p>
            {renderExchangeConditionCell(record)}
          </>
        ),
        okText: "Ngừng hoạt động",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await updateSchemeExchangeMutation.mutateAsync({
              exchangeId: record.id,
              schemeId: record.featureSchemeId,
              isActive: false,
            });

            showNotification({
              type: "success",
              message: `Ngừng hoạt động điều kiện nhận quà thành công`,
            });

            await schemesQuery.refetch();
          } catch (error) {
            console.error(error);

            showNotification({
              type: "error",
              message: `Ngừng hoạt động điều kiện nhận quà thất bại`,
            });
          }
        },
      });
    },
    [
      modal,
      renderExchangeConditionCell,
      schemesQuery,
      showNotification,
      updateSchemeExchangeMutation,
    ],
  );

  const handleActionDeleteExchangeCondition = useCallback(
    (record: FeatureSchemeExchangeInterface) => {
      modal.confirm({
        title: `Xóa điều kiện nhận quà`,
        content: (
          <>
            <p>Bạn có chắc chắn muốn xóa điều kiện nhận quà:</p>
            {renderExchangeConditionCell(record)}
          </>
        ),
        okText: "Xóa",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await deleteSchemeExchangeMutation.mutateAsync({
              exchangeId: record.id,
              schemeId: record.featureSchemeId,
            });

            showNotification({
              type: "success",
              message: `Xóa điều kiện nhận quà thành công`,
            });

            await schemesQuery.refetch();
          } catch (error) {
            console.error(error);

            showNotification({
              type: "error",
              message: `Xóa điều kiện nhận quà thất bại`,
            });
          }
        },
      });
    },
    [
      deleteSchemeExchangeMutation,
      modal,
      renderExchangeConditionCell,
      schemesQuery,
      showNotification,
    ],
  );

  const onCancelEditScheme = useCallback(() => {
    setIsEditConditionOpen(false);
    formEditScheme.resetFields();
  }, [formEditScheme]);

  return (
    <>
      <div className="justify-end flex">
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            setIsSchemeOpen(true);
            setSchemeAction(CURD.CREATE);
          }}
        >
          Thêm scheme
        </Button>
      </div>
      <div>
        {schemesQuery.data?.entities.map((scheme) => (
          <div key={scheme.id}>
            <div className="flex justify-between" id={`scheme${scheme.id}`}>
              <p>
                <span className="text-text-color font-semibold">
                  &gt; #{scheme.id} -{" "}
                </span>
                <span className="text-primary font-semibold">
                  {scheme.name}
                </span>

                <Button
                  type="link"
                  icon={<EditOutlined />}
                  onClick={() => {
                    setSchemeAction(CURD.UPDATE);
                    setSelectedScheme(scheme);
                    setIsSchemeOpen(true);
                  }}
                />
              </p>
              <p
                className="text-blue cursor-pointer font-semibold"
                onClick={() => {
                  setIsAddConditionOpen(true);
                  setSelectedScheme(scheme);
                }}
              >
                Thêm điều kiện nhận quà
              </p>
            </div>
            <Table
              rowKey={"id"}
              dataSource={scheme.featureSchemeExchanges}
              columns={[
                {
                  title: "Điều kiện nhận quà",
                  render: (_, record) => {
                    return renderExchangeConditionCell(record);
                  },
                },

                {
                  title: "Quà nhận được / phần quà",
                  render: (_, record) => {
                    return (
                      <>
                        {record.exchangeProceeds.map((item, index) => {
                          const isHaveAndText =
                            index < record.exchangeProceeds.length - 1;
                          if (item.projectProduct) {
                            return (
                              <p key={item.id}>
                                <span className="text-primary font-semibold">
                                  x{item.quantity}
                                </span>{" "}
                                {item.projectProduct?.product.code} -{" "}
                                {
                                  item.projectProduct?.productPackaging?.unit
                                    .name
                                }{" "}
                                - {item.projectProduct?.product.name}
                                {isHaveAndText && (
                                  <span className="text-hint"> và </span>
                                )}
                              </p>
                            );
                          }

                          if (item.projectItem) {
                            return (
                              <p key={item.id}>
                                <span className="text-primary font-semibold">
                                  x{item.quantity}
                                </span>{" "}
                                {item.projectItem?.item.code} -{" "}
                                {item.projectItem?.item.unit?.name} -{" "}
                                {item.projectItem?.item.name}
                                {isHaveAndText && (
                                  <span className="text-hint"> và </span>
                                )}
                              </p>
                            );
                          }
                          return <></>;
                        })}
                      </>
                    );
                  },
                },
                {
                  title: "Quà lucky draw",
                  className: "min-w-[50px]",
                  render: (_, record) => {
                    return record.luckyDrawCount > 0 ? (
                      <>
                        <span className="text-primary font-semibold">
                          {record.luckyDrawCount}x
                        </span>{" "}
                        {record?.luckyDraw?.name}{" "}
                      </>
                    ) : (
                      ""
                    );
                  },
                },
                {
                  title: "SL phần quà tối đa / hóa đơn",
                  dataIndex: "maxReceiveQuantity",
                  align: "right",
                },
                {
                  title: "Tình trạng",
                  dataIndex: "isActive",
                  render: (value, record, index) => {
                    return renderTableCell(value, record, index, "isActive");
                  },
                },
                {
                  render: (_, record) => (
                    <TableActionCell
                      actions={[
                        {
                          key: ConfigExchangeTabEnum.EDIT,
                          action: handleActionEditClick,
                        },
                        {
                          key: ConfigExchangeTabEnum.ACTIVE,
                          action: handleActionActiveClick,
                        },
                        {
                          key: ConfigExchangeTabEnum.INACTIVE,
                          action: handleActionInActiveClick,
                        },
                        {
                          key: ConfigExchangeTabEnum.DELETE,
                          action: handleActionDeleteExchangeCondition,
                        },
                      ]}
                      items={[
                        {
                          key: ConfigExchangeTabEnum.EDIT,
                          label: "Chỉnh sửa",
                          icon: <EditOutlined />,
                        },

                        !record.isActive
                          ? {
                              key: ConfigExchangeTabEnum.ACTIVE,
                              label: "Kích hoạt",
                              icon: <PlayCircleOutlined />,
                            }
                          : {
                              key: ConfigExchangeTabEnum.INACTIVE,
                              label: "Ngừng hoạt động",
                              icon: <PauseCircleOutlined />,
                            },
                        {
                          key: ConfigExchangeTabEnum.DELETE,
                          label: "Xóa khỏi chức năng",
                          icon: <DeleteOutlined />,
                        },
                      ]}
                      record={record}
                    />
                  ),
                },
              ]}
              pagination={false}
            />
          </div>
        ))}
      </div>

      {isSchemeOpen && (
        <ExchangeTabSchemeModal
          open={isSchemeOpen}
          projectAgency={projectAgency}
          componentFeatureId={componentFeatureId}
          cb={() => {
            schemesQuery.refetch();
            setSelectedScheme(null);
            setIsSchemeOpen(false);
            setSchemeAction(null);
          }}
          selectedScheme={selectedScheme}
          action={schemeAction}
          cancelCb={() => {
            setSelectedScheme(null);
            setIsSchemeOpen(false);
            setSchemeAction(null);
          }}
        />
      )}

      {isAddConditionOpen && (
        <ExchangeTabConditionModal
          open={isAddConditionOpen}
          projectAgency={projectAgency}
          selectedScheme={selectedScheme}
          componentFeatureId={componentFeatureId}
          cb={() => {
            setIsAddConditionOpen(false);
            setSelectedScheme(null);
            schemesQuery.refetch();
          }}
          cancelCb={() => {
            setSelectedScheme(null);
            setIsAddConditionOpen(false);
          }}
        />
      )}

      {isEditConditionOpen && (
        <ExchangeTabConditionEditNameModal
          open={isEditConditionOpen}
          componentFeatureId={componentFeatureId}
          cb={() => {
            schemesQuery.refetch();
            onCancelEditScheme();
          }}
          form={formEditScheme}
        />
      )}

      {contextHolder}
    </>
  );
};

export default ExchangeTab;
