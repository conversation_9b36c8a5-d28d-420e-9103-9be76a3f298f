.text-rest {
  color: var(--Text-hint, #8c8c8d);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}

.ant-menu-light .ant-menu-submenu-selected > .ant-menu-submenu-title {
  color: #000000;
  font-weight: 600;
}

button.ant-btn {
  height: 40px !important;
}

li.ant-picker-ok button.ant-btn {
  height: auto !important;
}

table {
  border: 1px solid #ecedef;
  box-shadow: 0 4px 16px 8px rgba(122, 122, 122, 0.01);
  border-radius: 4px !important;
}

div.ant-modal-confirm-paragraph > span.ant-modal-confirm-title {
  margin-bottom: 24px;
}

div.ant-modal-confirm-btns {
  margin-top: 40px !important;
}

.ant-modal-body h2 {
  margin-bottom: 15px;
}

.ant-upload .ant-upload-select {
  border: none;
}

table div.ant-input-number-input-wrap > input.ant-input-number-input {
  text-align: right;
}
