import { DEFAULT_CURRENT_PAGE, DEFAULT_PAGE_SIZE } from "@/common/constant.ts";
import CustomModal from "@/components/CustomModal.tsx";
import FilterClassicComponent from "@/components/FilterClassicComponent.tsx";
import OutletSearchFilterContent from "@/components/outletSearchFilterContent/OutletSearchFilterContent.tsx";
import renderStatusOnTopCell from "@/components/renderStatusOnTopCell.tsx";
import { SubChannelInterface } from "@/routes/subChannel/interface.ts";
import { useApp } from "@/UseApp.tsx";
import { ProjectAgencyChannelInterface } from "@project/interface.ts";
import { Form, Table } from "antd";
import _ from "lodash";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import {
  FeatureSamplingGroupInterface,
  FeatureSamplingOutletAvailabilityInterface,
} from "../../interface.ts";
import {
  useCreateSamplingGroupOutletMutation,
  useSamplingGroupOutletAvailablesQuery,
} from "../../service.ts";

interface AddOutletSamplingGroupModalProps {
  isOpen: boolean;
  projectId: number;
  componentFeatureId: number;
  samplingGroup?: FeatureSamplingGroupInterface;
  onCancelCb: () => void;
}

const AddOutletSamplingGroupModal = ({
  isOpen,
  projectId,
  componentFeatureId,
  samplingGroup,
  onCancelCb,
}: AddOutletSamplingGroupModalProps) => {
  const { showNotification } = useApp();

  const [searchForm] = Form.useForm();

  const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [selectedOutletKeys, setSelectedOutletKeys] = useState<React.Key[]>([]);
  const [filter, setFilter] = useState({});

  const schemeSamplingOutletAvailableQuery =
    useSamplingGroupOutletAvailablesQuery(
      componentFeatureId,
      { ...filter, take: pageSize, skip: (currentPage - 1) * pageSize },
      isOpen,
    );

  const createSamplingGroupOutletMutation =
    useCreateSamplingGroupOutletMutation(componentFeatureId);

  const searchHandler = useCallback(async () => {
    setCurrentPage(DEFAULT_CURRENT_PAGE);
    if (_.isEqual(searchForm.getFieldsValue(), filter)) {
      await schemeSamplingOutletAvailableQuery.refetch();
    }

    setFilter(searchForm.getFieldsValue());
  }, [filter, schemeSamplingOutletAvailableQuery, searchForm]);

  const pagination = useMemo(() => {
    return {
      current: currentPage,
      total: schemeSamplingOutletAvailableQuery.data?.count,
      pageSize: pageSize,
      onChange: (page: number) => {
        setCurrentPage(page);
      },
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: (_current: number, size: number) => {
        setPageSize(size);
        setCurrentPage(DEFAULT_CURRENT_PAGE);
      },
    };
  }, [currentPage, schemeSamplingOutletAvailableQuery.data?.count, pageSize]);

  const rowSelection = {
    selectedRowKeys: selectedOutletKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedOutletKeys(newSelectedRowKeys);
    },
    getCheckboxProps: (record: FeatureSamplingOutletAvailabilityInterface) => {
      return {
        disabled: !record.isAvailable || !record.isActive,
      };
    },
  };

  const content = (
    <>
      <p>
        Nhóm sampling đang phân bổ outlet:{" "}
        <span className={"text-primary font-semibold"}>
          {samplingGroup?.name}
        </span>
      </p>

      <FilterClassicComponent
        searchHandler={searchHandler}
        searchForm={searchForm}
        content={
          <OutletSearchFilterContent
            open={isOpen}
            searchForm={searchForm}
            projectId={projectId}
          />
        }
        className={"mb-5"}
      />

      <Table
        rowKey={(o) => o.id}
        dataSource={schemeSamplingOutletAvailableQuery.data?.entities}
        pagination={pagination}
        rowSelection={rowSelection}
        columns={[
          {
            title: "Mã outlet",
            dataIndex: "code",
          },
          {
            title: "Tên outlet",
            dataIndex: "name",
            render: (
              name: string,
              record: FeatureSamplingOutletAvailabilityInterface,
            ) => {
              const { isActive, featureSamplingGroupOutlets, isAvailable } =
                record;
              let isInCurrentScheme = false;

              if (
                featureSamplingGroupOutlets.length > 0 &&
                featureSamplingGroupOutlets[0].featureSamplingGroup.id ===
                  samplingGroup?.id
              ) {
                isInCurrentScheme = true;
              }

              return (
                <>
                  {isInCurrentScheme &&
                    renderStatusOnTopCell(
                      "Đã thêm vào nhóm hiện tại",
                      "#008916",
                      "#E5F5E7",
                    )}

                  {!isAvailable &&
                    !isInCurrentScheme &&
                    renderStatusOnTopCell(
                      "Đã thêm vào nhóm khác",
                      "#393939",
                      "#F5F5F5",
                    )}

                  {!isActive &&
                    renderStatusOnTopCell(
                      "Ngừng hoạt động",
                      "#DF3C3C",
                      "#FFEEEE",
                    )}
                  <p>{name}</p>
                </>
              );
            },
          },
          {
            title: "Số nhà",
            dataIndex: "houseNumber",
          },
          {
            title: "Tên đường",
            dataIndex: "streetName",
          },
          {
            title: "Tỉnh/ TP",
            dataIndex: "province",
            render: (province) => province?.name,
          },
          {
            title: "Quận/ Huyện",
            dataIndex: "district",
            render: (district) => district?.name,
          },
          {
            title: "Phường/ Xã",
            dataIndex: "ward",
            render: (ward) => ward?.name,
          },
          {
            title: "Kênh",
            dataIndex: "projectAgencyChannel",
            render: (projectAgencyChannel: ProjectAgencyChannelInterface) =>
              projectAgencyChannel?.channel.name,
          },
          {
            title: "Nhóm",
            dataIndex: "subChannel",
            render: (subChannel: SubChannelInterface) => subChannel?.name,
          },
          {
            title: "Tên nhóm sampling",
            render: (_, record: FeatureSamplingOutletAvailabilityInterface) => {
              const { featureSamplingGroupOutlets } = record;
              if (featureSamplingGroupOutlets.length > 0) {
                return featureSamplingGroupOutlets[0].featureSamplingGroup.name;
              }
              return "";
            },
          },
        ]}
      />
    </>
  );

  const newAvailableSelectedOutletKeys = useMemo(() => {
    return (
      schemeSamplingOutletAvailableQuery.data?.entities
        .filter(
          (projectOutlet) =>
            selectedOutletKeys.includes(projectOutlet.id) &&
            projectOutlet.isAvailable,
        )
        .map((item) => item.id) ?? []
    );
  }, [selectedOutletKeys, schemeSamplingOutletAvailableQuery.data?.entities]);

  const onCancel = useCallback(async () => {
    setSelectedOutletKeys([]);
    searchForm.resetFields();
    setFilter({});
    onCancelCb();
  }, [onCancelCb, searchForm]);

  const onConfirm = useCallback(async () => {
    if (samplingGroup) {
      await createSamplingGroupOutletMutation.mutateAsync({
        id: samplingGroup?.id,
        projectOutletIds: newAvailableSelectedOutletKeys,
      });

      showNotification({
        message: "Thêm outlet vào scheme thành công",
        type: "success",
      });
      onCancel();
    }
  }, [
    createSamplingGroupOutletMutation,
    newAvailableSelectedOutletKeys,
    onCancel,
    samplingGroup,
    showNotification,
  ]);

  useEffect(() => {
    setSelectedOutletKeys(
      schemeSamplingOutletAvailableQuery.data?.entities
        .filter(
          (projectOutlet) =>
            projectOutlet.featureSamplingGroupOutlets?.[0]?.featureSamplingGroup
              ?.id === samplingGroup?.id,
        )
        .map((item) => item.id) ?? [],
    );
  }, [samplingGroup?.id, schemeSamplingOutletAvailableQuery.data?.entities]);

  return (
    <CustomModal
      title={"Thêm outlet vào nhóm sampling"}
      isOpen={isOpen}
      content={content}
      width={1200}
      confirmText={`Thêm ${newAvailableSelectedOutletKeys.length} outlet vào nhóm`}
      confirmDisable={newAvailableSelectedOutletKeys.length === 0}
      onConfirm={onConfirm}
      onCancel={onCancel}
    />
  );
};

export default AddOutletSamplingGroupModal;
