import { useApp } from "@/UseApp";
import { CURD, SELECT_ALL, SELECT_ALL_LABEL } from "@/common/constant";
import { formErrorResponseHandler } from "@/common/helper";
import DragSortRowComponent from "@/components/DragSortRowComponent";
import ModalCURD from "@/components/ModalCURD";
import TableActionCell from "@/components/TableActionCell";
import { renderTableCell } from "@/components/table-cell";
import { useUrlFilters } from "@/hooks/useUrlFilters";
import {
  ArrowRightOutlined,
  DeleteOutlined,
  EditOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { DndContext, DragEndEvent } from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
  arrayMove,
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import {
  Button,
  Checkbox,
  Col,
  Form,
  Input,
  InputNumber,
  Modal,
  Radio,
  Row,
  Select,
  Space,
  Table,
} from "antd";
import TextArea from "antd/es/input/TextArea";
import { ColumnsType } from "antd/es/table";
import { AxiosError } from "axios";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import {
  MultiSubjectMultimediaInformationCapturingActionEnum,
  MultiSubjectMultimediaInformationCapturingInterface,
  MultiSubjectMultimediaInformationCapturingType,
} from "./interface";
import {
  useArrangementMultiSubjectMultimediaInformationCapturingMutation,
  useCreateMultiSubjectMultimediaInformationCapturingMutation,
  useDeleteMultiSubjectMultimediaInformationCapturingMutation,
  useMultiSubjectMultimediaInformationCapturingsQuery,
  useUpdateIsActiveMultiSubjectMultimediaInformationCapturingMutation,
  useUpdateMultiSubjectMultimediaInformationCapturingMutation,
} from "./service";

/**
 * Ghi nhận thông tin dạng danh sách
 *
 * @return {JSX.Element} The rendered page component
 */
export default function MultiSubjectMultimediaInformationCapturingPage() {
  const componentFeatureId = parseInt(useParams().componentFeatureId ?? "0");
  const { showNotification } = useApp();

  const [searchForm] = Form.useForm();
  const [form] = Form.useForm();
  const [isOpen, setIsOpen] = useState(false);
  const [action, setAction] = useState<CURD | null>(null);
  const [modal, contextHolder] = Modal.useModal();
  const { filter, handleSearch } = useUrlFilters({
    formInstance: searchForm,
    handleSearchCallback: () => {
      multiSubjectMultimediaInformationCapturingsQuery.refetch();
    },
  });

  const multiSubjectMultimediaInformationCapturingsQuery =
    useMultiSubjectMultimediaInformationCapturingsQuery(componentFeatureId, {
      take: 0,
      skip: 0,
      ...filter,
    });

  const [dataSource, setDataSource] = useState(
    multiSubjectMultimediaInformationCapturingsQuery.data?.entities ?? [],
  );

  const createMultiSubjectMultimediaInformationCapturingMutation =
    useCreateMultiSubjectMultimediaInformationCapturingMutation(
      componentFeatureId,
    );
  const updateMultiSubjectMultimediaInformationCapturingMutation =
    useUpdateMultiSubjectMultimediaInformationCapturingMutation(
      componentFeatureId,
    );
  const updateIsActiveMultiSubjectMultimediaInformationCapturingMutation =
    useUpdateIsActiveMultiSubjectMultimediaInformationCapturingMutation(
      componentFeatureId,
    );
  const deleteMultiSubjectMultimediaInformationCapturingMutation =
    useDeleteMultiSubjectMultimediaInformationCapturingMutation(
      componentFeatureId,
    );
  const arrangementMultiSubjectMultimediaInformationCapturingMutation =
    useArrangementMultiSubjectMultimediaInformationCapturingMutation(
      componentFeatureId,
    );

  const filterOptions = useMemo(() => {
    return [
      {
        label: SELECT_ALL_LABEL,
        value: SELECT_ALL,
      },
      {
        label: "Thông tin cần ghi nhận",
        value: "title",
      },
    ];
  }, []);

  const formContent = (
    <>
      <Form.Item name="id" hidden>
        <Input />
      </Form.Item>

      <Form.Item
        name="title"
        label="Tên thông tin cần ghi nhận"
        rules={[
          {
            required: true,
            message: "Tên thông tin cần ghi nhận không được để trống",
          },
          {
            max: 64,
            message: "Tên thông tin cần ghi nhận không được vượt quá 64 ký tự",
          },
        ]}
        className="mb-0 pb-0"
      >
        <Input />
      </Form.Item>
      <Form.Item name={"isTextFieldRequired"} valuePropName="checked">
        <Checkbox disabled={action === CURD.UPDATE}>Bắt buộc nhập</Checkbox>
      </Form.Item>
      <Form.Item label="Yêu cầu chụp hình" name={"type"}>
        <Radio.Group disabled={action === CURD.UPDATE}>
          <Space direction="vertical">
            <Radio
              value={MultiSubjectMultimediaInformationCapturingType.MIN_MAX}
            >
              Chụp trong khoảng số lượng hình yêu cầu
            </Radio>
            <Radio value={MultiSubjectMultimediaInformationCapturingType.FULL}>
              Chụp đủ số lượng hình yêu cầu
            </Radio>
            <Radio value={MultiSubjectMultimediaInformationCapturingType.NONE}>
              Không cần chụp hình
            </Radio>
          </Space>
        </Radio.Group>
      </Form.Item>
      <Form.Item noStyle dependencies={["type"]}>
        {() => (
          <>
            {form.getFieldValue("type") ===
              MultiSubjectMultimediaInformationCapturingType.MIN_MAX && (
              <Row justify={"space-between"}>
                <Col md={11}>
                  <Form.Item
                    label="Số lượng hình tối thiểu"
                    name="minimumImages"
                    rules={[
                      {
                        required: true,
                        message: "Số lượng hình tối thiểu không được bỏ trống",
                      },
                    ]}
                  >
                    <InputNumber
                      disabled={action === CURD.UPDATE}
                      controls={false}
                      step={0}
                      style={{
                        width: "100%",
                      }}
                      min={0}
                      max={19}
                    />
                  </Form.Item>
                </Col>

                <Col md={11}>
                  <Form.Item
                    label="Số lượng hình tối đa"
                    name="maximumImages"
                    dependencies={["minimumImages"]}
                    rules={[
                      {
                        required: true,
                        message: "Số lượng hình tối đa không được bỏ trống",
                      },
                      ({ getFieldValue }) => ({
                        validator(_, value) {
                          if (
                            !value ||
                            getFieldValue("minimumImages") < value
                          ) {
                            return Promise.resolve();
                          }
                          return Promise.reject(
                            new Error(
                              "Số lần hình tối đa phải lớn hơn số lượng hình tối thiểu",
                            ),
                          );
                        },
                      }),
                    ]}
                  >
                    <InputNumber
                      disabled={action === CURD.UPDATE}
                      controls={false}
                      step={0}
                      style={{ width: "100%" }}
                      min={1}
                      max={20}
                    />
                  </Form.Item>
                </Col>
              </Row>
            )}

            {form.getFieldValue("type") ===
              MultiSubjectMultimediaInformationCapturingType.FULL && (
              <Form.Item
                label="Số lượng hình yêu cầu"
                name="full"
                rules={[
                  {
                    required: true,
                    message: "Số lượng hình yêu cầu không được bỏ trống",
                  },
                ]}
              >
                <InputNumber
                  disabled={action === CURD.UPDATE}
                  controls={false}
                  step={0}
                  style={{
                    width: "100%",
                  }}
                  min={1}
                  max={20}
                />
              </Form.Item>
            )}

            {form.getFieldValue("type") !==
              MultiSubjectMultimediaInformationCapturingType.NONE && (
              <>
                <Form.Item
                  name="description"
                  label={"Mô tả hướng dẫn nhân viên chụp hình đúng yêu cầu"}
                  rules={[
                    {
                      max: 128,
                      message: "Mô tả không được vượt quá 128 ký tự",
                    },
                  ]}
                >
                  <TextArea showCount />
                </Form.Item>

                <Form.Item name="isWatermarkRequired" valuePropName="checked">
                  <Checkbox disabled={action === CURD.UPDATE}>
                    Ghi timestamp lên ảnh chụp
                  </Checkbox>
                </Form.Item>
              </>
            )}
          </>
        )}
      </Form.Item>
    </>
  );

  const formFinishHandler = useCallback(async () => {
    try {
      const data = {
        description: form.getFieldValue("description") as string | undefined,
        title: form.getFieldValue("title" as string),
        minimumImages: form.getFieldValue("minimumImages") as number,
        maximumImages: form.getFieldValue("maximumImages") as number,
        isWatermarkRequired: form.getFieldValue(
          "isWatermarkRequired",
        ) as boolean,
        isTextFieldRequired: form.getFieldValue(
          "isTextFieldRequired",
        ) as boolean,
        id: 0,
      };

      if (
        form.getFieldValue("type") ===
        MultiSubjectMultimediaInformationCapturingType.FULL
      ) {
        data.minimumImages = form.getFieldValue("full");
        data.maximumImages = form.getFieldValue("full");
      }

      if (
        form.getFieldValue("type") ===
        MultiSubjectMultimediaInformationCapturingType.NONE
      ) {
        data.minimumImages = 0;
        data.maximumImages = 0;
        data.isWatermarkRequired = false;
      }

      if (action === CURD.CREATE) {
        await createMultiSubjectMultimediaInformationCapturingMutation.mutateAsync(
          data,
        );
        showNotification({
          type: "success",
          message: "Thêm thông tin cần ghi nhận thành công.",
        });
      }

      if (action === CURD.UPDATE) {
        data.id = form.getFieldValue("id");
        await updateMultiSubjectMultimediaInformationCapturingMutation.mutateAsync(
          data,
        );
        showNotification({
          type: "success",
          message: "Thêm loại hình thành công",
        });
      }

      form.resetFields();
      setIsOpen(false);
      multiSubjectMultimediaInformationCapturingsQuery.refetch();
      setAction(null);
    } catch (error) {
      formErrorResponseHandler(form, error as AxiosError);
    }
  }, [
    action,
    createMultiSubjectMultimediaInformationCapturingMutation,
    form,
    multiSubjectMultimediaInformationCapturingsQuery,
    showNotification,
    updateMultiSubjectMultimediaInformationCapturingMutation,
  ]);

  const handleActionEditClick = useCallback(
    (record: MultiSubjectMultimediaInformationCapturingInterface) => {
      let type = null;
      if (record.minimumImages === 0 && record.maximumImages === 0) {
        type = MultiSubjectMultimediaInformationCapturingType.NONE;
      } else if (record.minimumImages === record.maximumImages) {
        type = MultiSubjectMultimediaInformationCapturingType.FULL;
      } else {
        type = MultiSubjectMultimediaInformationCapturingType.MIN_MAX;
      }

      form.setFieldsValue({
        title: record.title,
        description: record.description,
        isWatermarkRequired: record.isWatermarkRequired,
        isTextFieldRequired: record.isTextFieldRequired,
        full: record.minimumImages,
        minimumImages: record.minimumImages,
        maximumImages: record.maximumImages,
        type,
        id: record.id,
      });

      setIsOpen(true);
      setAction(CURD.UPDATE);
    },
    [form],
  );

  const handleActionActiveClick = useCallback(
    (record: MultiSubjectMultimediaInformationCapturingInterface) => {
      modal.confirm({
        title: `Kích hoạt thông tin cần ghi nhận: ${record.title}`,
        content: `Bạn có chắc chắn muốn kích hoạt thông tin cần ghi nhận ${record.title} này?`,
        okText: "Kích hoạt",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await updateIsActiveMultiSubjectMultimediaInformationCapturingMutation.mutateAsync(
              {
                id: record.id,
                isActive: true,
              },
            );

            showNotification({
              type: "success",
              message: `Kích hoạt thông tin cần ghi nhận ${record.title} thành công`,
            });

            multiSubjectMultimediaInformationCapturingsQuery.refetch();
          } catch (error) {
            console.error(error);

            showNotification({
              type: "error",
              message: `Kích hoạt thông tin cần ghi nhận ${record.title} thất bại`,
            });
          }
        },
      });
    },
    [
      modal,
      multiSubjectMultimediaInformationCapturingsQuery,
      showNotification,
      updateIsActiveMultiSubjectMultimediaInformationCapturingMutation,
    ],
  );

  const handleActionInactiveClick = useCallback(
    (record: MultiSubjectMultimediaInformationCapturingInterface) => {
      modal.confirm({
        title: `Ngừng hoạt động thông tin cần ghi nhận: ${record.title}`,
        content: `Bạn có chắc chắn muốn ngừng hoạt động thông tin cần ghi nhận ${record.title} này?`,
        okText: "Ngừng hoạt động",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await updateIsActiveMultiSubjectMultimediaInformationCapturingMutation.mutateAsync(
              {
                id: record.id,
                isActive: false,
              },
            );

            showNotification({
              type: "success",
              message: `Ngừng hoạt động thông tin cần ghi nhận ${record.title} thành công`,
            });

            multiSubjectMultimediaInformationCapturingsQuery.refetch();
          } catch (error) {
            console.error(error);

            showNotification({
              type: "error",
              message: `Ngừng hoạt động thông tin cần ghi nhận ${record.title} thất bại`,
            });
          }
        },
      });
    },
    [
      modal,
      multiSubjectMultimediaInformationCapturingsQuery,
      showNotification,
      updateIsActiveMultiSubjectMultimediaInformationCapturingMutation,
    ],
  );

  const handleActionDeleteClick = useCallback(
    (record: MultiSubjectMultimediaInformationCapturingInterface) => {
      modal.confirm({
        title: `Xóa thông tin cần ghi nhận: ${record.title}`,
        content: `Bạn có chắc chắn muốn xóa thông tin cần ghi nhận ${record.title} này?`,
        okText: "Xóa",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await deleteMultiSubjectMultimediaInformationCapturingMutation.mutateAsync(
              record.id,
            );

            showNotification({
              type: "success",
              message: `Xóa thông tin cần ghi nhận ${record.title} thành công`,
            });

            multiSubjectMultimediaInformationCapturingsQuery.refetch();
          } catch (error) {
            console.error(error);

            showNotification({
              type: "error",
              message: `Xóa thông tin cần ghi nhận ${record.title} thất bại`,
            });
          }
        },
      });
    },
    [
      deleteMultiSubjectMultimediaInformationCapturingMutation,
      modal,
      multiSubjectMultimediaInformationCapturingsQuery,
      showNotification,
    ],
  );

  const columns: ColumnsType<MultiSubjectMultimediaInformationCapturingInterface> =
    useMemo(() => {
      const actionItems = [
        {
          key: MultiSubjectMultimediaInformationCapturingActionEnum.EDIT,
          label: "Chỉnh sửa",
          icon: <EditOutlined />,
        },
        {
          key: MultiSubjectMultimediaInformationCapturingActionEnum.INACTIVE,
          label: "Ngừng hoạt động",
          icon: <PauseCircleOutlined />,
        },
        {
          key: MultiSubjectMultimediaInformationCapturingActionEnum.ACTIVE,
          label: "Kích hoạt",
          icon: <PlayCircleOutlined />,
        },
        {
          key: MultiSubjectMultimediaInformationCapturingActionEnum.DELETE,
          label: "Xóa khỏi chức năng",
          icon: <DeleteOutlined />,
        },
      ];

      const actionActions = [
        {
          key: MultiSubjectMultimediaInformationCapturingActionEnum.EDIT,
          action: handleActionEditClick,
        },
        {
          key: MultiSubjectMultimediaInformationCapturingActionEnum.ACTIVE,
          action: handleActionActiveClick,
        },
        {
          key: MultiSubjectMultimediaInformationCapturingActionEnum.INACTIVE,
          action: handleActionInactiveClick,
        },
        {
          key: MultiSubjectMultimediaInformationCapturingActionEnum.DELETE,
          action: handleActionDeleteClick,
        },
      ];

      const ACTION_ACTIVE = [
        MultiSubjectMultimediaInformationCapturingActionEnum.INACTIVE,
        MultiSubjectMultimediaInformationCapturingActionEnum.DELETE,
        MultiSubjectMultimediaInformationCapturingActionEnum.EDIT,
      ];

      const ACTION_INACTIVE = [
        MultiSubjectMultimediaInformationCapturingActionEnum.ACTIVE,
        MultiSubjectMultimediaInformationCapturingActionEnum.DELETE,
        MultiSubjectMultimediaInformationCapturingActionEnum.EDIT,
      ];

      return [
        {
          key: "sort",
        },
        {
          title: "Thông tin cần ghi nhận",
          dataIndex: "title",
        },
        {
          title: "Ghi chú",
          render(
            _,
            record: MultiSubjectMultimediaInformationCapturingInterface,
          ) {
            if (record.isTextFieldRequired) {
              return (
                <p className="text-main-color bg-[#FFEEEE] rounded-3xl inline-block text-sm p-0 m-0 pl-2 pr-2">
                  Bắt buộc nhập
                </p>
              );
            }
            return (
              <p className="bg-[#F5F5F5] rounded-3xl inline-block text-sm p-0 m-0 pl-2 pr-2">
                Không bắt buộc nhập
              </p>
            );
          },
        },
        {
          title: "Số lượng hình cần chụp",
          render: (
            _,
            record: MultiSubjectMultimediaInformationCapturingInterface,
          ) => {
            if (
              record.minimumImages === record.maximumImages &&
              record.minimumImages === 0
            ) {
              return (
                <span className="font-semibold">Không yêu cầu chụp hình</span>
              );
            }

            if (record.minimumImages === record.maximumImages) {
              return (
                <>
                  <span className="font-semibold">Chụp đủ số lượng:</span>{" "}
                  {record.minimumImages}
                </>
              );
            }
            return (
              <>
                <span className="font-semibold">Chụp tối thiểu:</span>{" "}
                {record.minimumImages} hình{" "}
                <ArrowRightOutlined className="pl-3 pr-3" />
                <span className="font-semibold">Chụp tối đa:</span>{" "}
                {record.maximumImages} hình
              </>
            );
          },
        },
        {
          title: "Timestamp trên ảnh chụp",
          dataIndex: "isWatermarkRequired",
          render: (isWatermarkRequired: boolean) =>
            isWatermarkRequired ? (
              <span>Ghi timestamp lên ảnh chụp</span>
            ) : (
              <span>Không có</span>
            ),
        },
        {
          title: "Tình trạng",
          dataIndex: "isActive",
          render: (value, record, index) => {
            return renderTableCell(value, record, index, "isActive");
          },
        },
        {
          key: "actions",
          render: (_, record) => {
            const actionKeys = record.isActive
              ? ACTION_ACTIVE
              : ACTION_INACTIVE;
            const items = actionItems.filter((item) =>
              actionKeys.includes(item.key),
            );
            return (
              <TableActionCell
                actions={actionActions}
                items={items}
                record={record}
              />
            );
          },
          width: 100,
        },
      ];
    }, [
      handleActionActiveClick,
      handleActionDeleteClick,
      handleActionEditClick,
      handleActionInactiveClick,
    ]);

  const onDragEnd = useCallback(
    async ({ active, over }: DragEndEvent) => {
      if (!over) {
        return;
      }
      if (active.id !== over?.id) {
        setDataSource((previous) => {
          const activeIndex = previous?.findIndex((i) => i.id === active.id);
          const overIndex = previous?.findIndex((i) => i.id === over?.id);
          return arrayMove(previous, activeIndex, overIndex);
        });

        await arrangementMultiSubjectMultimediaInformationCapturingMutation.mutateAsync(
          {
            activeId: active.id as number,
            overId: over?.id as number,
          },
        );
      }
    },
    [arrangementMultiSubjectMultimediaInformationCapturingMutation],
  );

  useEffect(() => {
    setDataSource(
      (
        multiSubjectMultimediaInformationCapturingsQuery.data?.entities ?? []
      ).sort((a, b) => a.ordinal - b.ordinal),
    );
  }, [multiSubjectMultimediaInformationCapturingsQuery.data]);

  return (
    <>
      <div className="bg-white pt-10 pl-10 pr-10 rounded pb-6">
        <Row justify={"space-between"} className={"mb-6"}>
          <Col>
            <Form
              layout="inline"
              form={searchForm}
              onFinish={handleSearch}
              initialValues={{ filterField: SELECT_ALL }}
            >
              <Space.Compact>
                <Form.Item
                  name="filterField"
                  style={{ marginInlineEnd: "0px", borderRight: "none" }}
                >
                  <Select style={{ width: "150px" }} options={filterOptions} />
                </Form.Item>
                <Form.Item name="filterValue">
                  <Input
                    prefix={<SearchOutlined />}
                    placeholder={"Nhập nội dung cần tìm"}
                    allowClear
                  />
                </Form.Item>
              </Space.Compact>

              <Form.Item>
                <Button htmlType="submit" type="default">
                  Tìm kiếm
                </Button>
              </Form.Item>
            </Form>
          </Col>

          <Col>
            <Button
              icon={<PlusOutlined />}
              type="primary"
              onClick={() => {
                setIsOpen(true);
                setAction(CURD.CREATE);
                form.setFieldsValue({
                  type: MultiSubjectMultimediaInformationCapturingType.MIN_MAX,
                  isWatermarkRequired: true,
                });
              }}
            >
              Thêm mới
            </Button>
          </Col>
        </Row>

        <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
          <SortableContext
            items={dataSource.map((i) => i.id)}
            strategy={verticalListSortingStrategy}
          >
            <Table
              rowKey={"id"}
              columns={columns}
              components={{
                body: {
                  row: DragSortRowComponent,
                },
              }}
              dataSource={dataSource}
              pagination={false}
              loading={
                multiSubjectMultimediaInformationCapturingsQuery.isFetching ||
                arrangementMultiSubjectMultimediaInformationCapturingMutation.isPending
              }
            />
          </SortableContext>
        </DndContext>
      </div>

      <ModalCURD
        title={
          action === CURD.CREATE
            ? "Thêm thông tin cần ghi nhận"
            : "Thông tin ghi nhận"
        }
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        formContent={formContent}
        form={form}
        onFinish={formFinishHandler}
        action={action}
        btnConfirmLoading={
          createMultiSubjectMultimediaInformationCapturingMutation.isPending ||
          updateMultiSubjectMultimediaInformationCapturingMutation.isPending
        }
      />

      {contextHolder}
    </>
  );
}
