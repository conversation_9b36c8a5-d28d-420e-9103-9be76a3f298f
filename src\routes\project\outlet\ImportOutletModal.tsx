import { createFileAndDownLoad } from "@/common/export-excel.helper";
import {
  getErrorMessageFromAxiosError,
  validateAndClearVietnamPhoneNumber,
} from "@/common/helper";
import {
  setExcelError,
  validateExcelData,
} from "@/common/import-excel.helper.ts";
import { ImportStatus } from "@/common/interface";
import { HOME_PAGE } from "@/common/url.helper";
import { useApp } from "@/UseApp";
import {
  CheckCircleFilled,
  CheckCircleTwoTone,
  CloseCircleTwoTone,
  CloseOutlined,
  DownloadOutlined,
  InboxOutlined,
  LoadingOutlined,
} from "@ant-design/icons";
import {
  DistrictInterface,
  ProvinceInterface,
  WardInterface,
} from "@location/interface";
import {
  findDistrictIdByName,
  findProvinceIdByName,
  findWardIdByName,
} from "@location/service";
import { <PERSON><PERSON>, Button, Form, Modal, Space, Table } from "antd";
import Dragger from "antd/es/upload/Dragger";
import { UploadFile } from "antd/lib";
import { AxiosError } from "axios";
import dayjs from "dayjs";
import Excel from "exceljs";
import Joi from "joi";
import React, { useCallback, useRef, useState } from "react";
import { Importer } from "xlsx-import/lib/Importer";
import { ChannelInterface } from "../../channel/interface";
import { SubChannelInterface } from "../../subChannel/interface";
import { ProjectEmployeeUserInterface } from "../employee/interface";
import { findLeaderIdByPhoneByProjectAgencyId } from "../employee/service";
import { ProjectAgencyInterface } from "../interface";
import {
  HAS_OVERNIGHT_SHIFT_FALSE_LABEL,
  HAS_OVERNIGHT_SHIFT_TRUE_LABEL,
} from "./interface";
import {
  findChannelByName,
  findChannelProjectAgencyByName,
  findSubChannelIdByName,
  useCreateOutletMutation,
} from "./service";

export interface OutletImportDataInterface {
  code: string;
  name: string;
  houseNumber: string;
  streetName: string;
  province: string;
  district: string;
  ward: string;
  channel: string;
  subChannel: string;
  agency: string;
  leaderPhone: string;
  hasOvernightShiftText: string;
  id: number;
  status?: ImportStatus;
  errorMessage?: string;
  leaderId?: number;
  provinceId?: number;
  districtId?: number;
  wardId?: number;
  projectAgencyChannelId?: number;
  subChannelId?: number;
  projectAgencyId?: number;
  hasOvernightShift?: boolean;
}

const outletImportDataSchema = Joi.object({
  code: Joi.string().trim().allow("").optional(),
  name: Joi.string().trim().required(),
  houseNumber: Joi.string().trim().allow("").optional(),
  streetName: Joi.string().trim().allow("").optional(),
  province: Joi.string().trim().required(),
  district: Joi.string().trim().required(),
  ward: Joi.string().trim().optional().allow(""),
  channel: Joi.string().trim().required(),
  subChannel: Joi.string().trim().allow("").optional(),
  agency: Joi.string().trim().required(),
  leaderPhone: Joi.string().trim().allow("").optional(),
  id: Joi.number().required(),
  latitude: Joi.string().trim().allow("").optional(),
  longitude: Joi.string().trim().allow("").optional(),
  status: Joi.string()
    .valid("pending", "success", "error")
    .allow("")
    .optional(),
  errorMessage: Joi.string().allow("").optional(),
  leaderId: Joi.number().allow("").optional(),
  hasOvernightShiftText: Joi.string()
    .valid(HAS_OVERNIGHT_SHIFT_TRUE_LABEL, HAS_OVERNIGHT_SHIFT_FALSE_LABEL)
    .required(),
});

const headers = [
  { index: 1, key: "code", header: "Mã outlet" },
  { index: 2, key: "name", header: "Tên outlet" },
  { index: 3, key: "houseNumber", header: "Số nhà" },
  { index: 4, key: "streetName", header: "Tên đường" },
  { index: 5, key: "province", header: "Tỉnh/ TP" },
  { index: 6, key: "district", header: "Quận/ Huyện" },
  { index: 7, key: "ward", header: "Phường/ Xã" },
  { index: 8, key: "hasOvernightShiftText", header: "Loại ca làm việc" },
  { index: 9, key: "channel", header: "Kênh" },
  { index: 10, key: "subChannel", header: "Nhóm" },
  { index: 11, key: "agency", header: "Agency phụ trách" },
  {
    index: 12,
    key: "leaderPhone",
    header: "SĐT trưởng nhóm quản lý outlet này",
  },
  { index: 13, key: "latitude", header: "Latitude" },
  { index: 14, key: "longitude", header: "Longitude" },
];

export default function ImportOutletModal({
  isOpen,
  setIsOpen,
  projectId,
  cb,
}: Readonly<{
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  projectId: number;
  cb: () => void;
}>) {
  const { axiosGet } = useApp();

  const [uploadForm] = Form.useForm();
  const [isDataVisible, setIsDataVisible] = useState(false);
  const [importData, setImportData] = useState<OutletImportDataInterface[]>([]);
  const [displayData, setDisplayData] = useState<OutletImportDataInterface[]>(
    [],
  );
  const [successfulImports, setSuccessfulImports] = useState<
    OutletImportDataInterface[]
  >([]);
  const [failedImports, setFailedImports] = useState<
    OutletImportDataInterface[]
  >([]);
  const [isImporting, setIsImporting] = useState(false);
  const tableRef: Parameters<typeof Table>[0]["ref"] = React.useRef(null);
  const provinces = useRef<ProvinceInterface[]>([]);
  const districts = useRef<DistrictInterface[]>([]);
  const wards = useRef<WardInterface[]>([]);
  const channels = useRef<ChannelInterface[]>([]);
  const subChannels = useRef<SubChannelInterface[]>([]);
  const leaders = useRef<ProjectEmployeeUserInterface[]>([]);
  const channelProjectAgencies = useRef<
    { channel: ChannelInterface; projectAgency: ProjectAgencyInterface }[]
  >([]);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [isFileError, setIsFileError] = useState(false);
  const [excelErrorMessage, setExcelErrorMessage] = useState<string>("");

  const createOutletMutation = useCreateOutletMutation(projectId);

  const processExcelFile = useCallback(async (file: File) => {
    const buffer = await file.arrayBuffer();
    const workbook = new Excel.Workbook();
    await workbook.xlsx.load(buffer);

    const config = {
      worksheet: "Data",
      type: "list",
      rowOffset: 0,
      columns: headers,
    };

    const importer = new Importer(workbook);
    const data = importer.getAllItems<OutletImportDataInterface>(config);

    validateExcelData(data, headers);

    setDisplayData(data.map((item, index) => ({ ...item, id: index })));
    setImportData(data.map((item, index) => ({ ...item, id: index })));
  }, []);

  const beforeUploadHandler = useCallback(
    (file: File) => {
      if (file) {
        processExcelFile(file).catch(({ message }: { message: string }) => {
          setExcelError(
            message,
            setFileList,
            setIsFileError,
            setExcelErrorMessage,
          );
        });
      }

      return false;
    },
    [processExcelFile],
  );

  const importHandler = useCallback(async () => {
    const validateLocation = async (value: OutletImportDataInterface) => {
      const { province, district, ward } = value;
      const provinceId = await findProvinceIdByName(
        axiosGet,
        provinces,
        province,
      );
      if (!provinceId) {
        throw new Error("Tỉnh/Thành phố không tồn tại");
      }
      value.provinceId = provinceId;

      const districtId = await findDistrictIdByName(
        axiosGet,
        districts,
        provinceId,
        district,
      );
      if (!districtId) {
        throw new Error("Quận/Huyện không tồn tại");
      }
      value.districtId = districtId;

      if (ward) {
        const wardId = await findWardIdByName(
          axiosGet,
          wards,
          districtId,
          ward,
        );
        if (!wardId) {
          throw new Error("Phường/Xã không tồn tại");
        }
        value.wardId = wardId;
      }

      return value;
    };

    const validateChannelSubChannelAgencyLeaderPhone = async (
      value: Promise<OutletImportDataInterface>,
    ) => {
      const item = await value;
      const { channel, subChannel, agency, leaderPhone } = item;

      const foundChannel = await findChannelByName(
        axiosGet,
        projectId,
        channels,
        channel,
      );
      if (!foundChannel) {
        throw new Error("Kênh không tồn tại");
      }

      const channelProjectAgency = await findChannelProjectAgencyByName(
        axiosGet,
        projectId,
        foundChannel,
        channelProjectAgencies,
        agency,
      );
      if (!channelProjectAgency) {
        throw new Error("Agency không tồn tại");
      }
      item.projectAgencyId = channelProjectAgency.projectAgency.id;

      item.projectAgencyChannelId =
        channelProjectAgency.projectAgency.projectAgencyChannels[0].id;

      if (subChannel) {
        const subChannelId = await findSubChannelIdByName(
          axiosGet,
          projectId,
          foundChannel.id,
          subChannels,
          subChannel,
        );
        if (!subChannelId) {
          throw new Error("Nhóm không tồn tại");
        }
        item.subChannelId = subChannelId;
      }

      if (leaderPhone) {
        const phoneValidateResult =
          validateAndClearVietnamPhoneNumber(leaderPhone);
        if (!phoneValidateResult.isValid) {
          throw new Error(
            "Số điện thoại trưởng nhóm quản lý outlet không hợp lệ",
          );
        }
        const leaderId = await findLeaderIdByPhoneByProjectAgencyId(
          axiosGet,
          projectId,
          leaders,
          phoneValidateResult.cleanedPhoneNumber,
          channelProjectAgency.projectAgency.id,
        );
        if (!leaderId) {
          throw new Error("Số điện thoại trưởng nhóm không tồn tại");
        }
        item.leaderId = leaderId;
      }

      return item;
    };

    const processItem = async (item: OutletImportDataInterface) => {
      const itemIndex = displayData.findIndex(
        (dataItem) => dataItem.id === item.id,
      );

      if (itemIndex === -1) {
        console.error(`Item with id ${item.id} not found in dataShow`);
        return;
      }
      displayData[itemIndex].status = "doing";
      setDisplayData([...displayData]);

      await outletImportDataSchema
        .custom(validateLocation)
        .custom(validateChannelSubChannelAgencyLeaderPhone)
        .validateAsync(item)
        .then(async (value) => {
          try {
            await createOutletMutation.mutateAsync({
              code: value.code,
              name: value.name,
              houseNumber: value.houseNumber,
              streetName: value.streetName,
              provinceId: value.provinceId,
              districtId: value.districtId,
              wardId: value.wardId,
              subChannelId: value.subChannelId,
              projectAgencyId: value.projectAgencyId,
              leaderId: value.leaderId,
              projectAgencyChannelId: value.projectAgencyChannelId,
              hasOvernightShift:
                value.hasOvernightShiftText === HAS_OVERNIGHT_SHIFT_TRUE_LABEL,
              latitude: value.latitude ? Number(value.latitude) : undefined,
              longitude: value.longitude ? Number(value.longitude) : undefined,
            });

            displayData[itemIndex].status = "success";
            setDisplayData([...displayData]);
            setSuccessfulImports((importSuccess) => [...importSuccess, item]);
          } catch (error) {
            if (error instanceof AxiosError) {
              displayData[itemIndex].status = "error";
              displayData[itemIndex].errorMessage =
                getErrorMessageFromAxiosError(error);
              setDisplayData([...displayData]);
              setFailedImports((importError) => [
                ...importError,
                displayData[itemIndex],
              ]);
            }
          }
        })
        .catch((error: Error) => {
          displayData[itemIndex].status = "error";
          displayData[itemIndex].errorMessage = error.message;
          setDisplayData([...displayData]);
          setFailedImports((importError) => [
            ...importError,
            displayData[itemIndex],
          ]);
        });
    };

    const processItems = async (items: OutletImportDataInterface[]) => {
      setIsImporting(true);
      for (const item of items) {
        await processItem(item);
      }
      setIsImporting(false);
    };

    await processItems(importData);
  }, [
    axiosGet,
    channels,
    createOutletMutation,
    displayData,
    districts,
    importData,
    leaders,
    channelProjectAgencies,
    projectId,
    provinces,
    subChannels,
    wards,
  ]);

  const handleImportErrorClick = useCallback(async () => {
    const fileName = `Import outlet loi `;
    const importErrorStrings = failedImports.map((item) => [
      ...headers.map(
        (header) => item[header.key as keyof typeof item]?.toString() ?? "",
      ),
      item.errorMessage ?? "",
    ]);

    const headerStrings = [...headers.map((header) => header.header), "Lỗi"];

    await createFileAndDownLoad({
      data: importErrorStrings,
      headers: headerStrings,
      fileName,
    });
  }, [failedImports]);

  const onModalShowDataClose = useCallback(() => {
    setIsDataVisible(false);
    setIsImporting(false);
    setImportData([]);
    setDisplayData([]);
    setSuccessfulImports([]);
    setFailedImports([]);
    setIsOpen(false);
    uploadForm.resetFields();
    setFileList([]);
    setIsFileError(false);
    cb();
  }, [setIsOpen, uploadForm, cb]);

  return (
    <>
      <Modal
        open={isOpen}
        closable={false}
        styles={{ content: { padding: 0 } }}
        footer={false}
      >
        <div className={"pl-10 pr-10"}>
          <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
            <h2 className="text-neutral-700 text-2xl font-semibold ">
              Import outlet
            </h2>
            <div className="pt-5">
              <Button
                type="link"
                onClick={onModalShowDataClose}
                size="large"
                icon={<CloseOutlined />}
              />
            </div>
          </div>
          <Alert
            message={
              <>
                <span>
                  Để thực hiện thêm mới outlet bằng cách import file excel bạn
                  cần sử dụng đúng template.{" "}
                </span>
                <Button
                  className="ml-0 pl-0 text-blue"
                  type="link"
                  href={`${HOME_PAGE}/excels/template-import-outlet.xlsx`}
                  download={`Import outlet ${dayjs().format("DDMMYY")}`}
                >
                  Tải file template
                </Button>
              </>
            }
            type="info"
            showIcon
          />
          <br />

          <Form form={uploadForm}>
            <Form.Item name={"upload"}>
              <Dragger
                className={"pt-5"}
                listType={"picture"}
                accept={".xlsx, .xlsm"}
                multiple={false}
                beforeUpload={beforeUploadHandler}
                maxCount={1}
                fileList={fileList}
                onChange={({ fileList }) => {
                  if (fileList.length === 0) {
                    setIsFileError(false);
                  }
                  setFileList(fileList);
                }}
              >
                <p className="ant-upload-drag-icon">
                  <InboxOutlined />
                </p>
                <p className="ant-upload-text">
                  Kéo thả hoặc chọn file để tải lên
                </p>
                <p className="ant-upload-hint">Định dạng hỗ trợ: xlsm, xlsx</p>
              </Dragger>
              {isFileError && (
                <p className={"text-[#F73A3A]"}>{excelErrorMessage}</p>
              )}
            </Form.Item>
          </Form>
        </div>

        <div className="flex justify-end gap-4 py-4 rounded max-md:max-w-full max-md:flex-wrap pb-4 pl-10 pr-10 bg-[#F7F8FA]">
          <Button htmlType="button" onClick={onModalShowDataClose}>
            Đóng
          </Button>
          <Button
            htmlType="submit"
            type={"primary"}
            disabled={fileList.length === 0 || isFileError}
            onClick={() => {
              setIsDataVisible(true);
              setIsOpen(false);
              importHandler();
            }}
          >
            Import
          </Button>
        </div>
      </Modal>
      <Modal
        open={isDataVisible}
        width={"90%"}
        closable={false}
        footer={false}
        styles={{ content: { padding: 0 } }}
        closeIcon={null}
      >
        <div className={"pl-10 pr-10"}>
          <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
            <h2 className="text-neutral-700 text-2xl font-semibold ">
              Import outlet
            </h2>
            <div className="pt-5">
              <Button
                type="link"
                onClick={onModalShowDataClose}
                size="large"
                icon={<CloseOutlined />}
              />
            </div>
          </div>
        </div>
        <div className={"pl-10 pr-10 mb-8"}>
          <p className={"m-0"}>
            Bạn đã tải lên {importData.length} dòng dữ liệu, phía dưới là kết
            quả sau khi import
          </p>

          <Space className="pb-3">
            <Alert
              type="success"
              showIcon
              message={`Import thành công: ${successfulImports.length}`}
            />
            <Alert
              type="error"
              showIcon
              message={`Import thất bại: ${failedImports.length}`}
            />
          </Space>

          <Table
            ref={tableRef}
            rowKey={(record) => record.id as React.Key}
            dataSource={displayData}
            scroll={{ x: "max-content", y: 450 }}
            columns={[
              {
                dataIndex: "code",
                title: "Mã outlet",
                className: "min-w-[100px]",
              },
              {
                dataIndex: "name",
                title: "Tên outlet",
                className: "min-w-[100px]",
              },
              {
                dataIndex: "houseNumber",
                title: "Số nhà",
                className: "min-w-[50px]",
              },

              {
                dataIndex: "streetName",
                title: "Tên đường",
                className: "min-w-[100px]",
              },
              {
                dataIndex: "province",
                title: "Tỉnh/Thành phố",
                className: "min-w-[100px]",
              },
              {
                dataIndex: "district",
                title: "Quận/Huyện",
                className: "min-w-[100px]",
              },
              {
                dataIndex: "ward",
                title: "Phường/Xã",
                className: "min-w-[100px]",
              },
              {
                dataIndex: "hasOvernightShiftText",
                title: "Loại ca làm việc",
                className: "min-w-[100px]",
              },
              {
                dataIndex: "channel",
                title: "Kênh",
                className: "min-w-[100px]",
              },
              {
                dataIndex: "subChannel",
                title: "Nhóm",
                className: "min-w-[100px]",
              },
              {
                dataIndex: "agency",
                title: "Agency phụ trách",
                className: "min-w-[100px]",
              },
              {
                dataIndex: "leaderPhone",
                title: "SĐT trưởng nhóm quản lý outlet này",
                className: "min-w-[100px]",
              },
              {
                dataIndex: "latitude",
                title: "Latitude",
                className: "min-w-[50px]",
              },
              {
                dataIndex: "longitude",
                title: "Longitude",
                className: "min-w-[50px]",
              },
              {
                title: "Trạng thái",
                fixed: "right",
                dataIndex: "status",
                className: "min-w-[100px]",
                render: (value: ImportStatus, record) => {
                  switch (value) {
                    case "doing":
                      return (
                        <>
                          <LoadingOutlined /> Đang import
                        </>
                      );
                    case "success":
                      return (
                        <>
                          <CheckCircleTwoTone twoToneColor="#52c41a" /> Đã
                          import
                        </>
                      );
                    case "error":
                      return (
                        <>
                          <CloseCircleTwoTone twoToneColor="#f5222d" />{" "}
                          {record.errorMessage}
                        </>
                      );
                    default:
                      return null;
                  }
                },
              },
            ]}
            pagination={false}
          />
        </div>

        <div className="flex justify-between gap-4 py-4 rounded max-md:max-w-full max-md:flex-wrap pb-4 pl-10 pr-10 bg-[#F7F8FA]">
          {failedImports.length + successfulImports.length <
          importData.length ? (
            <>
              {isImporting && (
                <>
                  <p>
                    <LoadingOutlined /> Đang tiến hành import
                  </p>
                  <p>Vui lòng không tắt popup cho đến khi hoàn thành</p>
                </>
              )}
            </>
          ) : (
            <>
              <p>
                <CheckCircleFilled style={{ color: "green" }} /> Hoàn thành quá
                trình import
              </p>
              <Space>
                <Button type="default" onClick={onModalShowDataClose}>
                  Đóng
                </Button>
                <Button
                  type="default"
                  icon={<DownloadOutlined />}
                  onClick={handleImportErrorClick}
                  disabled={failedImports.length === 0}
                >
                  Tải data lỗi import
                </Button>
              </Space>
            </>
          )}
        </div>
      </Modal>
    </>
  );
}
