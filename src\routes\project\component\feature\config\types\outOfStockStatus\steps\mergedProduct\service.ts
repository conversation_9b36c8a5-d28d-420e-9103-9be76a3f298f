import { AbstractFilterInterface } from "@/common/interface";
import { useApp } from "@/UseApp";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  OosMergedProductInterface,
  OosMergedProductItemAvailableInterface,
  OosProductInterface,
} from "../../interface";

export const useCreateOosMergedProductMutation = (featureId: number) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createOosMergedProduct", featureId],
    mutationFn: (data: {
      projectBrandId: number;
      unitId: number;
      imageId: number;
      productCode: string;
      productName: string;
    }) => axiosPost(`/features/${featureId}/oos-merged-products`, data),
  });
};

export const useGetOosMergedProductsQuery = (
  featureId: number,
  filter?: AbstractFilterInterface & object,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["getOosMergedProducts", featureId, filter],
    queryFn: () =>
      axiosGet<
        { entities: OosMergedProductInterface[]; count: number },
        unknown
      >(`/features/${featureId}/oos-merged-products`, filter),
  });
};

export const useUpdateOosMergedProductMutation = (featureId: number) => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["updateOosMergedProduct", featureId],
    mutationFn: (data: {
      projectBrandId?: number;
      unitId?: number;
      imageId?: number;
      productCode?: string;
      productName?: string;
      isActive?: boolean;
      id: number;
    }) =>
      axiosPatch(`/features/${featureId}/oos-merged-products/${data.id}`, data),
  });
};

export const useDeleteOosMergedProductMutation = (featureId: number) => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deleteOosMergedProduct", featureId],
    mutationFn: (id: number) =>
      axiosDelete(`/features/${featureId}/oos-merged-products/${id}`),
  });
};

export const useArrangeOosMergedProductMutation = (featureId: number) => {
  const { axiosPut } = useApp();

  return useMutation({
    mutationKey: ["arrangeOosMergedProduct", featureId],
    mutationFn: (data: { id: number; overFeatureOosProductId: number }) =>
      axiosPut(
        `/features/${featureId}/oos-merged-products/${data.id}/arrangement`,
        data,
      ),
  });
};

export const useAddOosMergedProductMutation = (
  featureId: number,
  id: number,
) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["addOosMergedProduct", featureId, id],
    mutationFn: (data: { projectProductIds: number[] }) =>
      axiosPost(
        `/features/${featureId}/oos-merged-products/${id}/products`,
        data,
      ),
  });
};

export const useRemoveOosMergedProductMutation = (
  featureId: number,
  id: number,
) => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["removeOosMergedProduct", featureId, id],
    mutationFn: (data: number[]) =>
      axiosDelete(`/features/${featureId}/oos-merged-products/${id}/products`, {
        featureOosMergedProductItemIds: data,
      }),
  });
};

export const useOosMergedProductProductsQuery = (
  featureId: number,
  id: number,
  filter?: AbstractFilterInterface & object,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["oosMergedProductProducts", featureId, id, filter],
    queryFn: () =>
      axiosGet<{ entities: OosProductInterface[]; count: number }, unknown>(
        `/features/${featureId}/oos-merged-products/${id}/products`,
        filter,
      ),
  });
};

export const useOosMergedProductProductAvailablesQuery = (
  featureId: number,
  id: number,
  filter?: AbstractFilterInterface & object,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["oosMergedProductProductAvailables", featureId, id, filter],
    queryFn: () =>
      axiosGet<
        { entities: OosMergedProductItemAvailableInterface[]; count: number },
        unknown
      >(
        `/features/${featureId}/oos-merged-products/${id}/product-availables`,
        filter,
      ),
  });
};
