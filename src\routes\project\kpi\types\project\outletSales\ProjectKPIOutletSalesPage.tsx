import { DEFAULT_PAGE_SIZE } from "@/common/constant";
import TableActionCell from "@/components/TableActionCell";
import { CheckSquareOutlined } from "@ant-design/icons";
import { Col, Row, Select, Table } from "antd";
import { useCallback, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import {
  KPIInterface,
  KPIScopeEnum,
  PeriodTypeEnum,
  PropagationModalType,
} from "../../../interface";
import KPIEditInput from "../../../KPIEditInput";
import KPIPropagationModal from "../../../KPIPropagationModal";
import { useProjectKPIQuery } from "../../../service";

const ProjectKPIOutletSalesPage = () => {
  const projectId = parseInt(useParams().id ?? "0");
  const {
    data: kpiData,
    refetch,
    isRefetching,
  } = useProjectKPIQuery(projectId, KPIScopeEnum.OUTLET, PeriodTypeEnum.DAILY);

  const [selectedOutletId, setSelectedOutletId] = useState<
    number | undefined
  >();
  const [openedProjectOutlet, setOpenedProjectOutlet] = useState<
    KPIInterface | undefined
  >();

  const cb = useCallback(() => {
    refetch();
  }, [refetch]);

  const data = useMemo(() => {
    if (!kpiData) return [];
    if (!selectedOutletId) return kpiData;
    return kpiData.filter(
      (item) => item.projectOutlet?.id === selectedOutletId,
    );
  }, [kpiData, selectedOutletId]);

  const outletOptions = useMemo(() => {
    if (!kpiData) return [];
    return kpiData
      .filter((item) => item.projectOutlet)
      .map((item) => ({
        value: item.projectOutlet!.id,
        label: item.projectOutlet!.name,
      }));
  }, [kpiData]);

  const kpiPropagationModalCancel = useCallback(() => {
    refetch();
    setOpenedProjectOutlet(undefined);
  }, [refetch]);

  const propagationCb = useCallback(() => {
    refetch();
    setOpenedProjectOutlet(undefined);
  }, [refetch]);

  return (
    <>
      <Row justify={"space-between"}>
        <Col>
          <Select
            showSearch
            allowClear
            style={{ width: 300 }}
            placeholder="Tìm kiếm cửa hàng"
            optionFilterProp="label"
            filterOption={(input, option) => {
              const label = option?.label as string;
              return label.toLowerCase().includes(input.toLowerCase());
            }}
            options={outletOptions}
            value={selectedOutletId}
            onChange={setSelectedOutletId}
          />
        </Col>
      </Row>

      {!isRefetching && (
        <Table
          rowKey={(o) => o.projectOutlet?.id?.toString() ?? ""}
          className="mt-8"
          dataSource={data ?? []}
          pagination={{ defaultPageSize: DEFAULT_PAGE_SIZE }}
          columns={[
            {
              title: "Tên outlet",
              dataIndex: "projectOutlet",
              render: (projectOutlet) => projectOutlet?.name,
            },
            {
              title: "KPI doanh số",
              render: (record: {
                salesRevenue: string;
                projectOutlet: { id: number };
              }) => {
                return (
                  <KPIEditInput
                    kpi={
                      record.salesRevenue ? Number(record.salesRevenue) : null
                    }
                    cb={cb}
                    projectId={projectId}
                    type={"salesRevenue"}
                    projectOutletId={record.projectOutlet?.id}
                    periodType={PeriodTypeEnum.DAILY}
                    key={`${record.projectOutlet?.id}-salesRevenue`}
                  />
                );
              },
            },
            {
              title: "KPI sampling",
              render: (record: {
                hit: string;
                projectOutlet: { id: number };
              }) => {
                return (
                  <KPIEditInput
                    kpi={record.hit ? Number(record.hit) : null}
                    cb={cb}
                    projectId={projectId}
                    type={"hit"}
                    projectOutletId={record.projectOutlet?.id}
                    periodType={PeriodTypeEnum.DAILY}
                    key={`${record.projectOutlet?.id}-hit`}
                  />
                );
              },
            },

            {
              render: (_, record: KPIInterface) => (
                <TableActionCell
                  actions={[
                    {
                      key: "apply",
                      action: (record: KPIInterface) => {
                        setOpenedProjectOutlet(record);
                      },
                    },
                  ]}
                  items={[
                    {
                      label: "Áp dụng KPI này cho các cửa hàng còn lại",
                      icon: <CheckSquareOutlined />,
                      key: "apply",
                    },
                  ]}
                  record={record}
                />
              ),
            },
          ]}
        />
      )}

      {openedProjectOutlet && (
        <KPIPropagationModal
          options={
            outletOptions?.filter(
              (item) => item?.value !== openedProjectOutlet.projectOutlet?.id,
            ) ?? []
          }
          isOpen={!!openedProjectOutlet}
          cancel={kpiPropagationModalCancel}
          projectId={projectId}
          cb={propagationCb}
          contentTitle={
            <>
              <p>
                Cửa hàng đang chọn:{" "}
                <span className="text-blue font-semibold">
                  {openedProjectOutlet.projectOutlet?.name}
                </span>
              </p>
              <p>Chọn loại KPI cần áp dụng hàng loạt</p>
            </>
          }
          type={PropagationModalType.OUTLET}
          projectOutlet={openedProjectOutlet.projectOutlet}
          periodType={PeriodTypeEnum.DAILY}
        />
      )}
    </>
  );
};

export default ProjectKPIOutletSalesPage;
