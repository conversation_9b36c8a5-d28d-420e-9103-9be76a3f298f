import { useApp } from "@/UseApp";
import { CURD, SELECT_ALL, SELECT_ALL_LABEL } from "@/common/constant";
import { formErrorResponseHandler } from "@/common/helper";
import DragSortRowComponent from "@/components/DragSortRowComponent";
import ModalCURD from "@/components/ModalCURD";
import TableActionCell from "@/components/TableActionCell";
import { renderTableCell } from "@/components/table-cell";
import {
  ArrowRightOutlined,
  DeleteOutlined,
  EditOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { DndContext, DragEndEvent } from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
  arrayMove,
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import {
  Button,
  Checkbox,
  Col,
  Form,
  Input,
  InputNumber,
  Modal,
  Radio,
  Row,
  Select,
  Space,
  Table,
} from "antd";
import TextArea from "antd/es/input/TextArea";
import { ColumnsType } from "antd/es/table";
import { AxiosError } from "axios";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import {
  PhotographyActionEnum,
  PhotographyInterface,
  PhotographyType,
} from "./interface";
import {
  useArrangementPhotographyMutation,
  useCreatePhotographyMutation,
  useDeletePhotographyMutation,
  usePhotographiesQuery,
  useUpdateIsActivePhotographyMutation,
  useUpdatePhotographyMutation,
} from "./service";

interface PhotographyPageProps {
  isChild?: boolean;
}

export default function PhotographyPage({
  isChild = false,
}: Readonly<PhotographyPageProps>) {
  const componentFeatureId = parseInt(useParams().componentFeatureId ?? "0");
  const { showNotification } = useApp();

  const [searchForm] = Form.useForm();
  const [form] = Form.useForm();
  const [isOpen, setIsOpen] = useState(false);
  const [action, setAction] = useState<CURD | null>(null);
  const [filter, setFilter] = useState({});
  const [modal, contextHolder] = Modal.useModal();

  const photographiesQuery = usePhotographiesQuery(componentFeatureId, {
    ...filter,
    take: 0,
  });

  const createPhotographyMutation =
    useCreatePhotographyMutation(componentFeatureId);
  const updatePhotographyMutation =
    useUpdatePhotographyMutation(componentFeatureId);
  const updateIsActivePhotographyMutation =
    useUpdateIsActivePhotographyMutation(componentFeatureId);
  const deletePhotographyMutation =
    useDeletePhotographyMutation(componentFeatureId);
  const arrangementPhotographyMutation =
    useArrangementPhotographyMutation(componentFeatureId);

  const [dataSource, setDataSource] = useState(
    photographiesQuery.data?.entities ?? [],
  );

  const searchHandler = useCallback(() => {
    setFilter({
      ...searchForm.getFieldsValue(),
    });
  }, [searchForm]);

  const filterOptions = useMemo(() => {
    return [
      {
        label: SELECT_ALL_LABEL,
        value: SELECT_ALL,
      },
      {
        label: "Tên loại hình",
        value: "name",
      },
    ];
  }, []);

  const formFinishHandler = useCallback(async () => {
    try {
      const data = {
        description: form.getFieldValue("description"),
        name: form.getFieldValue("name"),
        maximum: form.getFieldValue("maximum"),
        minimum: form.getFieldValue("minimum"),
        isWatermarkRequired: form.getFieldValue("isWatermarkRequired"),
        id: 0,
      };

      if (form.getFieldValue("type") === PhotographyType.FULL) {
        data.minimum = form.getFieldValue("full");
        data.maximum = form.getFieldValue("full");
      }

      if (action === CURD.CREATE) {
        await createPhotographyMutation.mutateAsync(data);
        showNotification({
          type: "success",
          message: "Thêm loại hình thành công",
        });
      }

      if (action === CURD.UPDATE) {
        data.id = form.getFieldValue("id");
        await updatePhotographyMutation.mutateAsync(data);
        showNotification({
          type: "success",
          message: "Thêm loại hình thành công",
        });
      }

      form.resetFields();
      setIsOpen(false);
      photographiesQuery.refetch();
      setAction(null);
    } catch (error) {
      formErrorResponseHandler(form, error as AxiosError);
    }
  }, [
    action,
    createPhotographyMutation,
    form,
    photographiesQuery,
    showNotification,
    updatePhotographyMutation,
  ]);

  const formContent = (
    <>
      <Form.Item name="id" hidden>
        <Input />
      </Form.Item>
      <Form.Item
        label="Tên loại hình"
        name="name"
        rules={[
          { required: true, message: "Tên loại hình không được để trống" },
          { max: 64, message: "Tên loại hình không được quá 64 ký tự" },
        ]}
      >
        <Input />
      </Form.Item>

      <Form.Item label="Yêu cầu chụp hình" name="type">
        <Radio.Group disabled={action === CURD.UPDATE}>
          <Radio value={PhotographyType.MIN_MAX}>
            Chụp trong khoảng số lượng hình yêu cầu
          </Radio>
          <Radio value={PhotographyType.FULL}>
            Chụp đủ số lượng hình yêu cầu
          </Radio>
        </Radio.Group>
      </Form.Item>
      <Form.Item noStyle dependencies={["type"]}>
        {() => (
          <>
            {form.getFieldValue("type") === PhotographyType.MIN_MAX ? (
              <Row justify={"space-between"}>
                <Col md={11}>
                  <Form.Item
                    label="Số lượng hình tối thiểu"
                    name="minimum"
                    rules={[
                      {
                        required: true,
                        message: "Số lượng hình tối thiểu không được bỏ trống",
                      },
                    ]}
                  >
                    <InputNumber
                      disabled={action === CURD.UPDATE}
                      controls={false}
                      step={0}
                      style={{
                        width: "100%",
                      }}
                      min={0}
                      max={19}
                    />
                  </Form.Item>
                </Col>

                <Col md={11}>
                  <Form.Item
                    label="Số lượng hình tối đa"
                    name="maximum"
                    dependencies={["minimum"]}
                    rules={[
                      {
                        required: true,
                        message: "Số lượng hình tối đa không được bỏ trống",
                      },
                      ({ getFieldValue }) => ({
                        validator(_, value) {
                          if (!value || getFieldValue("minimum") < value) {
                            return Promise.resolve();
                          }
                          return Promise.reject(
                            new Error(
                              "Số lần hình tối đa phải lớn hơn số lượng hình tối thiểu",
                            ),
                          );
                        },
                      }),
                    ]}
                  >
                    <InputNumber
                      disabled={action === CURD.UPDATE}
                      controls={false}
                      step={0}
                      style={{ width: "100%" }}
                      min={1}
                      max={20}
                    />
                  </Form.Item>
                </Col>
              </Row>
            ) : (
              <Form.Item
                label="Số lượng hình yêu cầu"
                name="full"
                rules={[
                  {
                    required: true,
                    message: "Số lượng hình yêu cầu không được bỏ trống",
                  },
                ]}
              >
                <InputNumber
                  disabled={action === CURD.UPDATE}
                  controls={false}
                  step={0}
                  style={{
                    width: "100%",
                  }}
                  min={1}
                  max={20}
                />
              </Form.Item>
            )}
          </>
        )}
      </Form.Item>

      <Form.Item
        label="Mô tả hướng dẫn nhân viên chụp hình đúng yêu cầu"
        name="description"
        rules={[{ max: 128, message: "Mô tả không được quá 128 ký tự" }]}
      >
        <TextArea showCount />
      </Form.Item>

      <Form.Item name="isWatermarkRequired" valuePropName="checked">
        <Checkbox disabled={action === CURD.UPDATE}>
          Ghi timestamp lên ảnh chụp
        </Checkbox>
      </Form.Item>
    </>
  );

  const handleActionEditClick = useCallback(
    (record: PhotographyInterface) => {
      const type =
        record.minimum === record.maximum
          ? PhotographyType.FULL
          : PhotographyType.MIN_MAX;

      form.setFieldsValue({
        name: record.name,
        description: record.description,
        isWatermarkRequired: record.isWatermarkRequired,
        full: record.minimum,
        minimum: record.minimum,
        maximum: record.maximum,
        type,
        id: record.id,
      });

      setIsOpen(true);
      setAction(CURD.UPDATE);
    },
    [form],
  );

  const handleActionActiveClick = useCallback(
    (record: PhotographyInterface) => {
      modal.confirm({
        title: `Kích hoạt loại hình: ${record.name}`,
        content: `Bạn có chắc chắn muốn kích hoạt loại hình ${record.name} này?`,
        okText: "Kích hoạt",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await updateIsActivePhotographyMutation.mutateAsync({
              id: record.id,
              isActive: true,
            });

            showNotification({
              type: "success",
              message: `Kích hoạt loại hình ${record.name} thành công`,
            });

            photographiesQuery.refetch();
          } catch (error) {
            console.error(error);

            showNotification({
              type: "error",
              message: `Kích hoạt loại hình ${record.name} thất bại`,
            });
          }
        },
      });
    },
    [
      modal,
      photographiesQuery,
      showNotification,
      updateIsActivePhotographyMutation,
    ],
  );

  const handleActionInactiveClick = useCallback(
    (record: PhotographyInterface) => {
      modal.confirm({
        title: `Ngừng hoạt động loại hình: ${record.name}`,
        content: `Bạn có chắc chắn muốn ngừng hoạt động loại hình ${record.name} này?`,
        okText: "Ngừng hoạt động",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await updateIsActivePhotographyMutation.mutateAsync({
              id: record.id,
              isActive: false,
            });

            showNotification({
              type: "success",
              message: `Ngừng hoạt động loại hình ${record.name} thành công`,
            });

            photographiesQuery.refetch();
          } catch (error) {
            console.error(error);

            showNotification({
              type: "error",
              message: `Ngừng hoạt động loại hình ${record.name} thất bại`,
            });
          }
        },
      });
    },
    [
      modal,
      photographiesQuery,
      showNotification,
      updateIsActivePhotographyMutation,
    ],
  );

  const handleActionDeleteClick = useCallback(
    (record: PhotographyInterface) => {
      modal.confirm({
        title: `Xóa loại hình: ${record.name}`,
        content: `Bạn có chắc chắn muốn xóa loại hình ${record.name} này?`,
        okText: "Xóa",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await deletePhotographyMutation.mutateAsync(record.id);

            showNotification({
              type: "success",
              message: `Xóa loại hình ${record.name} thành công`,
            });

            photographiesQuery.refetch();
          } catch (error) {
            console.error(error);

            showNotification({
              type: "error",
              message: `Xóa loại hình ${record.name} thất bại`,
            });
          }
        },
      });
    },
    [deletePhotographyMutation, modal, photographiesQuery, showNotification],
  );

  const columns: ColumnsType<PhotographyInterface> = useMemo(() => {
    const actionItems = [
      {
        key: PhotographyActionEnum.EDIT,
        label: "Chỉnh sửa",
        icon: <EditOutlined />,
      },
      {
        key: PhotographyActionEnum.INACTIVE,
        label: "Ngừng hoạt động",
        icon: <PauseCircleOutlined />,
      },
      {
        key: PhotographyActionEnum.ACTIVE,
        label: "Kích hoạt",
        icon: <PlayCircleOutlined />,
      },
      {
        key: PhotographyActionEnum.DELETE,
        label: "Xóa khỏi chức năng",
        icon: <DeleteOutlined />,
      },
    ];

    const actionActions = [
      {
        key: PhotographyActionEnum.EDIT,
        action: handleActionEditClick,
      },
      {
        key: PhotographyActionEnum.ACTIVE,
        action: handleActionActiveClick,
      },
      {
        key: PhotographyActionEnum.INACTIVE,
        action: handleActionInactiveClick,
      },
      {
        key: PhotographyActionEnum.DELETE,
        action: handleActionDeleteClick,
      },
    ];

    const ACTION_ACTIVE = [
      PhotographyActionEnum.INACTIVE,
      PhotographyActionEnum.DELETE,
      PhotographyActionEnum.EDIT,
    ];

    const ACTION_INACTIVE = [
      PhotographyActionEnum.ACTIVE,
      PhotographyActionEnum.DELETE,
      PhotographyActionEnum.EDIT,
    ];

    return [
      {
        key: "sort",
      },
      {
        title: "Tên loại hình",
        key: "name",
        dataIndex: "name",
      },
      {
        title: "Số lượng hình cần chụp",
        render: (_, record: PhotographyInterface) => {
          if (record.minimum === record.maximum) {
            return (
              <>
                <span className="font-semibold">Chụp đủ số lượng:</span>{" "}
                {record.minimum} hình
              </>
            );
          }
          return (
            <>
              <span className="font-semibold">Chụp tối thiểu:</span>{" "}
              {record.minimum} hình <ArrowRightOutlined className="pl-3 pr-3" />
              <span className="font-semibold">Chụp tối đa:</span>{" "}
              {record.maximum} hình
            </>
          );
        },
      },
      {
        title: "Tình trạng",
        dataIndex: "isActive",
        render: (value, record, index) => {
          return renderTableCell(value, record, index, "isActive");
        },
      },
      {
        key: "actions",
        render: (_, record) => {
          const actionKeys = record.isActive ? ACTION_ACTIVE : ACTION_INACTIVE;
          const items = actionItems.filter((item) =>
            actionKeys.includes(item.key),
          );
          return (
            <TableActionCell
              actions={actionActions}
              items={items}
              record={record}
            />
          );
        },
        width: 100,
      },
    ];
  }, [
    handleActionActiveClick,
    handleActionDeleteClick,
    handleActionEditClick,
    handleActionInactiveClick,
  ]);

  const onDragEnd = useCallback(
    async ({ active, over }: DragEndEvent) => {
      if (!over) {
        return;
      }
      if (active.id !== over?.id) {
        setDataSource((previous) => {
          const activeIndex = previous?.findIndex((i) => i.id === active.id);
          const overIndex = previous?.findIndex((i) => i.id === over?.id);
          return arrayMove(previous, activeIndex, overIndex);
        });

        await arrangementPhotographyMutation.mutateAsync({
          activeId: active.id as number,
          overId: over?.id as number,
        });
      }
    },
    [arrangementPhotographyMutation],
  );

  useEffect(() => {
    setDataSource(
      (photographiesQuery.data?.entities ?? []).sort(
        (a, b) => a.ordinal - b.ordinal,
      ),
    );
  }, [photographiesQuery.data]);

  return (
    <>
      <div
        className={`bg-white pt-10 rounded pb-10 ${!isChild ? "px-10" : ""}`}
      >
        <Row justify={"space-between"} className={"mb-6"}>
          <Col>
            <Form
              layout="inline"
              form={searchForm}
              onFinish={searchHandler}
              initialValues={{ filterField: SELECT_ALL }}
            >
              <Space.Compact>
                <Form.Item
                  name="filterField"
                  style={{ marginInlineEnd: "0px", borderRight: "none" }}
                >
                  <Select style={{ width: "150px" }} options={filterOptions} />
                </Form.Item>
                <Form.Item name="filterValue">
                  <Input
                    prefix={<SearchOutlined />}
                    placeholder={"Nhập nội dung cần tìm"}
                    allowClear
                  />
                </Form.Item>
              </Space.Compact>

              <Form.Item>
                <Button htmlType="submit" type="default">
                  Tìm kiếm
                </Button>
              </Form.Item>
            </Form>
          </Col>

          <Col>
            <Button
              icon={<PlusOutlined />}
              type="primary"
              onClick={() => {
                setIsOpen(true);
                setAction(CURD.CREATE);
                form.setFieldsValue({
                  type: PhotographyType.MIN_MAX,
                  isWatermarkRequired: true,
                });
              }}
            >
              Thêm mới
            </Button>
          </Col>
        </Row>
        <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
          <SortableContext
            items={dataSource.map((i) => i.id)}
            strategy={verticalListSortingStrategy}
          >
            <Table
              components={{
                body: {
                  row: DragSortRowComponent,
                },
              }}
              rowKey={"id"}
              columns={columns}
              dataSource={dataSource}
              pagination={false}
              loading={
                photographiesQuery.isFetching ||
                arrangementPhotographyMutation.isPending
              }
            />
          </SortableContext>
        </DndContext>
      </div>

      <ModalCURD
        title={
          action === CURD.CREATE ? "Thêm loại hình" : "Thông tin loại hình"
        }
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        formContent={formContent}
        form={form}
        onFinish={formFinishHandler}
        action={action}
      />

      {contextHolder}
    </>
  );
}
