import { useMemo } from "react";
import { DEFAULT_CURRENT_PAGE } from "./constant";

export const useTablePagination = ({
  currentPage,
  total,
  pageSize,
  setCurrentPage,
  setPageSize,
}: {
  currentPage: number;
  pageSize: number;
  total: number;
  setCurrentPage: (page: number) => void;
  setPageSize: (page: number) => void;
}) =>
  useMemo(() => {
    return {
      current: currentPage,
      total: total,
      pageSize: pageSize,
      onChange: (page: number) => {
        setCurrentPage(page);
      },
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: (_current: number, size: number) => {
        setPageSize(size);
        setCurrentPage(DEFAULT_CURRENT_PAGE);
      },
    };
  }, [currentPage, pageSize, setCurrentPage, setPageSize, total]);
