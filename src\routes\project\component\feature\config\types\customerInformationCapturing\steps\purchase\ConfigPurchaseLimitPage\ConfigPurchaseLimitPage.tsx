import { Tabs } from "antd";
import { useState } from "react";
import { useParams } from "react-router-dom";
import { OrderLimitTypeEnum } from "../../../interface";
import LimitCustomerTab from "../../exchange/limitTabs/limitCustomerTab/LimitCustomerTab";

const ConfigPurchaseLimitPage = () => {
  const componentFeatureId = parseInt(useParams().componentFeatureId ?? "0");
  const [activeKey, setActiveKey] = useState(
    OrderLimitTypeEnum.ORDER.toString(),
  );

  return (
    <Tabs
      type="card"
      activeKey={activeKey}
      onChange={setActiveKey}
      items={[
        {
          key: OrderLimitTypeEnum.ORDER,
          label: "Giới hạn đơn theo khách",
          children: (
            <LimitCustomerTab
              componentFeatureId={componentFeatureId}
              type={OrderLimitTypeEnum.ORDER}
              activeKey={activeKey}
            />
          ),
        },
        {
          key: OrderLimitTypeEnum.PURCHASE,
          label: "G<PERSON>ới hạn sản phẩm theo khách",
          children: (
            <LimitCustomerTab
              componentFeatureId={componentFeatureId}
              type={OrderLimitTypeEnum.PURCHASE}
              activeKey={activeKey}
            />
          ),
        },
      ]}
    />
  );
};

export default ConfigPurchaseLimitPage;
