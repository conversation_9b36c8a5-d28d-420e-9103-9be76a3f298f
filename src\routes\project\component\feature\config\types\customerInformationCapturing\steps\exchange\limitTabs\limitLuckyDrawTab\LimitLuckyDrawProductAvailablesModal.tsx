import { filterOption } from "@/common/helper";
import CustomModal from "@/components/CustomModal";
import ProductItemCell from "@/components/ProductItemCell";
import { useUrlFiltersWithQuery } from "@/hooks/useUrlFiltersWithQuery";
import {
  ProductInterface,
  ProductPackagingInterface,
} from "@/routes/product/interface";
import { useProjectBrandsQuery } from "@/routes/project/general/services";
import { ProjectProductInterface } from "@/routes/project/product/interface";
import { Button, Col, Form, Input, Row, Select, Table } from "antd";
import { useCallback, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import {
  useAddLuckyDrawOrderLimitProductMutation,
  useLuckyDrawOrderLimitProductsAvailableQuery,
} from "../../service";

interface LimitLuckyDrawProductAvailablesModalProps {
  componentFeatureId: number;
  orderLuckyDrawLimitId: number;
  onCancel: () => void;
  cb: () => void;
}
const LimitLuckyDrawProductAvailablesModal = ({
  componentFeatureId,
  orderLuckyDrawLimitId,
  onCancel,
  cb,
}: LimitLuckyDrawProductAvailablesModalProps) => {
  const projectId = parseInt(useParams().id ?? "0");

  const [selectedItemKeys, setSelectedItemKeys] = useState<React.Key[]>([]);
  const [form] = Form.useForm();

  const {
    query: { data, isFetching, isRefetching },
    handleSearch,
    getPaginationProps,
  } = useUrlFiltersWithQuery<ProjectProductInterface>({
    formInstance: form,
    useQueryHook: useLuckyDrawOrderLimitProductsAvailableQuery,
    queryParams: [componentFeatureId, orderLuckyDrawLimitId],
  });
  const projectBrandsQuery = useProjectBrandsQuery(projectId);

  const addLuckyDrawOrderLimitProductMutation =
    useAddLuckyDrawOrderLimitProductMutation(
      componentFeatureId,
      orderLuckyDrawLimitId,
    );

  const onSelectChange = useCallback((newSelectedRowKeys: React.Key[]) => {
    setSelectedItemKeys(newSelectedRowKeys);
  }, []);

  const rowSelection = {
    selectedRowKeys: selectedItemKeys,
    onChange: onSelectChange,
    getCheckboxProps: (record: ProjectProductInterface) => {
      return {
        disabled: !record.product.isActive || !record.isAvailable,
      };
    },
  };

  const newSelectedItemKeys = useMemo(
    () =>
      selectedItemKeys.filter((key) => {
        const productPacking = data?.entities?.find(
          (item) => item.id === key && item.isAvailable,
        );

        return !!productPacking;
      }),
    [data?.entities, selectedItemKeys],
  );

  const pagination = useMemo(() => getPaginationProps(), [getPaginationProps]);

  const add = useCallback(async () => {
    try {
      await addLuckyDrawOrderLimitProductMutation.mutateAsync(
        newSelectedItemKeys.map((key) => Number(key)),
      );

      cb?.();
      onCancel();
    } catch (e) {
      console.error(e);
    }
  }, [
    addLuckyDrawOrderLimitProductMutation,
    cb,
    newSelectedItemKeys,
    onCancel,
  ]);

  const content = (
    <div>
      <Form layout="vertical" form={form} onFinish={handleSearch}>
        <Row justify={"space-between"}>
          <Col md={7}>
            <Form.Item name="projectBrandId" label="Nhãn hàng">
              <Select
                allowClear
                showSearch
                optionFilterProp="children"
                filterOption={filterOption}
                options={projectBrandsQuery.data?.map((projectBrand) => ({
                  label: projectBrand.brand.name,
                  value: projectBrand.id,
                }))}
                popupMatchSelectWidth={false}
                className={"h-10"}
              />
            </Form.Item>
          </Col>
          <Col md={7}>
            <Form.Item name="keyword" label="Sản phẩm">
              <Input
                allowClear
                placeholder={"Nhập tên hoặc mã sản phẩm"}
                className={"h-10"}
              />
            </Form.Item>
          </Col>
          <Col md={5}>
            <Form.Item name="isMainPackaging" label="Loại quy cách">
              <Select
                allowClear
                showSearch
                optionFilterProp="children"
                filterOption={filterOption}
                options={[
                  {
                    label: "Tất cả",
                    value: "all",
                  },
                  {
                    label: "Quy cách chuẩn",
                    value: "true",
                  },
                  {
                    label: "Thường",
                    value: "false",
                  },
                ]}
                popupMatchSelectWidth={false}
                className={"h-10"}
              />
            </Form.Item>
          </Col>
          <Col>
            <Form.Item label=" ">
              <Button htmlType="submit">Tìm kiếm</Button>
            </Form.Item>
          </Col>
        </Row>
      </Form>

      <Table
        rowKey={"id"}
        rowSelection={rowSelection}
        dataSource={data?.entities}
        loading={isFetching || isRefetching}
        pagination={pagination}
        columns={[
          {
            title: "Tên sản phẩm",
            dataIndex: "product",
            render: (product: ProductInterface, record) => {
              const { isAvailable } = record;

              return (
                <ProductItemCell
                  variants={product?.image?.variants ?? []}
                  name={product.name}
                  isActive={record.product.isActive}
                  isAvailable={isAvailable}
                  isAvailableFalseText={"Đã thêm vào chức năng"}
                />
              );
            },
          },
          {
            title: "Mã sản phẩm",
            dataIndex: "product",
            render: (product: ProductInterface) => product.code,
          },
          {
            title: "Nhãn hàng",
            dataIndex: "product",
            render: (product: ProductInterface) => product.brand.name,
          },
          {
            title: "Loại quy cách",
            dataIndex: "productPackaging",
            className: "w-[150px]",
            render: (productPackaging: ProductPackagingInterface) =>
              productPackaging.isMainPackaging ? "Quy cách chuẩn" : "Thường",
          },
          {
            title: "Quy cách",
            dataIndex: "productPackaging",
            render: (productPackaging: ProductPackagingInterface) =>
              productPackaging.unit.name,
          },
        ]}
      />
    </div>
  );

  return (
    <CustomModal
      title={"Thêm sản phẩm"}
      isOpen={true}
      content={content}
      width={900}
      onCancel={onCancel}
      onConfirm={add}
      confirmText={`Thêm ${newSelectedItemKeys.length} sản phẩm`}
      confirmLoading={addLuckyDrawOrderLimitProductMutation.isPending}
    />
  );
};

export default LimitLuckyDrawProductAvailablesModal;
