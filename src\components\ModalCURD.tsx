import useDeviceType from "@/hooks/useDeviceType";
import { CloseOutlined } from "@ant-design/icons";
import { Button, Col, Form, FormInstance, Modal, ModalProps, Row } from "antd";
import { useCallback } from "react";
import { useApp } from "../UseApp";
import { BTN_CREATE_TEXT, BTN_UPDATE_TEXT, CURD } from "../common/constant";

interface ModalCURDProps extends ModalProps {
  readonly title: string;
  readonly isOpen: boolean;
  readonly setIsOpen?: (setIsOpen: boolean) => void;
  readonly formContent: React.ReactNode;
  readonly form: FormInstance;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  readonly onFinish: (values?: any) => void;
  readonly action?: CURD | null;
  readonly btnText?: string;
  readonly btnConfirmDisable?: boolean;
  readonly btnConfirmLoading?: boolean;
  readonly onCancelCb?: () => void;
}

const ModalCURD = ({
  title,
  isOpen,
  setIsOpen,
  formContent,
  form,
  onFinish,
  action,
  btnText,
  btnConfirmDisable,
  btnConfirmLoading,
  onCancelCb,
  ...props
}: Readonly<ModalCURDProps>) => {
  const { loading } = useApp();
  const isMobile = useDeviceType();

  const onCancel = useCallback(() => {
    setIsOpen?.(false);
    form.resetFields();
    if (onCancelCb) {
      onCancelCb();
    }
  }, [form, onCancelCb, setIsOpen]);

  if (isMobile) {
    return (
      <Modal
        open={isOpen}
        title={title}
        onCancel={onCancel}
        confirmLoading={btnConfirmLoading || loading}
        okText={
          btnText ??
          (action === CURD.CREATE ? BTN_CREATE_TEXT : BTN_UPDATE_TEXT)
        }
        cancelText={"Đóng"}
        okButtonProps={{
          disabled: btnConfirmDisable,
        }}
        onOk={onFinish}
      >
        <Form form={form} onFinish={onFinish} layout={"vertical"}>
          {formContent}
        </Form>
      </Modal>
    );
  }

  return (
    <Modal
      open={isOpen}
      footer={null}
      closeIcon={null}
      styles={{ content: { padding: 0 } }}
      {...props}
    >
      <Form form={form} onFinish={onFinish} layout={"vertical"}>
        <div className={isMobile ? "px-3 pt-0" : `pl-10 pr-10 pt-3`}>
          <Row justify={"space-between"}>
            <Col md={20}>
              <h2 className="text-neutral-700 text-2xl font-semibold ">
                {title}
              </h2>
            </Col>
            <Col md={2} className="flex items-center">
              <Button
                type="link"
                onClick={onCancel}
                size="large"
                icon={<CloseOutlined />}
              />
            </Col>
          </Row>

          {formContent}
        </div>
        <div className="flex justify-end gap-4 py-4 rounded max-md:max-w-full max-md:flex-wrap pl-10 bg-[#F7F8FA] pr-10 pb-4">
          <Button htmlType="button" loading={loading} onClick={onCancel}>
            Đóng
          </Button>
          <Button
            htmlType="submit"
            type={"primary"}
            loading={btnConfirmLoading || loading}
            disabled={btnConfirmDisable}
          >
            {btnText ??
              (action === CURD.CREATE ? BTN_CREATE_TEXT : BTN_UPDATE_TEXT)}
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

export default ModalCURD;
