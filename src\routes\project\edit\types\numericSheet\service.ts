import { AbstractFilterInterface } from "@/common/interface";
import { useApp } from "@/UseApp";
import { useMutation, useQuery } from "@tanstack/react-query";
import { EditNumericSheetInterface } from "./interface";

export const useAttendanceNumericSheetsQuery = (
  projectId: number,
  attendanceId: number,
  featureId: number,
  filter?: AbstractFilterInterface & object,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: [
      "attendanceNumericSheets",
      projectId,
      attendanceId,
      featureId,
      filter,
    ],
    queryFn: () =>
      axiosGet<EditNumericSheetInterface, unknown>(
        `/tools/projects/${projectId}/attendances/${attendanceId}/features/${featureId}/numeric-sheets`,
        filter,
      ),
  });
};

export const useCreateAttendanceNumericSheetMutation = (
  projectId: number,
  attendanceId: number,
  featureId: number,
) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: [
      "createAttendanceNumericSheet",
      projectId,
      attendanceId,
      featureId,
    ],
    mutationFn: (data: {
      dataUuid: string;
      dataTimestamp: string;
      values: {
        featureNumericId: number;
        featureNumericAttributeId: number;
        value: number | null;
      }[];
    }) =>
      axiosPost(
        `/tools/projects/${projectId}/attendances/${attendanceId}/features/${featureId}/numeric-sheets`,
        data,
      ),
  });
};
