import { useMutation, useQuery } from "@tanstack/react-query";
import { AbstractFilterInterface } from "@/common/interface.ts";
import { useApp } from "@/UseApp";
import { AppContextInterface } from "@/interface.ts";
import {
  ApiUserResponseInterface,
  GenderEnum,
  UserInterface,
  UserTypeEnum,
} from "./interface";

export const getUsers = async (
  axiosGet: AppContextInterface["axiosGet"],
  filter?: AbstractFilterInterface & object,
) => {
  return await axiosGet<ApiUserResponseInterface, unknown>("/users", filter);
};

export const getUsersByType = async (
  axiosGet: AppContextInterface["axiosGet"],
  type: UserTypeEnum,
  filter: unknown,
) => {
  return await axiosGet<ApiUserResponseInterface, unknown>(
    `/users/${type}`,
    filter,
  );
};

export const useCreateUserMutation = () => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createUser"],
    mutationFn: (data: {
      email: string;
      username: string;
      name: string;
      phone: string;
      password: string;
      type: string;
      picture?: string;
    }) => axiosPost<UserInterface, unknown>("/users/find-or-create", data),
  });
};

export const useUpdateUserMutation = () => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["updateUser"],
    mutationFn: (data: {
      id: number;
      email?: string;
      username?: string;
      name?: string;
      phone?: string;
      type?: string;
      isActive?: boolean;
    }) => axiosPatch<UserInterface, unknown>(`/users/${data.id}`, data),
  });
};

export const useFindOrCreateUserMutation = () => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["findOrCreateUser"],
    mutationFn: (data: {
      email: string;
      username: string;
      name: string;
      phone: string;
      password: string;
      type: string;
      gender?: GenderEnum;
    }) => axiosPost<UserInterface, unknown>("/users/find-or-create", data),
  });
};

export const useUsersQuery = (filter?: AbstractFilterInterface & object) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["users", filter],
    queryFn: () => getUsers(axiosGet, filter),
  });
};
