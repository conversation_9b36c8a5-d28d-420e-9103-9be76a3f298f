import { useApp } from "@/UseApp";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  ProjectDashboardInterface,
  ProjectDashboardTypeEnum,
} from "./interface";

export const useGetProjectDashboards = (projectId: number) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["projectDashboards", projectId],
    queryFn: () =>
      axiosGet<
        { entities: ProjectDashboardInterface[]; count: number },
        unknown
      >(`/projects/${projectId}/dashboards`),
  });
};

export const useCreateProjectDashboardMutation = (projectId: number) => {
  const { axiosPost, showNotification } = useApp();

  return useMutation({
    mutationKey: ["createProjectDashboard", projectId],
    mutationFn: (data: {
      name: string;
      description?: string;
      type: ProjectDashboardTypeEnum;
    }) => axiosPost(`/projects/${projectId}/dashboards`, data),
    onSuccess: () => {
      showNotification({
        type: "success",
        message: `Tạo dashboard thành công`,
      });
    },
  });
};

export const useUpdateProjectDashboardMutation = (projectId: number) => {
  const { axiosPatch, showNotification } = useApp();

  return useMutation({
    mutationKey: ["updateProjectDashboard", projectId],
    mutationFn: (data: { id: number; name?: string; description?: string }) =>
      axiosPatch(`/projects/${projectId}/dashboards/${data.id}`, data),
    onSuccess: () => {
      showNotification({
        type: "success",
        message: `Cập nhật dashboard thành công`,
      });
    },
  });
};

export const useDeleteProjectDashboardMutation = (projectId: number) => {
  const { axiosDelete, showNotification } = useApp();

  return useMutation({
    mutationKey: ["deleteProjectDashboard", projectId],
    mutationFn: (id: number) =>
      axiosDelete(`/projects/${projectId}/dashboards/${id}`),
    onSuccess: () => {
      showNotification({
        type: "success",
        message: `Xoá dashboard thành công`,
      });
    },
  });
};

export const useArrangementProjectDashboardMutation = (projectId: number) => {
  const { axiosPut, showNotification } = useApp();

  return useMutation({
    mutationKey: ["arrangementProjectDashboard", projectId],
    mutationFn: (data: { id: number; arrangement: number[] }) =>
      axiosPut(
        `/projects/${projectId}/dashboards/${data.id}/arrangement`,
        data,
      ),
    onSuccess: () => {
      showNotification({
        type: "success",
        message: `Thay đổi thu tục dashboard thành công`,
      });
    },
  });
};
