import { useProjectAgenciesQuery } from "@project/general/services";
import { UseQueryResult } from "@tanstack/react-query";
import { Switch, Tabs, TabsProps } from "antd";
import { useCallback } from "react";
import { useOutletContext, useParams, useSearchParams } from "react-router-dom";
import StepLockPage from "../../../StepLockPage.tsx";
import { OrderInterface, StepLockEnum } from "../../../interface.ts";
import { useUpdateStepStatusMutation } from "../../../service.ts";
import ExchangeTab from "./ExchangeTab.tsx";

export default function ConfigExchangePage() {
  const projectId = parseInt(useParams().id ?? "0");
  const componentFeatureId = parseInt(useParams().componentFeatureId ?? "0");

  const [orderLatestQuery]: [UseQueryResult<OrderInterface, unknown>] =
    useOutletContext();
  const [searchParams, setSearchParams] = useSearchParams();

  const projectAgenciesQuery = useProjectAgenciesQuery(projectId);

  const updateStepStatusMutation =
    useUpdateStepStatusMutation(componentFeatureId);

  const items: TabsProps["items"] = projectAgenciesQuery.data?.map(
    (projectAgency) => ({
      key: projectAgency.id.toString(),
      label: projectAgency.agency.name,
      children: (
        <ExchangeTab
          projectAgency={projectAgency}
          componentFeatureId={componentFeatureId}
        />
      ),
    }),
  );

  const onRequiredExchangeSwitchChange = useCallback(
    async (value: boolean) => {
      updateStepStatusMutation
        .mutateAsync({
          isExchangeRequired: !value,
        })
        .then(() => {
          orderLatestQuery.refetch();
        });
    },
    [orderLatestQuery, updateStepStatusMutation],
  );

  if (!orderLatestQuery.data?.hasExchange) {
    return (
      <StepLockPage
        title="Chức năng đổi quà"
        description="Chức năng cho phép cấu hình scheme quà và chỉ đỉnh outlet sẽ thực hiện scheme quà nào"
        type={StepLockEnum.Exchange}
        orderLatestQuery={orderLatestQuery}
        locked={!orderLatestQuery.data?.hasExchange}
      />
    );
  }

  return (
    <>
      <div className="mb-6">
        <span className="mr-4">Cho phép skip trên app</span>
        <Switch
          value={!orderLatestQuery?.data?.isExchangeRequired}
          loading={updateStepStatusMutation.isPending}
          onChange={onRequiredExchangeSwitchChange}
        />
      </div>
      <Tabs
        items={items}
        type={"card"}
        onTabClick={(key) => {
          setSearchParams({ tab: key });
        }}
        activeKey={searchParams.get("tab") ?? undefined}
      />
    </>
  );
}
