import { useApp } from "@/UseApp";
import { useMutation, useQuery } from "@tanstack/react-query";
import { EditMultipleEntitiesQuantityCapturingInterface } from "./interface";

export const useEditMultipleEntitiesQuantityCapturingQuery = (
  projectId: number,
  attendanceId?: number,
  componentFeatureId?: number,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: [
      "editMultipleEntitiesQuantityCapturing",
      projectId,
      attendanceId,
      componentFeatureId,
    ],
    queryFn: () =>
      axiosGet<EditMultipleEntitiesQuantityCapturingInterface, unknown>(
        `/tools/projects/${projectId}/attendances/${attendanceId}/features/${componentFeatureId}/quantities`,
      ),
    enabled: !!projectId && !!attendanceId && !!componentFeatureId,
  });
};

export const useCreateEditMultipleEntitiesQuantityCapturingMutation = (
  projectId: number,
  attendanceId?: number,
  componentFeatureId?: number,
) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: [
      "createEditMultipleEntitiesQuantityCapturing",
      projectId,
      attendanceId,
      componentFeatureId,
    ],
    mutationFn: (data: {
      dataUuid: string;
      dataTimestamp: string;
      values: {
        featureQuantityId: number;
        value: number | null;
      }[];
    }) =>
      axiosPost(
        `/tools/projects/${projectId}/attendances/${attendanceId}/features/${componentFeatureId}/quantities`,
        data,
      ),
  });
};
