import ItemProductQuantity from "@project/report/ItemProductQuantity.tsx";
import { RecordOrderExchangeInterface } from "./interface.ts";

const CustomerExchangesCell = ({
  orderExchanges,
}: {
  orderExchanges: RecordOrderExchangeInterface[];
}) => {
  const itemProductQuantities = orderExchanges.map((orderExchange) => {
    const { quantity: orderExchangeQuantity, featureSchemeExchange } =
      orderExchange;
    const { name, id } = featureSchemeExchange;
    return {
      id,
      name,
      items: orderExchange.featureSchemeExchange.exchangeProceeds.map(
        (exchangeProceed) => {
          const { projectItem, projectProduct } = exchangeProceed;

          let unitName = "";
          let code = "";
          let name = "";

          if (projectItem) {
            unitName = projectItem.item?.unit?.name ?? "";
            code = projectItem.item?.code ?? "";
            name = projectItem.item?.name ?? "";
          }

          if (projectProduct) {
            unitName = projectProduct.productPackaging?.unit?.name ?? "";
            code = projectProduct.product?.code ?? "";
            name = projectProduct.product?.name ?? "";
          }

          return {
            quantity: exchangeProceed.quantity * orderExchangeQuantity,
            unitName,
            name,
            code,
          };
        },
      ),
    };
  });

  return itemProductQuantities.map(({ name, items }, index) => (
    <div key={index} className={"mt-3"}>
      {name && (
        <p className="font-semibold bg-input-disabled inline pl-1 pr-1">
          {name}
        </p>
      )}

      {items.map(({ quantity, unitName, name, code }, index) => (
        <ItemProductQuantity
          isFirst={index === 0}
          key={index}
          quantity={quantity}
          unitName={unitName}
          name={name}
          code={code}
          classNameMarginTop={"mt-1"}
        />
      ))}
    </div>
  ));
};

export default CustomerExchangesCell;
