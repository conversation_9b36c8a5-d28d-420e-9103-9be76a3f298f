import { ACTIVE_LABEL, CURD, INACTIVE_LABEL } from "@/common/constant.ts";
import TableActionCell from "@/components/TableActionCell.tsx";
import { useApp } from "@/UseApp.tsx";
import {
  DeleteOutlined,
  EditOutlined,
  FileSearchOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined,
  PlusOutlined,
  SyncOutlined,
} from "@ant-design/icons";
import ProjectEmployeeLeaderEmployeeModal from "@project/employee/employee-leader/modal/ProjectEmployeeLeaderEmployeeModal.tsx";
import ProjectEmployeeLeaderOutletModal from "@project/employee/employee-leader/modal/ProjectEmployeeLeaderOutletModal.tsx";
import {
  EmployeeActionEnum,
  ProjectEmployeeUserInterface,
} from "@project/employee/interface.ts";
import ProfileModal from "@project/employee/ProfileModal.tsx";
import {
  useDeleteEmployeeMutation,
  useUpdateEmployeeMutation,
} from "@project/employee/service.ts";
import EmployeeLeaderTabModal from "@project/employee/tabs/employee-leader-tab/EmployeeLeaderTabModal.tsx";
import { RoleInterface } from "@project/role/interface.ts";
import { Modal } from "antd";
import { useCallback, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import EmployeeLeaderChangeLeaderModal from "./EmployeeLeaderChangeLeaderModal";

interface EmployeeLeaderActionCellProps {
  record: ProjectEmployeeUserInterface;
  role: RoleInterface;
  cb: () => void;
}

const EmployeeLeaderActionCell = ({
  record,
  role,
  cb,
}: EmployeeLeaderActionCellProps) => {
  const { showNotification, openDeleteModal } = useApp();
  const projectId = parseInt(useParams().id ?? "0");
  const navigate = useNavigate();

  const [action, setAction] = useState<CURD | null>(null);
  const [selectedProjectEmployeeUser, setSelectedProjectEmployeeUser] =
    useState<ProjectEmployeeUserInterface | undefined>(undefined);
  const [modal, contextHolder] = Modal.useModal();
  const [isModalAddEmployeeOpen, setIsModalAddEmployeeOpen] = useState(false);
  const [isModalAddOutletOpen, setIsModalAddOutletOpen] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [isChangeLeaderOpen, setIsChangeLeaderOpen] = useState(false);

  const updateEmployeeMutation = useUpdateEmployeeMutation(projectId);
  const deleteEmployeeMutation = useDeleteEmployeeMutation(projectId);

  const handleActionEditClick = useCallback(
    (record: ProjectEmployeeUserInterface) => {
      setSelectedProjectEmployeeUser(record);
      setAction(CURD.UPDATE);
    },
    [],
  );

  const handleActionInActiveClick = useCallback(
    (record: ProjectEmployeeUserInterface) => {
      modal.confirm({
        title: `Ngừng hoạt động ${role.name}: ${record.user.name}`,
        content: `Bạn có chắc chắn muốn ngừng hoạt động ${role.name} này?`,
        okText: "Ngừng hoạt động",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await updateEmployeeMutation.mutateAsync({
              id: record.id,
              isActive: false,
            });

            showNotification({
              type: "success",
              message: `Ngừng hoạt động ${role.name} thành công`,
            });

            cb();
          } catch (error) {
            console.error(error);

            showNotification({
              type: "error",
              message: `Ngừng hoạt động ${role.name} thất bại`,
            });
          }
        },
      });
    },
    [cb, modal, role.name, showNotification, updateEmployeeMutation],
  );

  const handleActionActiveClick = useCallback(
    (record: ProjectEmployeeUserInterface) => {
      modal.confirm({
        title: `Kích hoạt ${role.name}: ${record.user.name}`,
        content: `Bạn có chắc chắn muốn kích hoạt ${role.name} này?`,
        okText: "Kích hoạt",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await updateEmployeeMutation.mutateAsync({
              id: record.id,
              isActive: true,
            });

            showNotification({
              type: "success",
              message: `Kích hoạt ${role.name} thành công`,
            });

            cb();
          } catch (error) {
            console.error(error);

            showNotification({
              type: "error",
              message: `Kích hoạt ${role.name} thất bại`,
            });
          }
        },
      });
    },
    [cb, modal, role.name, showNotification, updateEmployeeMutation],
  );

  const handleActionViewOutletClick = useCallback(
    (record: ProjectEmployeeUserInterface) => {
      navigate(`/project/${projectId}/employee-leader/${record.id}/outlet`, {
        state: record,
      });
    },
    [navigate, projectId],
  );

  const handleActionViewEmployeeClick = useCallback(
    (record: ProjectEmployeeUserInterface) => {
      navigate(`/project/${projectId}/employee-leader/${record.id}/employee`, {
        state: record,
      });
    },
    [navigate, projectId],
  );

  const handleActionAddEmployeeClick = useCallback(() => {
    setIsModalAddEmployeeOpen(true);
  }, []);

  const handleActionAddOutletClick = useCallback(() => {
    setIsModalAddOutletOpen(true);
  }, []);

  const handleActionDeleteClick = useCallback(
    (record: ProjectEmployeeUserInterface) => {
      openDeleteModal({
        content: (
          <p>
            Bạn muốn xóa nhân viên{" "}
            <span className={"font-semibold"}>
              {record.user.name} - {record.user.phone}
            </span>{" "}
            khỏi dự án?
          </p>
        ),
        deleteText: "Xác nhận xóa",
        loading: false,
        onCancel(): void {},
        onDelete: async () => {
          await deleteEmployeeMutation.mutateAsync(record.id);
          showNotification({
            type: "success",
            message: "Xóa nhân viên field thành công",
          });
          cb();
        },
        title: `Xóa nhân viên field`,
        titleError: "Không thể xóa nhân viên field",
        contentHeader: (
          <>
            Không thể xóa nhân viên{" "}
            <span className={"font-semibold"}>
              {record.user.name} - {record.user.phone}
            </span>{" "}
            khỏi dự án bởi vì:
          </>
        ),
      });
    },
    [cb, deleteEmployeeMutation, openDeleteModal, showNotification],
  );

  const handleActionProfileClick = useCallback(
    (record: ProjectEmployeeUserInterface) => {
      setSelectedProjectEmployeeUser(record);
      setIsProfileOpen(true);
    },
    [],
  );

  const handleActionChangeLeaderClick = useCallback(
    (record: ProjectEmployeeUserInterface) => {
      setIsChangeLeaderOpen(true);
      setSelectedProjectEmployeeUser(record);
    },
    [],
  );

  const actionItems = [
    {
      key: EmployeeActionEnum.EDIT,
      label: "Chỉnh sửa",
      icon: <EditOutlined />,
    },
    {
      key: EmployeeActionEnum.PROFILE,
      label: "Chỉnh sửa profile",
      icon: <EditOutlined />,
    },
    {
      key: EmployeeActionEnum.ADD_OUTLET,
      label: "Phân bổ outlet cho trưởng nhóm",
      icon: <PlusOutlined />,
    },
    {
      key: EmployeeActionEnum.VIEW_OUTLET,
      label: "Xem outlet trưởng nhóm quản lý",
      icon: <FileSearchOutlined />,
    },
    {
      key: EmployeeActionEnum.ADD_EMPLOYEE,
      label: "Thêm nhân viên vào nhóm",
      icon: <PlusOutlined />,
    },
    {
      key: EmployeeActionEnum.VIEW_EMPLOYEE,
      label: "Xem thành viên trong nhóm",
      icon: <FileSearchOutlined />,
    },
    {
      key: EmployeeActionEnum.CHANGE_LEADER,
      label: "Đổi trưởng nhóm",
      icon: <SyncOutlined />,
    },
    {
      key: EmployeeActionEnum.INACTIVE,
      label: INACTIVE_LABEL,
      icon: <PauseCircleOutlined />,
    },
    {
      key: EmployeeActionEnum.ACTIVE,
      label: ACTIVE_LABEL,
      icon: <PlayCircleOutlined />,
    },
    {
      key: EmployeeActionEnum.DELETE,
      label: "Xóa khỏi dự án",
      icon: <DeleteOutlined />,
    },
  ];

  const actionActions = [
    {
      key: EmployeeActionEnum.EDIT,
      action: handleActionEditClick,
    },
    {
      key: EmployeeActionEnum.ADD_OUTLET,
      action: handleActionAddOutletClick,
    },
    {
      key: EmployeeActionEnum.VIEW_OUTLET,
      action: handleActionViewOutletClick,
    },
    {
      key: EmployeeActionEnum.ADD_EMPLOYEE,
      action: handleActionAddEmployeeClick,
    },
    {
      key: EmployeeActionEnum.VIEW_EMPLOYEE,
      action: handleActionViewEmployeeClick,
    },
    {
      key: EmployeeActionEnum.INACTIVE,
      action: handleActionInActiveClick,
    },
    {
      key: EmployeeActionEnum.ACTIVE,
      action: handleActionActiveClick,
    },
    {
      key: EmployeeActionEnum.DELETE,
      action: handleActionDeleteClick,
    },
    {
      key: EmployeeActionEnum.PROFILE,
      action: handleActionProfileClick,
    },
    {
      key: EmployeeActionEnum.CHANGE_LEADER,
      action: handleActionChangeLeaderClick,
    },
  ];

  const ACTION_ACTIVE = [
    EmployeeActionEnum.EDIT,
    EmployeeActionEnum.ADD_OUTLET,
    EmployeeActionEnum.VIEW_OUTLET,
    EmployeeActionEnum.ADD_EMPLOYEE,
    EmployeeActionEnum.VIEW_EMPLOYEE,
    EmployeeActionEnum.INACTIVE,
    EmployeeActionEnum.DELETE,
    EmployeeActionEnum.PROFILE,
    EmployeeActionEnum.CHANGE_LEADER,
  ];
  const ACTION_INACTIVE = [
    EmployeeActionEnum.EDIT,
    EmployeeActionEnum.ADD_OUTLET,
    EmployeeActionEnum.VIEW_OUTLET,
    EmployeeActionEnum.ADD_EMPLOYEE,
    EmployeeActionEnum.VIEW_EMPLOYEE,
    EmployeeActionEnum.ACTIVE,
    EmployeeActionEnum.DELETE,
    EmployeeActionEnum.PROFILE,
    EmployeeActionEnum.CHANGE_LEADER,
  ];

  const actionKeys = record.isActive ? ACTION_ACTIVE : ACTION_INACTIVE;
  const items = actionItems.filter((item) => actionKeys.includes(item.key));

  return (
    <>
      <TableActionCell actions={actionActions} items={items} record={record} />

      {isProfileOpen && (
        <ProfileModal
          isOpen={isProfileOpen}
          onCloseCb={() => {
            setSelectedProjectEmployeeUser(undefined);
            setIsProfileOpen(false);
          }}
          projectEmployeeUser={selectedProjectEmployeeUser}
          projectId={projectId}
        />
      )}

      {isModalAddOutletOpen && (
        <ProjectEmployeeLeaderOutletModal
          isOpen={isModalAddOutletOpen}
          setIsOpen={setIsModalAddOutletOpen}
          leader={record}
          projectId={projectId}
          cb={() => {
            cb();
          }}
        />
      )}

      {!!action && (
        <EmployeeLeaderTabModal
          action={action}
          role={role}
          cb={() => {
            setAction(null);
            cb();
          }}
          cancelCb={() => setAction(null)}
          selectedProjectEmployeeUser={selectedProjectEmployeeUser}
        />
      )}

      {isModalAddEmployeeOpen && (
        <ProjectEmployeeLeaderEmployeeModal
          isOpen={isModalAddEmployeeOpen}
          setIsOpen={setIsModalAddEmployeeOpen}
          leader={record}
          projectId={projectId}
          cb={() => {
            cb();
          }}
        />
      )}

      {isChangeLeaderOpen && selectedProjectEmployeeUser && (
        <EmployeeLeaderChangeLeaderModal
          isOpen={isChangeLeaderOpen}
          leader={selectedProjectEmployeeUser}
          projectId={projectId}
          cancelCb={() => {
            setIsChangeLeaderOpen(false);
            setSelectedProjectEmployeeUser(undefined);
          }}
        />
      )}

      {contextHolder}
    </>
  );
};

export default EmployeeLeaderActionCell;
