import { useApp } from "@/UseApp.tsx";
import { AbstractFilterInterface } from "@/common/interface.ts";
import { useMutation, useQuery } from "@tanstack/react-query";
import { ClientInterface } from "./interface.ts";

export const useClientsQuery = (
  filter: AbstractFilterInterface & { getInActive?: boolean },
  enabled?: boolean,
) => {
  const { axiosGet } = useApp();
  const { getInActive, ...restFilter } = filter;
  const queryFilter = {
    ...(getInActive ? {} : { isActive: true }),
    ...restFilter,
  };

  return useQuery({
    queryKey: ["clients", queryFilter],
    queryFn: async () =>
      axiosGet<{ entities: ClientInterface[]; count: number }, unknown>(
        "/clients",
        queryFilter,
      ),
    enabled,
  });
};

export const useCreateClientMutation = () => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createClient"],
    mutationFn: async (data: {
      name: string;
      email?: string;
      phone?: string;
      legalName?: string;
    }) => axiosPost<ClientInterface, unknown>("/clients", data),
  });
};

export const useUpdateClientMutation = () => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["updateClient"],
    mutationFn: ({
      id,
      data,
    }: {
      id: number;
      data: {
        name?: string;
        email?: string;
        phone?: string;
        legalName?: string;
        isActive?: boolean;
      };
    }) => axiosPatch<ClientInterface, unknown>(`/clients/${id}`, data),
  });
};

export const useDeleteClientMutation = () => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deleteClient"],
    mutationFn: (id: number) => axiosDelete(`/clients/${id}`),
  });
};
