import { <PERSON>, <PERSON><PERSON>, Row, Skeleton } from "antd";
import { Suspense, useCallback, useEffect, useMemo, useState } from "react";
import { Outlet, useLocation, useNavigate } from "react-router-dom";

const ProjectKPILayout = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const [activeKey, setActiveKey] = useState<string>("project");

  const setRouteActive = useCallback(
    (value: string) => {
      navigate(value);
    },
    [navigate],
  );

  const items: {
    label: JSX.Element | string;
    key: string;
    type: "group" | "submenu";
    children: { key: string; icon?: JSX.Element; label: string }[];
  }[] = useMemo(() => {
    return [
      {
        key: "masterProject",
        label: "Tổng dự án",
        children: [
          { key: "project", label: "• KPI tổng dự án" },
          { key: "project/channel", label: "• K<PERSON> theo kênh" },
          { key: "project/province", label: "• KPI theo Tỉnh/ TP" },
          { key: "project/rolling", label: "• Timegone theo hit" },
          { key: "project/outlet", label: "• KPI theo outlet" },
          { key: "project/outlet/sales", label: "• KPI doanh số theo outlet" },
        ],
        type: "submenu",
      },
      // {
      //   key: "agency",
      //   label: "Agency",
      //   children: [
      //     { key: "agency/project", label: "• KPI tổng dự án" },
      //     { key: "agency/channel", label: "• KPI theo kênh" },
      //     { key: "agency/province", label: "• KPI theo Tỉnh/ TP" },
      //     { key: "agency/outlet", label: "• KPI theo outlet" },
      //   ],
      //   type: "submenu",
      // },
    ];
  }, []);

  useEffect(() => {
    const activeItem = items
      .flatMap((item) => item.children)
      .find((item) => location.pathname.endsWith(item.key));
    if (activeItem) {
      setActiveKey(activeItem.key.toString());
    }
  }, [items, setActiveKey, location.pathname]);

  return (
    <>
      <h2>Cấu hình KPI</h2>
      <div className="bg-white pt-10 pl-10 rounded pb-5 mt-5 pr-10">
        <Row className="" justify={"space-between"}>
          <Col span={4}>
            <Menu
              className="bg-[#FAFAFA] config-customer-menu"
              items={items}
              mode="inline"
              style={{ borderInline: "none" }}
              onClick={({ key }) => setRouteActive(key)}
              selectedKeys={[activeKey]}
              defaultOpenKeys={["masterProject", "agency"]}
            />
          </Col>
          <Col span={19}>
            <Suspense fallback={<Skeleton active />}>
              <Outlet />
            </Suspense>
          </Col>
        </Row>
      </div>
    </>
  );
};

export default ProjectKPILayout;
