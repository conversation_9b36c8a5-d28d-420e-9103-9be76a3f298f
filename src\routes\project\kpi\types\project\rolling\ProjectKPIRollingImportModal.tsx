import { DATE_FORMAT } from "@/common/constant";
import {
  createFileAndDownLoad,
  createTemplateMultipleSheetAndDownload,
} from "@/common/export-excel.helper";
import { formatNumber, getErrorMessageFromAxiosError } from "@/common/helper";
import { setExcelError, validateExcelData } from "@/common/import-excel.helper";
import { ImportStatus } from "@/common/interface";
import { ChannelInterface } from "@/routes/channel/interface";
import { useProvincesQuery } from "@/routes/location/service";
import { useProjectChannelQuery } from "@/routes/project/general/services";
import { findChannelByName } from "@/routes/project/outlet/service";
import { useApp } from "@/UseApp";
import {
  CheckCircleFilled,
  CheckCircleTwoTone,
  CloseCircleTwoTone,
  CloseOutlined,
  DownloadOutlined,
  InboxOutlined,
  LoadingOutlined,
} from "@ant-design/icons";
import { <PERSON><PERSON>, <PERSON><PERSON>, Form, Modal, Space, Table } from "antd";
import Dragger from "antd/es/upload/Dragger";
import { UploadFile } from "antd/lib";
import { AxiosError } from "axios";
import dayjs from "dayjs";
import Excel from "exceljs";
import Joi from "joi";
import React, { useCallback, useRef, useState } from "react";
import { Importer } from "xlsx-import/lib/Importer";
import { ItemKpiTypeEnum } from "../../../interface";
import { useCreateKpiRollingMutation } from "./service";

interface ProjectTimegoneImportModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  projectId: number;
  cb: () => void;
}

interface RollingImportDataInterface {
  targetDate: string;
  channel: string;
  targetName: string;
  province: string;
  targetKpi: number;
  status?: ImportStatus;
  errorMessage?: string;
  id: number;
  channelId?: number;
  provinceId?: number;
}

const rollingImportDataScheme = Joi.object({
  targetDate: Joi.date().required(),
  channel: Joi.string().trim().required(),
  province: Joi.string().trim().required(),
  targetKpi: Joi.number().required(),
  targetName: Joi.string().trim().required(),

  id: Joi.number().required(),
  status: Joi.string()
    .valid("pending", "success", "error")
    .allow("")
    .optional(),
  errorMessage: Joi.string().allow("").optional(),
  channelId: Joi.number().allow("").optional(),
  provinceId: Joi.number().allow("").optional(),
});

const headers = [
  { index: 1, header: "Ngày thực hiện", key: "targetDate" },
  { index: 2, header: "Kênh thực hiện", key: "channel" },
  { index: 3, header: "Tên outlet", key: "targetName" },
  { index: 4, header: "Tỉnh/ TP", key: "province" },
  { index: 5, header: "Target hit sampling", key: "targetKpi" },
];

const ProjectKPIRollingImportModal = ({
  open,
  setOpen,
  projectId,
  cb,
}: ProjectTimegoneImportModalProps) => {
  const { axiosGet } = useApp();

  const [uploadForm] = Form.useForm();
  const [isDataVisible, setIsDataVisible] = useState(false);
  const [importData, setImportData] = useState<RollingImportDataInterface[]>(
    [],
  );
  const [displayData, setDisplayData] = useState<RollingImportDataInterface[]>(
    [],
  );
  const [successfulImports, setSuccessfulImports] = useState<
    RollingImportDataInterface[]
  >([]);
  const [failedImports, setFailedImports] = useState<
    RollingImportDataInterface[]
  >([]);
  const [isImporting, setIsImporting] = useState(false);
  const tableRef: Parameters<typeof Table>[0]["ref"] = React.useRef(null);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [isFileError, setIsFileError] = useState(false);
  const [excelErrorMessage, setExcelErrorMessage] = useState<string>("");
  const channels = useRef<ChannelInterface[]>([]);

  const projectChannelQuery = useProjectChannelQuery(projectId);
  const provincesQuery = useProvincesQuery();

  const createKpiRollingMutation = useCreateKpiRollingMutation(projectId);

  const downloadTemplate = useCallback(async () => {
    const fileName = `Import timegone hit ${dayjs().format("DDMMYY")}`;

    createTemplateMultipleSheetAndDownload({
      columns: headers,
      fileName,
      descriptionColumns: [
        {
          color: "Ff0000",
          message: "Bắt buộc điền",
        },
        {
          color: "Ff0000",
          message: "Bắt buộc điền",
        },
        {
          color: "Ff0000",
          message: "Bắt buộc điền",
        },
        {
          color: "Ff0000",
          message: "Bắt buộc điền",
        },
        {
          color: "Ff0000",
          message: "Bắt buộc điền",
        },
      ],
      extraWorksheetsData: [
        {
          name: "Kênh thực hiện",
          headers: ["id", "Kênh thực hiện"],
          data:
            projectChannelQuery.data?.map((channel) => [
              channel.id,
              channel.name,
            ]) ?? [],
        },
        {
          name: "Tỉnh TP",
          headers: ["id", "Tỉnh/TP"],
          data:
            provincesQuery.data?.map((province) => [
              province.id,
              province.name,
            ]) ?? [],
        },
      ],
      columnsTypeListWithSheet: [
        {
          index: 2,
          range: `'Kênh thực hiện'!$B$2:$B$${(projectChannelQuery.data?.length ?? 0) + 1}`,
        },
        {
          index: 4,
          range: `'Tỉnh TP'!$B$2:$B$${(provincesQuery.data?.length ?? 0) + 1}`,
        },
      ],
    });
  }, [projectChannelQuery.data, provincesQuery.data]);

  const onModalShowDataClose = useCallback(() => {
    setOpen(false);
    uploadForm.resetFields();
    setIsDataVisible(false);
    setIsImporting(false);
    setImportData([]);
    setDisplayData([]);
    setSuccessfulImports([]);
    setFailedImports([]);
    setFileList([]);
    setIsFileError(false);
    setExcelErrorMessage("");
    cb();
  }, [cb, setOpen, uploadForm]);

  const processExcelFile = useCallback(async (file: File) => {
    const buffer = await file.arrayBuffer();
    const workbook = new Excel.Workbook();
    await workbook.xlsx.load(buffer);

    const config = {
      worksheet: "Data",
      type: "list",
      rowOffset: 0,
      columns: headers,
    };

    const importer = new Importer(workbook);
    const data = importer.getAllItems<RollingImportDataInterface>(config);

    validateExcelData(data, headers);

    setDisplayData(data.map((item, index) => ({ ...item, id: index })));
    setImportData(data.map((item, index) => ({ ...item, id: index })));
  }, []);

  function beforeUploadHandler(file: File) {
    if (file) {
      processExcelFile(file).catch(({ message }: { message: string }) => {
        setExcelError(
          message,
          setFileList,
          setIsFileError,
          setExcelErrorMessage,
        );
      });
    }

    return false;
  }

  const importHandler = useCallback(async () => {
    const validateChannel = async (
      value: Promise<RollingImportDataInterface>,
    ) => {
      const item = await value;
      const { channel } = item;
      const foundChannel = await findChannelByName(
        axiosGet,
        projectId,
        channels,
        channel,
      );
      if (!foundChannel) {
        throw new Error(`Kênh ${channel} không tồn tại`);
      }
      item.channelId = foundChannel.id;
      return item;
    };

    const validateProvince = async (
      value: Promise<RollingImportDataInterface>,
    ) => {
      const item = await value;
      const { province } = item;
      const foundProvince = provincesQuery.data?.find(
        (provinceItem) => provinceItem.name === province,
      );
      if (!foundProvince) {
        throw new Error(`Tình ${province} không tồn tại`);
      }
      item.provinceId = foundProvince.id;
      return item;
    };

    const processItem = async (item: RollingImportDataInterface) => {
      const itemIndex = displayData.findIndex(
        (dataItem) => dataItem.id === item.id,
      );

      if (itemIndex === -1) {
        console.warn(`Item with index ${item.id} not found in dataShow`);
        return;
      }

      if (tableRef.current) {
        tableRef.current?.scrollTo({ index: itemIndex });
      }
      displayData[itemIndex].status = "doing";
      setDisplayData([...displayData]);

      await rollingImportDataScheme
        // .custom(validateDate)
        .custom(validateChannel)
        .custom(validateProvince)
        .validateAsync(item)
        .then(
          async ({
            channelId,
            provinceId,
            targetDate,
            targetKpi,
            targetName,
          }: RollingImportDataInterface) => {
            try {
              await createKpiRollingMutation.mutateAsync({
                channelId: channelId as number,
                provinceId: provinceId as number,
                targetDate: dayjs(targetDate).toISOString(),
                targetKpi,
                targetName,
                type: ItemKpiTypeEnum.HIT,
              });

              displayData[itemIndex].status = "success";
              setDisplayData([...displayData]);
              setSuccessfulImports((importSuccess) => [...importSuccess, item]);
            } catch (error) {
              if (error instanceof AxiosError) {
                displayData[itemIndex].status = "error";
                displayData[itemIndex].errorMessage =
                  getErrorMessageFromAxiosError(error);
                setDisplayData([...displayData]);
                setFailedImports((importError) => [
                  ...importError,
                  displayData[itemIndex],
                ]);
              }
            }
          },
        )
        .catch((error: Error) => {
          displayData[itemIndex].status = "error";
          displayData[itemIndex].errorMessage = error.message;
          setDisplayData([...displayData]);
          setFailedImports((importError) => [
            ...importError,
            displayData[itemIndex],
          ]);
        });
    };

    const processItems = async (items: RollingImportDataInterface[]) => {
      setIsImporting(true);
      for (const item of items) {
        await processItem(item);
      }
      setIsImporting(false);
    };

    await processItems(importData);
  }, [
    axiosGet,
    createKpiRollingMutation,
    displayData,
    importData,
    projectId,
    provincesQuery.data,
  ]);

  const handleImportErrorClick = useCallback(async () => {
    const fileName = `Import kpi timegone loi `;
    const importErrorStrings = failedImports.map((item) => [
      ...headers.map(
        (header) => item[header.key as keyof typeof item]?.toString() ?? "",
      ),
      item.errorMessage ?? "",
    ]);

    const headerStrings = [...headers.map((header) => header.header), "Lỗi"];

    await createFileAndDownLoad({
      data: importErrorStrings,
      headers: headerStrings,
      fileName,
    });
  }, [failedImports]);

  return (
    <>
      <Modal
        open={open}
        closable={false}
        styles={{ content: { padding: 0 } }}
        footer={false}
      >
        <div className={"pl-10 pr-10"}>
          <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
            <h2 className="text-neutral-700 text-2xl font-semibold ">
              Import plan
            </h2>
            <div className="pt-5">
              <Button
                type="link"
                onClick={onModalShowDataClose}
                size="large"
                icon={<CloseOutlined />}
              />
            </div>
          </div>
          <Alert
            message={
              <>
                <span>
                  Để thực hiện thêm mới outlet bằng cách import file excel bạn
                  cần sử dụng đúng template.{" "}
                </span>
                <Button
                  className="ml-0 pl-0 text-blue"
                  type="link"
                  onClick={downloadTemplate}
                >
                  Tải file template
                </Button>
              </>
            }
            type="info"
            showIcon
          />
          <br />

          <Form form={uploadForm}>
            <Form.Item name={"upload"}>
              <Dragger
                className={"pt-5"}
                listType={"picture"}
                accept={".xlsx, .xlsm"}
                multiple={false}
                beforeUpload={beforeUploadHandler}
                maxCount={1}
                fileList={fileList}
                onChange={({ fileList }) => {
                  if (fileList.length === 0) {
                    setIsFileError(false);
                  }
                  setFileList(fileList);
                }}
              >
                <p className="ant-upload-drag-icon">
                  <InboxOutlined />
                </p>
                <p className="ant-upload-text">
                  Kéo thả hoặc chọn file để tải lên
                </p>
                <p className="ant-upload-hint">Định dạng hỗ trợ: xlsm, xlsx</p>
              </Dragger>
              {isFileError && (
                <p className={"text-[#F73A3A]"}>{excelErrorMessage}</p>
              )}
            </Form.Item>
          </Form>
        </div>

        <div className="flex justify-end gap-4 py-4 rounded max-md:max-w-full max-md:flex-wrap pb-4 pl-10 pr-10 bg-[#F7F8FA]">
          <Button htmlType="button" onClick={onModalShowDataClose}>
            Đóng
          </Button>
          <Button
            htmlType="submit"
            type={"primary"}
            disabled={fileList.length === 0 || isFileError}
            onClick={() => {
              setIsDataVisible(true);
              setOpen(false);
              importHandler();
            }}
          >
            Import
          </Button>
        </div>
      </Modal>
      <Modal
        open={isDataVisible}
        width={"90%"}
        closable={false}
        footer={false}
        styles={{ content: { padding: 0 } }}
        closeIcon={null}
      >
        <div className={"pl-10 pr-10"}>
          <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
            <h2 className="text-neutral-700 text-2xl font-semibold ">
              Import outlet
            </h2>
            <div className="pt-5">
              <Button
                type="link"
                onClick={onModalShowDataClose}
                size="large"
                icon={<CloseOutlined />}
              />
            </div>
          </div>
        </div>
        <div className={"pl-10 pr-10 mb-8"}>
          <p className={"m-0"}>
            Bạn đã tải lên {importData.length} dòng dữ liệu, phía dưới là kết
            quả sau khi import
          </p>

          <Space className="pb-3">
            <Alert
              type="success"
              showIcon
              message={`Import thành công: ${successfulImports.length}`}
            />
            <Alert
              type="error"
              showIcon
              message={`Import thất bại: ${failedImports.length}`}
            />
          </Space>

          <Table
            ref={tableRef}
            rowKey={(record) => record.targetName as React.Key}
            dataSource={displayData}
            scroll={{ x: "max-content", y: 450 }}
            columns={[
              {
                dataIndex: "targetDate",
                title: "Ngày thực hiện",
                className: "min-w-[100px]",
                render: (date) => dayjs(date).format(DATE_FORMAT),
              },
              {
                dataIndex: "channel",
                title: "Kênh thực hiện",
                className: "min-w-[100px]",
              },
              {
                dataIndex: "targetName",
                title: "Tên outlet",
                className: "min-w-[50px]",
              },
              {
                dataIndex: "province",
                title: "Tỉnh/ TP",
                className: "min-w-[100px]",
              },
              {
                dataIndex: "targetKpi",
                title: "Target hit sampling",
                className: "min-w-[100px]",
                render: (value: number) => formatNumber(value),
              },

              {
                title: "Trạng thái",
                fixed: "right",
                dataIndex: "status",
                className: "min-w-[100px]",
                render: (value: ImportStatus, record) => {
                  switch (value) {
                    case "doing":
                      return (
                        <>
                          <LoadingOutlined /> Đang import
                        </>
                      );
                    case "success":
                      return (
                        <>
                          <CheckCircleTwoTone twoToneColor="#52c41a" /> Đã
                          import
                        </>
                      );
                    case "error":
                      return (
                        <>
                          <CloseCircleTwoTone twoToneColor="#f5222d" />{" "}
                          {record.errorMessage}
                        </>
                      );
                    default:
                      return null;
                  }
                },
              },
            ]}
            pagination={false}
          />
        </div>

        <div className="flex justify-between gap-4 py-4 rounded max-md:max-w-full max-md:flex-wrap pb-4 pl-10 pr-10 bg-[#F7F8FA]">
          {failedImports.length + successfulImports.length <
          importData.length ? (
            <>
              {isImporting && (
                <>
                  <p>
                    <LoadingOutlined /> Đang tiến hành import
                  </p>
                  <p>Vui lòng không tắt popup cho đến khi hoàn thành</p>
                </>
              )}
            </>
          ) : (
            <>
              <p>
                <CheckCircleFilled style={{ color: "green" }} /> Hoàn thành quá
                trình import
              </p>
              <Space>
                <Button type="default" onClick={onModalShowDataClose}>
                  Đóng
                </Button>
                <Button
                  type="default"
                  icon={<DownloadOutlined />}
                  onClick={handleImportErrorClick}
                  disabled={failedImports.length === 0}
                >
                  Tải data lỗi import
                </Button>
              </Space>
            </>
          )}
        </div>
      </Modal>
    </>
  );
};

export default ProjectKPIRollingImportModal;
