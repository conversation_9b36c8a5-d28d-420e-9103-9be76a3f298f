import { CURD, SELECT_ALL, SELECT_ALL_LABEL } from "@/common/constant";
import { filterOption } from "@/common/helper.ts";
import FilterComponent from "@/components/FilterComponent.tsx";
import InnerContainer from "@/components/InnerContainer/InnerContainer.tsx";
import { renderTableCell } from "@/components/table-cell";
import { renderTableOptionCell } from "@/components/table-option-cell";
import { useUrlFilters } from "@/hooks/useUrlFilters.ts";
import { useApp } from "@/UseApp.tsx";
import {
  AlignRightOutlined,
  CloseOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import { Button, ColorPicker, Form, Input, Modal, Select, Table } from "antd";
import { useForm } from "antd/es/form/Form";
import { ColumnsType } from "antd/es/table";
import _ from "lodash";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { createSearchParams, useNavigate } from "react-router-dom";
import { useClientsQuery } from "../client/service.ts";
import ProductModal from "../product/ProductModal.tsx";
import { BrandInterface } from "./interface.ts";
import {
  useBrandsQuery,
  useCreateBrandMutation,
  useDeleteBrandMutation,
  useUpdateBrandMutation,
} from "./services.ts";

const BrandPage: React.FC = () => {
  const { showNotification, openDeleteModal } = useApp();
  const navigate = useNavigate();

  const [searchForm] = Form.useForm();
  const [form] = Form.useForm();
  const [isModalAddOrUpdateOpen, setIsModalAddOrUpdateOpen] = useState(false);
  const [formAction, setFormAction] = useState<CURD | null>(null);
  const [modalTitle, setModalTitle] = useState("");
  const [modal, contextHolder] = Modal.useModal();
  const [productForm] = useForm();
  const [isProductModalOpen, setIsProductModalOpen] = useState(false);
  const [selectedBrand, setSelectedBrand] = useState<
    BrandInterface | undefined
  >(undefined);
  const { filter, currentPage, pageSize, handleSearch, getPaginationProps } =
    useUrlFilters({
      formInstance: searchForm,
      handleSearchCallback: () => {
        brandsQuery.refetch();
      },
    });

  const clientsQuery = useClientsQuery({
    take: 0,
    skip: 0,
  });
  const brandsQuery = useBrandsQuery({
    ...filter,
    take: pageSize,
    skip: (currentPage - 1) * pageSize,
    getInActive: true,
  });

  const updateBrandMutation = useUpdateBrandMutation();
  const deleteBrandMutation = useDeleteBrandMutation();
  const createBrandMutation = useCreateBrandMutation();

  const handleBtnEditClick = useCallback(
    (record: BrandInterface) => {
      setSelectedBrand(record);
      setIsModalAddOrUpdateOpen(true);
      setFormAction(CURD.UPDATE);
      setModalTitle("Cập nhật nhãn hàng");
      form.setFieldsValue({ ...record, clientId: record.client.id });
    },
    [form],
  );

  const handleBtnInactiveClick = useCallback(
    (record: BrandInterface) => {
      modal.confirm({
        title: `Ngừng hoạt động brand: ${record.name}`,
        content: "Bạn có chắc chắn muốn ngừng hoạt động brand này?",
        okText: "Ngừng hoạt động",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await updateBrandMutation.mutateAsync({
              id: record.id,
              isActive: false,
            });

            showNotification({
              type: "success",
              message: "Ngừng hoạt động brand thành công",
            });

            brandsQuery.refetch();
          } catch (error) {
            console.error(error);

            showNotification({
              type: "error",
              message: "Ngừng hoạt động brand thất bại",
            });
          }
        },
      });
    },
    [brandsQuery, modal, showNotification, updateBrandMutation],
  );

  const handleBtnActiveClick = useCallback(
    (record: BrandInterface) => {
      modal.confirm({
        title: `Kích hoạt brand: ${record.name}`,
        content: "Bạn có chắc chắn muốn kích hoạt brand này?",
        okText: "Kích hoạt",
        cancelText: "Hủy",
        onOk: async () => {
          try {
            await updateBrandMutation.mutateAsync({
              id: record.id,
              isActive: true,
            });

            showNotification({
              type: "success",
              message: "Kích hoạt brand thành công",
            });
          } catch (error) {
            console.error(error);
            showNotification({
              type: "error",
              message: "Kích hoạt brand thất bại",
            });
          }

          brandsQuery.refetch();
        },
      });
    },
    [brandsQuery, modal, showNotification, updateBrandMutation],
  );

  const handleBtnDeleteClick = useCallback(
    (record: BrandInterface) => {
      openDeleteModal({
        content: (
          <>
            <p>
              Nhãn hàng sẽ được xóa khỏi hệ thống vĩnh viễn và không thể khôi
              phục
            </p>
            <p>
              Bạn vẫn muốn xóa nhãn hàng{" "}
              <span className={"font-semibold"}>{record.name}</span> khỏi hệ
              thống?
            </p>
          </>
        ),
        deleteText: "Xác nhận xóa",
        loading: deleteBrandMutation.isPending,
        onCancel(): void {},
        onDelete: async () => {
          await deleteBrandMutation.mutateAsync(record.id);
          showNotification({
            type: "success",
            message: "Xóa nhãn hàng thành công",
          });
          brandsQuery.refetch();
        },
        title: `Xóa nhãn hàng`,
        titleError: "Không thể xóa nhãn hàng",
        contentHeader: (
          <>
            Không thể xóa nhãn hàng{" "}
            <span className="font-semibold">{record.name}</span> bởi vì:
          </>
        ),
      });
    },
    [brandsQuery, deleteBrandMutation, openDeleteModal, showNotification],
  );

  const handleBtnAddProductClick = (record: BrandInterface) => {
    setIsProductModalOpen(true);
    setModalTitle("Thêm sản phẩm");
    productForm.setFields([
      {
        name: "brandId",
        value: record.id,
      },
      {
        name: "clientId",
        value: record.client.id,
      },
    ]);
  };

  const handleBtnViewProductClick = (record: BrandInterface) => {
    navigate({
      pathname: "/product",
      search: createSearchParams({
        filterValue: record.name,
        filterField: "brand.name",
      }).toString(),
    });
  };

  const extraActionInActive = {
    items: [
      {
        key: "VIEW_PRODUCT",
        label: "Xem sản phẩm",
        icon: <AlignRightOutlined />,
      },
    ],
    actions: [
      {
        key: "VIEW_PRODUCT",
        action: handleBtnViewProductClick,
      },
    ],
  };

  const extraActionActive = {
    items: [
      {
        key: "ADD_PRODUCT",
        label: "Thêm sản phẩm",
        icon: <PlusOutlined />,
      },
      {
        key: "VIEW_PRODUCT",
        label: "Xem sản phẩm",
        icon: <AlignRightOutlined />,
      },
    ],
    actions: [
      {
        key: "ADD_PRODUCT",
        action: handleBtnAddProductClick,
      },
      {
        key: "VIEW_PRODUCT",
        action: handleBtnViewProductClick,
      },
    ],
  };

  const columns: ColumnsType<BrandInterface> = [
    {
      title: "Tên nhãn hàng",
      key: "name",
      dataIndex: "name",
      render: renderTableCell,
      className: "min-w-[100px]",
    },
    {
      key: "products.name",
      title: "Sản phẩm trong nhãn hàng",
      dataIndex: "products",
      render: (value, record, index) => {
        return renderTableCell(
          value,
          record,
          index,
          "array",
          3,
          "sản phẩm khác",
        );
      },
      className: "min-w-[100px]",
      onCell: (record) => {
        return {
          onClick: () => {
            navigate({
              pathname: "/product",
              search: createSearchParams({
                filterValue: record.name,
                filterField: "brand.name",
                clientId: record.client.id.toString(),
              }).toString(),
            });
          },
        };
      },
    },
    {
      title: "Mô tả",
      dataIndex: "description",
      className: "min-w-[100px]",
    },
    {
      title: "Client",
      key: "client",
      dataIndex: "client",
      className: "min-w-[100px]",
      render: renderTableCell,
    },
    {
      key: "isActive",
      title: "Tình trạng",
      className: "min-w-[100px]",
      dataIndex: "isActive",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "isActive");
      },
    },
    {
      key: "createdAt",
      title: "Thời gian tạo",
      className: "min-w-[100px]",
      dataIndex: "createdAt",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "datetime");
      },
    },
    {
      key: "createdByUser.name",
      className: "min-w-[100px]",
      title: "Người tạo",
      dataIndex: "createdByUser",
      render: renderTableCell,
    },
    {
      key: "updatedAt",
      title: "Thời gian cập nhật",
      className: "min-w-[100px]",
      dataIndex: "updatedAt",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "datetime");
      },
    },
    {
      key: "updatedByUser.name",
      title: "Người cập nhật",
      className: "min-w-[100px]",
      dataIndex: "updatedByUser",
      render: renderTableCell,
    },
    {
      key: "actions",
      render: (_, record) => {
        const extraAction = record.isActive
          ? extraActionActive
          : extraActionInActive;

        return renderTableOptionCell(
          record,
          handleBtnEditClick,
          handleBtnInactiveClick,
          handleBtnActiveClick,
          handleBtnDeleteClick,
          extraAction,
        );
      },
      fixed: "right",
      width: 50,
    },
  ];

  const filterOptions = columns
    .filter(
      (item) =>
        !["isActive", "updatedAt", "createdAt", "actions", "client"].includes(
          item.key as string,
        ),
    )
    .map((column) => {
      return {
        label: column.title as string,
        value: column.key as string | number,
      };
    });

  filterOptions.unshift({ label: SELECT_ALL_LABEL, value: SELECT_ALL });

  const onCancel = useCallback(() => {
    setIsModalAddOrUpdateOpen(false);
    form.resetFields();
    setSelectedBrand(undefined);
  }, [form]);

  const handleAddOrUpdateFormSubmit = useCallback(async () => {
    try {
      await form.validateFields();
      const data = form.getFieldsValue();
      const id = form.getFieldValue("id");

      const { backgroundColor, foregroundColor } = data;

      data.backgroundColor =
        typeof backgroundColor === "string"
          ? backgroundColor
          : backgroundColor?.toHexString();

      data.foregroundColor =
        typeof foregroundColor === "string"
          ? foregroundColor
          : foregroundColor?.toHexString();

      switch (formAction) {
        case CURD.CREATE:
          await createBrandMutation.mutateAsync(data);

          showNotification({
            type: "success",
            message: "Thêm nhãn hàng thành công",
          });
          break;

        case CURD.UPDATE:
          await updateBrandMutation.mutateAsync({ ...data, id });

          showNotification({
            type: "success",
            message: "Cập nhật nhãn hàng thành công",
          });
          break;
        default:
          return;
      }

      form.resetFields();
      setIsModalAddOrUpdateOpen(false);
      setModalTitle("");
      brandsQuery.refetch();

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      const data: { message: { field: string; message: string }[] } =
        error?.response?.data;
      const { message } = data;
      form.setFields([
        { name: message[0].field, errors: [message[0].message] },
      ]);
    }
  }, [
    brandsQuery,
    createBrandMutation,
    form,
    formAction,
    showNotification,
    updateBrandMutation,
  ]);

  const handleAddButtonClick = () => {
    setIsModalAddOrUpdateOpen(true);
    setFormAction(CURD.CREATE);
    setModalTitle("Thêm nhãn hàng");
  };

  const pagination = useMemo(
    () => getPaginationProps(brandsQuery.data?.count),
    [getPaginationProps, brandsQuery.data?.count],
  );

  const loading = useMemo(
    () =>
      brandsQuery.isLoading ||
      brandsQuery.isFetching ||
      createBrandMutation.isPending ||
      updateBrandMutation.isPending ||
      deleteBrandMutation.isPending,
    [
      brandsQuery.isLoading,
      brandsQuery.isFetching,
      createBrandMutation.isPending,
      updateBrandMutation.isPending,
      deleteBrandMutation.isPending,
    ],
  );

  useEffect(() => {
    if (formAction === CURD.CREATE) {
      form.setFieldsValue({
        backgroundColor: "#FFFFFF",
        foregroundColor: "#000000",
      });
    }
  }, [form, formAction]);

  return (
    <div>
      <h2>Nhãn hàng</h2>
      <InnerContainer>
        <FilterComponent
          filterOptions={filterOptions}
          searchHandler={handleSearch}
          handleAddButtonClick={handleAddButtonClick}
          searchForm={searchForm}
          hasClient
          btnLoading={loading}
        />

        <Table
          dataSource={brandsQuery.data?.entities}
          columns={columns}
          rowKey={(record) => record.id}
          scroll={{
            x: "max-content",
            y: pagination.total ? "80vh" : undefined,
          }}
          pagination={pagination}
          loading={loading}
        />
      </InnerContainer>
      <Modal
        open={isModalAddOrUpdateOpen}
        footer={null}
        closeIcon={null}
        styles={{ content: { padding: 0 } }}
      >
        <div className={"pl-10 pr-10"}>
          <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
            <h2 className="text-neutral-700 text-2xl font-semibold ">
              {modalTitle}
            </h2>
            <div className="pt-5">
              <Button
                type="link"
                onClick={onCancel}
                size="large"
                icon={<CloseOutlined />}
              />
            </div>
          </div>
        </div>

        <Form
          name="brandForm"
          onFinish={handleAddOrUpdateFormSubmit}
          layout={"vertical"}
          form={form}
        >
          <div className={"pl-10 pr-10"}>
            <Form.Item name={"id"} hidden={true}></Form.Item>
            <Form.Item
              name="name"
              label={"Tên nhãn hàng"}
              rules={[
                {
                  required: true,
                  message: "Tên nhãn hàng không được để trống",
                },
              ]}
            >
              <Input />
            </Form.Item>

            <Form.Item name="description" label={"Mô tả"}>
              <Input.TextArea />
            </Form.Item>

            {(() => {
              const clientActiveOptions =
                clientsQuery.data?.entities.map((client) => ({
                  label: client.name,
                  value: client.id,
                })) ?? [];

              const { client } = selectedBrand ?? {};
              const selectedOptions = client
                ? [{ label: client?.name, value: client?.id }]
                : [];

              const clientOptions = _.unionBy(
                _.concat(clientActiveOptions, selectedOptions),
                (o) => o.value,
              );

              return (
                <Form.Item
                  name="clientId"
                  label={"Client"}
                  rules={[{ required: true }]}
                >
                  <Select
                    options={clientOptions}
                    showSearch
                    optionFilterProp="children"
                    filterOption={filterOption}
                    disabled={formAction === CURD.UPDATE}
                  />
                </Form.Item>
              );
            })()}

            <Form.Item
              name={"backgroundColor"}
              label={"Màu nền"}
              layout={"horizontal"}
            >
              <ColorPicker
                showText
                defaultValue={"#FFFFFF"}
                mode={"single"}
                disabledAlpha
                format="hex"
              />
            </Form.Item>

            <Form.Item
              name={"foregroundColor"}
              label={"Màu chữ"}
              layout={"horizontal"}
            >
              <ColorPicker
                showText
                defaultValue={"#000000"}
                disabledAlpha
                format="hex"
              />
            </Form.Item>
          </div>
          <div className="flex justify-end gap-4 py-4 rounded max-md:max-w-full max-md:flex-wrap pb-4 pl-10 pr-10 bg-[#F7F8FA]">
            <Button htmlType="button" onClick={onCancel}>
              Đóng
            </Button>
            <Button htmlType="submit" type={"primary"} loading={loading}>
              {formAction === CURD.CREATE ? "Thêm mới" : "Cập nhật"}
            </Button>
          </div>
        </Form>
      </Modal>

      <ProductModal
        isOpen={isProductModalOpen}
        modalTitle={modalTitle}
        setIsOpen={setIsProductModalOpen}
        form={productForm}
        formAction={CURD.CREATE}
        setModalTitle={setModalTitle}
        callback={() => {
          brandsQuery.refetch();
        }}
        disabledSelectBrand={true}
      />
      {contextHolder}
    </div>
  );
};

export default BrandPage;
