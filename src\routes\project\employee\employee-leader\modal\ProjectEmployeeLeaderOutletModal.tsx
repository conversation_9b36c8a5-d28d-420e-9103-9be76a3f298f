import { useApp } from "@/UseApp";
import { DEFAULT_CURRENT_PAGE, DEFAULT_PAGE_SIZE } from "@/common/constant";
import { filterOption } from "@/common/helper";
import FilterClassicComponent from "@/components/FilterClassicComponent";
import UserOptionComponent from "@/components/UserOptionComponent";
import renderStatusOnTopCell from "@/components/renderStatusOnTopCell.tsx";
import { renderTableCell } from "@/components/table-cell";
import { CloseOutlined, SearchOutlined } from "@ant-design/icons";
import {
  useDistrictsQuery,
  useProvincesQuery,
  useWardsQuery,
} from "@location/service";
import { ProjectOutletInterface } from "@project/outlet/interface";
import { useProjectAgencyOutletsQuery } from "@project/outlet/service";
import { Button, Form, Input, Modal, Select, Table } from "antd";
import _ from "lodash";
import { useCallback, useEffect, useMemo, useState } from "react";
import { ProjectEmployeeUserInterface } from "../../interface";
import { useAddProjectOutletToLeaderMutation } from "../../service";

interface ProjectEmployeeLeaderOutletModalProps {
  readonly isOpen: boolean;
  readonly setIsOpen: (isOpen: boolean) => void;
  readonly leader: ProjectEmployeeUserInterface | null;
  readonly projectId: number;
  readonly cb?: () => void;
}

const ProjectEmployeeLeaderOutletModal = (
  props: ProjectEmployeeLeaderOutletModalProps,
) => {
  const { isOpen, setIsOpen, leader, projectId, cb } = props;

  const { loading, showNotification } = useApp();

  const [searchForm] = Form.useForm();
  const [selectedOutletKeys, setSelectedOutletKeys] = useState<React.Key[]>([]);
  const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [selectedProvinceId, setSelectedProvinceId] = useState<number | null>(
    null,
  );
  const [selectedDistrictId, setSelectedDistrictId] = useState<number | null>(
    null,
  );
  const [filter, setFilter] = useState({});

  const provincesQuery = useProvincesQuery();
  const districtsQuery = useDistrictsQuery(selectedProvinceId);
  const wardsQuery = useWardsQuery(selectedProvinceId, selectedDistrictId);

  const projectAgencyOutletsQuery = useProjectAgencyOutletsQuery(
    projectId,
    leader?.projectAgencyId ?? 0,
    {
      ...filter,
      take: pageSize,
      skip: (currentPage - 1) * pageSize,
    },
  );

  useEffect(() => {
    setSelectedOutletKeys(
      projectAgencyOutletsQuery.data?.entities
        .filter((item) => item.projectEmployeeUser?.id === leader?.id)
        .map((item) => item.id) ?? [],
    );
  }, [leader?.id, projectAgencyOutletsQuery.data?.entities]);

  const addProjectOutletToLeaderMutation = useAddProjectOutletToLeaderMutation(
    projectId,
    leader?.id,
  );

  const onCancel = useCallback(() => {
    setIsOpen(false);
    setSelectedOutletKeys([]);
    setCurrentPage(DEFAULT_CURRENT_PAGE);
    setPageSize(DEFAULT_PAGE_SIZE);
  }, [setIsOpen]);

  const searchOnSelectHandler = useCallback(() => {
    setCurrentPage(DEFAULT_CURRENT_PAGE);
    const values = searchForm.getFieldsValue();
    if (_.isEqual(filter, values)) {
      projectAgencyOutletsQuery.refetch();
      return;
    }
    setFilter(values);
  }, [filter, projectAgencyOutletsQuery, searchForm]);

  const handleSelectProvinceChange = useCallback(
    (value: number) => {
      setSelectedProvinceId(value);
      searchForm.resetFields(["districtId", "wardId"]);
    },
    [searchForm],
  );

  const handleSelectDistrictChange = useCallback(
    (value: number) => {
      setSelectedDistrictId(value);
      searchForm.resetFields(["wardId"]);
    },
    [searchForm],
  );

  useEffect(() => {
    if (leader?.projectAgencyId && isOpen) projectAgencyOutletsQuery.refetch();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen, leader?.projectAgencyId]);

  const searchOnSelectContent = (
    <>
      <Form.Item name="keyword">
        <Input
          placeholder="Tìm theo mã, tên outlet, số nhà, tên đường"
          allowClear
          prefix={<SearchOutlined />}
        />
      </Form.Item>

      <Form.Item name="provinceId">
        <Select
          allowClear
          placeholder="Tỉnh/ TP"
          showSearch
          optionFilterProp="children"
          filterOption={filterOption}
          options={provincesQuery.data?.map((province) => ({
            label: province.name,
            value: province.id,
          }))}
          popupMatchSelectWidth={false}
          onChange={handleSelectProvinceChange}
        />
      </Form.Item>

      <Form.Item name="districtId">
        <Select
          allowClear
          placeholder="Quận/ Huyện"
          showSearch
          optionFilterProp="children"
          filterOption={filterOption}
          options={districtsQuery.data?.map((district) => ({
            label: district.name,
            value: district.id,
          }))}
          popupMatchSelectWidth={false}
          onChange={handleSelectDistrictChange}
        />
      </Form.Item>

      <Form.Item name="wardId">
        <Select
          allowClear
          placeholder="Phường/ Xã"
          showSearch
          optionFilterProp="children"
          filterOption={filterOption}
          options={wardsQuery.data?.map((ward) => ({
            label: ward.name,
            value: ward.id,
          }))}
          popupMatchSelectWidth={false}
        />
      </Form.Item>
    </>
  );

  const columns = [
    {
      title: "Mã outlet",
      key: "code",
      dataIndex: "code",
      className: "min-w-[150px]",
    },
    {
      title: "Tên outlet",
      key: "name",
      dataIndex: "name",
      className: "min-w-[150px]",
      render: (value: string, record: ProjectOutletInterface) => {
        return (
          <>
            {record.projectEmployeeUser?.id === leader?.id && (
              <>
                {renderStatusOnTopCell(
                  `${leader?.role.name} hiện tại đang quản lý`,
                  "#008916",
                  "#E5F5E7",
                )}
                <br />
              </>
            )}

            {!!record.projectEmployeeUser?.id &&
              record.projectEmployeeUser.id !== leader?.id && (
                <>
                  {renderStatusOnTopCell(
                    `${leader?.role.name} khác đang quản lý`,
                    "#393939",
                    "#F5F5F5",
                  )}
                  <br />
                </>
              )}

            {!record.isActive && (
              <>
                {renderStatusOnTopCell(`Ngừng hoạt động`, "#DF3C3C", "#FFEEEE")}
                <br />
              </>
            )}
            {value}
          </>
        );
      },
    },
    {
      title: "Số nhà",
      key: "houseNumber",
      dataIndex: "houseNumber",
      className: "min-w-[150px]",
      render: renderTableCell,
    },
    {
      title: "Tên đường",
      key: "streetName",
      dataIndex: "streetName",
      className: "min-w-[150px]",
      render: renderTableCell,
    },
    {
      title: "Tỉnh/ TP",
      key: "province",
      dataIndex: "province",
      className: "min-w-[150px]",
      render: renderTableCell,
    },
    {
      title: "Quận/ Huyện",
      key: "district",
      dataIndex: "district",
      className: "min-w-[150px]",
      render: renderTableCell,
    },
    {
      title: "Phường/ Xã",
      key: "ward",
      dataIndex: "ward",
      className: "min-w-[150px]",
      render: renderTableCell,
    },
    {
      title: "Trưởng nhóm quản lý",
      dataIndex: "projectEmployeeUser",
      className: "min-w-[150px]",
      key: "projectEmployeeUser",
      render: (value?: ProjectEmployeeUserInterface) => {
        const user = value?.user;
        if (!user) return "-";
        return (
          <UserOptionComponent
            avatarUrl={user.picture}
            name={user.name}
            phone={user.phone}
            email={user.email}
          />
        );
      },
    },
  ];

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedOutletKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys: selectedOutletKeys,
    onChange: onSelectChange,
    getCheckboxProps: (record: ProjectOutletInterface) => {
      return {
        disabled: !!record.projectEmployeeUser?.id || !record.isActive,
      };
    },
  };

  const handlePaginationChange = useCallback(
    (page: number) => {
      setCurrentPage(page);
    },
    [setCurrentPage],
  );

  const handlePageSizeChange = useCallback(
    (_current: number, size: number) => {
      setPageSize(size);
      setCurrentPage(DEFAULT_CURRENT_PAGE);
    },
    [setPageSize, setCurrentPage],
  );

  const pagination = useMemo(() => {
    return {
      current: currentPage,
      total: projectAgencyOutletsQuery?.data?.count ?? 0,
      pageSize: pageSize,
      onChange: handlePaginationChange,
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: handlePageSizeChange,
    };
  }, [
    currentPage,
    projectAgencyOutletsQuery?.data?.count,
    pageSize,
    handlePaginationChange,
    handlePageSizeChange,
  ]);

  const newSelectedOutletKeys = useMemo(() => {
    return projectAgencyOutletsQuery?.data?.entities
      .filter((item) => selectedOutletKeys.includes(item.id))
      .filter((item) => !item.projectEmployeeUser?.id)
      .map((item) => item.id);
  }, [projectAgencyOutletsQuery?.data?.entities, selectedOutletKeys]);

  const onConfirm = useCallback(async () => {
    if (newSelectedOutletKeys && newSelectedOutletKeys?.length > 0) {
      await addProjectOutletToLeaderMutation.mutateAsync(newSelectedOutletKeys);

      showNotification({
        type: "success",
        message: "Thêm outlet cho trưởng nhóm quản lý thành công",
      });
      setIsOpen(false);
      searchForm.resetFields();
      setSelectedOutletKeys([]);
      projectAgencyOutletsQuery.refetch();
      if (cb) cb();
    }
  }, [
    addProjectOutletToLeaderMutation,
    cb,
    newSelectedOutletKeys,
    projectAgencyOutletsQuery,
    searchForm,
    setIsOpen,
    showNotification,
  ]);

  return (
    <Modal
      open={isOpen}
      footer={null}
      closeIcon={null}
      styles={{ content: { padding: 0 } }}
      width={1300}
      onCancel={onCancel}
    >
      <div className="pl-10 pr-10">
        <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
          <h2 className="text-neutral-700 text-2xl font-semibold ">
            Thêm outlet cho trưởng nhóm quản lý
          </h2>
          <div className="pt-5">
            <Button
              type="link"
              onClick={onCancel}
              size="large"
              icon={<CloseOutlined />}
            />
          </div>
        </div>
        <h3>Trưởng nhóm</h3>
        <div className="table">
          <div className="border-solid border-2 border-[#C4D6FF] p-2 table-cell bg-[#F0F8FF] rounded">
            <UserOptionComponent
              avatarUrl={leader?.user.picture}
              name={leader?.user.name}
              phone={leader?.user.phone}
              email={leader?.user.email}
              roleName={leader?.role.name}
            />
          </div>
          &nbsp; &nbsp; &nbsp;
          <div className="border-solid border-2 border-[#C4D6FF] p-2 table-cell bg-[#F0F8FF] rounded pl-5 pr-10">
            <p className="text-[#8C8C8D] m-0">Trực thuộc Agency</p>
            <p className="text-[#393939] m-0">
              {leader?.projectAgency?.agency.name}
            </p>
          </div>
        </div>

        <FilterClassicComponent
          searchHandler={searchOnSelectHandler}
          searchForm={searchForm}
          content={searchOnSelectContent}
          className="pb-5 pt-5"
          handleAddButtonClick={() => {
            setIsOpen(true);
          }}
        />

        <Table
          dataSource={projectAgencyOutletsQuery.data?.entities ?? []}
          columns={columns}
          rowKey={(record) => record.id}
          rowSelection={rowSelection}
          scroll={{
            x: "max-content",
            y: pagination.total ? "80vh" : undefined,
          }}
          pagination={pagination}
          loading={loading}
        />
      </div>
      <div className="flex justify-end gap-4 py-4 max-md:max-w-full max-md:flex-wrap pb-4 pl-10 pr-10 bg-[#F7F8FA] rounded">
        <Button htmlType="button" loading={loading} onClick={onCancel}>
          Đóng
        </Button>
        <Button
          htmlType="submit"
          type={"primary"}
          loading={loading}
          disabled={newSelectedOutletKeys?.length === 0}
          onClick={onConfirm}
        >
          {`Thêm ${newSelectedOutletKeys?.length} outlet cho trưởng nhóm quản lý`}
        </Button>
      </div>
    </Modal>
  );
};

export default ProjectEmployeeLeaderOutletModal;
