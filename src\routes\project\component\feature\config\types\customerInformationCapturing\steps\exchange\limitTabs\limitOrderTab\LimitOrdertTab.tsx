import { Table } from "antd";
import { useEffect } from "react";
import { useSchemesQuery } from "../../service";
import InputNumberLimitOrder from "./InputNumberLimitOrder";

interface LimitOrderTabProps {
  componentFeatureId: number;
  activeKey: string;
}
const LimitOrderTab = ({
  componentFeatureId,
  activeKey,
}: LimitOrderTabProps) => {
  const schemesQuery = useSchemesQuery(componentFeatureId);

  useEffect(() => {
    if (activeKey === "order") {
      schemesQuery.refetch();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeKey]);

  return (
    <Table
      loading={schemesQuery.isLoading || schemesQuery.isFetching}
      dataSource={schemesQuery.data?.entities.map((scheme) => ({
        key: scheme.id,
        name: scheme.name,
        maxReceiveQuantity: scheme.maxReceiveQuantity,
      }))}
      rowKey={"key"}
      columns={[
        {
          title: "Mã scheme",
          dataIndex: "key",
        },
        {
          title: "Tên scheme",
          dataIndex: "name",
        },
        {
          title: "Giới hạn phần quà nhận được mỗi đơn hàng",
          align: "right",
          dataIndex: "maxReceiveQuantity",
          render: (maxReceiveQuantity, record) => {
            return (
              <InputNumberLimitOrder
                maxReceiveQuantity={maxReceiveQuantity}
                id={record.key}
                componentFeatureId={componentFeatureId}
                key={record.key}
              />
            );
          },
        },
        {
          title: "",
          className: "min-w-[300px]",
        },
      ]}
      pagination={false}
    />
  );
};

export default LimitOrderTab;
