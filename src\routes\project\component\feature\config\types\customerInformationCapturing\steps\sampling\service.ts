import { useApp } from "@/UseApp.tsx";
import { AbstractFilterInterface } from "@/common/interface.ts";
import { ProjectProductInterface } from "@project/product/interface.ts";
import { useMutation, useQuery } from "@tanstack/react-query";
import { CustomerFeatureSamplingInterface } from "../../interface.ts";

export const useSamplingsQuery = (
  componentFeatureId: number,
  filter?: {
    keyword?: string;
  } & AbstractFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["samplingsQuery", componentFeatureId, filter],
    queryFn: () =>
      axiosGet<
        { entities: CustomerFeatureSamplingInterface[]; count: number },
        unknown
      >(`/features/${componentFeatureId}/samplings`, filter),
  });
};

export const useSamplingsAvailablesQuery = (
  componentFeatureId: number,
  filter?: {
    keyword?: string;
    projectBrandId?: number;
  } & AbstractFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["samplingsAvailables", componentFeatureId, filter],
    queryFn: () =>
      axiosGet<
        {
          entities: ProjectProductInterface[];
          count: number;
        },
        unknown
      >(`/features/${componentFeatureId}/samplings/availables`, filter),
  });
};

export const useCreateSamplingMutation = (componentFeatureId: number) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createSampling", componentFeatureId],
    mutationFn: (data: { projectProductIds: number[] }) =>
      axiosPost(`/features/${componentFeatureId}/samplings`, data),
  });
};

export const useUpdateSamplingMutation = (componentFeatureId: number) => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["updateSampling", componentFeatureId],
    mutationFn: (data: { id: number; isActive?: boolean }) =>
      axiosPatch(`/features/${componentFeatureId}/samplings/${data.id}`, data),
  });
};

export const useDeleteSamplingMutation = (componentFeatureId: number) => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deleteSampling", componentFeatureId],
    mutationFn: (id: number) =>
      axiosDelete(`/features/${componentFeatureId}/samplings/${id}`),
  });
};
