import { CHART_COLOR_PALETTE } from "@/common/constant";
import { randomColor } from "@/common/helper";
import ChartContanier from "@/routes/project/chart/ChartContanier";
import { Pie } from "@ant-design/charts";
import { Col, Form, Row } from "antd";
import _ from "lodash";
import { useCallback, useMemo, useState } from "react";
import DashboardFilterZone from "../../DashboardFilterZone";
import { DashboardFilterInterface } from "../../interface";
import { OosStoreLevelDataInterface } from "./interface";
import { useOosStoreLevelQuery } from "./service";

interface OosStoreLevelTabProps {
  projectId: number;
  dashboardId: number;
}

const OosStoreLevelTab = ({
  projectId,
  dashboardId,
}: OosStoreLevelTabProps) => {
  const [form] = Form.useForm();
  const [filter, setFilter] = useState<DashboardFilterInterface>({});

  const oosStoreLevelQuery = useOosStoreLevelQuery(
    projectId,
    dashboardId,
    filter,
  );
  const oosStoreLevelRegionQuery = useOosStoreLevelQuery(
    projectId,
    dashboardId,
    {
      ...filter,
      controlBy: "region",
    },
  );
  const oosStoreLevelChannelQuery = useOosStoreLevelQuery(
    projectId,
    dashboardId,
    {
      ...filter,
      controlBy: "channel",
    },
  );

  const handleApply = useCallback(() => {
    const values = form.getFieldsValue();

    const {
      dateSingle,
      projectRegionIds,
      projectOutletIds,
      channelIds,
      provinceIds,
      leaderIds,
    } = values;

    const filterValue = {
      startDate: dateSingle.format("YYYY-MM-DD"),
      endDate: dateSingle.format("YYYY-MM-DD"),
      projectRegionIds,
      projectOutletIds,
      channelIds,
      provinceIds,
      leaderIds: leaderIds?.map((item: { value: number }) => item.value),
    };

    if (_.isEqual(filter, filterValue)) {
      oosStoreLevelQuery.refetch();
      oosStoreLevelRegionQuery.refetch();
      oosStoreLevelChannelQuery.refetch();
    } else {
      setFilter(filterValue);
    }
  }, [
    filter,
    form,
    oosStoreLevelChannelQuery,
    oosStoreLevelQuery,
    oosStoreLevelRegionQuery,
  ]);

  const getPieData = useCallback((data: OosStoreLevelDataInterface[]) => {
    const group = _.groupBy(data, "control.name");
    const result = [];

    for (const [name, values] of Object.entries(group)) {
      const total = values.reduce((sum, item) => sum + item.count, 0);
      result.push({
        name: name,
        count: total,
        backgroundColor: values[0].control.backgroundColor,
      });
    }

    return result;
  }, []);

  const total =
    oosStoreLevelQuery.data?.reduce((sum, item) => sum + item.count, 0) ?? 0;
  const pieConfig = useMemo(
    () => ({
      appendPadding: 10,
      data: oosStoreLevelQuery.data,
      angleField: "count",
      colorField: "name",
      radius: 0.8,
      interactions: [
        {
          type: "element-active",
        },
      ],

      label: {
        text: (d: { count: number; name: string }) =>
          `${d.count}  (${Math.round((d.count / total) * 100)}%)`,
        position: "spider",
        fontSize: 12,
        fontWeight: "bold",
      },
      legend: {
        color: {
          position: "bottom",
          layout: {
            justifyContent: "center",
          },
        },
      },
      title: {
        title: "Dashboard OOS instore",
        subtitle: "Tính theo outlet",
      },
      tooltip: {
        field: "count",
        title: (d: { name: string }) => d.name,
        value: (d: { count: number }) => d.count,
      },
      scale: {
        color: {
          palette: oosStoreLevelQuery.data?.map(
            (item) => item.backgroundColor ?? randomColor(),
          ),
        },
      },
    }),
    [oosStoreLevelQuery.data, total],
  );

  const oosStoreLevelRegionData = getPieData(
    oosStoreLevelRegionQuery.data ?? [],
  );
  const totalRegion = useMemo(
    () => oosStoreLevelRegionData.reduce((sum, item) => sum + item.count, 0),
    [oosStoreLevelRegionData],
  );
  const pieRegionConfig = useMemo(
    () => ({
      appendPadding: 10,
      data: oosStoreLevelRegionData,
      angleField: "count",
      colorField: "name",
      radius: 0.8,
      interactions: [
        {
          type: "element-active",
        },
      ],

      label: {
        text: (d: { count: number; name: string }) =>
          `${d.count}  (${Math.round((d.count / totalRegion) * 100)}%)`,
        position: "spider",
        fontSize: 12,
        fontWeight: "bold",
      },
      legend: {
        color: {
          position: "bottom",
          layout: {
            justifyContent: "center",
          },
        },
      },
      title: {
        title: "Dashboard OOS instore by area",
        subtitle: "Tính theo outlet",
      },
      tooltip: {
        field: "count",
        title: (d: { name: string }) => d.name,
        value: (d: { count: number }) => d.count,
      },
      scale: {
        color: {
          palette: CHART_COLOR_PALETTE,
        },
      },
    }),
    [oosStoreLevelRegionData, totalRegion],
  );

  const oosStoreLevelChannelData = getPieData(
    oosStoreLevelChannelQuery.data ?? [],
  );
  const totalChannel = useMemo(
    () => oosStoreLevelChannelData.reduce((sum, item) => sum + item.count, 0),
    [oosStoreLevelChannelData],
  );
  const pieChannelConfig = useMemo(
    () => ({
      appendPadding: 10,
      data: oosStoreLevelChannelData,
      angleField: "count",
      colorField: "name",
      radius: 0.8,
      interactions: [
        {
          type: "element-active",
        },
      ],

      label: {
        text: (d: { count: number; name: string }) =>
          `${d.count}  (${Math.round((d.count / totalChannel) * 100)}%)`,
        position: "spider",
        fontSize: 12,
        fontWeight: "bold",
      },
      legend: {
        color: {
          position: "bottom",
          layout: {
            justifyContent: "center",
          },
        },
      },
      title: {
        title: "Dashboard OOS instore by chain",
        subtitle: "Tính theo outlet",
      },
      tooltip: {
        field: "count",
        title: (d: { name: string }) => d.name,
        value: (d: { count: number }) => d.count,
      },
      scale: {
        color: {
          palette: oosStoreLevelChannelData?.map(
            (item) => item.backgroundColor ?? randomColor(),
          ),
        },
      },
    }),
    [oosStoreLevelChannelData, totalChannel],
  );

  return (
    <>
      <DashboardFilterZone
        handleApply={handleApply}
        fields={[
          "date.single",
          "region",
          "province",
          "chain",
          "leader",
          "outlet",
        ]}
        form={form}
        projectId={projectId}
        setFilter={setFilter}
      />
      <Row gutter={[16, { xs: 16, sm: 16 }]}>
        <Col md={12} xs={24}>
          <ChartContanier>
            <Pie {...pieConfig} height={500} />
          </ChartContanier>
        </Col>

        <Col md={12} xs={24}>
          <ChartContanier>
            <Pie {...pieRegionConfig} height={500} />
          </ChartContanier>
        </Col>

        <Col md={24} xs={24}>
          <ChartContanier>
            <Pie {...pieChannelConfig} height={500} />
          </ChartContanier>
        </Col>
      </Row>
    </>
  );
};

export default OosStoreLevelTab;
