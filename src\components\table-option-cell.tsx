import {
  DeleteOutlined,
  EditOutlined,
  EllipsisOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
} from "@ant-design/icons";
import { Button, Dropdown, MenuProps } from "antd";

export enum ItemAction {
  EDIT = "edit",
  INACTIVE = "inactive",
  ACTIVE = "active",
  DELETE = "DELETE",
}

export const renderTableOptionCell = (
  record: { id: number; isActive?: boolean },
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onUpdate?: (e: any) => void,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onInactive?: (e: any) => void,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onActive?: (e: any) => void,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onDelete?: (e: any) => void,
  extraActions?: {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    items: { key: any; label: JSX.Element | string; icon?: JSX.Element }[];
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    actions: { key: string; action: (e: any) => void }[];
  },
) => {
  const extraActionItems = extraActions?.items ?? [];
  const extraActionActions = extraActions?.actions ?? [];

  const onMenuClick: MenuProps["onClick"] = (e) => {
    switch (e.key) {
      case ItemAction.EDIT:
        onUpdate?.(record);
        break;
      case ItemAction.INACTIVE:
        onInactive?.(record);
        break;
      case ItemAction.ACTIVE:
        onActive?.(record);
        break;
      case ItemAction.DELETE:
        onDelete?.(record);
        break;
      default:
        extraActionActions.forEach((action) => {
          if (action.key === e.key) {
            action.action(record);
          }
        });
        break;
    }
  };

  const items = [
    {
      key: ItemAction.EDIT,
      label: "Chỉnh sửa",
      icon: <EditOutlined />,
    },
    ...extraActionItems,
    {
      key: ItemAction.INACTIVE,
      label: "Ngừng hoạt động",
      icon: <PauseCircleOutlined />,
    },
    {
      key: ItemAction.ACTIVE,
      label: "Khôi phục hoạt động",
      icon: <ReloadOutlined />,
    },
    {
      key: ItemAction.DELETE,
      label: "Xóa",
      icon: <DeleteOutlined />,
    },
  ];

  const menuProps = {
    items: record.isActive
      ? items.filter((item) => item.key !== ItemAction.ACTIVE)
      : items.filter((item) => item.key !== ItemAction.INACTIVE),
    onClick: onMenuClick,
  };
  return (
    <Dropdown menu={menuProps}>
      <Button type="link">
        <EllipsisOutlined />
      </Button>
    </Dropdown>
  );
};
