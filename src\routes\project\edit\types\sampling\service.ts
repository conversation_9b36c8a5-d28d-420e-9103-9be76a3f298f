import { useApp } from "@/UseApp";
import { useMutation, useQuery } from "@tanstack/react-query";
import { EditSamplingInterface } from "./interface";

export const useEditSamplingQuery = (
  projectId: number,
  attendanceId?: number,
  componentFeatureId?: number,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["editSampling", projectId, attendanceId, componentFeatureId],
    queryFn: () =>
      axiosGet<EditSamplingInterface, unknown>(
        `/tools/projects/${projectId}/attendances/${attendanceId}/features/${componentFeatureId}/samplings`,
      ),
    enabled: !!projectId && !!attendanceId && !!componentFeatureId,
  });
};

export const useCreateEditSamplingMutation = (
  projectId: number,
  attendanceId?: number,
  componentFeatureId?: number,
) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: [
      "createEditSampling",
      projectId,
      attendanceId,
      componentFeatureId,
    ],
    mutationFn: (data: {
      dataUuid: string;
      dataTimestamp: string;
      values: { featureSamplingId: number; value: number }[];
    }) =>
      axiosPost(
        `/tools/projects/${projectId}/attendances/${attendanceId}/features/${componentFeatureId}/samplings`,
        data,
      ),
  });
};
