import { useApp } from "@/UseApp";
import { AbstractFilterInterface } from "@/common/interface";
import { AppContextInterface } from "@/interface";
import { useMutation, useQuery } from "@tanstack/react-query";
import { ProjectRecordFeatureInterface } from "../../interface";

export const getReportMultimedia = (
  axiosGet: AppContextInterface["axiosGet"],
  projectId: number,
  componentFeatureId: number,
  filter?: object & AbstractFilterInterface,
) =>
  axiosGet<
    { entities: ProjectRecordFeatureInterface[]; count: number },
    unknown
  >(
    `/projects/${projectId}/report/features/${componentFeatureId}/multimedias`,
    filter,
  );

export const useReportMultimediaQuery = (
  projectId: number,
  componentFeatureId: number,
  filter?: object & AbstractFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["reportMultimedia", projectId, componentFeatureId, filter],
    queryFn: () =>
      getReportMultimedia(axiosGet, projectId, componentFeatureId, filter),
  });
};

export const useReportMultimediaMutation = (
  projectId: number,
  componentFeatureId: number,
) => {
  const { axiosGet } = useApp();

  return useMutation({
    mutationKey: ["reportMultimedia", projectId, componentFeatureId],
    mutationFn: (filter?: object & AbstractFilterInterface) =>
      getReportMultimedia(axiosGet, projectId, componentFeatureId, filter),
  });
};
