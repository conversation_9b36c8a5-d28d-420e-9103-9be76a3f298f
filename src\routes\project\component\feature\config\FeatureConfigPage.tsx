import { useApp } from "@/UseApp";
import { CURD } from "@/common/constant";
import ModalCURD from "@/components/ModalCURD";
import {
  ArrowLeftOutlined,
  ClockCircleOutlined,
  EditOutlined,
} from "@ant-design/icons";
import { Button, Col, Form, Input, Row, Skeleton, Space, Switch } from "antd";
import { Fragment, Suspense, useCallback, useState } from "react";
import { Link, Outlet, useLocation, useParams } from "react-router-dom";
import RoleSwitch from "../RoleSwitch";
import { FeatureTypeEnum } from "../interface";
import {
  useComponentFeatureQuery,
  useUpdateComponentFeatureMutation,
} from "../service";
import FeatureConfigScheduleModal from "./FeatureConfigScheduleModal";

export default function FeatureConfigPage() {
  const projectId = parseInt(useParams().id ?? "0");
  const projectComponentId = parseInt(useParams().projectComponentId ?? "0");
  const componentFeatureId = parseInt(useParams().componentFeatureId ?? "0");

  const location = useLocation();

  const { showNotification } = useApp();

  const [isOpen, setIsOpen] = useState(false);
  const [form] = Form.useForm();
  const [isSetupSchedule, setIsSetupSchedule] = useState(false);

  const componentFeatureQuery = useComponentFeatureQuery(
    projectId,
    projectComponentId,
    componentFeatureId,
  );

  const updateComponentFeatureMutation = useUpdateComponentFeatureMutation(
    projectId,
    projectComponentId,
  );

  const handleSubmit = useCallback(async () => {
    try {
      await updateComponentFeatureMutation.mutateAsync({
        id: componentFeatureId,
        data: {
          name: form.getFieldValue("name"),
        },
      });

      await componentFeatureQuery.refetch();
      showNotification({
        type: "success",
        message: "Cập nhật tên chức năng thành công.",
      });
      setIsOpen(false);
    } catch (e) {
      console.error(e);

      showNotification({
        type: "error",
        message: "Cập nhật tên chức năng thất bại.",
      });
    }
  }, [
    componentFeatureId,
    componentFeatureQuery,
    form,
    showNotification,
    updateComponentFeatureMutation,
  ]);

  return (
    <>
      <Link to={`/project/${projectId}/component/${projectComponentId}`}>
        <p className={"text-hint mt-[34px]"}>
          <ArrowLeftOutlined /> Quay lại
        </p>
      </Link>
      <h2>
        {componentFeatureQuery.data?.name} {"  "}{" "}
        <Button
          icon={<EditOutlined />}
          onClick={() => {
            setIsOpen(true);
            form.setFieldValue("name", componentFeatureQuery.data?.name);
          }}
          type="link"
        />
      </h2>
      <Row justify={"space-between"}>
        <Col>
          <p className={"mb-3"}>
            Loại data chức năng:{" "}
            <span className="text-blue font-semibold">
              {componentFeatureQuery.data?.featureTypeName}
            </span>
          </p>
        </Col>

        <Col>
          <Button
            icon={<ClockCircleOutlined />}
            type="text"
            onClick={() => setIsSetupSchedule(true)}
          >
            Ràng buộc thời gian nhập liệu
          </Button>
        </Col>
      </Row>

      <div className="bg-white pl-10 pr-10 pt-5 pb-5 rounded mb-3 justify-between flex">
        <Space>
          <span>Nhân viên sử dụng chức năng này: </span>
          {componentFeatureQuery.data?.employeeRoles?.map((role) => {
            return (
              <Fragment key={`${role.id}`}>
                <span key={`span_${role.id}`} className="pl-3">
                  {role.name}
                </span>
                <RoleSwitch
                  role={role}
                  projectId={projectId}
                  projectComponentId={projectComponentId}
                  componentFeatureId={componentFeatureId}
                  employeeRoles={componentFeatureQuery.data.employeeRoles ?? []}
                  cb={function (): void {
                    componentFeatureQuery.refetch();
                  }}
                />
              </Fragment>
            );
          })}
        </Space>

        {location.pathname.includes(
          FeatureTypeEnum.CustomerInformationCapturing.toString(),
        ) && (
          <Space>
            <span className="mr-2">Ghi đơn online</span>
            <Switch
              value={componentFeatureQuery.data?.isImmediateSendRequired}
              onChange={async (value) => {
                await updateComponentFeatureMutation.mutateAsync({
                  id: componentFeatureId,
                  data: {
                    isImmediateSendRequired: value,
                  },
                });

                showNotification({
                  type: "success",
                  message: "Cập nhật ghi đơn online thành công.",
                });

                await componentFeatureQuery.refetch();
              }}
              loading={updateComponentFeatureMutation.isPending}
            />
          </Space>
        )}
      </div>

      <Suspense fallback={<Skeleton active />}>
        <Outlet />
      </Suspense>

      <ModalCURD
        title={"Chỉnh sửa tên chức năng"}
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        formContent={
          <Form.Item
            name="name"
            label="Tên chức năng app "
            rules={[
              {
                required: true,
                message: "Tên chức năng app không được để trống.",
              },
            ]}
          >
            <Input />
          </Form.Item>
        }
        form={form}
        onFinish={handleSubmit}
        action={CURD.UPDATE}
        btnConfirmLoading={updateComponentFeatureMutation.isPending}
      />

      {isSetupSchedule && (
        <FeatureConfigScheduleModal
          componentFeatureId={componentFeatureId}
          projectId={projectId}
          onCancelCb={() => setIsSetupSchedule(false)}
          projectComponentId={projectComponentId}
        />
      )}
    </>
  );
}
