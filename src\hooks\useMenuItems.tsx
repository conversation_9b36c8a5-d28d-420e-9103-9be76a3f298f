import { useCanPermission } from "@/layouts/MainLayout/hook";
import { FeatureTypeEnum } from "@/routes/project/component/feature/interface";
import {
  useProjectOtpsQuery,
  useProjectSitecheckConfigQuery,
} from "@/routes/project/config/service";
import { PermissionEnum } from "@/routes/project/interface";
import { useProjectReportComponentsQuery } from "@/routes/project/report/service";
import { UserTypeEnum } from "@/routes/user/interface";
import { useApp } from "@/UseApp";
import { ArrowLeftOutlined, SwitcherOutlined } from "@ant-design/icons";
import { MenuProps } from "antd";
import { useCallback, useLayoutEffect, useMemo, useState } from "react";
import { useLocation } from "react-router-dom";
import { JSX } from "react/jsx-runtime";

type MenuItem = Required<MenuProps>["items"][number];

const PROJECT_DETAIL_PATTERN = /^\/project\/([^/]+)\/([a-z-]+)(\/.+)?$/;
const REPORT_TYPE = [
  FeatureTypeEnum.AttendanceClockingIn,
  FeatureTypeEnum.Photography,
  FeatureTypeEnum.MultiSubjectMultimediaInformationCapturing,
  FeatureTypeEnum.MultipleEntitiesQuantityCapturing,
  FeatureTypeEnum.CustomerInformationCapturing,
  FeatureTypeEnum.Sampling,
  FeatureTypeEnum.Urgency,
  FeatureTypeEnum.NumericSheet,
  FeatureTypeEnum.OutOfStockStatus,
];

const MAIN_MENU_ITEMS: MenuItem[] = [
  {
    label: <span className="font-bold">MASTER DATA</span>,
    key: "master",
    type: "group",
    children: [
      {
        key: "/client",
        icon: <SwitcherOutlined />,
        label: "Client",
      },
      {
        key: "/agency",
        icon: <SwitcherOutlined />,
        label: "Agency",
      },
      {
        key: "/channel",
        icon: <SwitcherOutlined />,
        label: "Kênh thực hiện",
      },
      {
        key: "/sub-channel",
        icon: <SwitcherOutlined />,
        label: "Nhóm thực hiện",
      },
      {
        key: "/brand",
        icon: <SwitcherOutlined />,
        label: "Nhãn hàng",
      },
      {
        key: "/unit",
        icon: <SwitcherOutlined />,
        label: "Đơn vị tính",
      },
      {
        key: "/product",
        icon: <SwitcherOutlined />,
        label: "Sản phẩm",
      },
      {
        key: "/item-type",
        icon: <SwitcherOutlined />,
        label: "Loại vật phẩm",
      },
      {
        key: "/item",
        icon: <SwitcherOutlined />,
        label: "Vật phẩm",
      },
      {
        key: "/user",
        icon: <SwitcherOutlined />,
        label: "Tài khoản",
      },
    ],
  },
  {
    label: <span className="font-bold"> DỰ ÁN </span>,
    key: "master-project",
    type: "group",
    children: [
      {
        key: "/project",
        icon: <SwitcherOutlined />,
        label: "Danh sách dự án",
      },
    ],
  },
];

const useMenuItems = (projectId: number) => {
  const location = useLocation();
  const { userLogin } = useApp();

  const [menuItems, setMenuItems] = useState<MenuItem[]>(MAIN_MENU_ITEMS);

  const projectReportComponentsQuery = useProjectReportComponentsQuery(
    Number(projectId),
  );
  const projectSitecheckConfigQuery = useProjectSitecheckConfigQuery(projectId);
  const projectOtpsQuery = useProjectOtpsQuery(projectId);

  const { canPermissionFunction } = useCanPermission(projectId);

  const PROJECT_MENU_ITEMS = useMemo(
    () => [
      canPermissionFunction(PermissionEnum.NONE_ACCESS)
        ? {
            key: "general",
            icon: <SwitcherOutlined />,
            label: "Thông tin cơ bản",
          }
        : null,
      canPermissionFunction(PermissionEnum.NONE_ACCESS)
        ? {
            key: "product",
            icon: <SwitcherOutlined />,
            label: "Sản phẩm trong dự án",
          }
        : null,
      canPermissionFunction(PermissionEnum.NONE_ACCESS)
        ? {
            key: "item",
            icon: <SwitcherOutlined />,
            label: "Vật phẩm trong dự án ",
          }
        : null,
      canPermissionFunction(PermissionEnum.NONE_ACCESS)
        ? {
            key: "role-employee",
            icon: <SwitcherOutlined />,
            label: "Role nhân viên field",
          }
        : null,
      canPermissionFunction(PermissionEnum.EMPLOYEE)
        ? {
            key: "employee",
            icon: <SwitcherOutlined />,
            label: "Nhân viên field",
          }
        : null,
      canPermissionFunction(PermissionEnum.NONE_ACCESS)
        ? {
            key: "config-outlet",
            icon: <SwitcherOutlined />,
            label: "Cấu hình outlet",
          }
        : null,
      canPermissionFunction(PermissionEnum.NONE_ACCESS)
        ? {
            key: "outlet",
            icon: <SwitcherOutlined />,
            label: "Outlet trong dự án",
          }
        : null,
      canPermissionFunction(PermissionEnum.NONE_ACCESS)
        ? {
            key: "component",
            icon: <SwitcherOutlined />,
            label: "Chức năng app",
          }
        : null,
      canPermissionFunction(PermissionEnum.NONE_ACCESS)
        ? {
            key: "kpi",
            icon: <SwitcherOutlined />,
            label: "Cấu hình KPI",
          }
        : null,
      canPermissionFunction(PermissionEnum.NONE_ACCESS)
        ? {
            key: "config",
            icon: <SwitcherOutlined />,
            label: "Cấu hình",
          }
        : null,
      canPermissionFunction(PermissionEnum.NONE_ACCESS)
        ? {
            key: "region",
            icon: <SwitcherOutlined />,
            label: "Phân vùng địa bàn",
          }
        : null,
      canPermissionFunction(PermissionEnum.NONE_ACCESS)
        ? {
            key: "config-dashboard",
            icon: <SwitcherOutlined />,
            label: "Cấu hình dashboard",
          }
        : null,
      canPermissionFunction(PermissionEnum.OTP_DELIVERY) &&
      projectOtpsQuery.data?.id
        ? {
            key: "otp-delivery",
            icon: <SwitcherOutlined />,
            label: "OTP deliveries",
          }
        : null,
      canPermissionFunction(PermissionEnum.LUCKY_DRAW_ALLOCATION)
        ? {
            key: "config-lucky-wheel",
            icon: <SwitcherOutlined />,
            label: "Cấu hình vòng quay",
          }
        : null,
    ],
    [canPermissionFunction, projectOtpsQuery.data?.id],
  );

  const getProjectMenuItems = useCallback((): MenuItem[] => {
    const isDashboardSabeco = canPermissionFunction(
      PermissionEnum.DASHBOARD_SABECO,
    );

    return [
      {
        key: "/project",
        icon: <ArrowLeftOutlined />,
        label: "Danh sách dự án",
        style: {
          border: "1.5px solid #DDE1EA",
          listStylePosition: "inside",
          marginBottom: "20px",
        },
      },
      canPermissionFunction(PermissionEnum.EMPLOYEE) ||
      canPermissionFunction(PermissionEnum.OTP_DELIVERY) ||
      canPermissionFunction(PermissionEnum.LUCKY_DRAW_ALLOCATION) ||
      canPermissionFunction(PermissionEnum.NONE_ACCESS)
        ? {
            label: <span className="font-bold">CẤU HÌNH DỰ ÁN</span>,
            key: "masterProject",
            type: "group",
            children: PROJECT_MENU_ITEMS.filter((item) => !!item).map(
              (item) => ({
                ...item,
                key: `/project/${projectId}/${item?.key}`,
              }),
            ),
          }
        : null,
      canPermissionFunction(PermissionEnum.DASHBOARD)
        ? {
            label: <span className="font-bold">DASHBOARD</span>,
            key: "masterDashboard",
            type: "group",
            children: [
              !isDashboardSabeco || userLogin?.type === UserTypeEnum.ADMIN
                ? {
                    key: `/project/${projectId}/chart`,
                    icon: <SwitcherOutlined />,
                    label: "Tổng quát",
                  }
                : null,

              projectSitecheckConfigQuery?.data?.id
                ? {
                    key: `/project/${projectId}/sitecheck`,
                    icon: <SwitcherOutlined />,
                    label: "Sitecheck",
                  }
                : null,

              isDashboardSabeco
                ? {
                    key: `/project/${projectId}/dashboard/v3`,
                    icon: <SwitcherOutlined />,
                    label: "Dashboard",
                  }
                : null,
            ],
          }
        : null,
      canPermissionFunction(PermissionEnum.REPORT)
        ? {
            label: <span className="font-bold">BÁO CÁO</span>,
            key: "masterReport",
            type: "group",
            children: projectReportComponentsQuery.data?.map(
              (projectComponent) => {
                const sortedProjectFeatures = [
                  ...projectComponent.projectFeatures,
                ]
                  .filter((projectFeature) =>
                    REPORT_TYPE.includes(projectFeature.type),
                  )
                  .sort((a, b) => a.ordinal - b.ordinal);

                const reportMenu: { key: string; label: JSX.Element }[] = [];

                const isReportOos = canPermissionFunction(
                  PermissionEnum.REPORT_OOS,
                );
                if (isReportOos && userLogin?.type !== UserTypeEnum.ADMIN) {
                  const projectFeature = sortedProjectFeatures.find(
                    (item) => item.type === FeatureTypeEnum.OutOfStockStatus,
                  );
                  if (projectFeature) {
                    reportMenu.push({
                      key: `/project/${projectId}/report/component/${projectComponent.id}/feature/${projectFeature.id}/${projectFeature.type}/sale`,
                      label: <span>{projectFeature.name} (Sale)</span>,
                    });
                  }

                  return {
                    key: `/project/${projectId}/report/${projectComponent.id}`,
                    label: <span>{projectComponent.name}</span>,
                    icon: <SwitcherOutlined />,
                    children: reportMenu,
                  };
                }

                for (const projectFeature of sortedProjectFeatures) {
                  if (
                    projectFeature.type === FeatureTypeEnum.OutOfStockStatus
                  ) {
                    reportMenu.push({
                      key: `/project/${projectId}/report/component/${projectComponent.id}/feature/${projectFeature.id}/${projectFeature.type}`,
                      label: <span>{projectFeature.name}</span>,
                    });

                    reportMenu.push({
                      key: `/project/${projectId}/report/component/${projectComponent.id}/feature/${projectFeature.id}/${projectFeature.type}/highlight`,
                      label: <span>{projectFeature.name} (Client)</span>,
                    });

                    if (
                      !canPermissionFunction(
                        PermissionEnum.REPORT_WITHOUT_OOS,
                      ) ||
                      canPermissionFunction(PermissionEnum.NONE_ACCESS)
                    ) {
                      reportMenu.push({
                        key: `/project/${projectId}/report/component/${projectComponent.id}/feature/${projectFeature.id}/${projectFeature.type}/sale`,
                        label: <span>{projectFeature.name} (Sale)</span>,
                      });
                    }
                  } else {
                    reportMenu.push({
                      key: `/project/${projectId}/report/component/${projectComponent.id}/feature/${projectFeature.id}/${projectFeature.type}`,
                      label: <span>{projectFeature.name}</span>,
                    });
                  }
                }

                return {
                  key: `/project/${projectId}/report/${projectComponent.id}`,
                  label: <span>{projectComponent.name}</span>,
                  icon: <SwitcherOutlined />,
                  children: reportMenu,
                };
              },
            ),
          }
        : null,
      canPermissionFunction(PermissionEnum.PROFILE)
        ? {
            label: <span className="font-bold">XÉT DUYỆT YÊU CẦU</span>,
            key: "masterApprove",
            type: "group",
            children: [
              {
                key: `/project/${projectId}/profile`,
                icon: <SwitcherOutlined />,
                label: "Profile nhân viên",
              },
            ],
          }
        : null,
      canPermissionFunction(PermissionEnum.TOOL)
        ? {
            label: <span className="font-bold">TOOL</span>,
            key: "masterTool",
            type: "group",
            children: [
              {
                key: `/project/${projectId}/edit`,
                icon: <SwitcherOutlined />,
                label: "Chỉnh sửa dữ liệu",
              },
            ],
          }
        : null,
    ];
  }, [
    canPermissionFunction,
    PROJECT_MENU_ITEMS,
    userLogin?.type,
    projectId,
    projectSitecheckConfigQuery?.data?.id,
    projectReportComponentsQuery.data,
  ]);

  useLayoutEffect(() => {
    let menuItems;
    if (
      userLogin?.type === UserTypeEnum.ADMIN ||
      userLogin?.type === UserTypeEnum.CLIENT ||
      userLogin?.type === UserTypeEnum.AGENCY ||
      userLogin?.type === UserTypeEnum.MANAGER
    ) {
      if (RegExp(PROJECT_DETAIL_PATTERN).exec(location.pathname)) {
        menuItems = getProjectMenuItems();
      } else {
        menuItems = MAIN_MENU_ITEMS;
      }
    } else {
      menuItems = MAIN_MENU_ITEMS;
    }

    // Remove "master" and "masterProject" menu items for clients and agencies
    const filteredMenuItems =
      userLogin?.type !== UserTypeEnum.ADMIN
        ? menuItems.filter((item) => {
            const key = item?.key?.toString();
            return !["master"].includes(key ?? "");
          })
        : menuItems;

    setMenuItems(filteredMenuItems);
  }, [location.pathname, getProjectMenuItems, userLogin?.type]);

  const selectedKeys = useMemo(() => {
    if (location.pathname.startsWith(`/project/${projectId}/component`)) {
      return [`/project/${projectId}/component`];
    }
    if (location.pathname.startsWith(`/project/${projectId}/kpi`)) {
      return [`/project/${projectId}/kpi`];
    }
    return [location.pathname];
  }, [location.pathname, projectId]);

  return { menuItems, selectedKeys };
};

export default useMenuItems;
