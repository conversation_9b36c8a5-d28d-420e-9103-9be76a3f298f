export default function ItemProductQuantity(
  props: Readonly<{
    name: string;
    code: string;
    unitName: string;
    quantity: number;
    isFirst?: boolean;
    classNameMarginTop?: string;
  }>,
) {
  const { name, unitName, quantity, code, isFirst, classNameMarginTop } = props;
  return (
    <div className={!isFirst ? classNameMarginTop ?? "mt-3" : ""}>
      <p className={"p-0 m-0"}>
        <span className={"text-blue font-semibold"}>x{quantity}</span>{" "}
        {unitName} - {name}
      </p>
      <p className={"p-0 m-0 text-hint text-xs"}>#{code}</p>
    </div>
  );
}
