import { Col, Form, Row } from "antd";
import dayjs from "dayjs";
import _ from "lodash";
import { useCallback, useMemo, useState } from "react";
import DashboardFilterZone from "../../DashboardFilterZone";
import { DashboardFilterInterface } from "../../interface";
import { useNumericSheetTotalQuery } from "../numericSheet/service";
import PieChartEachAttribute from "./PieChartEachAttribute";

interface NumericSheetEachAttributeTabProps {
  projectId: number;
  dashboardId: number;
}
const NumericSheetEachAttributeTab = ({
  projectId,
  dashboardId,
}: NumericSheetEachAttributeTabProps) => {
  const [form] = Form.useForm();
  const [filter, setFilter] = useState<DashboardFilterInterface>({});

  const numericSheetTotalQuery = useNumericSheetTotalQuery(
    projectId,
    dashboardId,
    filter,
  );

  const handleApply = useCallback(() => {
    const values = form.getFieldsValue();

    const {
      dateSingle,
      projectRegionIds,
      projectOutletIds,
      channelIds,
      provinceIds,
      leaderIds,
    } = values;

    const filterValue = {
      startDate: dayjs(dateSingle).startOf("date").toISOString(),
      endDate: dayjs(dateSingle).endOf("date").toISOString(),
      projectRegionIds,
      projectOutletIds,
      channelIds,
      provinceIds,
      leaderIds: leaderIds?.map((item: { value: number }) => item.value),
    };

    if (_.isEqual(filter, filterValue)) {
      numericSheetTotalQuery.refetch();
    } else {
      setFilter(filterValue);
    }
  }, [filter, form, numericSheetTotalQuery]);

  const groupData = useMemo(
    () => _.groupBy(numericSheetTotalQuery.data ?? [], "attribute.name"),
    [numericSheetTotalQuery.data],
  );

  return (
    <>
      <DashboardFilterZone
        handleApply={handleApply}
        fields={[
          "date.single",
          "region",
          "province",
          "chain",
          "leader",
          "outlet",
        ]}
        form={form}
        projectId={projectId}
        setFilter={setFilter}
      />

      <Row gutter={[16, { xs: 16, sm: 16 }]}>
        {Object.entries(groupData).map(([name, data]) => (
          <Col key={name} md={12} xs={24}>
            <PieChartEachAttribute data={data} name={name} key={name} />
          </Col>
        ))}
      </Row>
    </>
  );
};

export default NumericSheetEachAttributeTab;
