import { useApp } from "@/UseApp.tsx";
import { AbstractFilterInterface } from "@/common/interface.ts";
import { AppContextInterface } from "@/interface.ts";
import { useMutation, useQuery } from "@tanstack/react-query";
import { ApiItemResponseInterface } from "./interface";

export const getItems = async (
  axiosGet: AppContextInterface["axiosGet"],
  filter: unknown,
) => {
  return await axiosGet<ApiItemResponseInterface, unknown>("/items", filter);
};

export const useItemsQuery = (
  filter: { clientId?: number } & AbstractFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["items", filter],
    queryFn: () => getItems(axiosGet, filter),
  });
};

export const useDeleteItemMutation = () => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deleteItem"],
    mutationFn: (id: number) => axiosDelete(`/items/${id}`),
  });
};
