import { useQuery } from "@tanstack/react-query";
import { useApp } from "@/UseApp";
import { ProjectComponentInterface } from "../component/interface";

export const useProjectReportComponentsQuery = (projectId: number) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["projectReportComponents", projectId],
    enabled: !!projectId,
    queryFn: () =>
      axiosGet<ProjectComponentInterface[], unknown>(
        `/projects/${projectId}/report/components`,
      ),
  });
};
