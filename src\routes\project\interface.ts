import { AbstractEntityInterface } from "@/common/interface";
import { AgencyInterface } from "../agency/interface";
import { BrandInterface } from "../brand/interface";
import { ChannelInterface } from "../channel/interface";
import { ClientInterface } from "../client/interface.ts";

export enum PermissionEnum {
  NONE_ACCESS = "none_access",
  DASHBOARD = "dashboard",
  REPORT = "report",
  ALL = "all",
  TOOL = "tool",
  PROFILE = "profile",
  EMPLOYEE = "employee",
  EDIT_ORDER = "edit_order",
  REPORT_OOS = "report_oos",
  DASHBOARD_SABECO = "dashboard_sabeco",
  REPORT_WITHOUT_OOS = "report_without_oos",
  OTP_DELIVERY = "otp_delivery",
  LUCKY_DRAW_ALLOCATION = "lucky_draw_allocation",
}

export interface ProjectDashboardInterface extends AbstractEntityInterface {
  projectId: number;
  link: string;
}

export interface ProjectClientUserInterface extends AbstractEntityInterface {
  userId: number;
  permission: PermissionEnum[];
  clientId: number;
  roleId?: number;
}

export interface ProjectAgencyUserInterface extends AbstractEntityInterface {
  userId: number;
  permission: PermissionEnum[];
  projectAgencyId: number;
  roleId?: number;
}

export interface ProjectInterface extends AbstractEntityInterface {
  name: string;
  startDate: string;
  endDate: string;
  status: string;
  client: ClientInterface;
  projectClientUsers: ProjectClientUserInterface[];
  projectAgencyUsers: ProjectAgencyUserInterface[];
  projectDashboards: ProjectDashboardInterface[];
}

export interface ApiProjectResponseInterface {
  entities: ProjectInterface[];
  count: number;
}

export interface ProjectAgencyChannelInterface extends AbstractEntityInterface {
  channel: ChannelInterface;
  projectAgency: ProjectAgencyInterface;
  projectAgencyId: number;
  channelId: number;
}

export interface ProjectAgencyInterface {
  id: number;
  project: ProjectInterface;
  agency: AgencyInterface;
  projectAgencyChannels: ProjectAgencyChannelInterface[];
  projectId: number;
}

export interface ApiProjectAgencyResponseInterface {
  entities: ProjectAgencyInterface[];
  count: number;
}

export interface ProjectBrandInterface {
  id: number;
  project: ProjectInterface;
  brand: BrandInterface;
  productsCount: number;
  brandId: number;
}

export interface ApiProjectBrandResponseInterface {
  entities: ProjectBrandInterface[];
  count: number;
}

export interface ProjectChannelInterface extends AbstractEntityInterface {
  channel: ChannelInterface;
}
