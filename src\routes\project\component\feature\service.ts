import { AbstractFilterInterface } from "@/common/interface.ts";
import { useApp } from "@/UseApp";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  ComponentFeatureInterface,
  FeatureInterface,
  FeatureScheduleInterface,
} from "./interface";

export const useComponentFeatureTypesQuery = (
  projectId: number,
  projectComponentId: number,
  enabled?: boolean,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["componentFeatureTypes", projectId, projectComponentId],
    queryFn: () =>
      axiosGet<FeatureInterface[], unknown>(
        `/projects/${projectId}/components/${projectComponentId}/feature-types`,
      ),
    enabled,
  });
};

export const useCreateComponentFeatureMutation = (
  projectId: number,
  projectComponentId: number,
) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createComponentFeature", projectId, projectComponentId],
    mutationFn: (data: { name: string; type: string }) =>
      axiosPost<ComponentFeatureInterface, unknown>(
        `/projects/${projectId}/components/${projectComponentId}/features`,
        data,
      ),
  });
};

export const useComponentFeaturesQuery = (
  projectId: number,
  projectComponentId: number,
  filter: AbstractFilterInterface & {
    name?: string;
    roleName?: string;
    keyword?: string;
  },
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["componentFeatures", projectId, projectComponentId, filter],
    queryFn: () =>
      axiosGet<
        { entities: ComponentFeatureInterface[]; count: number },
        unknown
      >(
        `/projects/${projectId}/components/${projectComponentId}/features`,
        filter,
      ),
  });
};

export const useArrangementComponentFeatureMutation = (
  projectId: number,
  projectComponentId: number,
) => {
  const { axiosPut } = useApp();

  return useMutation({
    mutationKey: ["arrangementComponentFeature", projectId, projectComponentId],
    mutationFn: (data: { activeId: number; overId: number }) =>
      axiosPut<ComponentFeatureInterface, unknown>(
        `/projects/${projectId}/components/${projectComponentId}/features/${data.activeId}/arrangement`,
        {
          overProjectFeatureId: data.overId,
        },
      ),
  });
};

export const useUpdateComponentFeatureMutation = (
  projectId: number,
  projectComponentId: number,
) => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["updateComponentFeature", projectId, projectComponentId],
    mutationFn: ({
      id,
      data,
    }: {
      id: number;
      data: {
        name?: string;
        isActive?: boolean;
        isImmediateSendRequired?: boolean;
      };
    }) =>
      axiosPatch<ComponentFeatureInterface, unknown>(
        `/projects/${projectId}/components/${projectComponentId}/features/${id}`,
        data,
      ),
  });
};

export const useDeleteComponentFeatureMutation = (
  projectId: number,
  projectComponentId: number,
) => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["updateComponentFeature", projectId, projectComponentId],
    mutationFn: (id: number) =>
      axiosDelete(
        `/projects/${projectId}/components/${projectComponentId}/features/${id}`,
      ),
  });
};

export const useUpdateDependentOnComponentFeaturesMutation = (
  projectId: number,
  projectComponentId: number,
) => {
  const { axiosPut } = useApp();

  return useMutation({
    mutationKey: [
      "updateDependentOnComponentFeatures",
      projectId,
      projectComponentId,
    ],
    mutationFn: ({
      id,
      data,
    }: {
      id: number;
      data: { dependentProjectFeatureIds: number[] };
    }) =>
      axiosPut(
        `/projects/${projectId}/components/${projectComponentId}/features/${id}/dependents`,
        data,
      ),
  });
};

export const useComponentFeatureQuery = (
  projectId: number,
  projectComponentId: number,
  componentFeatureId: number,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: [
      "componentFeature",
      projectId,
      projectComponentId,
      componentFeatureId,
    ],
    queryFn: () =>
      axiosGet<ComponentFeatureInterface, unknown>(
        `/projects/${projectId}/components/${projectComponentId}/features/${componentFeatureId}`,
      ),
  });
};

export const useUpdateRolesOnComponentFeaturesMutation = (
  projectId: number,
  projectComponentId: number,
  componentFeatureId: number,
) => {
  const { axiosPut } = useApp();

  return useMutation({
    mutationKey: [
      "updateRolesOnComponentFeatures",
      projectId,
      projectComponentId,
      componentFeatureId,
    ],
    mutationFn: (roleIds: number[]) =>
      axiosPut(
        `/projects/${projectId}/components/${projectComponentId}/features/${componentFeatureId}/roles`,
        {
          roleIds,
        },
      ),
  });
};

export const useComponentFeatureSchedulesQuery = (
  projectId: number,
  projectComponentId: number,
  componentFeatureId: number,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: [
      "componentFeatureSchedules",
      projectId,
      projectComponentId,
      componentFeatureId,
    ],
    queryFn: () =>
      axiosGet<FeatureScheduleInterface[], unknown>(
        `/projects/${projectId}/components/${projectComponentId}/features/${componentFeatureId}/schedules`,
      ),
  });
};

export const useUpdateComponentFeatureSchedulesMutation = (
  projectId: number,
  projectComponentId: number,
  componentFeatureId: number,
) => {
  const { axiosPut } = useApp();

  return useMutation({
    mutationKey: [
      "updateComponentFeatureSchedules",
      projectId,
      projectComponentId,
      componentFeatureId,
    ],
    mutationFn: (schedules: { startTime: string; endTime: string }[]) =>
      axiosPut(
        `/projects/${projectId}/components/${projectComponentId}/features/${componentFeatureId}/schedules`,
        {
          schedules,
        },
      ),
  });
};
