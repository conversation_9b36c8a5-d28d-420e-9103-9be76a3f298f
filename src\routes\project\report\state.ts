import { create } from "zustand";
import {
  AdvancedFilterFieldType,
  AdvancedFilterFormValueInterface,
} from "./interface";
import dayjs from "dayjs";

type AdvancedFilterValuesAction = {
  setFilter: (values: AdvancedFilterFormValueInterface) => void;
  setIsFiltered: (isFiltered: boolean) => void;
};

type AdvancedFilterValuesState = {
  values: AdvancedFilterFormValueInterface;
  isFiltered: boolean;
};

export const useAdvancedFilterValuesStore = create<
  AdvancedFilterValuesState & AdvancedFilterValuesAction
>((set) => ({
  values: {
    attendanceStartDate: dayjs().startOf("date").toDate(),
    attendanceEndDate: dayjs().endOf("date").toDate(),
  },
  isFiltered: false,
  setFilter: (values) => set({ values: values }),
  setIsFiltered: (isFiltered) => set({ isFiltered: isFiltered }),
}));

type AdvancedFilterFieldsAction = {
  setFileds: (fileds: AdvancedFilterFieldType[]) => void;
};

type AdvancedFilterFieldsState = { fields: AdvancedFilterFieldType[] };

export const useAdvancedFilterFiledsStore = create<
  AdvancedFilterFieldsState & AdvancedFilterFieldsAction
>((set) => ({
  fields: [
    "Agency phụ trách",
    "Role ghi nhận",
    "Ngày chấm công",
    "Nhân viên ghi nhận",
    "Mã/ Tên outlet",
    "Tỉnh/ TP",
    "Quận/ Huyện",
    "Kênh",
    "Nhóm",
    "Loại booth",
    "Trưởng nhóm quản lý",
    "Cách hiển thị data",
  ],
  setFileds: (fileds) => set({ fields: fileds }),
}));
