import SpinWinwheel, { SpinWinwheelInstance } from "@/components/SpinWinwheel";
import { <PERSON><PERSON>, Modal } from "antd";
import { AxiosError } from "axios";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useSearchParams } from "react-router-dom";
import {
  useCreateLuckyDrawsOfOrderMutation,
  useLuckyDrawsOfOrderQuery,
} from "./service";

const LuckyWheelPage = () => {
  const [searchParams] = useSearchParams();

  const wheelRef = useRef<SpinWinwheelInstance>(null!);
  const [open, setOpen] = useState(false);
  const [segments, setSegments] = useState<
    { text: string; id: number; index: number }[]
  >([]);
  const [reward, setReward] = useState<string>("");
  const [isSpinning, setIsSpinning] = useState(false);
  const [isEmptyPrize, setIsEmptyPrize] = useState(false);

  const token = useMemo(
    () => searchParams.get("token") as string,
    [searchParams],
  );

  const attendanceId = useMemo(
    () => parseInt(searchParams.get("attendanceId") ?? "0"),
    [searchParams],
  );

  const orderId = useMemo(
    () => parseInt(searchParams.get("orderId") ?? "0"),
    [searchParams],
  );

  const luckyDrawsOfOrderQuery = useLuckyDrawsOfOrderQuery(
    token,
    attendanceId,
    orderId,
  );

  const createLuckyDrawsOfOrderMutation = useCreateLuckyDrawsOfOrderMutation(
    token,
    attendanceId,
    orderId,
  );

  const customerLuckydraw = useMemo(() => {
    return luckyDrawsOfOrderQuery.data?.customerLuckyDraws?.[0];
  }, [luckyDrawsOfOrderQuery.data]);

  const handleWinwheelFinished = useCallback(() => {
    setOpen(true);
    setIsSpinning(false);
  }, []);

  useEffect(() => {
    if (luckyDrawsOfOrderQuery.isSuccess) {
      setSegments(
        customerLuckydraw?.prizeItems
          .sort((a, b) => a.ordinal - b.ordinal)
          .map((item) => ({
            text: item.projectItem.item.name,
            id: item.id,
            index: item.ordinal + 1,
          })) ?? [],
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [customerLuckydraw]);

  const spin = useCallback(async () => {
    try {
      const result = await createLuckyDrawsOfOrderMutation.mutateAsync(
        customerLuckydraw?.id ?? 0,
      );

      setIsSpinning(true);

      const segment = segments.find((item) => item.id === result.prizeItemId);

      if (segment) {
        setReward(
          segments.find((item) => item.id === result.prizeItemId)?.text ?? "",
        );

        const wheelInstance = wheelRef.current;
        if (wheelInstance && !wheelInstance.isSpinning) {
          wheelInstance.spin(segment?.index ?? 0);
        }
      }

      await luckyDrawsOfOrderQuery.refetch();
    } catch (error) {
      if (error instanceof AxiosError) {
        switch (error.response?.status) {
          case 400:
            setIsEmptyPrize(true);
            break;
          case 401:
            alert("Vui lòng đăng nhập lại");
            break;
          default:
            alert("Có lỗi xảy ra, vui lòng thử lại");
            break;
        }
      }
    }
  }, [
    createLuckyDrawsOfOrderMutation,
    customerLuckydraw?.id,
    luckyDrawsOfOrderQuery,
    segments,
  ]);

  if (token == null || attendanceId == 0 || orderId == 0) {
    return <>Data not found</>;
  }

  if (luckyDrawsOfOrderQuery.error) {
    const error = luckyDrawsOfOrderQuery.error;
    if (error instanceof AxiosError) {
      if (error.response?.status === 401) {
        alert("Vui lòng đăng nhập lại");
      }
    }
  }

  return (
    <>
      <div className="">
        <p className="text-white text-center mx-3 text-xl font-bold mb-0">
          CHÚC MỪNG QUÝ KHÁCH NHẬN ĐƯỢC VÒNG QUAY MAY MẮN
        </p>
        <div className="flex flex-col">
          <SpinWinwheel
            ref={wheelRef}
            width={400}
            height={400}
            onFinished={handleWinwheelFinished}
            segments={segments}
          />
        </div>

        <p className="text-center text-[#FFEA7D]">
          {!isEmptyPrize && (
            <> Bạn còn {customerLuckydraw?.availableTurns ?? 0} lượt quay</>
          )}

          {isEmptyPrize && <>HẾT QUÀ</>}
        </p>

        <div className="text-center mx-5">
          {!isEmptyPrize && customerLuckydraw?.availableTurns != 0 && (
            <Button
              block
              type="primary"
              className="rounded-full bg-[#FFE096] text-[#E65100] font-bold"
              onClick={spin}
              loading={createLuckyDrawsOfOrderMutation.isPending || isSpinning}
              disabled={
                customerLuckydraw?.availableTurns == 0 ||
                !luckyDrawsOfOrderQuery.isSuccess
              }
            >
              {!(createLuckyDrawsOfOrderMutation.isPending || isSpinning) && (
                <>BẮT ĐẦU</>
              )}
            </Button>
          )}

          {(isEmptyPrize || customerLuckydraw?.availableTurns == 0) && (
            <Button
              block
              type="default"
              className="rounded-full bg-[#FFFFFF] !text-[#E65100] font-bold"
              onClick={() => (window.location.href = "/exit")}
              disabled={createLuckyDrawsOfOrderMutation.isPending || isSpinning}
            >
              THOÁT
            </Button>
          )}
        </div>

        <div className="flex justify-center mt-6 text-white">
          <div>
            <p className="mb-0">THÔNG TIN KHÁCH HÀNG</p>
            <ul className="pl-5">
              {luckyDrawsOfOrderQuery.data?.customerInfoFields.map((item) => {
                return (
                  <li key={item.id}>
                    {item.label} : {item.value}
                  </li>
                );
              })}
            </ul>
          </div>
        </div>
      </div>
      <Modal
        open={open}
        onCancel={() => setOpen(false)}
        onClose={() => setOpen(false)}
        footer={null}
      >
        <h2
          className="text-5xl font-bold text-[#FCED79] text-center uppercase mt-0"
          style={{
            textShadow:
              "-2px -2px 0 #DFB342, 2px -2px 0 #DFB342, -2px 2px 0 #DFB342, 2px 2px 0 #DFB342",
          }}
        >
          {reward}
        </h2>
        <p className="text-center text-xl uppercase mb-0">
          CHÚC MỪNG BẠN ĐÃ TRÚNG THƯỞNG {reward}
        </p>
      </Modal>

      <Modal open={isEmptyPrize} footer={null} closeIcon={null}>
        <p className="text-center text-xl font-semibold">
          SỐ LƯỢNG QUÀ KHÔNG ĐỦ ĐỂ THỰC HIỆN VÒNG QUAY MAY MẮN, VUI LÒNG BỔ SUNG
          QUÀ
        </p>
      </Modal>
    </>
  );
};

export default LuckyWheelPage;
