import { formatNumber } from "@/common/helper";
import ChartContanier from "@/routes/project/chart/ChartContanier";
import { Form } from "antd";
import dayjs from "dayjs";
import _ from "lodash";
import { useCallback, useMemo, useState } from "react";
import ReactApexChart from "react-apexcharts";
import DashboardFilterZone from "../../DashboardFilterZone";
import { DashboardFilterInterface } from "../../interface";
import { useQuantityAverageQuery } from "./service";

interface QuantityAverageTabProps {
  projectId: number;
  dashboardId: number;
}
const QuantityAverageTab = ({
  projectId,
  dashboardId,
}: QuantityAverageTabProps) => {
  const [form] = Form.useForm();
  const [filter, setFilter] = useState<DashboardFilterInterface>({});

  const quantityAverageQuery = useQuantityAverageQuery(
    projectId,
    dashboardId,
    filter,
  );

  const handleApply = useCallback(() => {
    const values = form.getFieldsValue();

    const {
      dateSingle,
      projectRegionIds,
      projectOutletIds,
      channelIds,
      provinceIds,
      leaderIds,
    } = values;

    const filterValue = {
      startDate: dayjs(dateSingle).startOf("date").toISOString(),
      endDate: dayjs(dateSingle).endOf("date").toISOString(),
      projectRegionIds,
      projectOutletIds,
      channelIds,
      provinceIds,
      leaderIds: leaderIds?.map((item: { value: number }) => item.value),
    };

    if (_.isEqual(filterValue, filter)) {
      quantityAverageQuery.refetch();
    } else {
      setFilter(filterValue);
    }
  }, [filter, form, quantityAverageQuery]);

  const series = useMemo(() => {
    return (
      quantityAverageQuery.data?.map((item) => Math.round(item.average)) ?? []
    );
  }, [quantityAverageQuery.data]);

  const options = useMemo(() => {
    return {
      chart: {
        type: "line" as const,
        zoom: {
          enabled: false,
        },
        toolbar: {
          show: false,
        },
      },
      colors: ["#E51B23"],
      dataLabels: {
        enabled: true,
        position: "top" as const,
        background: {
          enabled: false,
        },
        offsetY: -5,
        style: {
          colors: ["#000000"],
        },
        formatter: (val: number) => formatNumber(Math.round(val)),
      },
      stroke: {
        curve: "smooth" as const,
        width: 3,
      },
      title: {
        text: "Pricing",
        align: "left" as const,
        style: {
          fontSize: "16px",
          fontWeight: "bold",
        },
      },
      subtitle: {
        text: "Ngàn VNĐ",
        style: {
          fontSize: "12px",
        },
      },
      markers: {
        size: 4,
      },
      xaxis: {
        categories:
          quantityAverageQuery.data?.map(
            (item) => item.shortName ?? item.name,
          ) ?? [],
        labels: {
          show: true,
          rotate: -45,
          rotateAlways: true,
        },
      },
      yaxis: {},
      tooltip: {
        enabled: true,
        y: {
          formatter: (value: number) => formatNumber(value),
        },
      },
    };
  }, [quantityAverageQuery.data]);

  return (
    <>
      <DashboardFilterZone
        handleApply={handleApply}
        fields={[
          "date.single",
          "region",
          "province",
          "chain",
          "leader",
          "outlet",
        ]}
        form={form}
        projectId={projectId}
        setFilter={setFilter}
      />

      <ChartContanier>
        <ReactApexChart
          options={options}
          series={[
            {
              name: "",
              data: series,
            },
          ]}
          type="line"
          height={500}
        />
      </ChartContanier>
    </>
  );
};

export default QuantityAverageTab;
