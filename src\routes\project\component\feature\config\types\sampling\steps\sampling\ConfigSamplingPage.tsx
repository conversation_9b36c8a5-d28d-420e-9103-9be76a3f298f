import { CURD } from "@/common/constant";
import { EditOutlined, LoadingOutlined, PlusOutlined } from "@ant-design/icons";
import { Button, Collapse, CollapseProps, Form } from "antd";
import { useCallback, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import { useSamplingGroupsQuery } from "../../service";
import ConfigGroupSamplingModal from "./ConfigGroupSamplingModal";
import SamplingGroupCollapseChild from "./SamplingGroupCollapseChild.tsx";

const ConfigSamplingPage = () => {
  const projectId = parseInt(useParams().id ?? "0");
  const componentFeatureId = parseInt(useParams().componentFeatureId ?? "0");

  const [isGroupOpen, setIsGroupOpen] = useState(false);
  const [action, setAction] = useState<CURD | null>(null);
  const [form] = Form.useForm();

  const samplingGroupsQuery = useSamplingGroupsQuery(componentFeatureId, {
    take: 0,
    skip: 0,
  });

  const onAddGroup = useCallback(() => {
    setAction(CURD.CREATE);
    setIsGroupOpen(true);
  }, []);

  const items: CollapseProps["items"] = useMemo(() => {
    return samplingGroupsQuery.data?.entities.map((samplingGroup) => ({
      key: samplingGroup.id,
      label: (
        <>
          <span className={"font-semibold text-primary mr-3"}>
            {samplingGroup.name}
          </span>

          <EditOutlined
            className="cursor-pointer"
            onClick={() => {
              setAction(CURD.UPDATE);
              setIsGroupOpen(true);
              form.setFieldsValue({
                name: samplingGroup.name,
                id: samplingGroup.id,
              });
            }}
          />
        </>
      ),
      children: (
        <SamplingGroupCollapseChild
          componentFeatureId={componentFeatureId}
          samplingGroupId={samplingGroup.id}
          projectId={projectId}
        />
      ),
    }));
  }, [componentFeatureId, form, projectId, samplingGroupsQuery.data?.entities]);

  return (
    <>
      <div className={"flex justify-end"}>
        <Button type={"primary"} icon={<PlusOutlined />} onClick={onAddGroup}>
          Thêm mới nhóm sampling
        </Button>
      </div>
      {(() => {
        if (samplingGroupsQuery.isLoading)
          return (
            <p>
              <LoadingOutlined />
            </p>
          );
        return (
          <Collapse
            defaultActiveKey={[samplingGroupsQuery.data?.entities[0]?.id ?? ""]}
            ghost
            items={items}
            destroyInactivePanel
            collapsible="icon"
          />
        );
      })()}

      {isGroupOpen && action !== null && (
        <ConfigGroupSamplingModal
          isOpen={isGroupOpen}
          onCancelCb={async () => {
            setIsGroupOpen(false);
            setAction(null);
            form.resetFields();
            await samplingGroupsQuery.refetch();
          }}
          action={action}
          form={form}
        />
      )}
    </>
  );
};

export default ConfigSamplingPage;
