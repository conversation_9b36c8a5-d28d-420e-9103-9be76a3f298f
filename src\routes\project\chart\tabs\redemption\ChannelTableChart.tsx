import _ from "lodash";
import { useMemo } from "react";
import TableChart from "../../chart-types/TableChart";
import ChartContanier from "../../ChartContanier";
import { StatisticsTypeEnum } from "../../interface";
import { useChannelStatisticsQuery } from "../../service";

interface ChannelTableChartProps {
  projectId: number;
  type: StatisticsTypeEnum;
  filter: { provinceIds?: number[]; channelIds?: number[] };
  sort: "asc" | "desc" | undefined;
  title: string;
}

const ChannelTableChart = ({
  projectId,
  type,
  filter,
  sort,
  title,
}: ChannelTableChartProps) => {
  const channelStatisticsQuery = useChannelStatisticsQuery(
    projectId,
    type,
    filter,
  );

  const statistics = useMemo(() => {
    const data = _.map(
      _.groupBy(channelStatisticsQuery.data ?? [], "channelName"),
      (item, key) => ({
        label: key,
        items: item,
      }),
    );

    if (sort === "asc") {
      data.sort(
        (a, b) =>
          a.items.reduce((sum, item) => sum + item.totalValue, 0) -
          b.items.reduce((sum, item) => sum + item.totalValue, 0),
      );
    } else if (sort === "desc") {
      data.sort(
        (a, b) =>
          b.items.reduce((sum, item) => sum + item.totalValue, 0) -
          a.items.reduce((sum, item) => sum + item.totalValue, 0),
      );
    }

    return data;
  }, [channelStatisticsQuery.data, sort]);

  return (
    <ChartContanier>
      <TableChart
        data={statistics}
        sort={sort}
        title={title}
        subtitle={"Subtitle"}
        loading={
          channelStatisticsQuery.isLoading || channelStatisticsQuery.isFetching
        }
        firstColumnTitle="Kênh"
      />
    </ChartContanier>
  );
};

export default ChannelTableChart;
