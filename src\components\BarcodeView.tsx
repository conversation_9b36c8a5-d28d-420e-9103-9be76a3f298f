import { Modal } from "antd";
import Barcode from "react-barcode";
import { ProductPackagingInterface } from "../routes/product/interface";

const BarcodeView = ({
  packaging,
}: {
  packaging: ProductPackagingInterface;
}) => {
  return (
    <div
      className="flex place-items-center cursor-pointer"
      role="button"
      tabIndex={0}
      onClick={() => {
        Modal.info({
          icon: null,
          centered: true,
          footer: null,
          closable: true,
          maskClosable: true,
          styles: {
            mask: {
              backgroundColor: "gray",
            },
          },
          width: 550,
          content: (
            <div className={"text-center"}>
              <Barcode value={packaging?.barcode} height={55} fontSize={14} />
            </div>
          ),
        });
      }}
    >
      <Barcode
        value={packaging?.barcode}
        height={22}
        fontSize={14}
        width={1.5}
      />
    </div>
  );
};

export default BarcodeView;
