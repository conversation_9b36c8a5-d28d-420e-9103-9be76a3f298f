import { useApp } from "@/UseApp";
import { AbstractFilterInterface } from "@/common/interface";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  ApiProjectComponentResponseInterface,
  ProjectComponentInterface,
  ProjectComponentPublicationInterface,
} from "./interface";

export const useCreateProjectComponentMutation = (projectId: number) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createProjectComponent", projectId],
    mutationFn: (data: object) =>
      axiosPost(`/projects/${projectId}/components`, data),
  });
};

export const useProjectComponentsQuery = (
  projectId?: number,
  filter?: object & AbstractFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["projectComponents", projectId, filter],
    enabled: !!projectId,
    queryFn: async () =>
      axiosGet<ApiProjectComponentResponseInterface, unknown>(
        `/projects/${projectId}/components`,
        filter,
      ),
  });
};

export const useUpdateProjectComponentMutation = (projectId: number) => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["useUpdateProjectComponent", projectId],
    mutationFn: ({ id, data }: { id: number; data: object }) =>
      axiosPatch(`/projects/${projectId}/components/${id}`, data),
  });
};

export const useDeleteProjectComponentMutation = (projectId: number) => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deleteProjectComponent", projectId],
    mutationFn: (id: number) =>
      axiosDelete(`/projects/${projectId}/components/${id}`),
  });
};

export const useProjectComponentQuery = (projectId: number, id: number) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["projectComponent", projectId, id],
    queryFn: async () => {
      const response = await axiosGet<ProjectComponentInterface, unknown>(
        `/projects/${projectId}/components/${id}`,
      );
      if (!Array.isArray(response)) {
        return response;
      }
      return null;
    },
  });
};

export const useProjectComponentsPublicationsQuery = (
  projectId: number,
  filter: AbstractFilterInterface & object,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["projectComponentsPublications", projectId, filter],
    queryFn: async () =>
      axiosGet<
        { entities: ProjectComponentPublicationInterface[]; count: number },
        unknown
      >(`/projects/${projectId}/components/publications`, filter),
  });
};

export const useCreateProjectComponentsPublicationsMutation = (
  projectId: number,
) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createProjectComponentsPublications", projectId],
    mutationFn: (data: { projectComponentIds: number[] }) =>
      axiosPost(`/projects/${projectId}/components/publications`, data),
  });
};
