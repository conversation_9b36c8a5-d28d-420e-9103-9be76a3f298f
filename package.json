{"name": "fms-fe-templete", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build --outDir build", "build:prod": "dotenv -- bash -c 'npm run build --- --base=\"$VITE_BASENAME\"'", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@ant-design/charts": "^2.2.3", "@ant-design/icons": "^5.5.2", "@auth0/auth0-react": "^2.2.4", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@tanstack/react-query": "^5.62.3", "@tanstack/react-query-devtools": "^5.62.3", "antd": "^5.22.3", "apexcharts": "^4.1.0", "axios": "^1.7.9", "class-validator": "^0.14.1", "dayjs": "^1.11.13", "dotenv-cli": "^7.4.4", "exceljs": "^4.4.0", "gsap": "^3.12.5", "joi": "^17.13.3", "localforage": "^1.10.0", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "react": "^18.3.1", "react-apexcharts": "^1.7.0", "react-barcode": "^1.5.3", "react-dom": "^18.3.1", "react-router-dom": "^7.0.2", "react-timer-hook": "^3.0.8", "recoil": "^0.7.7", "uuid": "^11.0.3", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.2/xlsx-0.20.2.tgz", "xlsx-import": "^2.4.7", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/compat": "^1.2.4", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.16.0", "@tanstack/eslint-plugin-query": "^5.62.1", "@types/lodash": "^4.17.13", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.17.0", "@typescript-eslint/parser": "^8.17.0", "@vitejs/plugin-react-swc": "^3.7.2", "autoprefixer": "^10.4.20", "eslint": "^9.16.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.13.0", "postcss": "^8.4.49", "prettier": "3.4.2", "tailwindcss": "^3.4.16", "typescript": "^5.7.2", "vite": "^6.0.3"}}