import { Image, Space } from "antd";
import { FALLBACK_IMAGE_STRING, IMAGE_SIZE_PREVIEW } from "@/common/constant";
import { getImageVariants } from "@/common/image.helper";

export default function ItemProduct(
  props: Readonly<{
    variants: string[];
    quantity: number;
    unitName: string;
    name: string;
    code: string;
  }>,
) {
  const { variants, quantity, unitName, name, code } = props;
  return (
    <div className="pb-1">
      <Space>
        <Image
          className="rounded border-solid border-[1px] border-[#DDE1EA]"
          src={getImageVariants(variants, "thumbnail")}
          width={IMAGE_SIZE_PREVIEW}
          preview={{
            src: getImageVariants(variants, "public"),
          }}
          fallback={FALLBACK_IMAGE_STRING}
        />
        <div>
          <p className="m-0 p-0">
            <span className="text-blue font-semibold">{quantity}</span>{" "}
            {unitName}
          </p>
          <p className="m-0 p-0">{name}</p>
          <p className="m-0 p-0">
            <span className="text-hint">#{code}</span>
          </p>
        </div>
      </Space>
    </div>
  );
}
