import { Col, Row } from "antd";
import { useParams } from "react-router-dom";
import { StatisticsTypeEnum } from "../../interface";
import ChannelBarChart from "./ChannelBarChart";
import ProvinceBarChart from "./ProvinceBarChart";

interface SamplingActivityTabProps {
  sort: "asc" | "desc" | undefined;
  filter: { provinceIds?: number[]; channelIds?: number[] };
  activeKey: string;
}

const SamplingActivityTab = ({
  sort,
  filter,
  activeKey,
}: SamplingActivityTabProps) => {
  const projectId = parseInt(useParams().id ?? "0");

  return (
    <div>
      <Row gutter={16} className="mt-0 pt-0 mb-10">
        <Col md={12}>
          <ProvinceBarChart
            projectId={projectId}
            type={StatisticsTypeEnum.HIT}
            filter={filter}
            sort={sort}
            title="Hit by province"
            activeKey={activeKey}
            key={"Hit by province"}
          />
        </Col>

        <Col md={12}>
          <ChannelBarChart
            projectId={projectId}
            type={StatisticsTypeEnum.HIT}
            filter={filter}
            sort={sort}
            title="Hit by channel"
            activeKey={activeKey}
            key={"Hit by channel"}
          />
        </Col>
      </Row>

      <Row gutter={16} className="mt-0 pt-0 mb-10">
        <Col md={12}>
          <ProvinceBarChart
            projectId={projectId}
            type={StatisticsTypeEnum.SESSION}
            filter={filter}
            sort={sort}
            title="Sessions by province"
            activeKey={activeKey}
            key={"Sessions by province"}
          />
        </Col>

        <Col md={12}>
          <ChannelBarChart
            projectId={projectId}
            type={StatisticsTypeEnum.SESSION}
            filter={filter}
            sort={sort}
            title="Sessions by channel"
            activeKey={activeKey}
            key={"Sessions by channel"}
          />
        </Col>
      </Row>
    </div>
  );
};

export default SamplingActivityTab;
