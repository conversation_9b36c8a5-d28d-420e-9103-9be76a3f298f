import { useGetProjectOutletsMutation } from "@/routes/project/outlet/service";
import { useCallback } from "react";

const useFetchProjectOutletOptions = ({
  projectId,
  filter,
  enabled,
}: {
  projectId: number;
  filter?: { projectAgenyId?: number };
  enabled?: boolean;
}) => {
  const getProjectOutletsMutation = useGetProjectOutletsMutation(projectId);

  const fetchFunction = useCallback(
    async (keyword?: string) => {
      if (!enabled) {
        return [];
      }
      const { entities: projectOutlets } =
        await getProjectOutletsMutation.mutateAsync({
          keyword,
          take: 10,
          skip: 0,
          ...filter,
        });
      return projectOutlets.map((projectOutlet) => ({
        label: projectOutlet.name,
        value: projectOutlet.id,
      }));
    },
    [enabled, filter, getProjectOutletsMutation],
  );

  return fetchFunction;
};

export default useFetchProjectOutletOptions;
