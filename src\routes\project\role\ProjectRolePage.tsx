import { useApp } from "@/UseApp";
import {
  BTN_CANCEL_TEXT,
  BTN_CONFIRM_TEXT,
  CURD,
  SELECT_ALL,
  SELECT_ALL_LABEL,
} from "@/common/constant";
import { formErrorResponseHandler } from "@/common/helper";
import CustomTable from "@/components/CustomTable/CustomTable";
import FilterComponent from "@/components/FilterComponent";
import InnerContainer from "@/components/InnerContainer/InnerContainer";
import ModalCURD from "@/components/ModalCURD";
import TableActionCell from "@/components/TableActionCell";
import { renderTableCell } from "@/components/table-cell";
import {
  DeleteOutlined,
  EditOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined,
} from "@ant-design/icons";
import { Checkbox, Form, Input, Modal } from "antd";
import { ColumnsType } from "antd/es/table";
import { useCallback, useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { ProjectRoleActionEnum, RoleInterface } from "./interface";
import { getProjectRoles } from "./service";

export default function ProjectRolePage() {
  const projectId = parseInt(useParams().id ?? "0");

  const {
    loading,
    setLoading,
    axiosPost,
    axiosPatch,
    showNotification,
    axiosGet,
    axiosDelete,
    openDeleteModal,
  } = useApp();

  const [searchForm] = Form.useForm();
  const [data, setData] = useState<RoleInterface[]>([]);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [form] = Form.useForm();
  const [formAction, setFormAction] = useState<CURD | null>(null);
  const [modal, contextHolder] = Modal.useModal();

  const fetchData = useCallback(async () => {
    setLoading(true);

    const response = await getProjectRoles(axiosGet, projectId, {
      type: "EMPLOYEE",
      take: 0,
      skip: 0,
      ...searchForm.getFieldsValue(),
    });
    if (!Array.isArray(response)) {
      setData(response.entities);
    }

    setLoading(false);
  }, [axiosGet, projectId, searchForm, setLoading]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const handleActionEditClick = (record: RoleInterface) => {
    setFormAction(CURD.UPDATE);
    form.setFieldsValue(record);
    setIsOpen(true);
  };

  const handleActionInActiveClick = (record: RoleInterface) => {
    modal.confirm({
      title: `Ngừng hoạt động role: ${record.name}`,
      content: "Bạn có chắc chắn muốn ngừng hoạt động role này?",
      okText: BTN_CONFIRM_TEXT,
      cancelText: BTN_CANCEL_TEXT,
      onOk: async () => {
        setLoading(true);
        await axiosPatch(`/projects/${projectId}/roles/${record.id}`, {
          isActive: false,
        });
        showNotification({
          type: "success",
          message: "Ngừng hoạt động role thành công.",
        });
        setLoading(false);
        fetchData();
      },
    });
  };

  const handleActionActiveClick = (record: RoleInterface) => {
    modal.confirm({
      title: `Ngừng hoạt động role: ${record.name}`,
      content: "Bạn có chắc chắn muốn kích hoạt role này?",
      okText: BTN_CONFIRM_TEXT,
      cancelText: BTN_CANCEL_TEXT,
      onOk: async () => {
        setLoading(true);
        await axiosPatch(`/projects/${projectId}/roles/${record.id}`, {
          isActive: true,
        });
        showNotification({
          type: "success",
          message: "Kích hoạt role thành công.",
        });
        setLoading(false);
        fetchData();
      },
    });
  };

  const handleActionDeleteClick = (record: RoleInterface) => {
    openDeleteModal({
      content: (
        <p>
          Bạn muốn xóa role{" "}
          <span className={"font-semibold"}>{record.name}</span> khỏi dự án?
        </p>
      ),
      deleteText: "Xác nhận xóa",
      loading: false,
      onCancel(): void {},
      onDelete: async () => {
        await axiosDelete(`/projects/${projectId}/roles/${record.id}`);
        showNotification({
          type: "success",
          message: "Xóa role nhân viên field thành công",
        });
        await fetchData();
      },
      title: `Xóa role nhân viên field`,
      titleError: "Không thể xóa role nhân viên field",
      contentHeader: (
        <>
          Không thể xóa role{" "}
          <span className="font-semibold">{record.name}</span> khỏi dự án bởi
          vì:
        </>
      ),
    });
  };

  const projectRoleActionActions = [
    {
      key: ProjectRoleActionEnum.EDIT,
      action: handleActionEditClick,
    },
    {
      key: ProjectRoleActionEnum.INACTIVE,
      action: handleActionInActiveClick,
    },
    {
      key: ProjectRoleActionEnum.ACTIVE,
      action: handleActionActiveClick,
    },
    {
      key: ProjectRoleActionEnum.DELETE,
      action: handleActionDeleteClick,
    },
  ];

  const projectItemActionItems = [
    {
      key: ProjectRoleActionEnum.EDIT,
      label: "Chỉnh sửa",
      icon: <EditOutlined />,
    },
    {
      key: ProjectRoleActionEnum.INACTIVE,
      label: "Ngừng hoạt động",
      icon: <PauseCircleOutlined />,
    },
    {
      key: ProjectRoleActionEnum.ACTIVE,
      label: "Hoạt động trở lại",
      icon: <PlayCircleOutlined />,
    },
    {
      key: ProjectRoleActionEnum.DELETE,
      label: "Xóa khỏi dự án",
      icon: <DeleteOutlined />,
    },
  ];

  const ACTION_ACTIVE = [
    ProjectRoleActionEnum.INACTIVE,
    ProjectRoleActionEnum.DELETE,
    ProjectRoleActionEnum.EDIT,
  ];
  const ACTION_INACTIVE = [
    ProjectRoleActionEnum.ACTIVE,
    ProjectRoleActionEnum.DELETE,
    ProjectRoleActionEnum.EDIT,
  ];

  const columns: ColumnsType<RoleInterface> = [
    {
      title: "Tên role",
      key: "name",
      dataIndex: "name",
      className: "min-w-[100px]",
      render: renderTableCell,
    },
    {
      title: "Vị trí",
      key: "isLeader",
      className: "min-w-[100px]",
      dataIndex: "isLeader",
      render: (isLeader: boolean) => {
        if (isLeader) {
          return (
            <span className="bg-[#FFE8D2] rounded-full pl-2 pr-2 pt-1 pb-1">
              Trưởng nhóm
            </span>
          );
        }
        return (
          <span className="rounded-full pl-2 pr-2 pt-1 pb-1">Thành viên</span>
        );
      },
    },
    {
      title: "Mô tả",
      className: "min-w-[100px]",
      key: "description",
      dataIndex: "description",
      render: renderTableCell,
    },
    {
      key: "isActive",
      className: "min-w-[100px]",
      title: "Tình trạng",
      dataIndex: "isActive",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "isActive");
      },
    },
    {
      key: "actions",
      render: (_, record) => {
        const actionKeys = record.isActive ? ACTION_ACTIVE : ACTION_INACTIVE;
        const items = projectItemActionItems.filter((item) =>
          actionKeys.includes(item.key),
        );
        return (
          <TableActionCell
            actions={projectRoleActionActions}
            items={items}
            record={record}
          />
        );
      },
      fixed: "right",
      width: 100,
    },
  ];

  const filterOptions = columns
    .filter(
      (item) =>
        !["isActive", "updatedAt", "createdAt", "actions", "isLeader"].includes(
          item.key as string,
        ),
    )
    .map((column) => {
      return {
        label: column.title as string,
        value: column.key as string | number,
      };
    });

  filterOptions.unshift({ label: SELECT_ALL_LABEL, value: SELECT_ALL });

  function searchHandler(): void {
    fetchData();
  }

  function handleAddButtonClick(): void {
    setFormAction(CURD.CREATE);
    setIsOpen(true);
  }

  const formContent = (
    <>
      <Form.Item name={"id"} hidden={true}></Form.Item>

      <Form.Item
        name="name"
        label="Tên role"
        rules={[
          {
            required: true,
            message: "Tên role không được bỏ trống.",
          },
        ]}
      >
        <Input />
      </Form.Item>
      <Form.Item name="description" label="Mô tả">
        <Input.TextArea />
      </Form.Item>
      <Form.Item
        name="isLeader"
        extra="Trưởng nhóm là người quản lý các thành viên trong nhóm và các outlet được phân công phụ trách. Chỉ một role được giữ chức trưởng nhóm trong team."
        valuePropName="checked"
      >
        <Checkbox disabled={formAction === CURD.UPDATE}>Trưởng nhóm</Checkbox>
      </Form.Item>
    </>
  );

  const onFinish = async () => {
    setLoading(true);

    await form.validateFields();
    const data = form.getFieldsValue();
    if (!data.isLeader) {
      data.isLeader = false;
    }
    data.type = "EMPLOYEE";
    const id = form.getFieldValue("id");

    try {
      switch (formAction) {
        case CURD.CREATE:
          await axiosPost(`/projects/${projectId}/roles`, data);
          showNotification({
            type: "success",
            message: "Thêm role nhân viên field thành công",
          });
          break;
        case CURD.UPDATE:
          await axiosPatch(`/projects/${projectId}/roles/${id}`, data);
          showNotification({
            type: "success",
            message: "Cập nhật  role nhân viên field thành công",
          });
          break;
        default:
          console.error("Not found action");
          return;
      }

      setFormAction(null);
      setIsOpen(false);
      form.resetFields();
      fetchData();

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      formErrorResponseHandler(form, error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <h2>Khai báo role nhân viên field</h2>
      <InnerContainer>
        <div className="mb-6">
          <FilterComponent
            filterOptions={filterOptions}
            searchHandler={searchHandler}
            handleAddButtonClick={handleAddButtonClick}
            searchForm={searchForm}
          />
        </div>

        <CustomTable
          dataSource={data}
          columns={columns}
          scroll={{
            x: "max-content",
          }}
          pagination={false}
          loading={loading}
        />
        <p className={"pb-0 mb-0"}>Số kết quả trả về: {data.length}</p>
      </InnerContainer>

      <ModalCURD
        title={
          formAction === CURD.CREATE
            ? "Thêm role nhân viên field"
            : "Role nhân viên field"
        }
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        formContent={formContent}
        form={form}
        onFinish={onFinish}
        action={formAction}
      />

      {contextHolder}
    </div>
  );
}
