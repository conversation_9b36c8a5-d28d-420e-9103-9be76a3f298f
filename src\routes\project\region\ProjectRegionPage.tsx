import { CURD } from "@/common/constant";
import { useApp } from "@/UseApp";
import {
  DeleteOutlined,
  EditOutlined,
  PlusCircleOutlined,
} from "@ant-design/icons";
import { Button, Tree } from "antd";
import type { DataNode, TreeProps } from "antd/es/tree";
import React, { ReactNode, useCallback, useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { RegionInterface } from "./interface";
import ProjectRegionModal from "./ProjectRegionModal";
import {
  useDeleteProjectRegionMutation,
  useProjectRegionTreesQuery,
} from "./service";

interface ExtendedDataNode extends DataNode {
  children?: ExtendedDataNode[];
  record: RegionInterface;
}

const ProjectRegionPage = () => {
  const projectId = parseInt(useParams().id ?? "0");
  const { openDeleteModal, showNotification } = useApp();

  const [action, setAction] = useState<CURD | undefined>(undefined);
  const [treeData, setTreeData] = useState<ExtendedDataNode[]>([]);
  const [editingKey, setEditingKey] = useState<number | null>(null);
  const [selectedRegion, setSelectedRegion] = useState<RegionInterface | null>(
    null,
  );
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);

  const projectRegionTreesQuery = useProjectRegionTreesQuery(projectId);

  const deleteProjectRegionMutation = useDeleteProjectRegionMutation(projectId);

  const handleActionDeleteClick = (node: ExtendedDataNode) => {
    openDeleteModal({
      content: (
        <p>
          Bạn muốn xóa phân vùng{" "}
          <span className={"font-semibold"}>{node.title as ReactNode}</span>{" "}
          khỏi dự án?
        </p>
      ),
      deleteText: "Xác nhận xóa",
      loading: false,
      onCancel(): void {},
      onDelete: async () => {
        await deleteProjectRegionMutation.mutateAsync(node.key as number);
        showNotification({
          type: "success",
          message: "Xóa phân vùng thành công",
        });
        await projectRegionTreesQuery.refetch();
      },
      title: `Xóa phân vùng`,
      titleError: "Không thể xóa phân vùng",
      contentHeader: (
        <>
          Không thể xóa phân vùng{" "}
          <span className="font-semibold">{node.title as ReactNode}</span> khỏi
          dự án bởi vì:
        </>
      ),
    });
  };

  const setTreeDataFromQueryChildren = useCallback(
    (children: RegionInterface[]) => {
      const treeData: ExtendedDataNode[] = children.map((item) => ({
        title: (
          <>
            <span className="text-hint">{item.tag} - </span>
            <span>{item.name}</span>
          </>
        ),
        key: item.id,
        children: item.children
          ? setTreeDataFromQueryChildren(item.children)
          : undefined,
        record: item,
      }));
      return treeData;
    },
    [],
  );

  const setTreeDataFromQuery = useCallback(() => {
    if (projectRegionTreesQuery.data) {
      const data = projectRegionTreesQuery.data;
      const treeData: ExtendedDataNode[] = data.map((item) => ({
        title: (
          <>
            <span className="text-hint">{item.tag} - </span>
            <span>{item.name}</span>
          </>
        ),
        key: item.id,
        children: item.children
          ? setTreeDataFromQueryChildren(item.children)
          : undefined,
        record: item,
      }));
      setTreeData(treeData);
    }
  }, [projectRegionTreesQuery.data, setTreeDataFromQueryChildren]);

  const addNode = (parentKey: number | null) => {
    setEditingKey(parentKey);
    setAction(CURD.CREATE);
  };

  const renderTitleWithActions = (node: ExtendedDataNode) => {
    return (
      <div className="flex items-center justify-start w-full group">
        <span>{node.title as ReactNode}</span>
        <div className="flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity ml-5">
          <PlusCircleOutlined
            onClick={() => {
              addNode(node.key as number);
              setSelectedRegion(node.record);
            }}
          />
          <EditOutlined
            onClick={() => {
              setEditingKey(node.key as number);
              setAction(CURD.UPDATE);
              setSelectedRegion(node.record);
            }}
          />
          <DeleteOutlined onClick={() => handleActionDeleteClick(node)} />
        </div>
      </div>
    );
  };

  const renderTreeNodes = (data: ExtendedDataNode[]): TreeProps["treeData"] =>
    data.map((item) => ({
      ...item,
      title: renderTitleWithActions(item),
      children: item.children ? renderTreeNodes(item.children) : undefined,
    }));

  useEffect(() => {
    setTreeDataFromQuery();
  }, [projectRegionTreesQuery.data, setTreeDataFromQuery]);

  const getExpandedKeysRecursive = useCallback(
    (data: ExtendedDataNode[]): React.Key[] => {
      return data.flatMap((item) => [
        item.key,
        ...(item.children ? getExpandedKeysRecursive(item.children) : []),
      ]);
    },
    [],
  );

  useEffect(() => {
    setExpandedKeys(getExpandedKeysRecursive(treeData));
  }, [getExpandedKeysRecursive, treeData]);

  return (
    <div>
      <h2>Phân vùng địa bàn</h2>
      <div className="bg-white p-10 rounded">
        <Button
          icon={<PlusCircleOutlined />}
          onClick={() => addNode(null)}
          className="mb-4"
          type="link"
        >
          Thêm phân vùng
        </Button>
        <Tree
          className="draggable-tree"
          blockNode
          treeData={renderTreeNodes(treeData)}
          expandedKeys={expandedKeys}
          onExpand={setExpandedKeys}
        />
      </div>

      {action && (
        <ProjectRegionModal
          action={action}
          cancelCb={() => setAction(undefined)}
          projectId={projectId}
          cb={() => {
            setAction(undefined);
            projectRegionTreesQuery.refetch();
            setEditingKey(null);
            setSelectedRegion(null);
          }}
          activeNodeId={editingKey}
          selectedRegion={selectedRegion}
        />
      )}
    </div>
  );
};
export default ProjectRegionPage;
