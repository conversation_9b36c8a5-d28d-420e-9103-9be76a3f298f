/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  DEFAULT_CURRENT_PAGE,
  DEFAULT_PAGE_SIZE,
  MAX_PAGE_SIZE,
} from "@/common/constant";
import { FormInstance } from "antd";
import _ from "lodash";
import { useCallback, useEffect, useState } from "react";
import { useSearchParams } from "react-router-dom";

interface PaginationType {
  current: number;
  pageSize: number;
  total?: number;
  onChange: (page: number, pageSize: number) => void;
  showTotal: (total: number) => string;
}

interface UseUrlFiltersOptions {
  defaultPageSize?: number;
  defaultCurrentPage?: number;

  parseParamValue?: (key: string, value: string) => any;

  onFilterChange?: (newFilter: any) => void;

  defaultFilter?: Record<string, any>;
}

interface UseUrlFiltersOptions {
  defaultPageSize?: number;
  defaultCurrentPage?: number;
  parseParamValue?: (key: string, value: string) => any;
  onFilterChange?: (newFilter: any) => void;
  transformations?: {
    toFormValues?: Record<string, (value: any) => Record<string, any>>;
    toUrlValues?: Record<string, (value: any) => Record<string, any>>;
  };
}

export function useUrlFilters({
  formInstance,
  options = {},
  handleSearchCallback,
}: {
  formInstance: FormInstance;
  options?: UseUrlFiltersOptions;
  handleSearchCallback?: () => void;
  currentCount?: number;
}) {
  const {
    defaultPageSize = DEFAULT_PAGE_SIZE,
    defaultCurrentPage = DEFAULT_CURRENT_PAGE,
    parseParamValue,
    onFilterChange,
    transformations = {},
  } = options;

  const [searchParams, setSearchParams] = useSearchParams();
  const [filter, setFilter] = useState<Record<string, any>>({});
  const [currentPage, setCurrentPage] = useState(() =>
    parseInt(searchParams.get("page") ?? defaultCurrentPage.toString()),
  );
  const [pageSize, setPageSize] = useState(() => {
    const currentPageSize = parseInt(
      searchParams.get("pageSize") ?? defaultPageSize.toString(),
    );
    return currentPageSize > MAX_PAGE_SIZE ? MAX_PAGE_SIZE : currentPageSize;
  });

  // Initialize filter and form from URL params, applying URL-to-Form transformations
  useEffect(() => {
    const initialFilter: Record<string, any> = {};
    const params = Object.fromEntries(searchParams.entries());

    Object.entries(params).forEach(([key, value]) => {
      if (key !== "page" && key !== "pageSize") {
        const parsedValue = parseParamValue
          ? parseParamValue(key, value)
          : value;

        // Apply URL-to-Form transformation if it exists
        if (transformations.toFormValues?.[key]) {
          Object.assign(
            initialFilter,
            transformations.toFormValues[key](parsedValue),
          );
        } else {
          initialFilter[key] = parsedValue;
        }

        if (parseParamValue) {
          initialFilter[key] = parseParamValue(key, value);
        } else if (!isNaN(Number(value))) {
          initialFilter[key] = Number(value);
        } else if (value === "true" || value === "false") {
          initialFilter[key] = value === "true";
        } else {
          initialFilter[key] = value;
        }
      }
    });

    setFilter(initialFilter);
    formInstance.setFieldsValue(initialFilter);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Update URL when filter changes, applying Form-to-URL transformations
  useEffect(() => {
    const params = new URLSearchParams();

    if (currentPage !== defaultCurrentPage) {
      params.set("page", currentPage.toString());
    }
    if (pageSize !== defaultPageSize) {
      params.set("pageSize", pageSize.toString());
    }

    Object.entries(filter).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== "") {
        // Apply Form-to-URL transformation if it exists
        if (transformations.toUrlValues?.[key]) {
          const transformedValues = transformations.toUrlValues[key](value);
          Object.entries(transformedValues).forEach(
            ([transformedKey, transformedValue]) => {
              params.set(transformedKey, transformedValue.toString());
            },
          );
        } else {
          params.set(key, value.toString());
        }
      }
    });

    setSearchParams(params);
    onFilterChange?.(filter);
  }, [
    filter,
    currentPage,
    pageSize,
    setSearchParams,
    defaultCurrentPage,
    defaultPageSize,
    onFilterChange,
    transformations.toUrlValues,
  ]);

  const handleSearch = useCallback(() => {
    setCurrentPage(defaultCurrentPage);
    const searchData = formInstance.getFieldsValue();

    // Apply Form-to-URL transformations before setting filter
    Object.keys(transformations.toUrlValues || {}).forEach((key) => {
      if (searchData[key] !== undefined) {
        const transformed = transformations.toUrlValues![key](searchData[key]);
        Object.assign(searchData, transformed);
        delete searchData[key]; // Remove original field after transformation
      }
    });

    if (!_.isEqual(searchData, filter)) {
      setFilter(searchData);
    } else {
      handleSearchCallback?.();
    }
  }, [
    defaultCurrentPage,
    formInstance,
    filter,
    transformations.toUrlValues,
    handleSearchCallback,
  ]);

  const handleReset = useCallback(() => {
    formInstance.resetFields();
    setFilter({});
    setCurrentPage(defaultCurrentPage);
    setPageSize(defaultPageSize);
  }, [formInstance, defaultCurrentPage, defaultPageSize]);

  const getPaginationProps = useCallback(
    (total?: number): PaginationType => ({
      current: currentPage,
      pageSize: pageSize,
      total,
      onChange: (page: number, size: number) => {
        if (size !== pageSize) {
          setPageSize(size);
          setCurrentPage(defaultCurrentPage);
        } else {
          setCurrentPage(page);
        }
      },
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
    }),
    [currentPage, pageSize, defaultCurrentPage],
  );

  return {
    filter,
    currentPage,
    pageSize,
    handleSearch,
    handleReset,
    getPaginationProps,
    setCurrentPage,
  };
}
