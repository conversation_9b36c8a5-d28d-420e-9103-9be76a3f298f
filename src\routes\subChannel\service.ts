import { useApp } from "@/UseApp.tsx";
import { AbstractFilterInterface } from "@/common/interface";
import { useMutation, useQuery } from "@tanstack/react-query";
import { ApiSubChannelResponseInterface } from "./interface";

export const useDeleteSubChannelMutation = () => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deleteSubChannel"],
    mutationFn: (id: number) => axiosDelete(`/sub-channels/${id}`),
  });
};

export const useSubChannelsQuery = (
  filter: AbstractFilterInterface & {
    clientId?: number;
    getInActive?: boolean;
  },
) => {
  const { axiosGet } = useApp();
  const { getInActive, ...restFilter } = filter;
  const queryFilter = {
    ...(getInActive ? {} : { isActive: true }),
    ...restFilter,
  };

  return useQuery({
    queryKey: ["channels", queryFilter],
    queryFn: () =>
      axiosGet<ApiSubChannelResponseInterface, unknown>(
        `/sub-channels`,
        queryFilter,
      ),
  });
};

export const useUpdateSubChannelMutation = () => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["updateSubChannel"],
    mutationFn: (data: {
      id: number;
      name?: string;
      code?: string;
      description?: string;
      isActive?: boolean;
    }) => axiosPatch(`/sub-channels/${data.id}`, data),
  });
};

export const useCreateSubChannelMutation = () => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createSubChannel"],
    mutationFn: (data: {
      name: string;
      code: string;
      channelId: number;
      description?: string;
    }) => axiosPost("/sub-channels", data),
  });
};
