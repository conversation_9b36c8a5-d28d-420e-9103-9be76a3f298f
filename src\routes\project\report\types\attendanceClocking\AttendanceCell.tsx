import { formatDateTime } from "@/common/helper";
import { getImageVariants } from "@/common/image.helper";
import ImageItem from "@/components/ImageItem";
import { Space } from "antd";
import { AttendanceInterface } from "./interface";

export default function AttendanceCell(props: {
  readonly attendance?: AttendanceInterface;
}) {
  const { attendance } = props;

  if (!attendance) {
    return <></>;
  }

  return (
    <Space>
      <ImageItem
        preview={getImageVariants(attendance?.image?.variants ?? [], "public")}
        thumbnail={getImageVariants(
          attendance?.image?.variants ?? [],
          "thumbnail",
        )}
      />
      <div className="pl-3">
        <p>{formatDateTime(attendance.deviceTime)}</p>
        <p>
          <a
            href={`https://maps.google.com/?q=${attendance.latitude},${attendance.longitude}`}
            target="_blank"
          >
            Xem vị trí chấm công
          </a>
        </p>
      </div>
    </Space>
  );
}
