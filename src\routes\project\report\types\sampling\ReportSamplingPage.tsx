import {
  CHUNK_SIZE,
  DEFAULT_CURRENT_PAGE,
  DEFAULT_PAGE_SIZE,
} from "@/common/constant";
import { createFileAndDownLoad } from "@/common/export-excel.helper.ts";
import { removeVietnameseTones } from "@/common/helper.ts";
import getColumnsTableReport from "@project/report/ColumnsTableReport";
import FilterReportZone from "@project/report/FilterReportZone";
import {
  AdvancedFilterFormValueInterface,
  AdvancedFilterInterface,
} from "@project/report/interface";
import { useAdvancedFilterFiledsStore } from "@project/report/state.ts";
import { useProjectReportOutletContext } from "@project/report/UseProjectReportOutletContext.tsx";
import { Form, Table } from "antd";
import dayjs from "dayjs";
import _ from "lodash";
import { useCallback, useEffect, useMemo, useState } from "react";
import { RecordSamplingValueInterface } from "./interface.ts";
import RecordSamplingValuesCell from "./RecordSamplingValuesCell.tsx";
import {
  useGetReportSamplingsMutation,
  useReportSamplingsQuery,
} from "./service.ts";

export default function ReportSamplingPage() {
  const {
    componentFeatureQuery,
    projectId,
    componentFeatureId,
    advancedFilterValues,
    project,
  } = useProjectReportOutletContext();

  const { setFileds: setAdvancedFilterFileds } = useAdvancedFilterFiledsStore();

  const [filterForm] = Form.useForm();
  const [filter, setFilter] = useState<AdvancedFilterInterface>({
    attendanceStartDate: dayjs().startOf("date").toDate(),
    attendanceEndDate: dayjs().endOf("date").toDate(),
  });
  const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [isExport, setIsExport] = useState(false);

  const reportSamplingsQuery = useReportSamplingsQuery(
    projectId,
    componentFeatureId,
    {
      ...filter,
      take: pageSize,
      skip: (currentPage - 1) * pageSize,
    },
  );

  const getReportSamplingsMutation = useGetReportSamplingsMutation(
    projectId,
    componentFeatureId,
  );

  const pagination = useMemo(() => {
    return {
      current: currentPage,
      total: reportSamplingsQuery.data?.count,
      pageSize: pageSize,
      onChange: (page: number) => {
        setCurrentPage(page);
      },
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: (_current: number, size: number) => {
        setPageSize(size);
        setCurrentPage(DEFAULT_CURRENT_PAGE);
      },
    };
  }, [currentPage, pageSize, reportSamplingsQuery.data?.count]);

  const setFilterForQuery = useCallback(
    async (values: AdvancedFilterFormValueInterface) => {
      setCurrentPage(DEFAULT_CURRENT_PAGE);
      if (_.isEqual(filter, values)) {
        await reportSamplingsQuery.refetch();
      }
      setFilter(values);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [filter],
  );

  const onFilterFormFinish = useCallback(() => {
    const values = filterForm.getFieldsValue();

    if (values.attendance) {
      const [attendanceStartDate, attendanceEndDate] = values.attendance;
      values.attendanceStartDate = attendanceStartDate
        ? dayjs(attendanceStartDate).startOf("date").toDate()
        : undefined;
      values.attendanceEndDate = attendanceEndDate
        ? dayjs(attendanceEndDate).endOf("date").toDate()
        : undefined;

      delete values.attendance;
    }
    setFilterForQuery(values);
  }, [filterForm, setFilterForQuery]);

  useEffect(() => {
    setFilterForQuery(advancedFilterValues);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [advancedFilterValues]);

  useEffect(() => {
    setAdvancedFilterFileds([
      "Agency phụ trách",
      "Role ghi nhận",
      "Ngày chấm công",
      "Nhân viên ghi nhận",
      "Thông tin khách",
      "Mã/ Tên outlet",
      "Tỉnh/ TP",
      "Quận/ Huyện",
      "Kênh",
      "Nhóm",
      "Loại booth",
      "Trưởng nhóm quản lý",
    ]);
  }, [setAdvancedFilterFileds]);

  useEffect(() => {
    filterForm.resetFields();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location.pathname]);

  const onExport = useCallback(async () => {
    const total = pagination.total!;

    if (total === 0) {
      return;
    }

    const fetchAllData = async () => {
      const requests = [];

      for (let skip = 0; skip < total; skip += CHUNK_SIZE) {
        requests.push(
          getReportSamplingsMutation.mutateAsync({
            take: CHUNK_SIZE,
            skip: skip,
            ...filter,
          }),
        );
      }

      const results = await Promise.all(requests);
      const allEntities = results.flatMap((result) => result.entities);

      return allEntities;
    };

    setIsExport(true);

    try {
      const allData = await fetchAllData();

      const data = allData.flatMap((recordSampling) => {
        const { projectOutlet, projectBooth, projectAgency, leader } =
          recordSampling.projectRecordFeature.projectRecord;

        const { in: attendanceIn, out: attendanceOut } =
          recordSampling.projectRecordFeature.attendance;

        const { projectRecordEmployee } = recordSampling.projectRecordFeature;
        const { recordSamplingValues } = recordSampling;

        const quantities = recordSamplingValues.map((recordSamplingValue) => {
          const { featureSampling, conversionValue, value } =
            recordSamplingValue;
          const { projectProduct, unit } = featureSampling ?? {};

          return [
            projectProduct?.product.name,
            projectProduct?.product.code,
            unit?.name,
            value,
            projectProduct?.productPackaging?.unit.name,
            conversionValue,
          ];
        });

        const mergedArray = [];

        for (const element of quantities) {
          mergedArray.push([
            ...[
              project?.id,
              project?.name,
              projectOutlet.code,
              projectOutlet.name,
              projectBooth.name,
              dayjs(attendanceIn.deviceTime).add(7, "hour").toDate(),
              attendanceOut?.deviceTime
                ? dayjs(attendanceOut?.deviceTime).add(7, "hour").toDate()
                : "",
              projectOutlet.province?.name,
              projectOutlet.district?.name,
              projectOutlet.projectAgencyChannel.channel.name,
              projectOutlet.subChannel?.name,
              projectAgency.agency.name,
              projectRecordEmployee.employee.role.name,
              projectRecordEmployee.employee.user.id,
              projectRecordEmployee.employee.user.name,
              leader.id,
              leader.user.name,
            ],
            ...element,
            dayjs(recordSampling.dataTimestamp).add(7, "hour").toDate(),
          ]);
        }
        return mergedArray;
      });

      const headers = [
        "ID dự án",
        "Tên dự án",
        "Mã outlet",
        "Tên outlet",
        "Loại booth",
        "Thời gian chấm công vào",
        "Thời gian chấm công ra",
        "Tỉnh/ TP",
        "Quận/ Huyện",
        "Kênh",
        "Nhóm",
        "Agency phụ trách",
        "Role nhân viên chấm công",
        "ID nhân viên chấm công",
        "Họ tên nhân viên chấm công",
        "ID trưởng nhóm quản lý",
        "Họ tên trưởng nhóm quản lý",
        "Tên sản phẩm sampling",
        "Mã sản phẩm sampling",
        "Quy cách phát sampling",
        "Số lượng theo quy cách phát sampling",
        "Quy cách ban đầu",
        "Số lượng theo quy cách ban đầu",
        "Thời gian gửi dữ liệu",
      ];

      const fileName = removeVietnameseTones(
        componentFeatureQuery.data?.name ?? "",
      );
      await createFileAndDownLoad({
        data,
        headers,
        fileName,
        dateTimeColumns: [6, 7, 24],
      });
    } catch (e) {
      console.error(e);
    } finally {
      setIsExport(false);
    }
  }, [
    componentFeatureQuery.data?.name,
    filter,
    getReportSamplingsMutation,
    pagination.total,
    project?.id,
    project?.name,
  ]);

  return (
    <div>
      <h2>{componentFeatureQuery.data?.name}</h2>
      <div className="bg-white p-10 rounded">
        <FilterReportZone
          form={filterForm}
          loading={
            reportSamplingsQuery.isFetching ||
            reportSamplingsQuery.isLoading ||
            isExport
          }
          fields={["keyword", "roleId", "attendance"]}
          onFinish={onFilterFormFinish}
          onExport={onExport}
        />

        <Table
          rowKey={"id"}
          pagination={pagination}
          scroll={{
            x: "max-content",
            y: pagination.total ? "80vh" : undefined,
          }}
          className="mt-3"
          dataSource={reportSamplingsQuery.data?.entities.map((item) => ({
            id: item.id,
            projectOutlet:
              item.projectRecordFeature.projectRecord.projectOutlet,
            projectBooth: item.projectRecordFeature.projectRecord.projectBooth,
            projectRecordEmployee:
              item.projectRecordFeature.projectRecordEmployee,
            attendanceIn: item.projectRecordFeature.attendance.in ?? undefined,
            attendanceOut:
              item.projectRecordFeature.attendance.out ?? undefined,
            dataTimestamp: item.dataTimestamp,
            projectAgency:
              item.projectRecordFeature.projectRecord.projectAgency,
            leader: item.projectRecordFeature.projectRecord.leader,
            recordSamplingValues: item.recordSamplingValues,
          }))}
          columns={[
            ...getColumnsTableReport([
              { tableColumn: "outletCode" },
              { tableColumn: "outletName" },
              { tableColumn: "boothName" },
              { tableColumn: "attendance" },
              { tableColumn: "dataTimestamp" },
              { tableColumn: "address" },
              { tableColumn: "channelName" },
              { tableColumn: "subChannelName" },
              { tableColumn: "agencyName" },
              { tableColumn: "recordEmployee" },
              { tableColumn: "teamLeader" },
            ]),
            {
              title: "Sampling - Quy cách phát - Quy cách đóng gói",
              fixed: "right",
              className: "min-w-[200px]",
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              render: (_value: any, record: any) => {
                const {
                  recordSamplingValues,
                }: { recordSamplingValues: RecordSamplingValueInterface[] } =
                  record;

                return (
                  <RecordSamplingValuesCell
                    recordSamplingValues={recordSamplingValues}
                  />
                );
              },
            },
          ]}
        />
      </div>
    </div>
  );
}
