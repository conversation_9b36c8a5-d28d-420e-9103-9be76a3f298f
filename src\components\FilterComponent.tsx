import useDeviceType from "@/hooks/useDeviceType.ts";
import {
  PlusOutlined,
  SearchOutlined,
  VerticalAlignBottomOutlined,
} from "@ant-design/icons";
import {
  Button,
  Col,
  Form,
  FormInstance,
  Input,
  Modal,
  Row,
  Select,
  Space,
} from "antd";
import React, { useMemo, useState } from "react";
import { useApp } from "../UseApp";
import { SELECT_ALL } from "../common/constant";
import { cn, filterOption } from "../common/helper.ts";
import { useClientsQuery } from "../routes/client/service.ts";

interface FilterComponentProps {
  searchHandler: () => void;
  searchForm: FormInstance;
  filterOptions: { label: string; value: string | number }[];
  handleExcelButtonClick?: () => void;
  handleAddButtonClick?: () => void;
  style?: React.CSSProperties;
  className?: string;
  btnAddText?: string;
  showExcelBtn?: boolean;
  hideAddBtn?: boolean;
  hasClient?: boolean;
  btnLoading?: boolean;
}

const FilterComponent = (props: FilterComponentProps) => {
  const {
    searchHandler,
    searchForm,
    filterOptions,
    handleExcelButtonClick,
    handleAddButtonClick,
    style,
    className,
    btnAddText,
    showExcelBtn,
    hideAddBtn,
    hasClient,
    btnLoading,
  } = props;

  const { loading } = useApp();
  const isMobile = useDeviceType();

  const [open, setOpen] = useState(false);

  const clientsQuery = useClientsQuery({
    take: 0,
    skip: 0,
    getInActive: true,
  });

  const content = useMemo(
    () => (
      <>
        <Form.Item
          name="filterField"
          style={{ marginInlineEnd: "0px", borderRight: "none" }}
        >
          <Select
            style={{ width: "100%", height: "40px", borderRadius: "4px" }}
            options={filterOptions}
          />
        </Form.Item>
        <Form.Item name="filterValue">
          <Input
            prefix={<SearchOutlined />}
            placeholder={"Nhập nội dung cần tìm"}
            allowClear
            className="rounded h-10"
          />
        </Form.Item>

        {hasClient && (
          <Form.Item name={"clientId"}>
            <Select
              placeholder={"Client"}
              popupMatchSelectWidth={false}
              options={clientsQuery.data?.entities.map((client) => ({
                label: client.name,
                value: client.id,
              }))}
              showSearch
              optionFilterProp="children"
              filterOption={filterOption}
              allowClear
            />
          </Form.Item>
        )}
      </>
    ),
    [clientsQuery.data?.entities, filterOptions, hasClient],
  );

  if (isMobile) {
    return (
      <div className={`w-full ${className} mb-4`}>
        <Button
          className="w-full bg-input-disabled"
          icon={<SearchOutlined />}
          type="text"
          onClick={() => setOpen(true)}
        >
          Filter
        </Button>

        <Modal
          open={open}
          footer={null}
          onCancel={() => setOpen(false)}
          title={"Filter"}
        >
          <div className="mt-5">
            <Form
              form={searchForm}
              onFinish={() => {
                searchHandler();
                setOpen(false);
              }}
              initialValues={{
                filterField: "all",
              }}
            >
              {content}

              <Form.Item>
                <Button
                  htmlType="submit"
                  type="default"
                  loading={btnLoading ?? loading}
                  className="w-full bg-[#F0F1F5]"
                >
                  Tìm kiếm
                </Button>
              </Form.Item>
            </Form>
            {showExcelBtn && (
              <Button
                icon={<VerticalAlignBottomOutlined />}
                onClick={() => {
                  handleExcelButtonClick?.();
                  setOpen(false);
                }}
                loading={loading || btnLoading}
                className="w-full"
              >
                Tải excel
              </Button>
            )}
            {!hideAddBtn && (
              <Button
                icon={<PlusOutlined />}
                type="primary"
                onClick={() => {
                  handleAddButtonClick?.();
                  setOpen(false);
                }}
                disabled={loading || btnLoading}
                className="w-full"
              >
                {btnAddText ?? "Thêm mới"}
              </Button>
            )}
          </div>
        </Modal>
      </div>
    );
  }

  return (
    <Row
      justify={"space-between"}
      style={style}
      className={cn(className, "mb-6")}
    >
      <Col>
        <Form
          layout="inline"
          form={searchForm}
          onFinish={searchHandler}
          initialValues={{
            filterField: "all",
          }}
        >
          <Space.Compact>
            <Form.Item
              name="filterField"
              style={{ marginInlineEnd: "0px", borderRight: "none" }}
            >
              <Select
                style={{ width: "150px", height: "40px", borderRadius: "4px" }}
                options={filterOptions}
              />
            </Form.Item>
            <Form.Item name="filterValue">
              <Input
                prefix={<SearchOutlined />}
                placeholder={"Nhập nội dung cần tìm"}
                allowClear
                className="rounded h-10"
              />
            </Form.Item>
          </Space.Compact>

          {hasClient && (
            <Form.Item name={"clientId"}>
              <Select
                placeholder={"Client"}
                popupMatchSelectWidth={false}
                options={clientsQuery.data?.entities.map((client) => ({
                  label: client.name,
                  value: client.id,
                }))}
                showSearch
                optionFilterProp="children"
                filterOption={filterOption}
                allowClear
              />
            </Form.Item>
          )}

          <Form.Item>
            <Button
              htmlType="submit"
              type="default"
              loading={loading || btnLoading}
            >
              Tìm kiếm
            </Button>
          </Form.Item>
          <Form.Item name="filterField" initialValue={SELECT_ALL}>
            <Input type="hidden" value={SELECT_ALL} />
          </Form.Item>
        </Form>
      </Col>
      <Col>
        <Space>
          {showExcelBtn && (
            <Button
              icon={<VerticalAlignBottomOutlined />}
              onClick={handleExcelButtonClick}
              loading={loading || btnLoading}
            >
              Tải excel
            </Button>
          )}
          {!hideAddBtn && (
            <Button
              icon={<PlusOutlined />}
              type="primary"
              onClick={handleAddButtonClick}
              disabled={loading || btnLoading}
            >
              {btnAddText ?? "Thêm mới"}
            </Button>
          )}
        </Space>
      </Col>
    </Row>
  );
};

export default FilterComponent;
