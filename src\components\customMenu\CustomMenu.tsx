import { cn } from "@/common/helper";

interface CustomMenuItemProps {
  label: React.ReactNode | string;
  isSelected: boolean;
  onClick: (key: string) => void;
  key: string;
}

const CustomMenuItem: React.FC<CustomMenuItemProps> = ({
  label,
  isSelected,
  onClick,
  key,
}) => {
  return (
    <li
      key={key}
      className={cn(
        "flex items-start w-full px-4 py-2 text-sm text-left transition-colors hover:bg-accent hover:text-accent-foreground rounded-md cursor-pointer my-1",
        isSelected && "bg-white border border-solid border-[#DDE1EA]",
      )}
    >
      <span
        className="flex-1"
        onClick={() => onClick(key)}
        role="button"
        tabIndex={0}
      >
        {label}
      </span>
    </li>
  );
};

interface CustomMenuProps {
  className?: string;
  items: Array<{
    key: string;
    label: JSX.Element | string;
  }>;
  mode?: "inline" | "horizontal";
  style?: React.CSSProperties;
  onClick?: (info: { key: string }) => void;
  selectedKeys?: string[];
}

const CustomMenu: React.FC<CustomMenuProps> = ({
  className,
  items,
  mode = "inline",
  style,
  onClick,
  selectedKeys = [],
}) => {
  const handleItemClick = (key: string) => {
    if (onClick) {
      onClick({ key });
    }
  };

  return (
    <nav
      className={cn(
        "bg-[#FAFAFA] border border-solid border-[#DDE1EA] rounded-md ",
        mode === "inline" ? "flex flex-col" : "flex flex-row",
        className,
      )}
      style={{ ...style }}
    >
      <ul
        className={cn(
          "flex-1 px-3",
          mode === "inline" ? "space-y-0" : "flex space-x-2",
        )}
      >
        {items.map((item) => (
          <CustomMenuItem
            key={item.key}
            label={item.label}
            isSelected={selectedKeys.includes(item.key)}
            onClick={() => handleItemClick(item.key)}
          />
        ))}
      </ul>
    </nav>
  );
};

export default CustomMenu;
