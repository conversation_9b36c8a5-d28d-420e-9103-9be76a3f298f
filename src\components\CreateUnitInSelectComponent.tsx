import { PlusOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Col, Divider, Input, InputRef, Row } from "antd";
import React, { useCallback, useRef, useState } from "react";
import { useApp } from "../UseApp";

export interface CreateUnitInSelectComponentProps {
  cb: () => void;
  menu: React.ReactElement;
  clientId?: number;
}

const CreateUnitInSelectComponent = (
  props: CreateUnitInSelectComponentProps,
) => {
  const { cb, menu, clientId } = props;
  const { axiosPost, loading, setLoading } = useApp();

  const inputUnitRef = useRef<InputRef>(null);
  const [unitName, setUnitName] = useState<string>("");

  const createUnit = useCallback(
    async (name: string) => {
      setLoading(true);
      try {
        await axiosPost("/units", { name, clientId });
        cb();
      } catch (e) {
        console.error(e);
      } finally {
        setLoading(false);
      }
    },
    [axiosPost, clientId, cb, setLoading],
  );

  const onUnitNameChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setUnitName(event.target.value);
  };

  const handleButtonAddUnitClick = (
    e: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement>,
  ) => {
    e.preventDefault();
    setUnitName("");
    createUnit(unitName);
    setTimeout(() => {
      inputUnitRef.current?.focus();
    }, 0);
  };
  return (
    <>
      {menu}
      <Divider style={{ margin: "8px 0" }} />
      <Row style={{ padding: "0 8px 4px" }} justify={"space-between"}>
        <Col span={15}>
          <Input
            placeholder="Nhập ĐVT mới"
            onKeyDown={(e) => e.stopPropagation()}
            ref={inputUnitRef}
            value={unitName}
            onChange={onUnitNameChange}
          />
        </Col>
        <Col span={8}>
          <Button
            type="text"
            icon={<PlusOutlined />}
            onClick={handleButtonAddUnitClick}
            loading={loading}
            disabled={!clientId}
          >
            Thêm
          </Button>
        </Col>
      </Row>
    </>
  );
};

export default CreateUnitInSelectComponent;
