import { InputNumber } from "antd";
import { useState } from "react";

interface InputNumberInCellProps {
  initValue: number | null;
  onSubmit: (newValue: number | null) => void;
}

const InputNumberInCell = ({ initValue, onSubmit }: InputNumberInCellProps) => {
  const [value, setValue] = useState(initValue);

  return (
    <InputNumber
      value={value}
      formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",")}
      onChange={setValue}
      onBlur={() => {
        onSubmit(value);
      }}
      controls={false}
      step={0}
      onPressEnter={() => {
        onSubmit(value);
      }}
      onKeyDown={(event) => {
        const { key } = event;

        if (
          [
            "Backspace",
            "Delete",
            "ArrowLeft",
            "ArrowRight",
            "Control",
          ].includes(key)
        )
          return;

        if (!/\d/.test(key)) {
          event.preventDefault();
        }
      }}
    />
  );
};

export default InputNumberInCell;
