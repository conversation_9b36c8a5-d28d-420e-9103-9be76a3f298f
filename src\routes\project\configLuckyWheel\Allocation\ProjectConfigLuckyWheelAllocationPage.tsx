import { DATE_FORMAT } from "@/common/constant";
import CustomTable from "@/components/CustomTable/CustomTable";
import DebounceSelect from "@/components/DebounceSelectComponent";
import { useUrlFiltersWithQuery } from "@/hooks/useUrlFiltersWithQuery";
import { EyeInvisibleOutlined, EyeOutlined } from "@ant-design/icons";
import {
  Button,
  Col,
  DatePicker,
  Form,
  Pagination,
  Row,
  Space,
  TreeSelect,
} from "antd";
import dayjs, { Dayjs } from "dayjs";
import _ from "lodash";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import { ProjectOutletInterface } from "../../outlet/interface";
import { useGetProjectOutletsMutation } from "../../outlet/service";
import { RegionInterface, RegionOption } from "../../region/interface";
import { useProjectRegionTreesQuery } from "../../region/service";
import { useProjectQuery } from "../../services";
import { SelectedProjectOuutletLuckyDrawItemInterface } from "../interface";
import {
  useLuckyDrawAllocationsQuery,
  useLuckyDrawItemsQuery,
} from "../service";
import AllocationModal from "./AllocationModal";

const ProjectConfigLuckyWheelAllocationPage = () => {
  const projectId = parseInt(useParams().id ?? "0");
  const luckyDrawId = parseInt(useParams().luckyDrawId ?? "0");

  const [searchForm] = Form.useForm();
  const [type, setType] = useState<"total" | "date" | undefined>(undefined);
  const [selectedDate, setSelectedDate] = useState<string | undefined>(
    undefined,
  );
  const [
    selectedProjectOuutletLuckyDrawItem,
    setSelectedProjectOuutletLuckyDrawItem,
  ] = useState<SelectedProjectOuutletLuckyDrawItemInterface | undefined>(
    undefined,
  );
  const [hideRemaining, setHideRemaining] = useState(false);
  const [dates, setDates] = useState<[Dayjs | null, Dayjs | null]>([
    dayjs(),
    dayjs(),
  ]);

  const {
    query: { data, refetch, isFetching },
    handleSearch,
    getPaginationProps,
  } = useUrlFiltersWithQuery<ProjectOutletInterface>({
    formInstance: searchForm,
    useQueryHook: useLuckyDrawAllocationsQuery,
    queryParams: [projectId, luckyDrawId],
    options: {
      urlSync: {
        enabled: false,
      },
      defaultPageSize: 10,
    },
  });

  const luckyDrawItemsQuery = useLuckyDrawItemsQuery(projectId, luckyDrawId);
  const projectQuery = useProjectQuery(projectId);
  const projectRegionTreesQuery = useProjectRegionTreesQuery(projectId);

  const getProjectOutletsMutation = useGetProjectOutletsMutation(
    projectId ?? 0,
  );

  const dataSources = useMemo(() => {
    return (
      data?.entities?.flatMap((allocation) => {
        return (
          luckyDrawItemsQuery.data?.entities.map((item, index) => {
            const projectLuckyDrawAllocationTotal =
              allocation.projectLuckyDrawAllocations?.find(
                (itemOfAllocation) =>
                  itemOfAllocation.projectLuckyDrawItem.id === item.id &&
                  _.isNull(itemOfAllocation.allocatedDate),
              );
            return {
              projectItem: item.projectItem,
              id: `${allocation.id}-${item.projectItem.id}`,
              rowSpan:
                index === 0 ? luckyDrawItemsQuery.data?.entities.length : 0,
              projectOutlet: allocation,
              projectLuckyDrawItemId: item.id,
              total: {
                allocatedQuantity:
                  projectLuckyDrawAllocationTotal?.allocatedQuantity,
                issuedQuantity: projectLuckyDrawAllocationTotal?.issuedQuantity,
              },
            };
          }) ?? []
        );
      }) ?? []
    );
  }, [data?.entities, luckyDrawItemsQuery.data?.entities]);

  const pagination = useMemo(() => getPaginationProps(), [getPaginationProps]);

  const datesRange = useMemo(() => {
    const [startDate, endDate] = dates;

    if (startDate && endDate) {
      const start = dayjs(startDate);
      const end = dayjs(endDate);

      const diff = end.diff(start, "days");

      return Array.from({ length: diff + 1 }, (_, index) =>
        start.add(index, "days"),
      );
    }
  }, [dates]);

  const regionOptions = useMemo(() => {
    const getRegions = (regions: RegionInterface[]): RegionOption[] => {
      return regions.map((region: RegionInterface) => ({
        value: region.id,
        label: region.name,
        children: getRegions(region.children || []),
      }));
    };

    return getRegions(projectRegionTreesQuery.data || []);
  }, [projectRegionTreesQuery.data]);

  const fetchProjectOutletOptions = useCallback(
    async (keyword?: string) => {
      if (keyword?.length === 0) {
        return [];
      }

      const { entities: projectOutlets } =
        await getProjectOutletsMutation.mutateAsync({
          keyword,
          take: 10,
          skip: 0,
        });
      return projectOutlets.map((projectOutlet) => ({
        label: projectOutlet.name,
        value: projectOutlet.id,
      }));
    },
    [getProjectOutletsMutation],
  );

  useEffect(() => {
    setDates([
      dayjs(projectQuery.data?.startDate),
      dayjs(projectQuery.data?.endDate),
    ]);
  }, [projectQuery.data?.endDate, projectQuery.data?.startDate]);

  return (
    <>
      <Row justify={"space-between"}>
        <Col>
          <Form form={searchForm} layout="inline" onFinish={handleSearch}>
            <Form.Item name={"projectRegionId"}>
              <TreeSelect
                treeData={regionOptions}
                treeDefaultExpandAll
                placeholder="Phân vùng"
                popupMatchSelectWidth={false}
                allowClear
              />
            </Form.Item>

            <Form.Item name="projectOutletId">
              <DebounceSelect
                fetchOptions={fetchProjectOutletOptions}
                showSearch
                labelInValue={false}
                allowClear
                placeholder={"Outlet"}
                style={{ minWidth: 150 }}
                popupMatchSelectWidth={false}
              />
            </Form.Item>

            <Form.Item>
              <Button htmlType="submit">Tìm kiếm</Button>
            </Form.Item>
          </Form>
        </Col>
        <Col>
          <Space>
            <DatePicker.RangePicker
              minDate={dayjs(projectQuery.data?.startDate)}
              maxDate={dayjs(projectQuery.data?.endDate)}
              onChange={(values) => {
                if (values) {
                  setDates(values);
                } else {
                  setDates([null, null]);
                }
              }}
              value={dates}
              format={DATE_FORMAT}
              allowClear={false}
              allowEmpty={[false, false]}
            />
            <Button
              icon={hideRemaining ? <EyeOutlined /> : <EyeInvisibleOutlined />}
              onClick={() => setHideRemaining((value) => !value)}
            >
              {hideRemaining ? "Hiện còn lại" : "Ẩn còn lại"}
            </Button>
          </Space>
        </Col>
      </Row>

      <CustomTable
        pagination={false}
        className="mt-5"
        data={dataSources ?? []}
        rowKey={"id"}
        bordered
        loading={isFetching}
        scroll={{
          x: "max-content",
          y: "80vh",
        }}
        columns={[
          {
            title: "Tên outlet",
            dataIndex: "projectOutlet",
            render: (projectOutlet: ProjectOutletInterface) =>
              projectOutlet.name,
            onCell: (record) => ({
              rowSpan: record?.rowSpan,
            }),
            fixed: "left",
          },
          {
            title: "Quà",
            dataIndex: "projectItem",
            render: (projectItem) => projectItem.item.name,
            fixed: "left",
          },
          {
            title: "Tổng",
            dataIndex: "total",
            className: "min-w-[80px] cursor-pointer",
            align: "center",
            fixed: "left",
            onCell: (record) => ({
              onClick: () => {
                setType("total");
                setSelectedProjectOuutletLuckyDrawItem(record);
              },
            }),
            render: (total) => {
              const textClassName =
                _.isNumber(total.allocatedQuantity) &&
                total.allocatedQuantity - total.issuedQuantity > 0
                  ? "font-semibold"
                  : "text-hint";

              if (hideRemaining) {
                return (
                  <span className={textClassName}>
                    {total.allocatedQuantity ?? "_"}
                  </span>
                );
              }
              return (
                <span className={textClassName}>
                  {_.isNumber(total.allocatedQuantity) &&
                  _.isNumber(total.issuedQuantity)
                    ? `${total.allocatedQuantity - total.issuedQuantity}/${total.allocatedQuantity}`
                    : "_"}
                </span>
              );
            },
          },
          ...(datesRange?.map((date) => ({
            title: date.format("DD/MM"),
            className: "min-w-[80px] cursor-pointer",
            align: "center" as const,
            dataIndex: "projectOutlet",
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            render: (projectOutlet: ProjectOutletInterface, record: any) => {
              const projectLuckyDrawAllocation =
                projectOutlet.projectLuckyDrawAllocations?.find((item) => {
                  return (
                    date.format(DATE_FORMAT) ===
                      dayjs(item.allocatedDate, "YYYY-MM-DD").format(
                        DATE_FORMAT,
                      ) &&
                    record.projectLuckyDrawItemId ===
                      item.projectLuckyDrawItem.id
                  );
                });
              if (!projectLuckyDrawAllocation) {
                return <span className="text-hint">_</span>;
              }

              const { allocatedQuantity, issuedQuantity } =
                projectLuckyDrawAllocation ?? {};

              const textClassName =
                _.isNumber(allocatedQuantity) &&
                allocatedQuantity - (issuedQuantity ?? 0) > 0
                  ? "font-semibold"
                  : "text-hint";

              if (hideRemaining) {
                return (
                  <span className={textClassName}>
                    {allocatedQuantity ?? "_"}
                  </span>
                );
              }

              return (
                <span className={textClassName}>
                  {_.isNumber(allocatedQuantity) && _.isNumber(issuedQuantity)
                    ? `${allocatedQuantity - issuedQuantity}/${allocatedQuantity}`
                    : "_"}
                </span>
              );
            },
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            onCell: (record: any) => ({
              onClick: () => {
                setType("date");
                setSelectedProjectOuutletLuckyDrawItem(record);
                setSelectedDate(date.format(DATE_FORMAT));
              },
            }),
          })) ?? []),
        ]}
      />
      <div className="mt-3 flex justify-end">
        <Pagination {...pagination} />
      </div>

      {type && (
        <AllocationModal
          projectId={projectId}
          luckyDrawId={luckyDrawId}
          selectedProjectOuutletLuckyDrawItem={
            selectedProjectOuutletLuckyDrawItem
          }
          onClose={() => {
            setType(undefined);
            setSelectedProjectOuutletLuckyDrawItem(undefined);
            setType(undefined);
            setSelectedDate(undefined);
          }}
          cb={refetch}
          selectedDate={selectedDate}
          type={type}
        />
      )}
    </>
  );
};

export default ProjectConfigLuckyWheelAllocationPage;
