import { useApp } from "@/UseApp";
import { AbstractFilterInterface } from "@/common/interface";
import { ProjectProductInterface } from "@project/product/interface.ts";
import { useMutation, useQuery } from "@tanstack/react-query";
import { OrderProductInterface } from "../../interface";

export const useOrderProductsQuery = (
  componentFeatureId: number,
  filter?: object & AbstractFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["orderProducts", componentFeatureId, filter],
    queryFn: () =>
      axiosGet<{ entities: OrderProductInterface[]; count: number }, unknown>(
        `/features/${componentFeatureId}/order/products`,
        filter,
      ),
  });
};

export const useUpdateOrderProductPriceMutation = (
  componentFeatureId: number,
) => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["updateOrderProductPrice", componentFeatureId],
    mutationFn: (data: { id: number; price: number }) =>
      axiosPatch(
        `/features/${componentFeatureId}/order/products/${data.id}`,
        data,
      ),
  });
};

export const useOrderProductsAvailableQuery = (
  componentFeatureId: number,
  filter?: AbstractFilterInterface,
  enabled?: boolean,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["orderProductsAvailable", componentFeatureId, filter],
    queryFn: () =>
      axiosGet<{ entities: ProjectProductInterface[]; count: number }, unknown>(
        `/features/${componentFeatureId}/order/products/availables`,
        filter,
      ),
    enabled,
  });
};

export const useCreateOrderProductMutation = (componentFeatureId: number) => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createOrderProduct", componentFeatureId],
    mutationFn: (ids: number[]) =>
      axiosPost(`/features/${componentFeatureId}/order/products`, {
        projectProductIds: ids,
      }),
  });
};

export const useDeleteOrderProductMutation = (componentFeatureId: number) => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deleteOrderProduct", componentFeatureId],
    mutationFn: (id: number) =>
      axiosDelete(`/features/${componentFeatureId}/order/products/${id}`),
  });
};
