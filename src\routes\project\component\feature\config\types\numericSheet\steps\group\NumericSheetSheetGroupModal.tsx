import { CURD } from "@/common/constant";
import ModalCURD from "@/components/ModalCURD";
import { useApp } from "@/UseApp";
import { Form, Input } from "antd";
import { useCallback, useEffect } from "react";
import { FeatureNumericSheetInterface } from "../../interface";
import {
  useCreateNumericSheetMutation,
  useUpdateNumericSheetMutation,
} from "../../service";

interface NumericSheetGroupModalProps {
  cancelCb: () => void;
  componentFeatureId: number;
  cb: () => void;
  action: CURD | null;
  selectedNumericSheet: FeatureNumericSheetInterface | undefined;
}

const NumericSheetGroupModal = ({
  cancelCb,
  componentFeatureId,
  cb,
  action,
  selectedNumericSheet,
}: NumericSheetGroupModalProps) => {
  const { showNotification } = useApp();

  const [form] = Form.useForm();

  const createNumericSheetMutation =
    useCreateNumericSheetMutation(componentFeatureId);
  const updateNumericSheetMutation =
    useUpdateNumericSheetMutation(componentFeatureId);

  const formContent = (
    <Form.Item
      label={"Tên nhóm item"}
      name={"name"}
      rules={[{ required: true }]}
    >
      <Input />
    </Form.Item>
  );

  const onFinish = useCallback(async () => {
    if (action === CURD.CREATE) {
      await createNumericSheetMutation.mutateAsync(form.getFieldsValue());

      showNotification({
        type: "success",
        message: "Thêm nhóm item thành công",
      });
    }

    if (action === CURD.UPDATE) {
      await updateNumericSheetMutation.mutateAsync({
        ...form.getFieldsValue(),
        id: selectedNumericSheet?.id,
      });

      showNotification({
        type: "success",
        message: "Cập nhật nhóm item thành công",
      });
    }

    cb();
  }, [
    action,
    cb,
    createNumericSheetMutation,
    form,
    selectedNumericSheet?.id,
    showNotification,
    updateNumericSheetMutation,
  ]);

  useEffect(() => {
    if (action === CURD.UPDATE && selectedNumericSheet) {
      form.setFieldsValue({
        name: selectedNumericSheet.name,
      });
    }
  }, [action, form, selectedNumericSheet]);

  return (
    <ModalCURD
      title={action === CURD.CREATE ? "Thêm nhóm item" : "Cập nhật nhóm item"}
      isOpen={true}
      formContent={formContent}
      form={form}
      onFinish={onFinish}
      onCancelCb={() => {
        form.resetFields();
        cancelCb();
      }}
      action={action}
    />
  );
};

export default NumericSheetGroupModal;
