import { useApp } from "@/UseApp.tsx";
import { AbstractFilterInterface } from "@/common/interface.ts";
import { AppContextInterface } from "@/interface.ts";
import { useMutation, useQuery } from "@tanstack/react-query";
import { ApiChannelResponseInterface } from "./interface";

export const getChannels = async (
  axiosGet: AppContextInterface["axiosGet"],
  filter: unknown,
) => {
  return await axiosGet<ApiChannelResponseInterface, unknown>(
    "/channels",
    filter,
  );
};

export const useChannelsQuery = (
  filter: AbstractFilterInterface & {
    clientId?: number;
    getInActive?: boolean;
  },
) => {
  const { axiosGet } = useApp();
  const { getInActive, ...restFilter } = filter;
  const queryFilter = {
    ...(getInActive ? {} : { isActive: true }),
    ...restFilter,
  };

  return useQuery({
    queryKey: ["channels", queryFilter],
    queryFn: () => getChannels(axiosGet, queryFilter),
  });
};

export const useDeleteChannelMutation = () => {
  const { axiosDelete } = useApp();

  return useMutation({
    mutationKey: ["deleteChannel"],
    mutationFn: (id: number) => axiosDelete(`/channels/${id}`),
  });
};

export const useCreateChannelMutation = () => {
  const { axiosPost } = useApp();

  return useMutation({
    mutationKey: ["createChannel"],
    mutationFn: (data: {
      name: string;
      code: string;
      clientId: number;
      description?: string;
    }) => axiosPost("/channels", data),
  });
};

export const useUpdateChannelMutation = () => {
  const { axiosPatch } = useApp();

  return useMutation({
    mutationKey: ["updateChannel"],
    mutationFn: (data: {
      id: number;
      name?: string;
      code?: string;
      description?: string;
      isActive?: boolean;
    }) => axiosPatch(`/channels/${data.id}`, data),
  });
};
