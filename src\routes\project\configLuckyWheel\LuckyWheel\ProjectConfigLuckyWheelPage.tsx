import { CURD } from "@/common/constant";
import { useCanPermission } from "@/layouts/MainLayout/hook";
import {
  EditOutlined,
  PlusOutlined,
  UnorderedListOutlined,
} from "@ant-design/icons";
import { Button, Collapse } from "antd";
import { useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { PermissionEnum } from "../../interface";
import { ProjectLuckyDrawInterface } from "../interface";
import { useLuckyDrawsQuery } from "../service";
import ProjectConfigLuckyWheelCollapseChild from "./ProjectConfigLuckyWheelCollapseChild";
import ProjectConfigLuckyWheelModal from "./ProjectConfigLuckyWheelModal";

const ProjectConfigLuckyWheelPage = () => {
  const projectId = parseInt(useParams().id ?? "0");
  const navigate = useNavigate();
  const { canPermissionFunction } = useCanPermission(projectId);

  const [luckyWheelAction, setLuckyWheelAction] = useState<CURD | undefined>(
    undefined,
  );
  const [selectedLuckydraw, setSelectedLuckydraw] = useState<
    ProjectLuckyDrawInterface | undefined
  >(undefined);

  const luckyDrawsQuery = useLuckyDrawsQuery(projectId, {
    take: 0,
  });

  return (
    <>
      {canPermissionFunction(PermissionEnum.NONE_ACCESS) && (
        <div className="justify-end flex">
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setLuckyWheelAction(CURD.CREATE);
            }}
          >
            Thêm vòng quay may mắn
          </Button>
        </div>
      )}

      <Collapse
        destroyInactivePanel
        collapsible="icon"
        // className="mt-5"
        ghost
        items={luckyDrawsQuery.data?.entities.map((item) => ({
          key: item.id,
          label: (
            <>
              <span className={"font-semibold text-primary mr-3"}>
                {item.name}
              </span>

              {canPermissionFunction(PermissionEnum.NONE_ACCESS) && (
                <EditOutlined
                  className="cursor-pointer mr-3"
                  onClick={() => {
                    setLuckyWheelAction(CURD.UPDATE);
                    setSelectedLuckydraw(item);
                  }}
                />
              )}

              <UnorderedListOutlined
                className="cursor-pointer"
                onClick={() => {
                  navigate(`${item.id}/allocation`);
                }}
              />
            </>
          ),
          children: (
            <ProjectConfigLuckyWheelCollapseChild
              projectId={projectId}
              luckyDrawId={item.id}
            />
          ),
        }))}
      />

      <ProjectConfigLuckyWheelModal
        open={!!luckyWheelAction}
        onClose={() => {
          setLuckyWheelAction(undefined);
          setSelectedLuckydraw(undefined);
        }}
        action={luckyWheelAction}
        projectId={projectId}
        cb={() => {
          luckyDrawsQuery.refetch();
        }}
        selectedLuckydraw={selectedLuckydraw}
      />
    </>
  );
};

export default ProjectConfigLuckyWheelPage;
