import { FolderOpenOutlined, LoadingOutlined } from "@ant-design/icons";
import { Form, FormInstance, Upload } from "antd";
import { useCallback, useEffect, useState } from "react";

interface UploadImageComponentProps {
  readonly form: FormInstance;
  readonly imageUrlFieldName: string;
  readonly fieldLabel: string;
}
export default function UploadImageComponent(props: UploadImageComponentProps) {
  const { form, imageUrlFieldName, fieldLabel } = props;

  const [isLoadingUpload, setIsLoadingUpload] = useState(false);
  const [imageUrl, setImageUrl] = useState<string | undefined>(undefined);

  const uploadButton = (
    <div>{isLoadingUpload ? <LoadingOutlined /> : <FolderOpenOutlined />}</div>
  );

  const beforeUpload = useCallback(
    async (file: File) => {
      setIsLoadingUpload(true);
      const imageUrl = URL.createObjectURL(file);
      if (imageUrl) {
        setImageUrl(imageUrl);
        form.setFieldValue(imageUrlFieldName, imageUrl);
      }
      setIsLoadingUpload(false);
      return false;
    },
    [form, imageUrlFieldName],
  );

  useEffect(() => {
    setImageUrl(form.getFieldValue("initUrl"));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [form.getFieldValue("initUrl")]);

  return (
    <>
      <Form.Item name={imageUrlFieldName} hidden></Form.Item>
      <Form.Item label={fieldLabel} name={"imageFile"}>
        <Upload
          name="avatar"
          listType="picture-card"
          showUploadList={false}
          beforeUpload={beforeUpload}
          accept={"image/*"}
        >
          {(() => {
            if (isLoadingUpload) {
              return <LoadingOutlined />;
            }
            if (imageUrl) {
              return (
                <img src={imageUrl} alt="avatar" style={{ width: "100%" }} />
              );
            } else {
              return uploadButton;
            }
          })()}
        </Upload>
      </Form.Item>
    </>
  );
}
