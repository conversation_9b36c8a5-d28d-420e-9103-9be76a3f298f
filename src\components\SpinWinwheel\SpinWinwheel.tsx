import { default as pointerAsset } from "@/assets/img/nut.png";
import { default as wheelAsset } from "@/assets/img/vongquay.png";
import React, { useEffect, useId, useRef } from "react";
import { loadImageAsync, sleepAsync } from "../../common/helper";
import { Winwheel } from "./Winwheel";

export interface SpinWinwheelProps {
  width: number;
  height: number;
  style?: React.CSSProperties;
  onFinished?: (segmentNumber: number) => void;
  segments: { text: string }[];
}

export interface SpinWinwheelInstance {
  spin(segment: number): void;
  isSpinning: boolean;
}

const SpinWinwheel = React.forwardRef<SpinWinwheelInstance, SpinWinwheelProps>(
  (props, ref) => {
    const { style, width, height, onFinished, segments } = props;

    const canvasRef = useRef<HTMLCanvasElement>(null!);
    const canvasId = useId();

    useEffect(() => {
      const canvas = canvasRef.current;
      if (!canvas) {
        return;
      }

      let theWheel: Winwheel = null!;

      const { offsetWidth, offsetHeight, parentElement } = canvas;

      if (parentElement) {
        let percent = 1;

        try {
          const computedStyle = window.getComputedStyle(parentElement);
          const width =
            parseFloat(computedStyle.width) -
            parseFloat(computedStyle.paddingLeft) -
            parseFloat(computedStyle.paddingRight);
          percent = width / offsetWidth;
        } catch (_) {
          console.log(_);
          percent = parentElement.offsetWidth / offsetWidth;
        }

        if (percent < 1) {
          canvas.width = percent * offsetWidth;
          canvas.height = percent * offsetHeight;
        }
      }

      const ctx = canvas.getContext("2d");
      const centerX = canvas.width / 2;
      const centerY = canvas.height / 2;
      if (ctx) {
        const text = "Đang tải...";
        ctx.save();
        ctx.font = "20px Helvetica Neue";
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.fillText(text, centerX - ctx.measureText(text).width / 2, centerY);
        ctx.restore();
      }

      let squarePointer = 100;
      if (canvas.width / width < 1) {
        squarePointer = canvas.width * (5 / 24);
      }

      let squareWheel = 400;
      if (canvas.height / height < 1) {
        squareWheel = canvas.height * (5 / 6);
      }

      Promise.all([
        loadImageAsync(pointerAsset, squarePointer, squarePointer),
        loadImageAsync(wheelAsset, squareWheel, squareWheel),
        sleepAsync(0),
      ]).then((results) => {
        theWheel = new Winwheel({
          canvasId,
          numSegments: 8,
          drawMode: "image",
          pointerImage: results[0],
          wheelImage: results[1],
          segments: segments,
          // Definition of the animation
          animation: {
            type: "spinToStop",
            duration: 5,
            spins: 8,
            callbackFinished: onFinished,
          },
        });

        theWheel.draw();

        const instance: SpinWinwheelInstance = {
          spin: (segment: number) => {
            const stopAt = theWheel.getRandomForSegment(segment);
            if (stopAt === 0) {
              return;
            }
            theWheel.stopAnimation(false); // @ts-expect-error not in its
            theWheel.rotationAngle = 0; // @ts-expect-error not in its
            theWheel.animation.stopAngle = stopAt;
            theWheel.startAnimation();
          },
          isSpinning: false,
        };

        if (ref) {
          if (typeof ref === "function") {
            ref(instance);
          } else if (typeof ref === "object") {
            ref.current = instance;
          }
        }
      });

      return () => {
        if (theWheel) {
          theWheel.clearCanvas();
          theWheel.draw();
        }
      };
    }, [canvasId, canvasRef, onFinished, ref, width, height, segments]);

    return (
      <canvas
        id={canvasId}
        ref={canvasRef}
        style={style}
        width={width}
        height={height}
        data-responsivescaleheight={true}
        data-margin={0}
      >
        Canvas not supported, use another browser.
      </canvas>
    );
  },
);

export default SpinWinwheel;
