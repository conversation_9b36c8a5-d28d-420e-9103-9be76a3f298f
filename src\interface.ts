import { AxiosInstance } from "axios";
import React from "react";
import { UserInterface } from "./routes/user/interface";

export interface AlertTypeInterface {
  type: "success" | "error" | "warning" | "info";
  title?: string;
  message?: string;
}

export interface NotificationTypeInterface {
  type: "success" | "error" | "warning" | "info";
  message?: string;
}

export interface ModalPropsInterface {
  title: string | React.ReactNode;
  loading?: boolean;
  onCancel: () => void;
  content: React.ReactNode;
}

export interface DeleteModalPropsInterface extends ModalPropsInterface {
  onDelete: () => void | Promise<void>;
  deleteText: string;
  hideDeleteButton?: boolean;
  titleError?: string;
  contentHeader?: React.ReactNode;
}

export interface ConfirmModalPropsInterface extends ModalPropsInterface {
  onConfirm: () => void | Promise<void>;
  confirmText?: string;
  hideDeleteButton?: boolean;
  titleError?: string;
}

export interface ErrorModalPropsInterface {
  title: string;
  content: React.ReactNode;
}

export interface AppContextInterface {
  axiosInstance: AxiosInstance | null;
  showAlert: (alert: AlertTypeInterface) => void;

  axiosGet<E, P>(url: string, params?: P): Promise<E>;

  axiosPost<E, P>(url: string, body: P): Promise<E>;

  axiosPatch<E, P>(url: string, body: P): Promise<E | void>;

  axiosPut<E, P>(url: string, body: P): Promise<E | void>;

  axiosDelete<E, P>(url: string, body?: P): Promise<E | void>;

  showNotification: (notification: NotificationTypeInterface) => void;

  loading: boolean;

  setLoading: (loading: boolean) => void;

  userLogin?: UserInterface | null;

  setUserLogin: (user: UserInterface) => void;

  openDeleteModal: (props: DeleteModalPropsInterface) => void;

  openConfirmModal: (props: ConfirmModalPropsInterface) => void;
}
