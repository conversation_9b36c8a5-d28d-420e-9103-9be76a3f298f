import { Col, Row } from "antd";
import { useParams } from "react-router-dom";
import { StatisticsTypeEnum } from "../../interface";
import ChannelTableChart from "./ChannelTableChart";
import ProvinceTableChart from "./ProvinceTableChart";

interface RedemptionTabProps {
  sort: "asc" | "desc" | undefined;
  filter: { provinceIds?: number[]; channelIds?: number[] };
  activeKey: string;
}
const RedemptionTab = ({ sort, filter, activeKey }: RedemptionTabProps) => {
  const projectId = parseInt(useParams().id ?? "0");

  return (
    <>
      <h2>Progress by province</h2>
      <Row gutter={16} className="mt-0 pt-0 mb-10">
        <Col md={12}>
          <ProvinceTableChart
            projectId={projectId}
            type={StatisticsTypeEnum.GIFT}
            filter={filter}
            sort={sort}
            title={"Quà thường"}
            activeKey={activeKey}
          />
        </Col>

        <Col md={12}>
          <ProvinceTableChart
            projectId={projectId}
            type={StatisticsTypeEnum.GAME}
            filter={filter}
            sort={sort}
            title={"Quà game"}
            activeKey={activeKey}
          />
        </Col>
      </Row>

      <h2>Progress by channel</h2>
      <Row gutter={16} className="mt-0 pt-0 mb-10">
        <Col md={12}>
          <ChannelTableChart
            projectId={projectId}
            type={StatisticsTypeEnum.GIFT}
            filter={filter}
            sort={sort}
            title={"Quà thường"}
          />
        </Col>

        <Col md={12}>
          <ChannelTableChart
            projectId={projectId}
            type={StatisticsTypeEnum.GAME}
            filter={filter}
            sort={sort}
            title={"Quà game"}
          />
        </Col>
      </Row>
    </>
  );
};

export default RedemptionTab;
