import { AbstractEntityInterface } from "@/common/interface";
import { RecordQuantityValueInterface } from "@/routes/project/report/types/multipleEntitiesQuantityCapturing/interface";
import { UserInterface } from "@/routes/user/interface";

interface ToolQuantityInterface extends AbstractEntityInterface {
  createdByUser: UserInterface;
}

export interface EditMultipleEntitiesQuantityCapturingInterface
  extends AbstractEntityInterface {
  dataUuid: string;
  dataTimestamp: string;
  createdByUser: UserInterface;
  toolQuantity?: ToolQuantityInterface;
  recordQuantityValues: RecordQuantityValueInterface[];
}
