import equal from "@/assets/equal.svg";
import { CURD } from "@/common/constant";
import { filterOption } from "@/common/helper.ts";
import DebounceSelect from "@/components/DebounceSelectComponent";
import ModalCURD from "@/components/ModalCURD";
import { useApp } from "@/UseApp.tsx";
import {
  useProjectBrandsQuery,
  useProjectUnitsQuery,
} from "@project/general/services";
import { useFindProjectProductMutation } from "@project/product/service";
import { Col, Form, FormInstance, Input, InputNumber, Row, Select } from "antd";
import { useCallback } from "react";
import {
  useCreateSamplingInSamplingGroupMutation,
  useUpdateSamplingInSamplingGroupMutation,
} from "../../service.ts";

interface ConfigSamplingModalProps {
  isOpen: boolean;
  action: CURD | null;
  projectId: number;
  componentFeatureId: number;
  samplingGroupId: number;
  onCancelCb: () => void;
  form: FormInstance;
}

const ConfigSamplingModal = ({
  isOpen,
  action,
  projectId,
  componentFeatureId,
  samplingGroupId,
  onCancelCb,
  form,
}: ConfigSamplingModalProps) => {
  const { showNotification } = useApp();

  const projectBrandsQuery = useProjectBrandsQuery(projectId);
  const projectUnitsQuery = useProjectUnitsQuery(
    projectId,
    { take: 0, skip: 0 },
    isOpen,
  );

  const findProjectProductMutation = useFindProjectProductMutation(projectId);
  const createSamplingInSamplingGroupMutation =
    useCreateSamplingInSamplingGroupMutation(
      componentFeatureId,
      samplingGroupId,
    );
  const updateSamplingInSamplingGroupMutation =
    useUpdateSamplingInSamplingGroupMutation(
      componentFeatureId,
      samplingGroupId,
    );

  const fetchProjectProductOptions = useCallback(
    async (keyword?: string) => {
      const brandId = form.getFieldValue("brandId");
      if (brandId) {
        const { entities: projectProducts } =
          await findProjectProductMutation.mutateAsync({
            keyword: keyword,
            brandId: form.getFieldValue("brandId"),
            take: 10,
            skip: 0,
          });
        return projectProducts.map((projectProduct) => ({
          label: `${projectProduct.product.code} - ${projectProduct.productPackaging?.unit.name} - ${projectProduct.product.name}`,
          value: projectProduct.id,
          unitName: projectProduct.productPackaging?.unit.name,
        }));
      }
      return [];
    },
    [findProjectProductMutation, form],
  );

  const formContent = (
    <>
      <Form.Item
        name={"brandId"}
        label="Nhãn hàng"
        rules={[{ required: true }]}
      >
        <Select
          showSearch={true}
          filterOption={filterOption}
          options={projectBrandsQuery.data?.map((projectBrand) => ({
            label: projectBrand.brand.name,
            value: projectBrand.brand.id,
          }))}
          onChange={() => {
            form.resetFields(["projectProductId"]);
          }}
          disabled={action === CURD.UPDATE}
        />
      </Form.Item>

      <Form.Item name={"projectProductId"} label="Sampling cần thêm">
        <DebounceSelect
          autoClearSearchValue
          fetchOptions={fetchProjectProductOptions}
          style={{ width: "100%" }}
          showSearch={true}
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          onChange={(_, option: any) => {
            form.setFieldValue("unitName", option.unitName);
          }}
          labelInValue={false}
          disabled={action === CURD.UPDATE}
        />
      </Form.Item>

      <hr className="border-dashed border-t-0 border-[#DDE1EA]" />

      <p className="font-semibold text-base">
        Tỷ lệ quy đổi từ quy cách ban đầu sang quy cách phát sampling
      </p>

      <Row gutter={16}>
        <Col md={12}>
          <Form.Item
            label="Số lượng"
            rules={[{ required: true }]}
            name={"numerator"}
          >
            <InputNumber controls={false} min={0} step={0} className="w-full" />
          </Form.Item>
        </Col>
        <Col md={12}>
          <Form.Item
            label="Quy cách ban đầu"
            rules={[{ required: true }]}
            name={"unitName"}
          >
            <Input disabled />
          </Form.Item>
        </Col>
      </Row>

      <div className="flex justify-center">
        <img src={equal} alt="equal" className="w-8 h-8" />
      </div>

      <Row gutter={16}>
        <Col md={12}>
          <Form.Item
            label="Số lượng"
            rules={[{ required: true }, { type: "integer" }]}
            name={"denominator"}
          >
            <InputNumber controls={false} min={0} step={0} className="w-full" />
          </Form.Item>
        </Col>
        <Col md={12}>
          <Form.Item
            label="Quy cách phát sampling"
            rules={[{ required: true }]}
            name={"unitId"}
          >
            <Select
              showSearch={true}
              filterOption={filterOption}
              options={projectUnitsQuery.data?.entities.map((unit) => ({
                label: unit.name,
                value: unit.id,
              }))}
            />
          </Form.Item>
        </Col>
      </Row>
    </>
  );

  const onFinish = useCallback(async () => {
    if (action === CURD.CREATE) {
      await createSamplingInSamplingGroupMutation.mutateAsync(
        form.getFieldsValue(),
      );

      showNotification({
        type: "success",
        message: "Thêm sampling thành công",
      });
    }

    if (action === CURD.UPDATE) {
      await updateSamplingInSamplingGroupMutation.mutateAsync({
        id: form.getFieldValue("id"),
        denominator: form.getFieldValue("denominator"),
        numerator: form.getFieldValue("numerator"),
        unitId: form.getFieldValue("unitId"),
      });

      showNotification({
        type: "success",
        message: "Cập nhật sampling thành công",
      });
    }

    form.resetFields();
    onCancelCb();
  }, [
    action,
    createSamplingInSamplingGroupMutation,
    form,
    onCancelCb,
    showNotification,
    updateSamplingInSamplingGroupMutation,
  ]);
  return (
    <ModalCURD
      title={action === CURD.CREATE ? "Thêm sampling" : "Cập nhật sampling"}
      isOpen={isOpen}
      formContent={formContent}
      form={form}
      onFinish={onFinish}
      action={action}
      onCancelCb={onCancelCb}
    />
  );
};

export default ConfigSamplingModal;
