import { BTN_CANCEL_TEXT, BTN_CONFIRM_TEXT, CURD } from "@/common/constant.ts";
import DragSortRowComponent from "@/components/DragSortRowComponent.tsx";
import { renderTableCell } from "@/components/table-cell.tsx";
import { renderTableOptionCell } from "@/components/table-option-cell.tsx";
import { useUrlFiltersWithQuery } from "@/hooks/useUrlFiltersWithQuery.ts";
import { useApp } from "@/UseApp.tsx";
import { PlusOutlined, SearchOutlined } from "@ant-design/icons";
import { DndContext, DragEndEvent } from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
  arrayMove,
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { OosZoneInterface } from "@project/component/feature/config/types/outOfStockStatus/interface.ts";
import OutOfStockStatusZoneModal from "@project/component/feature/config/types/outOfStockStatus/steps/zone/OutOfStockStatusZoneModal.tsx";
import {
  useArrangeOosZoneMutation,
  useDeleteOosZoneMutation,
  useOosZonesQuery,
  useUpdateOosZoneMutation,
} from "@project/component/feature/config/types/outOfStockStatus/steps/zone/service.ts";
import { Button, Col, Form, Input, Modal, Row, Space, Table } from "antd";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useParams } from "react-router-dom";

const OutOfStockStatusZonePage = () => {
  const { showNotification, openDeleteModal } = useApp();

  const componentFeatureId = parseInt(useParams().componentFeatureId ?? "0");

  const [searchForm] = Form.useForm();
  const [action, setAction] = useState<CURD | null>(null);
  const [selectedOosZone, setSelectedOosZone] =
    useState<OosZoneInterface | null>(null);

  const {
    query: { data, refetch, isFetching },
    handleSearch,
  } = useUrlFiltersWithQuery<OosZoneInterface>({
    formInstance: searchForm,
    useQueryHook: useOosZonesQuery,
    queryParams: [componentFeatureId],
    options: {
      urlSync: {
        enabled: false,
      },
      defaultPageSize: 0,
    },
  });

  const arrangeOosZoneMutation = useArrangeOosZoneMutation(componentFeatureId);
  const updateOosZoneMutation = useUpdateOosZoneMutation(componentFeatureId);
  const deleteOosZoneMutation = useDeleteOosZoneMutation(componentFeatureId);

  const [dataSource, setDataSource] = useState(data?.entities ?? []);

  const loading = useMemo(() => isFetching, [isFetching]);

  const onDragEnd = useCallback(
    async ({ active, over }: DragEndEvent) => {
      if (!over) {
        return;
      }
      if (active.id !== over?.id) {
        setDataSource((previous) => {
          const activeIndex = previous?.findIndex((i) => i.id === active.id);
          const overIndex = previous?.findIndex((i) => i.id === over?.id);
          return arrayMove(previous, activeIndex, overIndex);
        });

        await arrangeOosZoneMutation.mutateAsync({
          id: active.id as number,
          overFeatureOosZoneId: over?.id as number,
        });
      }
    },
    [arrangeOosZoneMutation],
  );

  const editClick = useCallback((record: OosZoneInterface) => {
    setAction(CURD.UPDATE);
    setSelectedOosZone(record);
  }, []);

  const activeClick = useCallback(
    (record: OosZoneInterface) => {
      Modal.confirm({
        title: `Kích hoạt động vị trí ghi nhận dữ liệu: ${record.name}`,
        content:
          "Bạn có chắc chắn muốn ngừng hoạt động vị trí ghi nhận dữ liệu này?",
        okText: BTN_CONFIRM_TEXT,
        cancelText: BTN_CANCEL_TEXT,
        onOk: async () => {
          await updateOosZoneMutation.mutateAsync({
            isActive: true,
            id: record.id,
          });

          showNotification({
            type: "success",
            message: "Ngừng hoạt động thành công.",
          });
          await refetch();
        },
      });
    },
    [refetch, showNotification, updateOosZoneMutation],
  );

  const inActiveClick = useCallback(
    (record: OosZoneInterface) => {
      Modal.confirm({
        title: `Ngừng hoạt động vị trí ghi nhận dữ liệu: ${record.name}`,
        content:
          "Bạn có chắc chắn muốn ngừng hoạt động vị trí ghi nhận dữ liệu này?",
        okText: BTN_CONFIRM_TEXT,
        cancelText: BTN_CANCEL_TEXT,
        onOk: async () => {
          await updateOosZoneMutation.mutateAsync({
            isActive: false,
            id: record.id,
          });

          showNotification({
            type: "success",
            message: "Ngừng hoạt động thành công.",
          });
          await refetch();
        },
      });
    },
    [refetch, showNotification, updateOosZoneMutation],
  );

  const handleBtnDeleteClick = useCallback(
    (record: OosZoneInterface) => {
      openDeleteModal({
        content: (
          <p>
            Bạn muốn xóa vị trí ghi nhận dữ liệu{" "}
            <span className={"font-semibold"}>{record.name}</span> khỏi chức
            năng?
          </p>
        ),
        deleteText: "Xác nhận xóa",
        loading: false,
        onCancel(): void {},
        onDelete: async () => {
          await deleteOosZoneMutation.mutateAsync(record.id);

          showNotification({
            type: "success",
            message: "Xóa vị trí ghi nhận dữ liệu thành công",
          });

          await refetch();
        },
        title: `Xóa vị trí ghi nhận dữ liệu`,
        titleError: "Không thể xóa vị trí ghi nhận dữ liệu",
        contentHeader: (
          <>
            Không thể xóa vị trí ghi nhận dữ liệu{" "}
            <span className="font-semibold">{record.name}</span> bởi vì:
          </>
        ),
      });
    },
    [deleteOosZoneMutation, openDeleteModal, refetch, showNotification],
  );

  useEffect(() => {
    setDataSource(data?.entities ?? []);
  }, [data?.entities]);

  return (
    <>
      <Row justify={"space-between"}>
        <Col>
          <Form form={searchForm} onFinish={handleSearch}>
            <Space>
              <Form.Item name={"keyword"}>
                <Input
                  placeholder="Tìm theo tên giá trị"
                  prefix={<SearchOutlined />}
                />
              </Form.Item>

              <Form.Item>
                <Button type="default" htmlType="submit" loading={loading}>
                  Tìm kiếm
                </Button>
              </Form.Item>
            </Space>
          </Form>
        </Col>
        <Col>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setAction(CURD.CREATE)}
          >
            Thêm giá trị
          </Button>
        </Col>
      </Row>

      <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
        <SortableContext
          items={dataSource.map((i) => i.id)}
          strategy={verticalListSortingStrategy}
        >
          <Table
            rowKey={(o) => o.id}
            dataSource={dataSource}
            loading={loading}
            components={{
              body: {
                row: DragSortRowComponent,
              },
            }}
            columns={[
              {
                key: "sort",
              },
              {
                title: "Vị trí ghi nhận dữ liệu",
                dataIndex: "name",
              },
              {
                title: "Mô tả",
                dataIndex: "description",
              },
              {
                key: "isActive",
                title: "Tình trạng",
                dataIndex: "isActive",
                render: (value, record, index) => {
                  return renderTableCell(value, record, index, "isActive");
                },
              },
              {
                key: "actions",
                render: (_, record) => {
                  return renderTableOptionCell(
                    record,
                    editClick,
                    inActiveClick,
                    activeClick,
                    handleBtnDeleteClick,
                  );
                },
              },
            ]}
            pagination={false}
          />
        </SortableContext>
      </DndContext>

      {action && (
        <OutOfStockStatusZoneModal
          onCancelCb={() => setAction(null)}
          componentFeatureId={componentFeatureId}
          cb={() => {
            setAction(null);
            refetch();
          }}
          action={action}
          selectedOosZone={selectedOosZone}
        />
      )}
    </>
  );
};

export default OutOfStockStatusZonePage;
