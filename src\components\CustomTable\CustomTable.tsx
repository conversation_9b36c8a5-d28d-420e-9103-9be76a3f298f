import useDeviceType from "@/hooks/useDeviceType";
import { Table, TableProps } from "antd";
import { ColumnsType } from "antd/es/table";
import { useMemo } from "react";

interface PaginationType {
  current: number;
  pageSize: number;
  total?: number;
}

interface CustomTableProps<T> extends Omit<TableProps<T>, "pagination"> {
  data?: T[];
  columns: ColumnsType<T>;
  loading?: boolean;
  pagination?: PaginationType | false;
  rowKey?: string | ((record: T) => string);
}

const CustomTable = <T extends object>({
  data,
  columns,
  loading,
  pagination,
  rowKey = "id",

  ...restProps
}: CustomTableProps<T>) => {
  const isMoblie = useDeviceType();

  const getRowKey = (record: T): string => {
    if (typeof rowKey === "function") {
      return rowKey(record);
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return (record as any)[rowKey];
  };

  const columnsWithFilter = useMemo(() => {
    columns.forEach((column) => {
      if (isMoblie) {
        column.fixed = undefined;
      }
    });
    return columns;
  }, [columns, isMoblie]);

  return (
    <Table<T>
      dataSource={data}
      columns={columnsWithFilter}
      rowKey={getRowKey}
      scroll={{
        x: "max-content",
        y: pagination && pagination?.total ? "80vh" : undefined,
      }}
      pagination={pagination}
      loading={loading}
      {...restProps}
    />
  );
};

export default CustomTable;
