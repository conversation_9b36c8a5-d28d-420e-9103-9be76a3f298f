import { AbstractEntityInterface } from "@/common/interface";

export interface ApiUserResponseInterface {
  entities: UserInterface[];
  count: number;
}

export interface AppConfigInterface {
  user: {
    role: string[];
    type: string[];
  };
}

export interface UserInterface extends AbstractEntityInterface {
  name: string;
  username: string;
  phone: string;
  email: string;
  type: UserTypeEnum;
  picture?: string;
  gender?: string;
  code?: string;
}

export enum UserTypeEnum {
  CLIENT = "CLIENT",
  ADMIN = "ADMIN",
  AGENCY = "AGENCY",
  EMPLOYEE = "EMPLOYEE",
  MANAGER = "MANAGER",
}

export enum GenderEnum {
  MALE = "male",
  FEMALE = "female",
  OTHER = "other",
}

export const GENDER = {
  ["Nam"]: GenderEnum.MALE,
  ["Nữ"]: GenderEnum.FEMALE,
  ["Khác"]: GenderEnum.OTHER,
};
