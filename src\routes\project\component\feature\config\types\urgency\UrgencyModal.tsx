import { CURD } from "@/common/constant";
import FormNumberInput from "@/components/FormNumberInput";
import ModalCURD from "@/components/ModalCURD";
import { useApp } from "@/UseApp";
import { ColorPicker, Form, Input, Switch } from "antd";
import { useCallback, useEffect } from "react";
import { FeatureUrgencyInterface } from "./interface";
import { useCreateUrgencyMutation, useUpdateUrgencyMutation } from "./service";

interface UrgencyModalProps {
  action: CURD;
  componentFeatureId: number;
  cb: () => void;
  selectedUrgency?: FeatureUrgencyInterface;
  cancelCb: () => void;
}

const UrgencyModal = ({
  action,
  componentFeatureId,
  cb,
  cancelCb,
  selectedUrgency,
}: UrgencyModalProps) => {
  const { showNotification } = useApp();
  const [form] = Form.useForm();

  const createUrgencyMutation = useCreateUrgencyMutation(componentFeatureId);
  const updateUrgencyMutation = useUpdateUrgencyMutation(componentFeatureId);

  const formContent = (
    <>
      <Form.Item
        label="Tên loại báo c<PERSON>o khẩn"
        name={"name"}
        rules={[{ required: true }]}
      >
        <Input />
      </Form.Item>

      <Form.Item
        name={"isNoteRequired"}
        valuePropName="checked"
        label="Cho phép nhập thêm ghi chú"
        layout="horizontal"
      >
        <Switch />
      </Form.Item>

      <Form.Item label="Thời gian tiêu chuẩn" name={"maxDuration"}>
        <FormNumberInput suffix={"phút"} />
      </Form.Item>

      <Form.Item
        name={"backgroundColor"}
        label={"Màu nền"}
        layout={"horizontal"}
      >
        <ColorPicker
          showText
          defaultValue={"#FFFFFF"}
          mode={"single"}
          disabledAlpha
          format="hex"
        />
      </Form.Item>

      <Form.Item
        name={"foregroundColor"}
        label={"Màu chữ"}
        layout={"horizontal"}
      >
        <ColorPicker
          showText
          defaultValue={"#000000"}
          disabledAlpha
          format="hex"
        />
      </Form.Item>
    </>
  );

  const onFinish = useCallback(async () => {
    const data = form.getFieldsValue();
    data.isNoteRequired = !!data.isNoteRequired;

    const { backgroundColor, foregroundColor } = data;

    data.backgroundColor =
      typeof backgroundColor === "string"
        ? backgroundColor
        : backgroundColor.toHexString();

    data.foregroundColor =
      typeof foregroundColor === "string"
        ? foregroundColor
        : foregroundColor.toHexString();

    if (action === CURD.CREATE) {
      await createUrgencyMutation.mutateAsync(data);

      showNotification({
        type: "success",
        message: "Thêm thành công",
      });
    }

    if (action === CURD.UPDATE && selectedUrgency) {
      await updateUrgencyMutation.mutateAsync({
        id: selectedUrgency.id,
        ...data,
      });

      showNotification({
        type: "success",
        message: "Cập nhật thành công",
      });
    }

    cb();
  }, [
    action,
    cb,
    createUrgencyMutation,
    form,
    selectedUrgency,
    showNotification,
    updateUrgencyMutation,
  ]);

  useEffect(() => {
    if (action === CURD.UPDATE && selectedUrgency) {
      form.setFieldsValue({
        name: selectedUrgency.name,
        isNoteRequired: selectedUrgency.isNoteRequired,
        maxDuration: selectedUrgency.maxDuration,
        backgroundColor: selectedUrgency.backgroundColor,
        foregroundColor: selectedUrgency.foregroundColor,
      });
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [action, selectedUrgency]);

  useEffect(() => {
    if (action === CURD.CREATE) {
      form.setFieldsValue({
        isNoteRequired: false,
        maxDuration: 10,
        backgroundColor: "#FFFFFF",
        foregroundColor: "#000000",
      });
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [action]);

  return (
    <ModalCURD
      title={
        action === CURD.CREATE
          ? "Thêm loại báo cáo khẩn"
          : "Cập nhật loại báo cáo khẩn"
      }
      isOpen={true}
      formContent={formContent}
      form={form}
      onFinish={onFinish}
      action={action}
      btnConfirmLoading={createUrgencyMutation.isPending}
      onCancelCb={cancelCb}
    />
  );
};

export default UrgencyModal;
