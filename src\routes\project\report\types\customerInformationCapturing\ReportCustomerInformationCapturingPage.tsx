import {
  DownOutlined,
  FileImageOutlined,
  RightOutlined,
} from "@ant-design/icons";
import { Table } from "antd";
import FilterReportZone from "../../FilterReportZone";
import { FILTER_FORM_FIELDS } from "./constants";
import { useReportCustomerInformationCapturing } from "./hooks/useReportCustomerInformationCapturing";
import {
  getReportTableColumns,
  transformTableData,
} from "./utils/tableColumns";

export default function ReportCustomerInformationCapturingPage() {
  const {
    componentFeatureQuery,
    reportOrdersQuery,
    pagination,
    filterForm,
    onFilterFormFinish,
    exportVertical,
    exportHorizontal,
    exportImages,
    isTableLoading,
    isFilterLoading,
  } = useReportCustomerInformationCapturing();

  return (
    <div>
      <h2>{componentFeatureQuery.data?.name}</h2>
      <div className="bg-white p-10 rounded">
        <FilterReportZone
          form={filterForm}
          loading={isFilterLoading}
          fields={FILTER_FORM_FIELDS}
          onFinish={onFilterFormFinish}
          customExports={[
            {
              label: "Dữ liệu đơn hàng (dọc)",
              icon: <DownOutlined />,
              onClick: exportVertical,
              key: "exportVertical",
            },
            {
              label: "Dữ liệu đơn hàng (ngang)",
              icon: <RightOutlined />,
              onClick: exportHorizontal,
              key: "exportHorizontal",
            },
            {
              label: "Hình đã chụp",
              icon: <FileImageOutlined />,
              onClick: exportImages,
              key: "exportImage",
            },
          ]}
        />

        <Table
          loading={isTableLoading}
          rowKey={"id"}
          pagination={pagination}
          scroll={{
            x: "max-content",
            y: pagination.total ? "80vh" : undefined,
          }}
          className="mt-3"
          dataSource={transformTableData(
            reportOrdersQuery.data?.entities ?? [],
          )}
          columns={getReportTableColumns()}
        />
      </div>
    </div>
  );
}
