import { useApp } from "@/UseApp";
import { useQuery } from "@tanstack/react-query";

export const useSitecheckChartByProvinceQuery = (projectId: number) => {
  const { axiosGet } = useApp();
  return useQuery({
    queryKey: ["sitecheckChartByProvince", projectId],
    queryFn: () =>
      axiosGet<
        {
          data: {
            datasets: {
              label: string;
              data: number[];
              color: string;
            }[];
            labels: string[];
          };
        },
        unknown
      >(`/projects/${projectId}/sitecheck/chart-by-province`),
  });
};
