import logo from "@/assets/img/logo.png";
import { FALLBACK_IMAGE_STRING } from "@/common/constant";
import MobileMenu from "@/components/MobileMenu/MobileMenu";
import { Image } from "antd";
import { Outlet, useNavigate, useParams } from "react-router-dom";

export default function MobileLayout() {
  const navigate = useNavigate();
  const projectId = parseInt(useParams().id ?? "0");

  return (
    <div className="flex flex-col min-h-screen bg-gray-100">
      <MobileMenu
        logo={
          <Image
            fallback={FALLBACK_IMAGE_STRING}
            preview={false}
            height={31}
            src={logo}
            onClick={() => navigate("/")}
            className={"cursor-pointer"}
          />
        }
        className="top-0 z-50"
        headerClassName="bg-gray-50"
        menuClassName="bg-white"
        menuItemClassName="menu-item-hover"
        hamburgerIconClassName="text-blue-600"
        closeIconClassName="text-red-600"
        projectId={projectId}
      />
      <main className="px-2">
        <Outlet />
      </main>
    </div>
  );
}
