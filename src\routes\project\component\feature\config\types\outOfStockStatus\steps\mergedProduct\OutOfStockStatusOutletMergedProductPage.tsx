import { CURD } from "@/common/constant.ts";
import { filterOption } from "@/common/helper.ts";
import ProductItemCell from "@/components/ProductItemCell";
import TableActionCell from "@/components/TableActionCell.tsx";
import { useUrlFiltersWithQuery } from "@/hooks/useUrlFiltersWithQuery.ts";
import { useApp } from "@/UseApp";
import {
  DeleteOutlined,
  EditOutlined,
  FileSearchOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { OosMergedProductInterface } from "@project/component/feature/config/types/outOfStockStatus/interface.ts";
import OutOfStockStatusOutletMergedProductModal from "@project/component/feature/config/types/outOfStockStatus/steps/mergedProduct/OutOfStockStatusOutletMergedProductModal.tsx";
import OutOfStockStatusOutletMergedProductProductModal from "@project/component/feature/config/types/outOfStockStatus/steps/mergedProduct/OutOfStockStatusOutletMergedProductProductModal.tsx";
import {
  useDeleteOosMergedProductMutation,
  useGetOosMergedProductsQuery,
} from "@project/component/feature/config/types/outOfStockStatus/steps/mergedProduct/service.ts";
import { useProjectBrandsQuery } from "@project/general/services.ts";
import { Button, Col, Form, Input, Row, Select, Space, Table } from "antd";
import { useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import OutOfStockStatusOutletMergedProductProductAvailablesModal from "./OutOfStockStatusOutletMergedProductProductAvailablesModal";

const OutOfStockStatusOutletMergedProductPage = () => {
  const componentFeatureId = parseInt(useParams().componentFeatureId ?? "0");
  const projectId = parseInt(useParams().id ?? "0");

  const { openDeleteModal, showNotification } = useApp();

  const [searchForm] = Form.useForm();
  const [action, setAction] = useState<CURD | null>(null);
  const [selectedOosMergedProduct, setSelectedOosMergedProduct] =
    useState<OosMergedProductInterface | null>(null);
  const [add, setAdd] = useState(false);
  const [view, setView] = useState(false);

  const projectBrandsQuery = useProjectBrandsQuery(projectId);

  const deleteOosMergedProductMutation =
    useDeleteOosMergedProductMutation(componentFeatureId);

  const {
    query: { data, refetch, isFetching },
    handleSearch,
  } = useUrlFiltersWithQuery<OosMergedProductInterface>({
    formInstance: searchForm,
    useQueryHook: useGetOosMergedProductsQuery,
    queryParams: [componentFeatureId],
    options: {
      urlSync: {
        enabled: false,
      },
      defaultPageSize: 0,
    },
  });

  const loading = useMemo(() => isFetching, [isFetching]);

  const actions = useMemo(
    () => [
      {
        key: "edit",
        action: (record: OosMergedProductInterface) => {
          setAction(CURD.UPDATE);
          setSelectedOosMergedProduct(record);
        },
      },
      {
        key: "add",
        action: (record: OosMergedProductInterface) => {
          setSelectedOosMergedProduct(record);
          setAdd(true);
        },
      },
      {
        key: "view",
        action: (record: OosMergedProductInterface) => {
          setSelectedOosMergedProduct(record);
          setView(true);
        },
      },
      {
        key: "delete",
        action: (record: OosMergedProductInterface) => {
          const { productName: name } = record;
          openDeleteModal({
            content: (
              <p>
                Bạn muốn xóa sản phẩm gộp{" "}
                <span className={"font-semibold"}>{name}</span> khỏi nhóm sản
                phẩm gộp?
              </p>
            ),
            deleteText: "Xác nhận xóa",
            loading: false,
            onCancel(): void {},
            onDelete: async () => {
              await deleteOosMergedProductMutation.mutateAsync(record.id);

              showNotification({
                type: "success",
                message: "Xóa sản phẩm gộp thành công",
              });

              await refetch();
            },
            title: `Xóa sản phẩm gộp`,
            titleError: "Không thể xóa sản phẩm gộp",
            contentHeader: (
              <>
                Không thể xóa sản phẩm gộp{" "}
                <span className="font-semibold">{name}</span> bởi vì:
              </>
            ),
          });
        },
      },
    ],
    [
      deleteOosMergedProductMutation,
      openDeleteModal,
      refetch,
      showNotification,
    ],
  );

  const items = useMemo(
    () => [
      {
        key: "edit",
        label: "Chỉnh sửa",
        icon: <EditOutlined />,
      },
      {
        key: "add",
        label: "Thêm sản phẩm vào nhóm",
        icon: <PlusOutlined />,
      },
      {
        key: "view",
        label: "Xem sản phẩm trong nhóm",
        icon: <FileSearchOutlined />,
      },
      {
        key: "delete",
        label: "Xóa nhóm",
        icon: <DeleteOutlined />,
      },
    ],
    [],
  );

  return (
    <>
      <Row justify={"space-between"}>
        <Col>
          <Form form={searchForm} onFinish={handleSearch}>
            <Space>
              <Form.Item name={"keyword"}>
                <Input
                  placeholder="Tìm theo tên, mã sản phẩm"
                  prefix={<SearchOutlined />}
                />
              </Form.Item>

              <Form.Item name={"projectBrandId"}>
                <Select
                  placeholder={"Nhãn hàng"}
                  allowClear
                  filterOption={filterOption}
                  options={projectBrandsQuery.data?.map((item) => ({
                    label: item.brand.name,
                    value: item.id,
                  }))}
                  popupMatchSelectWidth={false}
                />
              </Form.Item>

              <Form.Item>
                <Button type="default" htmlType="submit" loading={loading}>
                  Tìm kiếm
                </Button>
              </Form.Item>
            </Space>
          </Form>
        </Col>
        <Col>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setAction(CURD.CREATE)}
          >
            Thêm giá trị
          </Button>
        </Col>
      </Row>

      <Table
        rowKey={(o) => o.id}
        dataSource={data?.entities}
        loading={loading}
        columns={[
          {
            title: "Tên sản phẩm gộp",
            dataIndex: "productName",
            render: (_, record) => {
              return (
                <ProductItemCell
                  variants={record?.image?.variants ?? []}
                  name={record.productName}
                />
              );
            },
          },
          {
            title: "Mã sản phẩm gộp",
            dataIndex: "productCode",
          },
          {
            title: "Nhãn hàng",
            dataIndex: "projectBrand",
            render: (projectBrand) => projectBrand?.brand?.name,
          },
          {
            title: "Quy cách",
            dataIndex: "unit",
            render: (unit) => unit?.name,
          },
          {
            title: "Sản phẩm trong nhóm",
            dataIndex: "featureOosMergedProductItemsCount",
            align: "right",
            render: (count) => (
              <span className="cursor-pointer underline">{count}</span>
            ),
            onCell: (record: OosMergedProductInterface) => ({
              onClick: () => {
                setSelectedOosMergedProduct(record);
                setView(true);
              },
            }),
          },
          {
            key: "actions",
            align: "right",
            render: (_, record) => {
              return (
                <TableActionCell
                  actions={actions}
                  items={items}
                  record={record}
                />
              );
            },
          },
        ]}
        pagination={false}
      />

      {action && (
        <OutOfStockStatusOutletMergedProductModal
          action={action}
          componentFeatureId={componentFeatureId}
          projectId={projectId}
          cancelCb={() => {
            setAction(null);
            setSelectedOosMergedProduct(null);
          }}
          cb={() => {
            setSelectedOosMergedProduct(null);
            setAction(null);
            refetch();
          }}
          oosMergedProduct={selectedOosMergedProduct}
        />
      )}

      {add && selectedOosMergedProduct && (
        <OutOfStockStatusOutletMergedProductProductAvailablesModal
          cb={() => {
            setSelectedOosMergedProduct(null);
            setAdd(false);
            refetch();
          }}
          oosMergedProduct={selectedOosMergedProduct}
          componentFeatureId={componentFeatureId}
          projectId={projectId}
          onClose={() => {
            setAdd(false);
            setSelectedOosMergedProduct(null);
          }}
        />
      )}

      {view && selectedOosMergedProduct && (
        <OutOfStockStatusOutletMergedProductProductModal
          cb={() => {
            setSelectedOosMergedProduct(null);
            setView(false);
            refetch();
          }}
          oosMergedProduct={selectedOosMergedProduct}
          componentFeatureId={componentFeatureId}
          projectId={projectId}
          onClose={() => {
            setView(false);
            setSelectedOosMergedProduct(null);
          }}
        />
      )}
    </>
  );
};

export default OutOfStockStatusOutletMergedProductPage;
