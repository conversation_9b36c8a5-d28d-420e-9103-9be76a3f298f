import { getImageVariants } from "@/common/image.helper.ts";
import { stringRemoveAccents } from "@/common/helper.ts";
import { FeatureCustomerInterface } from "@project/component/feature/config/types/customerInformationCapturing/interface.ts";
import { ProjectInterface } from "@project/interface.ts";
import _ from "lodash";
import { 
  RecordOrderInterface,
  RecordOrderPhotoInterface,
} from "../interface";
import { 
  getFixedHeaders,
  processOrderData,
} from "../process";
import { 
  EXPORT_FILE_NAMES,
  IMAGE_EXPORT_HEADERS,
  IMAGE_EXPORT_POSITION_FIELDS,
} from "../constants";

/**
 * Process vertical export data
 */
export const processVerticalExportData = (
  entities: RecordOrderInterface[],
  project: ProjectInterface | undefined,
  featureCustomers: FeatureCustomerInterface[]
) => {
  return entities.flatMap((item) => {
    const {
      recordOrderPurchases,
      recordOrderExchanges,
      recordOrderSamplings,
      recordOrderPrizes,
    } = item;

    const orderData = processOrderData(project, item, featureCustomers);

    const purchasesData = recordOrderPurchases?.map((item) => {
      const { featureOrderProduct, quantity } = item;
      const { projectProduct, price } = featureOrderProduct;

      return [
        "Sản phẩm đã mua",
        "",
        projectProduct.product.code,
        projectProduct.product.name,
        projectProduct.productPackaging?.unit.name,
        quantity,
        price,
        (price ?? 0) * quantity,
      ];
    });

    const exchangesData =
      recordOrderExchanges?.flatMap((item) => {
        const { quantity, featureSchemeExchange } = item;
        const { name: featureSchemeExchangeName } = featureSchemeExchange;
        return item.featureSchemeExchange.exchangeProceeds.map(
          (exchangeProceed) => {
            let name;
            let code;
            let unit;

            const { projectItem, projectProduct } = exchangeProceed;
            if (projectItem) {
              name = projectItem.item.name;
              code = projectItem.item.code;
              unit = projectItem.item.unit?.name;
            }
            if (projectProduct) {
              name = projectProduct.product.name;
              code = projectProduct.product.code;
              unit = projectProduct.productPackaging?.unit.name;
            }

            return [
              "Quà đã nhận",
              featureSchemeExchangeName,
              code,
              name,
              unit,
              exchangeProceed.quantity * quantity,
              "",
              "",
            ];
          },
        );
      }) ?? [];

    let samplingsData = [];
    if (recordOrderSamplings?.length) {
      samplingsData = recordOrderSamplings?.map((item) => {
        return [
          "Sampling đã nhận",
          "",
          item.featureSampling.projectProduct.product.code,
          item.featureSampling.projectProduct.product.name,
          item.featureSampling.unit.name,
          item.quantity,
          "",
          "",
        ];
      });
    } else {
      samplingsData = [["Không nhận sampling", "", "", "", "", "", "", ""]];
    }

    const groupLuckyDraws = _.groupBy(
      recordOrderPrizes,
      (o) => o.projectLuckyDrawResult.projectLuckyDrawItem.projectItem.id,
    );

    const luckyDrawsData = Object.entries(groupLuckyDraws).map(
      ([, luckyDraws]) => {
        const luckyDraw = luckyDraws[0];
        const { projectItem } =
          luckyDraw.projectLuckyDrawResult.projectLuckyDrawItem;
        return [
          "Quà lucky draw",
          luckyDraw.projectLuckyDrawResult.projectLuckyDraw.name,
          projectItem.item.code,
          projectItem.item.name,
          projectItem.item.unit?.name,
          luckyDraws.length,
          "",
          "",
        ];
      },
    );

    const itemsData = _.concat(
      purchasesData,
      exchangesData,
      samplingsData,
      luckyDrawsData,
    );
    const mergedArray = [];
    if (itemsData.length > 0) {
      for (const element of itemsData) {
        mergedArray.push([...orderData, ...(element ?? [])]);
      }
    } else {
      mergedArray.push([...orderData]);
    }

    return mergedArray;
  });
};

/**
 * Get vertical export headers
 */
export const getVerticalExportHeaders = (featureCustomers: FeatureCustomerInterface[]) => {
  return [
    ...getFixedHeaders(featureCustomers),
    "Loại data ghi nhận",
    "Tên điều kiện nhận quà",
    "Mã item",
    "Tên item",
    "Quy cách",
    "Số lượng",
    "Đơn giá",
    "Thành tiền",
  ];
};

/**
 * Process images export data
 */
export const processImagesExportData = (
  entities: RecordOrderInterface[],
  project: ProjectInterface | undefined
) => {
  return entities.flatMap((entity) => {
    const { recordOrderPhotos } = entity;
    const fixData = processOrderData(
      project,
      entity,
      [],
      IMAGE_EXPORT_POSITION_FIELDS,
    );
    const mergedData = [];

    for (const recordOrderPhoto of recordOrderPhotos ?? []) {
      if (!recordOrderPhoto.recordPhoto) continue; // Không có recordPhoto ( Hình đã xóa nhưng vẫn còn liên kết)

      const { featurePhoto, image } = recordOrderPhoto.recordPhoto;
      mergedData.push([
        ...fixData,
        featurePhoto.name,
        getImageVariants(image.variants ?? [], "public"),
      ]);
    }
    return mergedData;
  });
};

/**
 * Get images export file name
 */
export const getImagesExportFileName = (componentFeatureName?: string) => {
  return stringRemoveAccents(
    componentFeatureName
      ? `${EXPORT_FILE_NAMES.IMAGES} ${componentFeatureName}`
      : "",
  );
};

/**
 * Get images export headers
 */
export const getImagesExportHeaders = () => {
  return IMAGE_EXPORT_HEADERS;
};
