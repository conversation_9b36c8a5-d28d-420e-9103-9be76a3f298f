import { CURD } from "@/common/constant.ts";
import { filterOption } from "@/common/helper.ts";
import { getImageVariants } from "@/common/image.helper";
import CustomModal from "@/components/CustomModal.tsx";
import UploadButton from "@/components/UploadButton.tsx";
import useUploadImage from "@/hooks/useUploadImage.ts";
import { useApp } from "@/UseApp.tsx";
import { LoadingOutlined } from "@ant-design/icons";
import { OosMergedProductInterface } from "@project/component/feature/config/types/outOfStockStatus/interface.ts";
import {
  useCreateOosMergedProductMutation,
  useUpdateOosMergedProductMutation,
} from "@project/component/feature/config/types/outOfStockStatus/steps/mergedProduct/service.ts";
import {
  useProjectBrandsQuery,
  useProjectUnitsQuery,
} from "@project/general/services.ts";
import { Form, Input, Select, Upload } from "antd";
import { useCallback, useEffect, useMemo, useState } from "react";

interface OutOfStockStatusOutletMergedProductModalProps {
  componentFeatureId: number;
  projectId: number;
  action: CURD | null;
  cb: () => void;
  cancelCb: () => void;
  oosMergedProduct: OosMergedProductInterface | null;
}

const OutOfStockStatusOutletMergedProductModal = ({
  componentFeatureId,
  projectId,
  action,
  cb,
  cancelCb,
  oosMergedProduct,
}: OutOfStockStatusOutletMergedProductModalProps) => {
  const { showNotification } = useApp();

  const [form] = Form.useForm();
  const [loadingUpload, setLoadingUpload] = useState(false);
  const [imageUrl, setImageUrl] = useState<string>();

  const uploadImage = useUploadImage();

  const projectUnitsQuery = useProjectUnitsQuery(projectId);
  const projectBrandsQuery = useProjectBrandsQuery(projectId);

  const createOosMergedProductMutation =
    useCreateOosMergedProductMutation(componentFeatureId);
  const updateOosMergedProductMutation =
    useUpdateOosMergedProductMutation(componentFeatureId);

  const beforeUpload = useCallback(
    async (file: File) => {
      setLoadingUpload(true);
      const imageUrl = URL.createObjectURL(file);
      if (imageUrl) {
        setImageUrl(imageUrl);
      }
      setLoadingUpload(false);
      return false;
    },
    [setLoadingUpload],
  );

  const submit = useCallback(async () => {
    const data = form.getFieldsValue();
    if (data.imageFile) {
      setLoadingUpload(true);

      data.imageId = (await uploadImage.upload(data.imageFile.file))?.id;
      delete data.imageFile;

      setLoadingUpload(false);
    }

    if (action === CURD.CREATE) {
      await createOosMergedProductMutation.mutateAsync(data);

      showNotification({
        message: "Thêm sản phẩm gộp thành công",
        type: "success",
      });
      cb();
    }

    if (action === CURD.UPDATE && oosMergedProduct) {
      await updateOosMergedProductMutation.mutateAsync({
        id: oosMergedProduct.id,
        ...data,
      });

      showNotification({
        message: "Chỉnh sửa sản phẩm gộp thành công",
        type: "success",
      });
      cb();
    }
  }, [
    action,
    cb,
    createOosMergedProductMutation,
    form,
    oosMergedProduct,
    showNotification,
    updateOosMergedProductMutation,
    uploadImage,
  ]);

  const content = (
    <Form form={form} layout={"vertical"} onFinish={submit}>
      <Form.Item
        name={"productName"}
        label={"Tên sản phẩm gộp"}
        rules={[
          {
            required: true,
          },
        ]}
      >
        <Input />
      </Form.Item>

      <Form.Item name="productShortName" label={"Tên rút gọn của sản phẩm"}>
        <Input />
      </Form.Item>

      <Form.Item
        name={"productCode"}
        label={"Mã sản phẩm gộp"}
        rules={[
          {
            required: true,
          },
        ]}
      >
        <Input className={"w-1/2"} />
      </Form.Item>

      <Form.Item
        name={"unitId"}
        label={"Quy cách"}
        rules={[
          {
            required: true,
          },
        ]}
        className={"w-1/2"}
      >
        <Select
          showSearch
          filterOption={filterOption}
          options={projectUnitsQuery.data?.entities.map((unit) => ({
            value: unit.id,
            label: unit.name,
          }))}
          popupMatchSelectWidth={false}
        />
      </Form.Item>

      <Form.Item
        name={"projectBrandId"}
        label={"Nhãn hàng"}
        rules={[
          {
            required: true,
          },
        ]}
        className={"w-1/2"}
      >
        <Select
          showSearch
          filterOption={filterOption}
          options={projectBrandsQuery.data?.map((projectBrand) => ({
            value: projectBrand.id,
            label: projectBrand.brand.name,
          }))}
          popupMatchSelectWidth={false}
        />
      </Form.Item>

      <Form.Item name="imageFile" label={"Ảnh sản phẩm"}>
        <Upload
          name="avatar"
          listType="picture-card"
          showUploadList={false}
          beforeUpload={beforeUpload}
          accept="image/*"
        >
          {(() => {
            if (loadingUpload) {
              return <LoadingOutlined />;
            }
            if (imageUrl) {
              return (
                <img
                  src={imageUrl}
                  alt="avatar"
                  style={{ width: "100%", height: "100%" }}
                />
              );
            } else {
              return <UploadButton loading={loadingUpload} />;
            }
          })()}
        </Upload>
      </Form.Item>
    </Form>
  );

  const loading = useMemo(
    () =>
      loadingUpload ||
      createOosMergedProductMutation.isPending ||
      updateOosMergedProductMutation.isPending,
    [
      createOosMergedProductMutation.isPending,
      loadingUpload,
      updateOosMergedProductMutation.isPending,
    ],
  );

  useEffect(() => {
    if (action === CURD.UPDATE && oosMergedProduct) {
      form.setFieldsValue({
        productName: oosMergedProduct.productName,
        productCode: oosMergedProduct.productCode,
        unitId: oosMergedProduct.unit.id,
        projectBrandId: oosMergedProduct.projectBrand.id,
        productShortName: oosMergedProduct.productShortName,
      });

      if (oosMergedProduct.image) {
        setImageUrl(
          getImageVariants(oosMergedProduct?.image?.variants ?? [], "public"),
        );
      }
    }
  }, [action, form, oosMergedProduct]);

  return (
    <CustomModal
      title={
        action === CURD.CREATE ? "Thêm sản phẩm gộp" : "Chỉnh sửa sản phẩm gộp"
      }
      isOpen={true}
      content={content}
      onCancel={() => cancelCb()}
      confirmLoading={loading}
      onConfirm={submit}
      confirmText={action === CURD.CREATE ? "Thêm" : "Cập nhật"}
    />
  );
};

export default OutOfStockStatusOutletMergedProductModal;
