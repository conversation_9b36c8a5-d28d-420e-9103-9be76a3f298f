import { filterOption } from "@/common/helper";
import ProductItemCell from "@/components/ProductItemCell";
import { useUrlFiltersWithQuery } from "@/hooks/useUrlFiltersWithQuery";
import { useItemTypesQuery } from "@/routes/item-type/services";
import { useApp } from "@/UseApp";
import { CloseOutlined } from "@ant-design/icons";
import { Button, Form, Input, Modal, Select, Space, Table } from "antd";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { QuantityEntityInterface } from "../../../multipleEntitiesQuantityCapturing/interface";
import {
  useCreateNumericSheetNumericMutation,
  useNumericSheetNumericAvailablesQuery,
} from "../../service";

interface NumericSheetGroupAvailablesModalProps {
  onModalClose: () => void;
  componentFeatureId: number;
  numericSheetId: number;
  cb: () => void;
}

const NumericSheetGroupAvailablesModal = ({
  onModalClose,
  componentFeatureId,
  numericSheetId,
  cb,
}: NumericSheetGroupAvailablesModalProps) => {
  const { showNotification } = useApp();

  const [searchModalForm] = Form.useForm();
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
  const [newSelectedKeys, setNewSelectedKeys] = useState<React.Key[]>([]);
  const {
    query: { data, refetch, isFetching },
    handleSearch,
    getPaginationProps,
  } = useUrlFiltersWithQuery<QuantityEntityInterface>({
    formInstance: searchModalForm,
    useQueryHook: useNumericSheetNumericAvailablesQuery,
    queryParams: [componentFeatureId, numericSheetId],
    options: {
      urlSync: {
        enabled: false,
      },
      transformations: {
        toFilterValues: {
          type: (type) => {
            let itemTypeId = undefined;
            let isProduct = undefined;
            if (type && type !== 0) {
              itemTypeId = type;
            }
            if (type === 0) {
              isProduct = true;
            }
            return {
              itemTypeId,
              isProduct,
            };
          },
        },
      },
    },
  });

  const itemTypesQuery = useItemTypesQuery({ take: 50, skip: 0 });
  const createNumericSheetNumericMutation =
    useCreateNumericSheetNumericMutation(componentFeatureId, numericSheetId);

  const typeOptions = useMemo(() => {
    const data = itemTypesQuery.data?.entities.map((item) => ({
      label: item.name,
      value: item.id,
    }));
    data?.unshift({ label: "Sản phẩm", value: 0 });
    return data;
  }, [itemTypesQuery.data?.entities]);

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys: selectedKeys,
    onChange: onSelectChange,
    getCheckboxProps: (record: QuantityEntityInterface) => {
      return {
        disabled: !record.isAvailable || !record.isActive,
      };
    },
  };

  const onSubmit = useCallback(async () => {
    if (newSelectedKeys.length === 0) {
      return;
    }
    const data = [];
    for (const element of newSelectedKeys) {
      const arr = element.toString().split("-");
      const projectProductId = Number(arr[0]) || null;
      const projectItemId = Number(arr[1]) || null;
      data.push({
        projectProductId,
        projectItemId,
      });
    }

    await createNumericSheetNumericMutation.mutateAsync(data);
    showNotification({
      type: "success",
      message: "Thêm item vào chức năng thành công",
    });

    setSelectedKeys([]);
    refetch();
    cb();
  }, [
    cb,
    createNumericSheetNumericMutation,
    newSelectedKeys,
    refetch,
    showNotification,
  ]);

  useEffect(() => {
    setSelectedKeys(
      data?.entities
        .filter((item) => !item.isAvailable)
        .map((item) => `${item.id}-${item.isAvailable}`) ?? [],
    );
  }, [data?.entities]);

  useEffect(() => {
    const updatedKeys = selectedKeys.filter(
      (key) => key.toString().split("-")[2] === "true",
    );
    setNewSelectedKeys(updatedKeys);
  }, [selectedKeys]);

  const pagination = useMemo(() => getPaginationProps(), [getPaginationProps]);

  return (
    <Modal
      open={true}
      footer={null}
      closeIcon={null}
      width={870}
      styles={{ content: { padding: 0 } }}
    >
      <div className="pl-10 pr-10 pt-3 pb-5">
        <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
          <h2 className="text-neutral-700 text-2xl font-semibold ">
            Thêm item vào chức năng
          </h2>
          <div className="pt-5">
            <Button
              type="link"
              onClick={onModalClose}
              size="large"
              icon={<CloseOutlined />}
            />
          </div>
        </div>
        <Form layout="vertical" onFinish={handleSearch} form={searchModalForm}>
          <Space>
            <Form.Item label="Loại" name={"type"}>
              <Select
                style={{ width: "200px" }}
                placeholder={"Tất cả"}
                allowClear
                options={typeOptions}
                showSearch
                filterOption={filterOption}
              />
            </Form.Item>
            <Form.Item label="Tên item" name={"keyword"}>
              <Input placeholder="Nhập tên hoặc mã" allowClear />
            </Form.Item>
            <Form.Item label=" ">
              <Button htmlType="submit">Tìm kiếm</Button>
            </Form.Item>
          </Space>
        </Form>

        <Table
          scroll={{
            y: "70vh",
          }}
          dataSource={data?.entities ?? []}
          loading={isFetching}
          columns={[
            {
              title: "Tên",
              className: "min-w-[100px]",
              dataIndex: "name",
              render: (_, record: QuantityEntityInterface) => {
                return (
                  <ProductItemCell
                    variants={record?.image?.variants ?? []}
                    name={record.name}
                    isActive={record.isActive}
                    isAvailable={record.isAvailable}
                  />
                );
              },
            },
            {
              title: "Mã",
              className: "min-w-[100px]",
              dataIndex: "code",
            },
            {
              title: "Loại",
              className: "min-w-[100px]",
              render: (_, record: QuantityEntityInterface) => {
                return record.itemTypeId ? record.itemTypeName : "Sản phẩm";
              },
            },
            {
              title: "Đơn vị tính",
              className: "min-w-[100px]",
              dataIndex: "unitName",
            },
          ]}
          rowSelection={rowSelection}
          rowKey={(record) => {
            return `${record.id}-${record.isAvailable}`;
          }}
          pagination={pagination}
        />
      </div>
      <div className="flex justify-end pb-4 pt-4 bg-[#F7F8FA]">
        <Space className="pr-10">
          <Button onClick={onModalClose}>Đóng</Button>
          <Button
            type={"primary"}
            disabled={!newSelectedKeys.length}
            onClick={onSubmit}
            loading={createNumericSheetNumericMutation.isPending}
          >
            Thêm {newSelectedKeys.length} item vào chức năng
          </Button>
        </Space>
      </div>
    </Modal>
  );
};

export default NumericSheetGroupAvailablesModal;
