import { CHUNK_SIZE } from "@/common/constant";
import { createFileAndDownLoad, downloadFile } from "@/common/export-excel.helper";
import { ProjectInterface } from "@project/interface.ts";
import { ProjectItemInterface } from "@project/item/interface.ts";
import { ProjectProductInterface } from "@project/product/interface.ts";
import { useFindProjectProductMutation } from "@project/product/service.ts";
import dayjs from "dayjs";
import Excel from "exceljs";
import _ from "lodash";
import { useCallback, useState } from "react";
import { AdvancedFilterInterface } from "../../../interface";
import { RecordOrderInterface } from "../interface";
import {
  ConditionColumnType,
  DataType,
  fillDataExcel,
  OtherStatisticsColumnType,
  ProceedsColumnType,
  processAndLogicalExchange,
  processOrderData,
  processOrLogicalExchange,
  processPurchasesWithoutExchange,
  renderExcelHeader,
  SchemeExchangesDataType,
  updateFeatureSchemeExchangeTotal,
  getFixedHeaders,
} from "../process";
import {
  useGetFeatureCustomersMutation,
  useGetReportOrdersMutation,
} from "../service";
import {
  processVerticalExportData,
  getVerticalExportHeaders,
  processImagesExportData,
  getImagesExportFileName,
  getImagesExportHeaders,
} from "../utils/exportHelpers";
import { EXPORT_FILE_NAMES, EXPORT_COLUMNS, EXCEL_CONFIG } from "../constants";

interface UseReportExportProps {
  projectId: number;
  componentFeatureId: number;
  project: ProjectInterface | undefined;
  filter: AdvancedFilterInterface;
  paginationTotal: number;
  componentFeatureName?: string;
}

/**
 * Custom hook for managing report export functionality
 */
export const useReportExport = ({
  projectId,
  componentFeatureId,
  project,
  filter,
  paginationTotal,
  componentFeatureName,
}: UseReportExportProps) => {
  const [isExport, setIsExport] = useState(false);

  const getReportOrdersMutation = useGetReportOrdersMutation(
    projectId,
    componentFeatureId,
  );
  const findProjectProductMutation = useFindProjectProductMutation(projectId);
  const getFeatureCustomersMutation = useGetFeatureCustomersMutation(
    projectId,
    componentFeatureId,
  );

  const fetchAllData = useCallback(async (): Promise<RecordOrderInterface[]> => {
    const total = paginationTotal;

    if (total === 0) {
      return [];
    }
    const requests = [];

    for (let skip = 0; skip < total; skip += CHUNK_SIZE) {
      requests.push(
        getReportOrdersMutation.mutateAsync({
          take: CHUNK_SIZE,
          skip: skip,
          ...filter,
        }),
      );
    }

    const results = await Promise.all(requests);
    const allEntities = results.flatMap((result) => result.entities);

    return allEntities;
  }, [filter, getReportOrdersMutation, paginationTotal]);

  const exportVertical = useCallback(async () => {
    setIsExport(true);

    try {
      const entities = await fetchAllData();
      const featureCustomers = await getFeatureCustomersMutation.mutateAsync();

      const data = processVerticalExportData(entities, project, featureCustomers);
      const headers = getVerticalExportHeaders(featureCustomers);

      await createFileAndDownLoad({
        data,
        headers,
        fileName: EXPORT_FILE_NAMES.ORDER_REPORT,
        dateTimeColumns: EXPORT_COLUMNS.VERTICAL.dateTimeColumns,
      });
    } finally {
      setIsExport(false);
    }
  }, [fetchAllData, getFeatureCustomersMutation, project]);

  const exportHorizontal = useCallback(async () => {
    setIsExport(true);

    try {
      const entities = await fetchAllData();
      const totalData = paginationTotal;

      const { entities: projectProducts } =
        await findProjectProductMutation.mutateAsync({
          take: 0,
        });

      const featureCustomers = await getFeatureCustomersMutation.mutateAsync();
      featureCustomers.sort((a, b) => b.ordinal - a.ordinal);

      // Dùng để làm header
      const featureSchemeExchangesTotal: {
        name: string;
        exchangeConditions: { projectProductId: number }[];
        exchangeProceeds: {
          projectProduct: ProjectProductInterface | null;
          projectItem: ProjectItemInterface | null;
        }[];
      }[] = [];

      const data: DataType[] = [];
      const luckyDrawsHeaders: {
        name: string;
        id: number;
        ordinal: number;
        columnName?: string;
      }[] = [];
      const samplingsHeaders: {
        name: string;
        id: number;
        ordinal: number;
        columnName?: string;
      }[] = [];

      for (const entity of entities) {
        const {
          recordOrderExchanges,
          recordOrderPurchases,
          recordOrderPrizes,
          recordOrderSamplings,
        } = entity;
        const schemeExchangesData: SchemeExchangesDataType[] = [];

        for (const recordOrderExchange of recordOrderExchanges ?? []) {
          const { featureSchemeExchange, quantity: orderQuantity } =
            recordOrderExchange;
          const {
            name: schemeName,
            exchangeProceeds,
            exchangeConditions,
            logical,
          } = featureSchemeExchange;

          updateFeatureSchemeExchangeTotal(
            featureSchemeExchangesTotal,
            exchangeConditions,
            exchangeProceeds,
            schemeName,
          );

          if (logical === "and") {
            processAndLogicalExchange(
              schemeName,
              schemeExchangesData,
              exchangeConditions,
              exchangeProceeds,
              recordOrderPurchases ?? [],
              orderQuantity,
            );
          }

          if (logical === "or") {
            processOrLogicalExchange(
              schemeName,
              schemeExchangesData,
              exchangeConditions,
              exchangeProceeds,
              recordOrderPurchases ?? [],
              orderQuantity,
            );
          }
        }

        schemeExchangesData.sort((a, b) => a.name.localeCompare(b.name));

        processPurchasesWithoutExchange(
          schemeExchangesData,
          recordOrderPurchases ?? [],
          featureSchemeExchangesTotal,
        );

        const orderData = processOrderData(project, entity, featureCustomers);

        const groupLuckyDraws = _.groupBy(
          recordOrderPrizes,
          (o) => o.projectLuckyDrawResult.projectLuckyDrawItem.projectItem.id,
        );

        const prizes = Object.entries(groupLuckyDraws).map(([, luckyDraws]) => {
          const luckyDraw = luckyDraws[0];

          const luckyDrawsHeader = luckyDrawsHeaders.find(
            (item) =>
              item.id ===
              luckyDraw.projectLuckyDrawResult.projectLuckyDrawItem.projectItem
                .id,
          );

          if (!luckyDrawsHeader) {
            luckyDrawsHeaders.push({
              name: luckyDraw.projectLuckyDrawResult.projectLuckyDrawItem
                .projectItem.item.name,
              id: luckyDraw.projectLuckyDrawResult.projectLuckyDrawItem
                .projectItem.id,
              ordinal:
                luckyDraw.projectLuckyDrawResult.projectLuckyDrawItem.ordinal,
            });
          }

          return {
            name: luckyDraw.projectLuckyDrawResult.projectLuckyDrawItem
              .projectItem.item.name,
            quantity: luckyDraws.length,
            id: luckyDraw.projectLuckyDrawResult.projectLuckyDrawItem.projectItem
              .id,
          };
        });

        const groupSamplings = _.groupBy(
          recordOrderSamplings,
          (o) => o.featureSampling.projectProduct.id,
        );

        const samplings = Object.entries(groupSamplings).map(([, samplings]) => {
          const sampling = samplings[0];

          const samplingsHeader = samplingsHeaders.find(
            (item) => item.id === sampling.featureSampling.projectProduct.id,
          );

          if (!samplingsHeader) {
            samplingsHeaders.push({
              name: sampling.featureSampling.projectProduct.product.name,
              id: sampling.featureSampling.projectProduct.id,
              ordinal: sampling.featureSampling.ordinal,
            });
          }

          return {
            name: sampling.featureSampling.projectProduct.product.name,
            quantity: samplings.length,
            id: sampling.featureSampling.projectProduct.id,
          };
        });

        data.push({
          schemeExchangesData,
          orderData,
          prizes,
          samplings,
        });
      }

      const conditionColumns: ConditionColumnType[] = [];
      const proceedsColumns: ProceedsColumnType[] = [];
      const otherStatisticsColumns: OtherStatisticsColumnType[] = [];

      const fixedHeaders = getFixedHeaders(featureCustomers);

      featureSchemeExchangesTotal.sort((a, b) => a.name.localeCompare(b.name));

      const workbook = new Excel.Workbook();
      const worksheet = workbook.addWorksheet("data", {});

      renderExcelHeader({
        worksheet,
        fixedHeaders,
        featureSchemeExchangesTotal,
        projectProducts,
        totalData,
        conditionColumns,
        proceedsColumns,
        otherStatisticsColumns,
        luckyDrawsHeaders,
        samplingsHeaders,
      });

      fillDataExcel(
        worksheet,
        data,
        EXCEL_CONFIG.BEGIN_ROW,
        conditionColumns,
        proceedsColumns,
        otherStatisticsColumns,
        luckyDrawsHeaders,
        samplingsHeaders,
      );

      downloadFile(
        workbook,
        `${EXPORT_FILE_NAMES.ORDER_REPORT.trim()} ${dayjs().format("DDMMYY")}.xlsx`,
      );
    } finally {
      setIsExport(false);
    }
  }, [
    fetchAllData,
    findProjectProductMutation,
    getFeatureCustomersMutation,
    paginationTotal,
    project,
  ]);

  const exportImages = useCallback(async () => {
    setIsExport(true);

    try {
      const entities = await fetchAllData();
      const data = processImagesExportData(entities, project);
      const headers = getImagesExportHeaders();
      const fileName = getImagesExportFileName(componentFeatureName);

      await createFileAndDownLoad({
        data,
        headers,
        fileName,
        hyperlinkColumns: EXPORT_COLUMNS.IMAGES.hyperlinkColumns,
        dateTimeColumns: EXPORT_COLUMNS.IMAGES.dateTimeColumns,
      });
    } finally {
      setIsExport(false);
    }
  }, [componentFeatureName, fetchAllData, project]);

  return {
    isExport,
    exportVertical,
    exportHorizontal,
    exportImages,
  };
};
