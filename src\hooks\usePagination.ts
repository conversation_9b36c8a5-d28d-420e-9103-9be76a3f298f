import { DEFAULT_CURRENT_PAGE, DEFAULT_PAGE_SIZE } from "@/common/constant";
import { useCallback, useState } from "react";

interface PaginationConfig {
  current: number;
  total?: number;
  pageSize: number;
  onChange: (page: number) => void;
  showTotal: (total: number) => string;
  showSizeChanger: boolean;
  onShowSizeChange: (current: number, size: number) => void;
}

interface UsePaginationReturn {
  getPagination: (total: number) => PaginationConfig;
  currentPage: number;
  pageSize: number;
  setCurrentPage: (page: number) => void;
  setPageSize: (size: number) => void;
}

interface UsePaginationProps {
  defaultCurrentPage?: number;
  defaultPageSize?: number;
}

const usePagination = ({
  defaultCurrentPage = DEFAULT_CURRENT_PAGE,
  defaultPageSize = DEFAULT_PAGE_SIZE,
}: UsePaginationProps = {}): UsePaginationReturn => {
  const [currentPage, setCurrentPage] = useState(defaultCurrentPage);
  const [pageSize, setPageSize] = useState(defaultPageSize);

  const getPagination = useCallback(
    (totalItems: number) => ({
      current: currentPage,
      total: totalItems,
      pageSize: pageSize,
      onChange: (page: number) => {
        setCurrentPage(page);
      },
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: (_current: number, size: number) => {
        setPageSize(size);
        setCurrentPage(defaultCurrentPage);
      },
    }),
    [currentPage, pageSize, defaultCurrentPage],
  );

  return {
    getPagination,
    currentPage,
    pageSize,
    setCurrentPage,
    setPageSize,
  };
};

export default usePagination;
