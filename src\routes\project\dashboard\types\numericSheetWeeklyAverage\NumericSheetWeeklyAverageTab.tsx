import { randomColor } from "@/common/helper";
import ChartContanier from "@/routes/project/chart/ChartContanier";
import { Area } from "@ant-design/charts";
import { Form } from "antd";
import _ from "lodash";
import { useCallback, useMemo, useState } from "react";
import DashboardFilterZone from "../../DashboardFilterZone";
import { DashboardFilterInterface } from "../../interface";
import { useNumericSheetWeeklyAverageQuery } from "./service";

interface NumericSheetWeeklyAverageTabProps {
  projectId: number;
  dashboardId: number;
}
const NumericSheetWeeklyAverageTab = ({
  projectId,
  dashboardId,
}: NumericSheetWeeklyAverageTabProps) => {
  const [form] = Form.useForm();
  const [filter, setFilter] = useState<DashboardFilterInterface>({});

  const numericSheetWeeklyAverageQuery = useNumericSheetWeeklyAverageQuery(
    projectId,
    dashboardId,
    filter,
  );

  const handleApply = useCallback(() => {
    const values = form.getFieldsValue();

    const {
      date,
      projectRegionIds,
      projectOutletIds,
      channelIds,
      provinceIds,
      leaderIds,
    } = values;

    const filterValue = {
      startDate: date?.[0] ? date[0].format("YYYY-MM-DD") : null,
      endDate: date?.[1] ? date[1].format("YYYY-MM-DD") : null,
      projectRegionIds,
      projectOutletIds,
      channelIds,
      provinceIds,
      leaderIds: leaderIds?.map((item: { value: number }) => item.value),
    };

    if (_.isEqual(filter, filterValue)) {
      numericSheetWeeklyAverageQuery.refetch();
    } else {
      setFilter(filterValue);
    }
  }, [filter, form, numericSheetWeeklyAverageQuery]);

  const data = useMemo(() => {
    const result = [];
    const group = _.groupBy(numericSheetWeeklyAverageQuery.data ?? [], "week");

    for (const [key, value] of Object.entries(group)) {
      for (const [key2, value2] of Object.entries(_.groupBy(value, "name"))) {
        let total = 0;
        total += value2.reduce((acc, cur) => acc + cur.average, 0);
        result.push({
          week: key,
          name: key2,
          value: total,
          startDate: value2[0].startDate,
        });
      }
    }

    return _.sortBy(result, ["startDate", "name"]);
  }, [numericSheetWeeklyAverageQuery.data]);

  const colors = useMemo(() => {
    const group = _.groupBy(numericSheetWeeklyAverageQuery.data ?? [], "name");

    const colors = Object.entries(group).map(([name, values]) => ({
      name,
      color: values[0].backgroundColor ?? randomColor(),
    }));

    return _.sortBy(colors, ["name"]).map((item) => item.color);
  }, [numericSheetWeeklyAverageQuery.data]);

  const totals = useMemo(() => {
    const group = _.groupBy(numericSheetWeeklyAverageQuery.data ?? [], "week");

    return Object.entries(group).map(([key, values]) => ({
      week: key,
      total: values.reduce((acc, cur) => acc + cur.average, 0),
    }));
  }, [numericSheetWeeklyAverageQuery.data]);

  const config = useMemo(() => {
    return {
      data: data,
      xField: "week",
      yField: "value",
      colorField: "name",
      normalize: true,
      stack: true,
      title: {
        title: "Visibility",
        subtitle: "Tỷ lệ trưng bày giữa các công ty",
      },
      label: {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        formatter: (d: number, datum: any) => {
          const total =
            totals.find((item) => item.week === datum.week)?.total ?? 0;
          const percent = ((d / total) * 100).toFixed(2);

          return `${percent}% (${d.toFixed(2)})`;
        },
        fontWeight: "700",
        textAlign: "center",
        fillOpacity: 1,
      },
      legend: {
        color: {
          position: "bottom",
          layout: {
            justifyContent: "center",
          },
        },
      },
      tooltip: false,
      axis: {
        y: { labelFormatter: ".0%" },
        x: {
          labelFormatter: (d: string) => `W${d}`,
        },
      },
      insetLeft: 40,
      insetRight: 30,
      scale: {
        color: {
          palette: colors,
        },
      },
    };
  }, [colors, data, totals]);

  return (
    <>
      <DashboardFilterZone
        handleApply={handleApply}
        fields={["date", "region", "province", "chain", "outlet"]}
        projectId={projectId}
        form={form}
        setFilter={setFilter}
        key={"NumericSheetWeeklyAverageTab"}
      />

      <ChartContanier>
        <Area {...config} height={500} />
      </ChartContanier>
    </>
  );
};

export default NumericSheetWeeklyAverageTab;
