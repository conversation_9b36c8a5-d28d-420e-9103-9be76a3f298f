import { useApp } from "@/UseApp";
import CustomModal from "@/components/CustomModal";
import { Form } from "antd";
import { useCallback, useEffect } from "react";
import {
  OosGroupInterface,
  OosMergedProductInterface,
  OosProductInterface,
} from "../../interface";
import { useOosLevelsQuery } from "../status/service";
import ThresholdSection from "./ThresholdSection";
import { useCreateOosThresholdMutation } from "./service";

interface OutOfStockStatusThresholdCollapseChildModalProps {
  oosProduct: OosProductInterface | null;
  oosMergedProduct: OosMergedProductInterface | null;
  oosGroup: OosGroupInterface;
  onCancelCb: () => void;
  cb: () => void;
  componentFeatureId: number;
}

const OutOfStockStatusThresholdCollapseChildModal = ({
  oosProduct,
  oosMergedProduct,
  oosGroup,
  onCancelCb,
  cb,
  componentFeatureId,
}: OutOfStockStatusThresholdCollapseChildModalProps) => {
  const { showNotification } = useApp();

  const [form] = Form.useForm();

  const oosLevelsQuery = useOosLevelsQuery(componentFeatureId, { take: 0 });
  const createOosThresholdMutation =
    useCreateOosThresholdMutation(componentFeatureId);

  const content = (
    <div className="mb-5">
      <div>
        <span className="text-hint">Nhóm sản phẩm: </span>
        <span className="text-primary font-medium">{oosGroup.name}</span>
      </div>
      <div>
        <span className="text-hint">Sản phẩm: </span>
        <span className="font-medium">
          {oosProduct?.projectProduct.product.name}
          {oosMergedProduct?.productName}
        </span>
      </div>
      <div>
        <span className="text-hint">Mã sản phẩm: </span>
        <span className="font-medium">
          {oosProduct?.projectProduct.product.code}
          {oosMergedProduct?.productCode}
        </span>
      </div>

      <p className="text-primary font-semibold">ĐIỀU KIỆN</p>
      <Form form={form}>
        {oosLevelsQuery.data?.entities.map((oosLevel) => (
          <ThresholdSection key={oosLevel.id} oosLevel={oosLevel} />
        ))}
      </Form>
    </div>
  );

  const confirm = useCallback(async () => {
    const formData = form.getFieldsValue();
    const thresholds = Object.entries(formData).reduce(
      (acc, [key, value]) => {
        const [levelIdStr, property] = key.split(".");
        const levelId = parseInt(levelIdStr, 10);

        let threshold = acc.find((t) => t.featureOosLevelId === levelId);
        if (!threshold) {
          threshold = {
            featureOosLevelId: levelId,
            upperValue: 0,
            lowerValue: 0,
          };
          acc.push(threshold);
        }

        if (property === "upperValue") {
          threshold.upperValue = value as number;
        } else if (property === "lowerValue") {
          threshold.lowerValue = value as number;
        }

        return acc;
      },
      [] as {
        featureOosLevelId: number;
        upperValue: number;
        lowerValue: number;
      }[],
    );

    await createOosThresholdMutation.mutateAsync({
      featureOosGroupId: oosGroup.id,
      featureOosProductId: oosProduct?.id,
      featureOosMergedProductId: oosMergedProduct?.id,
      thresholds,
    });

    showNotification({
      type: "success",
      message: "Cập nhật ràng buộc trạng thái sản phẩm thường thành công",
    });

    cb();
  }, [
    cb,
    createOosThresholdMutation,
    form,
    oosGroup.id,
    oosMergedProduct?.id,
    oosProduct?.id,
    showNotification,
  ]);

  useEffect(() => {
    if (oosProduct) {
      oosLevelsQuery.data?.entities.forEach((oosLevel) => {
        const threshold = oosProduct.featureOosThresholds.find(
          (t) => t.featureOosLevelId === oosLevel.id,
        );
        if (threshold) {
          form.setFieldValue(`${oosLevel.id}.upperValue`, threshold.upperValue);
          form.setFieldValue(`${oosLevel.id}.lowerValue`, threshold.lowerValue);
        }
      });
    }

    if (oosMergedProduct) {
      oosLevelsQuery.data?.entities.forEach((oosLevel) => {
        const threshold = oosMergedProduct.featureOosThresholds.find(
          (t) => t.featureOosLevelId === oosLevel.id,
        );
        if (threshold) {
          form.setFieldValue(`${oosLevel.id}.upperValue`, threshold.upperValue);
          form.setFieldValue(`${oosLevel.id}.lowerValue`, threshold.lowerValue);
        }
      });
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [oosLevelsQuery.data?.entities, oosProduct, oosMergedProduct]);

  return (
    <CustomModal
      title={"Ràng buộc trạng thái tồn"}
      isOpen={true}
      content={content}
      onCancel={onCancelCb}
      width={870}
      onConfirm={confirm}
      confirmText="Lưu thông tin"
      confirmLoading={createOosThresholdMutation.isPending}
    />
  );
};

export default OutOfStockStatusThresholdCollapseChildModal;
