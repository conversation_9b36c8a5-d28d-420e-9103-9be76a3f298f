import { formatNumber } from "@/common/helper";
import ProductItemCell from "@/components/ProductItemCell";
import TableActionCell from "@/components/TableActionCell";
import { EditOutlined } from "@ant-design/icons";
import {
  OosGroupInterface,
  OosMergedProductInterface,
  OosProductInterface,
  OosThresholdTypeEnum,
} from "@project/component/feature/config/types/outOfStockStatus/interface.ts";
import {
  useOosThresholdMergedProductsQuery,
  useOosThresholdProductsQuery,
} from "@project/component/feature/config/types/outOfStockStatus/steps/threshold/service.ts";
import { Table } from "antd";
import { useMemo, useState } from "react";
import { useOosLevelsQuery } from "../status/service";
import OutOfStockStatusThresholdCollapseChildModal from "./OutOfStockStatusThresholdCollapseChildModal";

interface OutOfStockStatusThresholdCollapseChildProps {
  componentFeatureId: number;
  oosGroup: OosGroupInterface;
  type: OosThresholdTypeEnum;
}

const OutOfStockStatusThresholdCollapseChild = ({
  componentFeatureId,
  oosGroup,
  type,
}: OutOfStockStatusThresholdCollapseChildProps) => {
  const [selectedOosProduct, setSelectedOosProduct] =
    useState<OosProductInterface | null>(null);
  const [selectedOosMergedProduct, setSelectedOosMergedProduct] =
    useState<OosMergedProductInterface | null>(null);

  const oosThresholdProductsQuery = useOosThresholdProductsQuery(
    componentFeatureId,
    {
      take: 0,
      featureOosGroupId: oosGroup.id,
    },
    type === OosThresholdTypeEnum.PRODUCTS,
  );

  const oosThresholdMergedProductsQuery = useOosThresholdMergedProductsQuery(
    componentFeatureId,
    {
      take: 0,
      featureOosGroupId: oosGroup.id,
    },
    type === OosThresholdTypeEnum.MERGED_PRODUCTS,
  );

  const oosLevelsQuery = useOosLevelsQuery(componentFeatureId, { take: 0 });

  const items = useMemo(
    () => [
      {
        key: "edit",
        label: "Chỉnh sửa",
        icon: <EditOutlined />,
      },
    ],
    [],
  );

  const actions = useMemo(
    () => [
      {
        key: "edit",
        action: (record: OosProductInterface | OosMergedProductInterface) => {
          if ("projectProduct" in record) {
            setSelectedOosProduct(record);
          }
          if ("productName" in record) {
            setSelectedOosMergedProduct(record);
          }
        },
      },
    ],
    [],
  );

  return (
    <>
      {type === OosThresholdTypeEnum.PRODUCTS && (
        <Table
          dataSource={oosThresholdProductsQuery.data?.entities}
          rowKey={"id"}
          columns={[
            {
              title: "Tên sản phẩm",
              className: "min-w-[100px]",
              render: (_, record: OosProductInterface) => {
                const { projectProduct } = record ?? {};

                if (projectProduct) {
                  return (
                    <ProductItemCell
                      name={projectProduct?.product.name}
                      variants={projectProduct.product.image?.variants ?? []}
                    />
                  );
                }
              },
            },
            {
              title: "Mã sản phẩm",
              className: "min-w-[100px]",
              render: (_, record: OosProductInterface) => {
                return record.projectProduct?.product.code;
              },
            },
            {
              title: "Quy cách",
              className: "min-w-[100px]",
              render: (_, record: OosProductInterface) => {
                return record.projectProduct?.productPackaging?.unit?.name;
              },
            },
            ...(oosLevelsQuery.data?.entities.map((oosLevel) => ({
              title: oosLevel.name,
              className: "min-w-[100px]",
              render: (_: unknown, record: OosProductInterface) => {
                const oosThreshold = record.featureOosThresholds.find(
                  (featureOosThreshold) =>
                    featureOosThreshold.featureOosLevelId === oosLevel.id,
                );
                if (!oosThreshold) return "_";

                return (
                  <>
                    {oosThreshold?.upperValue ? (
                      formatNumber(oosThreshold?.upperValue)
                    ) : (
                      <>+&infin; </>
                    )}{" "}
                    &#8805; Stock &#8805;{" "}
                    {oosThreshold?.lowerValue !== undefined ||
                    oosThreshold?.lowerValue !== null ? (
                      formatNumber(oosThreshold?.lowerValue)
                    ) : (
                      <>-&infin;</>
                    )}
                  </>
                );
              },
            })) ?? []),
            {
              render: (_, record: OosProductInterface) => {
                return (
                  <TableActionCell
                    actions={actions}
                    items={items}
                    record={record}
                  />
                );
              },
              align: "right",
            },
          ]}
          pagination={false}
        />
      )}

      {type === OosThresholdTypeEnum.MERGED_PRODUCTS && (
        <Table
          dataSource={oosThresholdMergedProductsQuery.data?.entities}
          rowKey={"id"}
          columns={[
            {
              title: "Tên sản phẩm",
              className: "min-w-[100px]",
              render: (_, record: OosMergedProductInterface) => {
                const { image, productName } = record ?? {};
                return (
                  <ProductItemCell
                    name={productName}
                    variants={image?.variants ?? []}
                  />
                );
              },
            },
            {
              title: "Mã sản phẩm",
              className: "min-w-[100px]",
              dataIndex: "productCode",
            },
            {
              title: "Quy cách",
              className: "min-w-[100px]",
              render: (_, record: OosMergedProductInterface) => {
                return record.unit.name;
              },
            },
            ...(oosLevelsQuery.data?.entities.map((oosLevel) => ({
              title: oosLevel.name,
              className: "min-w-[100px]",
              render: (_: unknown, record: OosMergedProductInterface) => {
                const oosThreshold = record.featureOosThresholds.find(
                  (featureOosThreshold) =>
                    featureOosThreshold.featureOosLevelId === oosLevel.id,
                );
                if (!oosThreshold) return "_";

                return (
                  <>
                    {oosThreshold?.upperValue ? (
                      formatNumber(oosThreshold?.upperValue)
                    ) : (
                      <>+&infin; </>
                    )}{" "}
                    &#8805; Stock &#8805;{" "}
                    {oosThreshold?.lowerValue !== undefined ||
                    oosThreshold?.lowerValue !== null ? (
                      formatNumber(oosThreshold?.lowerValue)
                    ) : (
                      <>-&infin;</>
                    )}
                  </>
                );
              },
            })) ?? []),
            {
              render: (_, record: OosMergedProductInterface) => {
                return (
                  <TableActionCell
                    actions={actions}
                    items={items}
                    record={record}
                  />
                );
              },
              align: "right",
            },
          ]}
          pagination={false}
        />
      )}

      {(selectedOosProduct || selectedOosMergedProduct) && (
        <OutOfStockStatusThresholdCollapseChildModal
          oosProduct={selectedOosProduct}
          oosMergedProduct={selectedOosMergedProduct}
          oosGroup={oosGroup}
          onCancelCb={() => {
            setSelectedOosProduct(null);
            setSelectedOosMergedProduct(null);
          }}
          cb={() => {
            setSelectedOosProduct(null);
            setSelectedOosMergedProduct(null);

            if (type === OosThresholdTypeEnum.PRODUCTS)
              oosThresholdProductsQuery.refetch();

            if (type === OosThresholdTypeEnum.MERGED_PRODUCTS)
              oosThresholdMergedProductsQuery.refetch();
          }}
          componentFeatureId={componentFeatureId}
        />
      )}
    </>
  );
};

export default OutOfStockStatusThresholdCollapseChild;
