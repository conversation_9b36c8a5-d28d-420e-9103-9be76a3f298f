import { CURD } from "@/common/constant";
import ModalCURD from "@/components/ModalCURD";
import { Form, Input } from "antd";
import { useCallback, useEffect } from "react";
import { RegionInterface } from "./interface";
import {
  useCreateProjectRegionMutation,
  useUpdateProjectRegionMutation,
} from "./service";

interface ProjectRegionModalProps {
  action: CURD | undefined;
  cancelCb: () => void;
  cb: () => void;
  projectId: number;
  activeNodeId: number | null;
  selectedRegion: RegionInterface | null;
}
const ProjectRegionModal = ({
  action,
  cancelCb,
  cb,
  projectId,
  activeNodeId,
  selectedRegion,
}: ProjectRegionModalProps) => {
  const [form] = Form.useForm();

  const createProjectRegionMutation = useCreateProjectRegionMutation(projectId);
  const updateProjectRegionMutation = useUpdateProjectRegionMutation(projectId);

  const formContent = (
    <>
      <Form.Item
        label="Loại phân vùng"
        name={"tag"}
        rules={[{ required: true }]}
      >
        <Input />
      </Form.Item>

      <Form.Item
        label="Tên phân vùng"
        name={"name"}
        rules={[{ required: true }]}
      >
        <Input />
      </Form.Item>
    </>
  );

  const onFinish = useCallback(async () => {
    if (action === CURD.CREATE) {
      await createProjectRegionMutation.mutateAsync({
        ...form.getFieldsValue(),
        parentId: activeNodeId,
      });
    }

    if (action === CURD.UPDATE && selectedRegion?.id) {
      await updateProjectRegionMutation.mutateAsync({
        data: form.getFieldsValue(),
        id: selectedRegion.id,
      });
    }

    cb();
  }, [
    action,
    activeNodeId,
    cb,
    createProjectRegionMutation,
    form,
    selectedRegion?.id,
    updateProjectRegionMutation,
  ]);

  useEffect(() => {
    if (action === CURD.UPDATE) {
      form.setFieldsValue({
        name: selectedRegion?.name,
        tag: selectedRegion?.tag,
      });
    }
  }, [action, form, selectedRegion?.name, selectedRegion?.tag]);

  return (
    <ModalCURD
      title={"Thêm phân vùng"}
      isOpen={true}
      formContent={formContent}
      form={form}
      onFinish={onFinish}
      action={action}
      onCancelCb={cancelCb}
    />
  );
};

export default ProjectRegionModal;
