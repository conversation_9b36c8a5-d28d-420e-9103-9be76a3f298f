import { useApp } from "@/UseApp";
import { AppContextInterface } from "@/interface.ts";
import { useQuery } from "@tanstack/react-query";
import {
  DistrictInterface,
  ProvinceInterface,
  WardInterface,
} from "./interface";

export const getProvines = async (
  axiosGet: AppContextInterface["axiosGet"],
): Promise<ProvinceInterface[]> => {
  const response = await axiosGet<ProvinceInterface, unknown>(
    "/locations/provinces",
  );
  if (Array.isArray(response)) {
    return response;
  }
  return [];
};

export const getDistricts = async (
  axiosGet: AppContextInterface["axiosGet"],
  provinceId: number,
) => {
  const response = await axiosGet<DistrictInterface, unknown>(
    `/locations/provinces/${provinceId}/districts`,
  );
  if (Array.isArray(response)) {
    return response;
  }
  return [];
};

export const getWards = async (
  axiosGet: AppContextInterface["axiosGet"],
  provinceId: number,
  districtId: number,
) => {
  const response = await axiosGet<WardInterface, unknown>(
    `/locations/provinces/${provinceId}/districts/${districtId}/wards`,
  );
  if (Array.isArray(response)) {
    return response;
  }
  return [];
};

export const useProvincesQuery = (enabled?: boolean) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["provinces"],
    queryFn: () => getProvines(axiosGet),
    enabled,
  });
};

export const useDistrictsQuery = (provinceId: number | null) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["districts", provinceId],
    queryFn: () => getDistricts(axiosGet, provinceId ?? 0),
    enabled: !!provinceId,
  });
};

export const useWardsQuery = (
  provinceId: number | null,
  districtId: number | null,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["wards", provinceId, districtId],
    queryFn: () => getWards(axiosGet, provinceId ?? 0, districtId ?? 0),
    enabled: !!provinceId && !!districtId,
  });
};

export const findProvinceIdByName = async (
  axiosGet: AppContextInterface["axiosGet"],
  provinces: React.MutableRefObject<ProvinceInterface[]>,
  provinceName: string,
) => {
  const province = provinces.current.find(
    (province) =>
      province.name.toLocaleLowerCase() === provinceName.toLocaleLowerCase(),
  );
  if (province) {
    return province.id;
  }

  const response = await getProvines(axiosGet);
  if (Array.isArray(response)) {
    provinces.current = response;
    const newProvince = response.find(
      (province) =>
        province.name.toLocaleLowerCase() === provinceName.toLocaleLowerCase(),
    );
    if (newProvince) {
      return newProvince.id;
    }
  }

  return null;
};

export const findDistrictIdByName = async (
  axiosGet: AppContextInterface["axiosGet"],
  districts: React.MutableRefObject<DistrictInterface[]>,
  provinceId: number,
  districtName: string,
) => {
  const district = districts.current.find(
    (district) =>
      district.provinceId === provinceId &&
      district.name.toLocaleLowerCase() === districtName.toLocaleLowerCase(),
  );
  if (district) {
    return district.id;
  }

  const response = await getDistricts(axiosGet, provinceId);
  if (Array.isArray(response)) {
    districts.current = [...districts.current, ...response];
    const newDistrict = response.find(
      (district) =>
        district.name.toLocaleLowerCase() === districtName.toLocaleLowerCase(),
    );
    if (newDistrict) {
      return newDistrict.id;
    }
  }

  return null;
};

export const findWardIdByName = async (
  axiosGet: AppContextInterface["axiosGet"],
  wards: React.MutableRefObject<WardInterface[]>,
  districtId: number,
  wardName: string,
) => {
  const ward = wards.current.find(
    (ward) =>
      ward.districtId === districtId &&
      ward.name.toLocaleLowerCase() === wardName.toLocaleLowerCase(),
  );
  if (ward) {
    return ward.id;
  }

  const response = await getWards(axiosGet, 1, districtId);
  if (Array.isArray(response)) {
    wards.current = [...wards.current, ...response];
    const newWard = response.find(
      (ward) => ward.name.toLocaleLowerCase() === wardName.toLocaleLowerCase(),
    );
    if (newWard) {
      return newWard.id;
    }
  }

  return null;
};
