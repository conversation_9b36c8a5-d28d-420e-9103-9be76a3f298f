import { formatNumber, formatNumberOnChart } from "@/common/helper";
import LoadingPage from "@/LoadingPage";
import { Bar } from "@ant-design/charts";
import { useMemo } from "react";

type ValueType = string | number | boolean | undefined;
interface BarChartProps {
  title: string;
  subtitle: string;
  data: { [key: string]: ValueType }[];
  xField: string;
  yField: string;
  colorField?: string;
  loading: boolean;
  height?: number;
  ratio?: number;
}

const BarChart = ({
  title,
  subtitle,
  data,
  xField,
  yField,
  colorField,
  loading,
  height,
  ratio,
}: BarChartProps) => {
  const maxValue = useMemo(() => {
    let max = 0;

    for (const item of data) {
      if (Number(item[yField]) > max) {
        max = Number(item[yField]);
      }
    }

    return max;
  }, [data, yField]);

  const scrollbar = useMemo(() => {
    if (data.length > 20) {
      return {
        x: {
          position: "right",
          ratio: ratio,
        },
      };
    }
    return undefined;
  }, [data.length, ratio]);

  const label = useMemo(() => {
    let allYFieldValueIsZero = true;
    for (const item of data) {
      if (item[yField] !== 0) {
        allYFieldValueIsZero = false;
      }
    }
    if (allYFieldValueIsZero) {
      return undefined;
    }

    return {
      position: "right",
      style: {
        textAlign: (value: { [key: string]: ValueType }) => {
          if (Number(value[yField]) > 0.75 * maxValue) {
            return "end";
          }
          return "start";
        },
        fill: (value: { [key: string]: ValueType }) =>
          Number(value[yField]) > 0.75 * maxValue ? "#fff" : "#000",
        dx: (value: { [key: string]: ValueType }) =>
          Number(value[yField]) > 0.75 * maxValue ? -5 : 5,
      },
      formatter: (value: ValueType, datum: { [key: string]: ValueType }) => {
        const formatterValue = formatNumberOnChart(Number(value));

        if (datum["type"] === "UTD" && Number(datum["KPI"]) > 0) {
          return `${formatterValue} (${Math.round((Number(datum[yField]) / Number(datum["KPI"])) * 100)}%)`;
        } else {
          return `${formatterValue}`;
        }
      },
      opacity: 1,
      transform: [
        {
          type: "contrastReverse",
        },
      ],
    };
  }, [data, maxValue, yField]);

  if (loading) {
    return <LoadingPage />;
  }

  return (
    <Bar
      height={height}
      data={data}
      title={{
        title: title,
        subtitle: subtitle,
      }}
      xField={xField}
      yField={yField}
      legend={{
        color: {
          layout: {
            justifyContent: "flex-end",
            alignItems: "center",
            flexDirection: "row",
          },
        },
      }}
      colorField={colorField}
      group={true}
      label={label}
      scrollbar={scrollbar}
      axis={{
        x: {
          line: true,
          tick: false,
          label: true,
          labelAutoHide: false,
          grid: true,
          labelOpacity: 1,
        },
        y: {
          labelFormatter: (value: number) => formatNumberOnChart(value),
          labelAutoRotate: false,
          labelOpacity: 1,
        },
      }}
      tooltip={(datum) => {
        const formatterValue = formatNumber(Number(datum[yField]));

        if (datum["type"] === "UTD" && Number(datum["KPI"]) > 0) {
          return {
            value: `${formatterValue} (${Math.round((datum[yField] / Number(datum["KPI"])) * 100)}%)`,
          };
        } else {
          return `${formatterValue}`;
        }
      }}
      style={{ maxHeight: 20 }}
    />
  );
};

export default BarChart;
