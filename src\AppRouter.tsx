import { withAuthenticationRequired } from "@auth0/auth0-react";
import { NumericSheetStepEnum } from "@project/component/feature/config/types/numericSheet/interface.ts";
import {
  OosThresholdTypeEnum,
  OutOfStockStatusStepEnum,
} from "@project/component/feature/config/types/outOfStockStatus/interface.ts";
import { FeatureTypeEnum } from "@project/component/feature/interface.ts";
import React from "react";
import { createBrowserRouter, RouterProvider } from "react-router-dom";
import { Error } from "./Error";

const ProjectKPIOutletSalesPage = React.lazy(
  () =>
    import(
      "./routes/project/kpi/types/project/outletSales/ProjectKPIOutletSalesPage"
    ),
);

const MainLayout = React.lazy(() => import("./layouts/MainLayout/MainLayout"));
const ProtectedMainLayout = withAuthenticationRequired(MainLayout);

const ForbiddenPage = React.lazy(() => import("./ForbiddenPage"));
const ProtectedForbiddenPage = withAuthenticationRequired(ForbiddenPage);

const LuckyWheelLayout = React.lazy(
  () => import("./layouts/LuckyWheelLayout/LuckyWheelLayout"),
);

// ----- Begin Master Route -----
const DashboardPage = React.lazy(
  () => import("./routes/dashboard/DashboardPage"),
);

const AgencyPage = React.lazy(() => import("./routes/agency/AgencyPage"));

const UnitPage = React.lazy(() => import("./routes/unit/UnitPage"));

const ChannelPage = React.lazy(() => import("./routes/channel/ChannelPage"));

const SubChannelPage = React.lazy(
  () => import("./routes/subChannel/SubChannelPage"),
);

const BrandPage = React.lazy(() => import("./routes/brand/BrandPage"));

const ProductPage = React.lazy(() => import("./routes/product/ProductPage"));

const ItemTypePage = React.lazy(
  () => import("./routes/item-type/ItemTypePage"),
);

const ItemPage = React.lazy(() => import("./routes/item/ItemPage"));

const UserPage = React.lazy(() => import("./routes/user/UserPage"));

const ClientPage = React.lazy(() => import("./routes/client/ClientPage"));

const ProjectPage = React.lazy(() => import("./routes/project/ProjectPage"));

// ----- End Master Route -----

// ----- Begin Project Route -----
const ProjectGeneralPage = React.lazy(
  () => import("./routes/project/general/ProjectGeneralPage"),
);

const ProjectItemPage = React.lazy(
  () => import("./routes/project/item/ProjectItemPage"),
);

const ProjectRolePage = React.lazy(
  () => import("./routes/project/role/ProjectRolePage"),
);

const ProjectConfigOutletPage = React.lazy(
  () => import("./routes/project/configOutlet/ProjectConfigOutletPage"),
);

const ProjectAdminPage = React.lazy(
  () => import("./routes/project/admin/ProjectAdminPage"),
);

const ProjectEmployeePage = React.lazy(
  () => import("./routes/project/employee/ProjectEmployeePage"),
);

const ProjectOutletPage = React.lazy(
  () => import("./routes/project/outlet/ProjectOutletPage"),
);

const ProjectProfilePage = React.lazy(
  () => import("./routes/project/profile/ProjectProfilePage"),
);

const ProjectProductPage = React.lazy(
  () => import("./routes/project/product/ProjectProductPage"),
);

const ProjectComponentPage = React.lazy(
  () => import("./routes/project/component/ProjectComponentPage"),
);

const ProjectEmployeeLeaderOutletPage = React.lazy(
  () =>
    import(
      "./routes/project/employee/employee-leader/ProjectEmployeeLeaderOutletPage"
    ),
);

const ProjectEmployeeLeaderEmployeePage = React.lazy(
  () =>
    import(
      "./routes/project/employee/employee-leader/ProjectEmployeeLeaderEmployeePage"
    ),
);

const ComponentFeaturePage = React.lazy(
  () => import("./routes/project/component/feature/ComponentFeaturePage"),
);

const FeatureConfigPage = React.lazy(
  () => import("./routes/project/component/feature/config/FeatureConfigPage"),
);

const AttendanceClockingInPage = React.lazy(
  () =>
    import(
      "./routes/project/component/feature/config/types/attendanceClocking/AttendanceClockingPage"
    ),
);

const PhotographyPage = React.lazy(
  () =>
    import(
      "./routes/project/component/feature/config/types/photography/PhotographyPage"
    ),
);

const MultiSubjectMultimediaInformationCapturingPage = React.lazy(
  () =>
    import(
      "./routes/project/component/feature/config/types/multiSubjectMultimediaInformationCapturing/MultiSubjectMultimediaInformationCapturingPage"
    ),
);

const MultipleEntitiesQuantityCapturingPage = React.lazy(
  () =>
    import(
      "./routes/project/component/feature/config/types/multipleEntitiesQuantityCapturing/MultipleEntitiesQuantityCapturingPage"
    ),
);

const SynchronizationPage = React.lazy(
  () =>
    import(
      "./routes/project/component/feature/config/types/synchronization/SynchronizationPage"
    ),
);

const OnlineIndividualSummaryReportPage = React.lazy(
  () =>
    import(
      "./routes/project/component/feature/config/types/onlineIndividualSummaryReport/OnlineIndividualSummaryReportPage"
    ),
);

const OnlineTeamSummaryReportPage = React.lazy(
  () =>
    import(
      "./routes/project/component/feature/config/types/onlineTeamSummaryReport/OnlineTeamSummaryReportPage"
    ),
);

const OnlineTeamAttendanceReportPage = React.lazy(
  () =>
    import(
      "./routes/project/component/feature/config/types/onlineTeamAttendanceReport/OnlineTeamAttendanceReportPage"
    ),
);

const SummaryReportPage = React.lazy(
  () =>
    import(
      "./routes/project/component/feature/config/types/summaryReport/SummaryReportPage"
    ),
);

const SamplingPage = React.lazy(
  () =>
    import(
      "./routes/project/component/feature/config/types/sampling/SamplingPage"
    ),
);

const CustomerInformationCapturingPage = React.lazy(
  () =>
    import(
      "./routes/project/component/feature/config/types/customerInformationCapturing/CustomerInformationCapturingPage"
    ),
);

const ConfigPurchasePage = React.lazy(
  () =>
    import(
      "./routes/project/component/feature/config/types/customerInformationCapturing/steps/purchase/ConfigPurchasePage/ConfigPurchasePage"
    ),
);

const ConfigPurchaseLimitPage = React.lazy(
  () =>
    import(
      "./routes/project/component/feature/config/types/customerInformationCapturing/steps/purchase/ConfigPurchaseLimitPage/ConfigPurchaseLimitPage"
    ),
);

const ConfigExchangePage = React.lazy(
  () =>
    import(
      "./routes/project/component/feature/config/types/customerInformationCapturing/steps/exchange/configExchangePage/ConfigExchangePage"
    ),
);

const ConfigCustomerPage = React.lazy(
  () =>
    import(
      "./routes/project/component/feature/config/types/customerInformationCapturing/steps/customer/ConfigCustomerPage"
    ),
);

const ConfigCustomerSamplingPage = React.lazy(
  () =>
    import(
      "./routes/project/component/feature/config/types/customerInformationCapturing/steps/sampling/ConfigCustomerSamplingPage"
    ),
);

const ConfigCustomerSamplingOutletPage = React.lazy(
  () =>
    import(
      "./routes/project/component/feature/config/types/customerInformationCapturing/steps/sampling/ConfigCustomerSamplingOutletPage"
    ),
);

const ConfigPhotoPage = React.lazy(
  () =>
    import(
      "./routes/project/component/feature/config/types/customerInformationCapturing/steps/photo/ConfigPhotoPage"
    ),
);

const ConfigExchangeLimitPage = React.lazy(
  () =>
    import(
      "./routes/project/component/feature/config/types/customerInformationCapturing/steps/exchange/ConfigExchangeLimitPage"
    ),
);

const ConfigExchangeOutletPage = React.lazy(
  () =>
    import(
      "./routes/project/component/feature/config/types/customerInformationCapturing/steps/exchange/ConfigExchangeOutletPage"
    ),
);

const UrgencyPage = React.lazy(
  () =>
    import(
      "./routes/project/component/feature/config/types/urgency/UrgencyPage"
    ),
);
const OnlineTeamUrgencyReportPage = React.lazy(
  () =>
    import(
      "./routes/project/component/feature/config/types/onlineTeamUrgencyReport/OnlineTeamUrgencyReportPage"
    ),
);

const NumericSheetPage = React.lazy(
  () =>
    import(
      "./routes/project/component/feature/config/types/numericSheet/NumericSheetPage"
    ),
);

const NumericSheetGroupPage = React.lazy(
  () =>
    import(
      "./routes/project/component/feature/config/types/numericSheet/steps/group/NumericSheetGroupPage"
    ),
);

const NumericSheetBindingPage = React.lazy(
  () =>
    import(
      "./routes/project/component/feature/config/types/numericSheet/steps/binding/NumericSheetBindingPage"
    ),
);

const NumericSheetDataPage = React.lazy(
  () =>
    import(
      "./routes/project/component/feature/config/types/numericSheet/steps/data/NumericSheetDataPage"
    ),
);

const NumericSheetOutletPage = React.lazy(
  () =>
    import(
      "./routes/project/component/feature/config/types/numericSheet/steps/outlet/NumericSheetOutletPage"
    ),
);

const OutOfStockStatusPage = React.lazy(
  () =>
    import(
      "./routes/project/component/feature/config/types/outOfStockStatus/OutOfStockStatusPage"
    ),
);

const OutOfStockStatusGroupPage = React.lazy(
  () =>
    import(
      "./routes/project/component/feature/config/types/outOfStockStatus/steps/group/OutOfStockStatusGroupPage"
    ),
);

const OutOfStockStatusStatusPage = React.lazy(
  () =>
    import(
      "./routes/project/component/feature/config/types/outOfStockStatus/steps/status/OutOfStockStatusStatusPage"
    ),
);

const OutOfStockStatusOutletPage = React.lazy(
  () =>
    import(
      "./routes/project/component/feature/config/types/outOfStockStatus/steps/outlet/OutOfStockStatusOutletPage"
    ),
);

const OutOfStockStatusOutletMergedProductPage = React.lazy(
  () =>
    import(
      "./routes/project/component/feature/config/types/outOfStockStatus/steps/mergedProduct/OutOfStockStatusOutletMergedProductPage"
    ),
);

const OutOfStockStatusZonePage = React.lazy(
  () =>
    import(
      "./routes/project/component/feature/config/types/outOfStockStatus/steps/zone/OutOfStockStatusZonePage"
    ),
);

const OutOfStockStatusThresholdPage = React.lazy(
  () =>
    import(
      "@project/component/feature/config/types/outOfStockStatus/steps/threshold/OutOfStockStatusThresholdPage"
    ),
);

const ProjectReportLayout = React.lazy(
  () => import("./routes/project/report/ProjectReportLayout"),
);

const ReportPhotographyPage = React.lazy(
  () =>
    import("./routes/project/report/types/photography/ReportPhotographyPage"),
);

const ReportAttendanceClockingPage = React.lazy(
  () =>
    import(
      "./routes/project/report/types/attendanceClocking/ReportAttendanceClockingPage"
    ),
);

const ReportMultiSubjectMultimediaInformationCapturingPage = React.lazy(
  () =>
    import(
      "./routes/project/report/types/multiSubjectMultimediaInformationCapturing/ReportMultiSubjectMultimediaInformationCapturingPage"
    ),
);

const ReportMultipleEntitiesQuantityCapturingPage = React.lazy(
  () =>
    import(
      "./routes/project/report/types/multipleEntitiesQuantityCapturing/ReportMultipleEntitiesQuantityCapturingPage"
    ),
);

const ReportCustomerInformationCapturingPage = React.lazy(
  () =>
    import(
      "./routes/project/report/types/customerInformationCapturing/ReportCustomerInformationCapturingPage"
    ),
);

const ReportSamplingPage = React.lazy(
  () => import("./routes/project/report/types/sampling/ReportSamplingPage"),
);

const ReportUrgencyPage = React.lazy(
  () => import("./routes/project/report/types/urgency/ReportUrgencyPage"),
);

const ReportNumericSheetPage = React.lazy(
  () =>
    import("./routes/project/report/types/numericSheet/ReportNumericSheetPage"),
);

const ReportOutOfStockStatusPage = React.lazy(
  () =>
    import(
      "./routes/project/report/types/outOfStockStatus/ReportOutOfStockStatusPage"
    ),
);

const ReportOutOfStockStatusHighlightPage = React.lazy(
  () =>
    import(
      "./routes/project/report/types/outOfStockStatus/ReportOutOfStockStatusHighlightPage"
    ),
);

const ConfigSamplingPage = React.lazy(
  () =>
    import(
      "./routes/project/component/feature/config/types/sampling/steps/sampling/ConfigSamplingPage"
    ),
);

const ConfigSamplingOutletPage = React.lazy(
  () =>
    import(
      "./routes/project/component/feature/config/types/sampling/steps/outlet/ConfigSamplingOutletPage"
    ),
);

const ProjectEditPage = React.lazy(
  () => import("./routes/project/edit/ProjectEditPage"),
);

const ProjectEditLayOut = React.lazy(
  () => import("./routes/project/edit/ProjectEditLayout"),
);

const EditCustomerInformationCapturingPage = React.lazy(
  () =>
    import(
      "./routes/project/edit/types/customerInformationCapturing/EditCustomerInformationCapturingPage"
    ),
);

const EditSamplingPage = React.lazy(
  () => import("./routes/project/edit/types/sampling/EditSamplingPage"),
);

const EditMultipleEntitiesQuantityCapturingPage = React.lazy(
  () =>
    import(
      "./routes/project/edit/types/multipleEntitiesQuantityCapturing/EditMultipleEntitiesQuantityCapturingPage"
    ),
);

const EditNumericSheetPage = React.lazy(
  () => import("./routes/project/edit/types/numericSheet/EditNumericSheetPage"),
);

const ProjectDashboardPage = React.lazy(
  () => import("./routes/project/dashboard/ProjectDashboardPage"),
);
const ProjectDashboardV2Page = React.lazy(
  () => import("./routes/project/dashboard/ProjectDashboardV2Page"),
);
const ProjectDashboardV3Page = React.lazy(
  () => import("./routes/project/dashboard/ProjectDashboardV3Page"),
);

const ProjectDetailPage = React.lazy(
  () => import("./routes/project/detail/ProjectDetailPage"),
);

const ProjectKPILayout = React.lazy(
  () => import("./routes/project/kpi/ProjectKPILayout"),
);

const ProjectProjectKPIPage = React.lazy(
  () =>
    import(
      "./routes/project/kpi/types/project/projectKpi/ProjectProjectKPIPage"
    ),
);

const ProjectChannelKPIPage = React.lazy(
  () =>
    import(
      "./routes/project/kpi/types/project/channelKpi/ProjectChannelKPIPage"
    ),
);

const ProjectProvinceKPIPage = React.lazy(
  () =>
    import(
      "./routes/project/kpi/types/project/provinceKpi/ProjectProvinceKPIPage"
    ),
);

const ProjectKPIRollingPage = React.lazy(
  () =>
    import("./routes/project/kpi/types/project/rolling/ProjectKPIRollingPage"),
);

const ProjectKPIOutletPage = React.lazy(
  () =>
    import("./routes/project/kpi/types/project/outlet/ProjectKPIOutletPage"),
);

const ProjectChartPage = React.lazy(
  () => import("./routes/project/chart/ChartPage"),
);

const ProjectConfigPage = React.lazy(
  () => import("./routes/project/config/ProjectConfigPage"),
);

const ProjectSitecheckPage = React.lazy(
  () => import("./routes/project/sitecheck/SitecheckPage"),
);

const ProjectRegionPage = React.lazy(
  () => import("./routes/project/region/ProjectRegionPage"),
);

const ProjectConfigDashboardPage = React.lazy(
  () => import("./routes/project/configDashboard/ProjectConfigDashboardPage"),
);

const ProjectOtpDeliveryPage = React.lazy(
  () => import("./routes/project/otpDelivery/ProjectOtpDeliveryPage"),
);

const ProjectConfigLuckyWheelLayout = React.lazy(
  () =>
    import("./routes/project/configLuckyWheel/ProjectConfigLuckyWheelLayout"),
);

const ProjectConfigLuckyWheelPage = React.lazy(
  () =>
    import(
      "./routes/project/configLuckyWheel/LuckyWheel/ProjectConfigLuckyWheelPage"
    ),
);

const ProjectConfigLuckyWheelAllocationPage = React.lazy(
  () =>
    import(
      "./routes/project/configLuckyWheel/Allocation/ProjectConfigLuckyWheelAllocationPage"
    ),
);

// ----- End Project Route -----
const AppRouter = () => {
  const router = createBrowserRouter(
    [
      {
        element: <ProtectedMainLayout />,
        errorElement: <Error />,
        children: [
          {
            index: true,
            element: <DashboardPage />,
          },
          {
            path: "user",
            element: <UserPage />,
          },
          {
            path: "agency",
            element: <AgencyPage />,
          },
          {
            path: "unit",
            element: <UnitPage />,
          },
          {
            path: "channel",
            element: <ChannelPage />,
          },
          {
            path: "sub-channel",
            element: <SubChannelPage />,
          },
          {
            path: "brand",
            element: <BrandPage />,
          },
          {
            path: "product",
            element: <ProductPage />,
          },
          {
            path: "item-type",
            element: <ItemTypePage />,
          },
          {
            path: "item",
            element: <ItemPage />,
          },
          {
            path: "client",
            element: <ClientPage />,
          },
          {
            path: "project",
            element: <ProjectPage />,
          },
          {
            id: "projectDetail",
            path: "project/:id",
            shouldRevalidate: () => false,
            children: [
              {
                path: "general",
                element: <ProjectGeneralPage />,
              },
              {
                path: "item",
                element: <ProjectItemPage />,
              },
              {
                path: "role-employee",
                element: <ProjectRolePage />,
              },
              {
                path: "config-outlet",
                element: <ProjectConfigOutletPage />,
              },
              {
                path: "admin-client",
                element: <ProjectAdminPage />,
              },
              {
                path: "employee",
                element: <ProjectEmployeePage />,
              },
              {
                path: "outlet",
                element: <ProjectOutletPage />,
              },
              {
                path: "product",
                element: <ProjectProductPage />,
              },
              {
                path: "profile",
                element: <ProjectProfilePage />,
              },
              {
                path: "employee-leader/:employeeId",
                children: [
                  {
                    path: "outlet",
                    element: <ProjectEmployeeLeaderOutletPage />,
                  },
                  {
                    path: "employee",
                    element: <ProjectEmployeeLeaderEmployeePage />,
                  },
                ],
              },
              {
                path: "component",
                element: <ProjectComponentPage />,
              },
              {
                path: "component/:projectComponentId",
                element: <ComponentFeaturePage />,
              },
              {
                path: "component/:projectComponentId/feature",
                element: <ComponentFeaturePage />,
              },
              {
                path: "component/:projectComponentId/feature/:componentFeatureId",
                element: <FeatureConfigPage />,
                children: [
                  {
                    path: FeatureTypeEnum.AttendanceClockingIn,
                    element: <AttendanceClockingInPage />,
                  },
                  {
                    path: FeatureTypeEnum.AttendanceClockingOut,
                    element: <AttendanceClockingInPage />,
                  },
                  {
                    path: FeatureTypeEnum.Photography,
                    element: <PhotographyPage />,
                  },
                  {
                    path: FeatureTypeEnum.MultiSubjectMultimediaInformationCapturing,
                    element: <MultiSubjectMultimediaInformationCapturingPage />,
                  },
                  {
                    path: FeatureTypeEnum.MultipleEntitiesQuantityCapturing,
                    element: <MultipleEntitiesQuantityCapturingPage />,
                  },
                  {
                    path: FeatureTypeEnum.Synchronization,
                    element: <SynchronizationPage />,
                  },
                  {
                    path: FeatureTypeEnum.OnlineIndividualSummaryReport,
                    element: <OnlineIndividualSummaryReportPage />,
                  },
                  {
                    path: FeatureTypeEnum.OnlineTeamSummaryReport,
                    element: <OnlineTeamSummaryReportPage />,
                  },
                  {
                    path: FeatureTypeEnum.OnlineTeamAttendanceReport,
                    element: <OnlineTeamAttendanceReportPage />,
                  },
                  {
                    path: FeatureTypeEnum.SummaryReport,
                    element: <SummaryReportPage />,
                  },
                  {
                    path: FeatureTypeEnum.Sampling,
                    element: <SamplingPage />,
                    children: [
                      {
                        index: true,
                        element: <ConfigSamplingPage />,
                      },
                      {
                        path: "outlet",
                        element: <ConfigSamplingOutletPage />,
                      },
                    ],
                  },
                  {
                    path: FeatureTypeEnum.CustomerInformationCapturing,
                    element: <CustomerInformationCapturingPage />,
                    children: [
                      {
                        path: "purchase",
                        element: <ConfigPurchasePage />,
                      },
                      {
                        path: "purchase/limit",
                        element: <ConfigPurchaseLimitPage />,
                      },
                      {
                        path: "exchange",
                        element: <ConfigExchangePage />,
                      },
                      {
                        path: "exchange/limit",
                        element: <ConfigExchangeLimitPage />,
                      },
                      {
                        path: "exchange/outlet",
                        element: <ConfigExchangeOutletPage />,
                      },
                      {
                        path: "customer",
                        element: <ConfigCustomerPage />,
                      },
                      {
                        path: "sampling",
                        element: <ConfigCustomerSamplingPage />,
                      },
                      {
                        path: "sampling/outlet",
                        element: <ConfigCustomerSamplingOutletPage />,
                      },
                      {
                        path: "photo",
                        element: <ConfigPhotoPage />,
                      },
                    ],
                  },
                  {
                    path: FeatureTypeEnum.Urgency,
                    element: <UrgencyPage />,
                  },
                  {
                    path: FeatureTypeEnum.OnlineTeamUrgencyReport,
                    element: <OnlineTeamUrgencyReportPage />,
                  },
                  {
                    path: FeatureTypeEnum.NumericSheet,
                    element: <NumericSheetPage />,
                    children: [
                      {
                        index: true,
                        element: <NumericSheetGroupPage />,
                      },
                      {
                        path: NumericSheetStepEnum.GROUP,
                        element: <NumericSheetGroupPage />,
                      },
                      {
                        path: NumericSheetStepEnum.BINDING,
                        element: <NumericSheetBindingPage />,
                      },
                      {
                        path: NumericSheetStepEnum.DATA,
                        element: <NumericSheetDataPage />,
                      },
                      {
                        path: NumericSheetStepEnum.OUTLET,
                        element: <NumericSheetOutletPage />,
                      },
                    ],
                  },
                  {
                    path: FeatureTypeEnum.OutOfStockStatus,
                    element: <OutOfStockStatusPage />,
                    children: [
                      {
                        index: true,
                        element: <OutOfStockStatusStatusPage />,
                      },
                      {
                        path: OutOfStockStatusStepEnum.STATUS,
                        element: <OutOfStockStatusStatusPage />,
                      },
                      {
                        path: OutOfStockStatusStepEnum.GROUP,
                        element: <OutOfStockStatusGroupPage />,
                      },
                      {
                        path: OutOfStockStatusStepEnum.OUTLET,
                        element: <OutOfStockStatusOutletPage />,
                      },
                      {
                        path: OutOfStockStatusStepEnum.MERGED_PRODUCT,
                        element: <OutOfStockStatusOutletMergedProductPage />,
                      },
                      {
                        path: OutOfStockStatusStepEnum.ZONE,
                        element: <OutOfStockStatusZonePage />,
                      },
                      {
                        path: OutOfStockStatusStepEnum.THRESHOLD_PRODUCT,
                        element: (
                          <OutOfStockStatusThresholdPage
                            type={OosThresholdTypeEnum.PRODUCTS}
                          />
                        ),
                      },
                      {
                        path: OutOfStockStatusStepEnum.THRESHOLD_MERGED_PRODUCT,
                        element: (
                          <OutOfStockStatusThresholdPage
                            type={OosThresholdTypeEnum.MERGED_PRODUCTS}
                          />
                        ),
                      },
                    ],
                  },
                ],
              },
              {
                path: "report/component/:projectComponentId/feature/:componentFeatureId",
                element: <ProjectReportLayout />,
                children: [
                  {
                    path: FeatureTypeEnum.Photography,
                    element: <ReportPhotographyPage />,
                  },
                  {
                    path: FeatureTypeEnum.AttendanceClockingIn,
                    element: <ReportAttendanceClockingPage />,
                  },
                  {
                    path: FeatureTypeEnum.MultiSubjectMultimediaInformationCapturing,
                    element: (
                      <ReportMultiSubjectMultimediaInformationCapturingPage />
                    ),
                  },
                  {
                    path: FeatureTypeEnum.MultipleEntitiesQuantityCapturing,
                    element: <ReportMultipleEntitiesQuantityCapturingPage />,
                  },
                  {
                    path: FeatureTypeEnum.CustomerInformationCapturing,
                    element: <ReportCustomerInformationCapturingPage />,
                  },
                  {
                    path: FeatureTypeEnum.Sampling,
                    element: <ReportSamplingPage />,
                  },
                  {
                    path: FeatureTypeEnum.Urgency,
                    element: <ReportUrgencyPage />,
                  },
                  {
                    path: FeatureTypeEnum.NumericSheet,
                    element: <ReportNumericSheetPage />,
                  },
                  {
                    path: FeatureTypeEnum.OutOfStockStatus,
                    element: <ReportOutOfStockStatusPage />,
                  },
                  {
                    path: `${FeatureTypeEnum.OutOfStockStatus}/highlight`,
                    element: (
                      <ReportOutOfStockStatusHighlightPage key={"highlight"} />
                    ),
                  },
                  {
                    path: `${FeatureTypeEnum.OutOfStockStatus}/sale`,
                    element: (
                      <ReportOutOfStockStatusHighlightPage
                        isSale
                        key={"sale"}
                      />
                    ),
                  },
                ],
              },
              {
                path: "edit",
                element: <ProjectEditPage />,
              },
              {
                path: "edit/attendance/:attendanceId/feature/:componentFeatureId",
                element: <ProjectEditLayOut />,
                children: [
                  {
                    path: FeatureTypeEnum.CustomerInformationCapturing,
                    element: <EditCustomerInformationCapturingPage />,
                  },
                  {
                    path: FeatureTypeEnum.Sampling,
                    element: <EditSamplingPage />,
                  },
                  {
                    path: FeatureTypeEnum.MultipleEntitiesQuantityCapturing,
                    element: <EditMultipleEntitiesQuantityCapturingPage />,
                  },
                  {
                    path: FeatureTypeEnum.NumericSheet,
                    element: <EditNumericSheetPage />,
                  },
                ],
              },
              {
                path: "dashboard",
                element: <ProjectDashboardPage />,
              },
              {
                path: "dashboard/v2",
                element: <ProjectDashboardV2Page />,
              },
              {
                path: "dashboard/v3",
                element: <ProjectDashboardV3Page />,
              },
              {
                path: "detail",
                element: <ProjectDetailPage />,
              },
              {
                path: "kpi",
                element: <ProjectKPILayout />,
                children: [
                  {
                    index: true,
                    element: <ProjectProjectKPIPage />,
                  },
                  {
                    path: "project",
                    element: <ProjectProjectKPIPage />,
                  },
                  {
                    path: "project/channel",
                    element: <ProjectChannelKPIPage />,
                  },
                  {
                    path: "project/province",
                    element: <ProjectProvinceKPIPage />,
                  },
                  {
                    path: "project/rolling",
                    element: <ProjectKPIRollingPage />,
                  },
                  {
                    path: "project/outlet",
                    element: <ProjectKPIOutletPage />,
                  },
                  {
                    path: "project/outlet/sales",
                    element: <ProjectKPIOutletSalesPage />,
                  },
                ],
              },
              {
                path: "chart",
                element: <ProjectChartPage />,
              },
              {
                path: "config",
                element: <ProjectConfigPage />,
              },
              {
                path: "sitecheck",
                element: <ProjectSitecheckPage />,
              },
              {
                path: "region",
                element: <ProjectRegionPage />,
              },
              {
                path: "config-dashboard",
                element: <ProjectConfigDashboardPage />,
              },
              {
                path: "otp-delivery",
                element: <ProjectOtpDeliveryPage />,
              },
              {
                path: "config-lucky-wheel",
                element: <ProjectConfigLuckyWheelLayout />,
                children: [
                  {
                    index: true,
                    element: <ProjectConfigLuckyWheelPage />,
                  },
                  {
                    path: ":luckyDrawId/allocation",
                    element: <ProjectConfigLuckyWheelAllocationPage />,
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        path: "forbidden",
        element: <ProtectedForbiddenPage />,
      },
      {
        path: "lucky",
        element: <LuckyWheelLayout />,
      },
      {
        path: "exit",
        element: <></>,
      },
    ],
    {
      basename: import.meta.env.VITE_BASENAME ?? "",
    },
  );

  return <RouterProvider router={router} />;
};

export default AppRouter;
