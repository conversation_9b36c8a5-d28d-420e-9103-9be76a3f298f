import sortDownBlack from "@/assets/sort-down-black.svg";
import sortDownBlue from "@/assets/sort-down-blue.svg";
import sortUpBlack from "@/assets/sort-up-black.svg";
import sortUpBlue from "@/assets/sort-up-blue.svg";
import { useProvincesQuery } from "@location/service";
import type { TabsProps } from "antd";
import { Button, Skeleton, Space, Tabs } from "antd";
import React, { Suspense, useState } from "react";
import { useParams } from "react-router-dom";
import { useProjectChannelsQuery } from "../outlet/service";
import PopoverCheckbox from "./PopoverCheckbox";

const OverallTab = React.lazy(() => import("./tabs/overall/OverallTab"));
const SamplingActivityTab = React.lazy(
  () => import("./tabs/samplingActivity/SamplingActivityTab"),
);
const RedemptionTab = React.lazy(
  () => import("./tabs/redemption/RedemptionTab"),
);
const SalesTab = React.lazy(() => import("./tabs/sales/SalesTab"));

const ChartPage = () => {
  const projectId = parseInt(useParams().id ?? "0");

  const [sort, setSort] = useState<"asc" | "desc" | undefined>("desc");
  const [filter, setFilter] = useState<{
    provinceIds?: number[];
    channelIds?: number[];
  }>({});
  const [activeKey, setActiveKey] = useState("OVERALL");

  const provincesQuery = useProvincesQuery();
  const projectChannels = useProjectChannelsQuery(projectId);

  const items: TabsProps["items"] = [
    {
      key: "OVERALL",
      label: "Overall",
      children: (
        <Suspense fallback={<Skeleton />}>
          <OverallTab key={"OVERALL"} activeKey={activeKey} />
        </Suspense>
      ),
    },
    {
      key: "SAMPLING/ACTIVITIES",
      label: "Sampling/Activities",
      children: (
        <Suspense fallback={<Skeleton />}>
          <SamplingActivityTab
            activeKey={activeKey}
            sort={sort}
            filter={filter}
          />
        </Suspense>
      ),
    },
    {
      key: "REDEMPTION",
      label: "Redemption",
      children: (
        <Suspense fallback={<Skeleton />}>
          <RedemptionTab sort={sort} filter={filter} activeKey={activeKey} />
        </Suspense>
      ),
    },
    {
      key: "SALES",
      label: "Sales",
      children: (
        <Suspense fallback={<Skeleton />}>
          <SalesTab sort={sort} filter={filter} activeKey={activeKey} />
        </Suspense>
      ),
    },
  ];

  return (
    <>
      <h2>Dashboard</h2>
      <div className="bg-white px-10 pt-[30px] rounded">
        <div className="flex justify-between">
          <p className="text-[18px] font-semibold">
            Tổng quan toàn chương trình
          </p>

          <Space className="mt-0 pt-0">
            <PopoverCheckbox
              options={
                provincesQuery.data?.map(({ id, name }) => ({
                  label: name,
                  value: id,
                })) ?? []
              }
              buttonTitle="Tỉnh/ TP"
              title="Tìm theo tỉnh"
              placeholder="Nhập tỉnh cần tìm"
              onOkeCb={(values) => {
                setFilter({ ...filter, provinceIds: values });
              }}
            />

            <PopoverCheckbox
              options={
                projectChannels.data?.map(({ id, name }) => ({
                  label: name,
                  value: id,
                })) ?? []
              }
              buttonTitle="Kênh"
              title="Tìm theo kênh"
              placeholder="Nhập kênh cần tìm"
              onOkeCb={(values) => {
                setFilter({ ...filter, channelIds: values });
              }}
            />

            <div className="w-[1px] h-[33px] bg-[#DDE1EA]"></div>

            <Button
              className="px-2"
              onClick={() => {
                if (sort !== "desc") {
                  setSort("desc");
                } else {
                  setSort(undefined);
                }
              }}
            >
              {sort === "desc" ? (
                <img src={sortDownBlue} alt="sort-down-blue" />
              ) : (
                <img src={sortDownBlack} alt="sort-down-black" />
              )}
            </Button>

            <Button
              className="px-2"
              onClick={() => {
                if (sort !== "asc") {
                  setSort("asc");
                } else {
                  setSort(undefined);
                }
              }}
            >
              {sort === "asc" ? (
                <img src={sortUpBlue} alt="sort-down" />
              ) : (
                <img
                  src={sortUpBlack}
                  alt="sort-down"
                  style={{ color: "red" }}
                />
              )}
            </Button>
          </Space>
        </div>

        <Tabs
          defaultActiveKey={"OVERALL"}
          items={items}
          onChange={(e) => {
            setActiveKey(e);
          }}
          activeKey={activeKey}
        />
      </div>
    </>
  );
};

export default ChartPage;
