import { HookAPI } from "antd/es/modal/useModal";
import { AppContextInterface } from "../interface.ts";

interface ActiveOrInactiveActionProps {
  modal: HookAPI;
  axiosPatch: AppContextInterface["axiosPatch"];
  showNotification: AppContextInterface["showNotification"];
  cb: () => Promise<void>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  record: any;
  url: string;
  recordName: string;
}

export const inactiveAction = async (props: ActiveOrInactiveActionProps) => {
  const { modal, axiosPatch, showNotification, cb, record, url, recordName } =
    props;
  modal.confirm({
    title: `Ngừng hoạt động ${recordName}: ${record.name}`,
    content: `Bạn có chắc chắn muốn ngừng hoạt động ${recordName} này?`,
    okText: "Ngừng hoạt động",
    cancelText: "Hủ<PERSON>",
    onOk: async () => {
      try {
        await axiosPatch(url, {
          isActive: false,
        });
        showNotification({
          type: "success",
          message: `Ngừng hoạt động ${recordName} thành công`,
        });
        cb();
      } catch (error) {
        console.error(error);
        showNotification({
          type: "error",
          message: `Ngừng hoạt động ${recordName} thất bại`,
        });
      }
    },
  });
};

export const activeAction = async (props: ActiveOrInactiveActionProps) => {
  const { modal, axiosPatch, showNotification, cb, record, url, recordName } =
    props;
  modal.confirm({
    title: `Kích hoạt ${recordName}: ${record.name}`,
    content: `Bạn có chắc chắn muốn kích hoạt ${recordName} này?`,
    okText: "Kích hoạt",
    cancelText: "Hủy",
    onOk: async () => {
      try {
        await axiosPatch(url, {
          isActive: true,
        });
        showNotification({
          type: "success",
          message: `Kích hoạt ${recordName} thành công`,
        });
        cb();
      } catch (error) {
        console.error(error);
        showNotification({
          type: "error",
          message: `Kích hoạt ${recordName} thất bại`,
        });
      }
    },
  });
};

interface DeleteActionProps {
  modal: HookAPI;
  axiosDelete: AppContextInterface["axiosDelete"];
  showNotification: AppContextInterface["showNotification"];
  cb: () => Promise<void>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  record: any;
  url: string;
  recordName: string;
}

export const deleteAction = async (props: DeleteActionProps) => {
  const { modal, axiosDelete, showNotification, cb, record, url, recordName } =
    props;
  modal.confirm({
    title: `Xóa ${recordName}: ${record.name}`,
    content: `Bạn có chắc chắn muốn xóa ${recordName} này?`,
    okText: "Xóa",
    cancelText: "Hủy",
    onOk: async () => {
      try {
        await axiosDelete(url);
        showNotification({
          type: "success",
          message: `Xóa ${recordName} thành công`,
        });
        cb();
      } catch (error) {
        console.error(error);
        showNotification({
          type: "error",
          message: `Xóa ${recordName} thất bại`,
        });
      }
    },
  });
};
