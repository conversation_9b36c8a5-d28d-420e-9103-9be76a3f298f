import { useApp } from "@/UseApp";
import { useQuery } from "@tanstack/react-query";
import { DashboardFilterInterface } from "../../interface";

export const useAttendanceDailyFrequencyQuery = (
  projectId: number,
  dashboardId: number,
  filter?: DashboardFilterInterface,
) => {
  const { axiosGet } = useApp();

  return useQuery({
    queryKey: ["attendanceDailyFrequency", projectId, dashboardId, filter],
    queryFn: () =>
      axiosGet<
        {
          date: string;
          totalActual: number;
          totalKpi: number;
        }[],
        unknown
      >(
        `/projects/${projectId}/dashboards/${dashboardId}/charts/attendance-daily-frequency`,
        filter,
      ),
  });
};
