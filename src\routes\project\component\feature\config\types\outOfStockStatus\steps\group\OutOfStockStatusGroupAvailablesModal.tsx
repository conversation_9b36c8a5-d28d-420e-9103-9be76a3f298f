import { filterOption } from "@/common/helper.ts";
import ProductItemCell from "@/components/ProductItemCell.tsx";
import { useUrlFiltersWithQuery } from "@/hooks/useUrlFiltersWithQuery.ts";
import { useItemTypesQuery } from "@/routes/item-type/services.ts";
import { ProjectProductInterface } from "@/routes/project/product/interface";
import { useApp } from "@/UseApp.tsx";
import { CloseOutlined } from "@ant-design/icons";
import { OosGroupInterface } from "@project/component/feature/config/types/outOfStockStatus/interface.ts";
import { Button, Form, Input, Modal, Select, Space, Table } from "antd";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import {
  useCreateOosGroupProductMutation,
  useOosGroupProductAvailablesQuery,
} from "./service";

interface OutOfStockStatusGroupAvailablesModalProps {
  onModalClose: () => void;
  componentFeatureId: number;
  oosGroup: OosGroupInterface | null;
  cb: () => void;
}

const OutOfStockStatusGroupAvailablesModal = ({
  onModalClose,
  componentFeatureId,
  oosGroup,
  cb,
}: OutOfStockStatusGroupAvailablesModalProps) => {
  const { showNotification } = useApp();
  const oosGroupId = oosGroup?.id ?? 0;

  const [searchModalForm] = Form.useForm();
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);

  const {
    query: { data, refetch, isFetching },
    handleSearch,
    getPaginationProps,
  } = useUrlFiltersWithQuery<ProjectProductInterface>({
    formInstance: searchModalForm,
    useQueryHook: useOosGroupProductAvailablesQuery,
    queryParams: [componentFeatureId, oosGroupId],
    options: {
      urlSync: {
        enabled: false,
      },
      transformations: {
        toFilterValues: {
          type: (type) => {
            let itemTypeId = undefined;
            let isProduct = undefined;
            if (type && type !== 0) {
              itemTypeId = type;
            }
            if (type === 0) {
              isProduct = true;
            }
            return {
              itemTypeId,
              isProduct,
            };
          },
        },
      },
    },
  });

  const itemTypesQuery = useItemTypesQuery({ take: 50, skip: 0 });
  const createOosGroupProductMutation = useCreateOosGroupProductMutation(
    componentFeatureId,
    oosGroupId,
  );

  const typeOptions = useMemo(() => {
    const data = itemTypesQuery.data?.entities.map((item) => ({
      label: item.name,
      value: item.id,
    }));
    data?.unshift({ label: "Sản phẩm", value: 0 });
    return data;
  }, [itemTypesQuery.data?.entities]);

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys: selectedKeys,
    onChange: onSelectChange,
    getCheckboxProps: (record: ProjectProductInterface) => {
      return {
        disabled: !record.isAvailable || !record.isActive,
      };
    },
  };

  const newSelectedKeys = useMemo(() => {
    return (
      data?.entities
        .filter(
          (projectProductAvailable) =>
            selectedKeys.includes(projectProductAvailable.id) &&
            projectProductAvailable.isAvailable,
        )
        .map((item) => item.id) ?? []
    );
  }, [data?.entities, selectedKeys]);

  const onSubmit = useCallback(async () => {
    if (newSelectedKeys.length === 0) {
      return;
    }
    const data = [];
    for (const element of newSelectedKeys) {
      data.push({
        projectProductId: element,
      });
    }

    await createOosGroupProductMutation.mutateAsync(data);
    showNotification({
      type: "success",
      message: "Thêm item vào chức năng thành công",
    });

    setSelectedKeys([]);
    refetch();
    cb();
  }, [
    cb,
    createOosGroupProductMutation,
    newSelectedKeys,
    refetch,
    showNotification,
  ]);

  useEffect(() => {
    setSelectedKeys(
      data?.entities
        .filter((item) => !item.isAvailable)
        .map((item) => `${item.id}-${item.isAvailable}`) ?? [],
    );
  }, [data?.entities]);

  const pagination = useMemo(() => getPaginationProps(), [getPaginationProps]);

  return (
    <Modal
      open={true}
      footer={null}
      closeIcon={null}
      width={870}
      styles={{ content: { padding: 0 } }}
    >
      <div className="pl-10 pr-10 pt-3 pb-5">
        <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
          <h2 className="text-neutral-700 text-2xl font-semibold ">
            Thêm sản phẩm vào chức năng
          </h2>
          <div className="pt-5">
            <Button
              type="link"
              onClick={onModalClose}
              size="large"
              icon={<CloseOutlined />}
            />
          </div>
        </div>

        <p>
          Sản phẩm gộp:{" "}
          <span className={"text-blue font-semibold"}>{oosGroup?.name}</span>
        </p>

        <Form layout="vertical" onFinish={handleSearch} form={searchModalForm}>
          <Space>
            <Form.Item label="Loại" name={"type"}>
              <Select
                style={{ width: "200px" }}
                placeholder={"Tất cả"}
                allowClear
                options={typeOptions}
                showSearch
                filterOption={filterOption}
              />
            </Form.Item>
            <Form.Item label="Tên sản phẩm" name={"keyword"}>
              <Input placeholder="Nhập tên hoặc mã" allowClear />
            </Form.Item>
            <Form.Item label=" ">
              <Button htmlType="submit">Tìm kiếm</Button>
            </Form.Item>
          </Space>
        </Form>

        <Table
          scroll={{
            y: pagination.total ? "70vh" : undefined,
            x: "max-content",
          }}
          dataSource={data?.entities ?? []}
          loading={isFetching}
          columns={[
            {
              title: "Tên",
              className: "min-w-[100px]",
              dataIndex: "name",
              render: (_, record: ProjectProductInterface) => {
                return (
                  <ProductItemCell
                    variants={record?.product?.image?.variants ?? []}
                    name={record.product.name}
                    isActive={record.isActive}
                    isAvailable={record.isAvailable}
                  />
                );
              },
            },
            {
              title: "Mã sản phẩm",
              className: "min-w-[150px]",
              dataIndex: "product",
              render: (product) => product.code,
            },
            {
              title: "Nhãn hàng",
              className: "min-w-[100px]",
              dataIndex: "product",
              render: (product) => product.brand.name,
            },
            {
              title: "Đơn vị tính",
              className: "min-w-[100px]",
              dataIndex: "productPackaging",
              render: (productPackaging) => productPackaging.unit.name,
            },
            {
              title: "Nhóm sản phẩm",
              className: "min-w-[100px]",
            },
          ]}
          rowSelection={rowSelection}
          rowKey={(record) => record.id}
          pagination={pagination}
        />
      </div>
      <div className="flex justify-end pb-4 pt-4 bg-[#F7F8FA]">
        <Space className="pr-10">
          <Button onClick={onModalClose}>Đóng</Button>
          <Button
            type={"primary"}
            disabled={!newSelectedKeys.length}
            onClick={onSubmit}
            loading={createOosGroupProductMutation.isPending}
          >
            Thêm {newSelectedKeys.length} item vào chức năng
          </Button>
        </Space>
      </div>
    </Modal>
  );
};
export default OutOfStockStatusGroupAvailablesModal;
