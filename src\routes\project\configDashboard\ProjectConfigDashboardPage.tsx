import CustomTable from "@/components/CustomTable/CustomTable";
import InnerContainer from "@/components/InnerContainer/InnerContainer";
import { useParams } from "react-router-dom";
import { ProjectDashboardInterface } from "./interface";
import { useGetProjectDashboards } from "./service";

const ProjectConfigDashboardPage = () => {
  const projectId = parseInt(useParams().id ?? "0");
  const { data, isLoading } = useGetProjectDashboards(projectId);

  return (
    <div>
      <h2>Cấu hình dashboard</h2>

      <InnerContainer>
        <CustomTable<ProjectDashboardInterface>
          dataSource={data?.entities ?? []}
          columns={[
            {
              title: "Name",
              dataIndex: "name",
            },
            {
              title: "Type",
              dataIndex: "type",
            },
          ]}
          pagination={false}
          loading={isLoading}
        />
      </InnerContainer>
    </div>
  );
};

export default ProjectConfigDashboardPage;
