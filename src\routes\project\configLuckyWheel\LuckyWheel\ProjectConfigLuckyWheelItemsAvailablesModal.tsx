import { filterOption } from "@/common/helper";
import CustomTable from "@/components/CustomTable/CustomTable";
import ProductItemCell from "@/components/ProductItemCell";
import { useUrlFiltersWithQuery } from "@/hooks/useUrlFiltersWithQuery";
import { ItemInterface } from "@/routes/item/interface";
import { CloseOutlined } from "@ant-design/icons";
import { Button, Form, Input, Modal, Select, Space } from "antd";
import { useCallback, useMemo, useState } from "react";
import { useProjectItemTypesQuery } from "../../general/services";
import { ProjectLuckyDrawItemAvaiIableInterface } from "../interface";
import {
  useAddLuckyDrawItemsMutation,
  useLuckyDrawItemsAvailableQuery,
} from "../service";

interface ProjectConfigLuckyWheelItemsAvailablesModalProps {
  cb: () => void;
  onModalClose: () => void;
  projectId: number;
  luckyDrawId: number;
}
const ProjectConfigLuckyWheelItemsAvailablesModal = ({
  cb,
  onModalClose,
  projectId,
  luckyDrawId,
}: ProjectConfigLuckyWheelItemsAvailablesModalProps) => {
  const [searchModalForm] = Form.useForm();
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);

  const {
    query: { data, refetch, isFetching },
    handleSearch,
    getPaginationProps,
  } = useUrlFiltersWithQuery<ProjectLuckyDrawItemAvaiIableInterface>({
    formInstance: searchModalForm,
    useQueryHook: useLuckyDrawItemsAvailableQuery,
    queryParams: [projectId, luckyDrawId],
    options: {
      urlSync: {
        enabled: false,
      },
    },
  });
  const projecetItemTypesQuery = useProjectItemTypesQuery(projectId);

  const addLuckyDrawItemsMutation = useAddLuckyDrawItemsMutation(
    projectId,
    luckyDrawId,
  );

  const pagination = useMemo(() => getPaginationProps(), [getPaginationProps]);

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys: selectedKeys,
    onChange: onSelectChange,
    getCheckboxProps: (record: ProjectLuckyDrawItemAvaiIableInterface) => {
      return {
        disabled: !record.isAvailable || !record.isActive,
      };
    },
  };

  const newSelectedKeys = useMemo(() => {
    return (
      data?.entities
        .filter((item) => selectedKeys.includes(item.id) && item.isAvailable)
        .map((item) => item.id) ?? []
    );
  }, [data?.entities, selectedKeys]);

  const onSubmit = useCallback(async () => {
    if (newSelectedKeys.length === 0) {
      return;
    }
    const data = [];
    for (const element of newSelectedKeys) {
      data.push({
        projectItemId: element,
      });
    }

    await addLuckyDrawItemsMutation.mutateAsync(data);

    setSelectedKeys([]);
    refetch();
    cb();
    onModalClose();
  }, [addLuckyDrawItemsMutation, cb, newSelectedKeys, onModalClose, refetch]);

  return (
    <Modal
      open={true}
      footer={null}
      closeIcon={null}
      width={870}
      styles={{ content: { padding: 0 } }}
    >
      <div className="pl-10 pr-10 pt-3 pb-5">
        <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
          <h2 className="text-neutral-700 text-2xl font-semibold ">
            Thêm sản phẩm vào chức năng
          </h2>
          <div className="pt-5">
            <Button
              type="link"
              onClick={onModalClose}
              size="large"
              icon={<CloseOutlined />}
            />
          </div>
        </div>

        <Form layout="vertical" onFinish={handleSearch} form={searchModalForm}>
          <Space>
            <Form.Item label="Loại" name={"itemTypeId"}>
              <Select
                style={{ width: "200px" }}
                placeholder={"Tất cả"}
                allowClear
                options={
                  projecetItemTypesQuery.data?.entities.map((itemType) => ({
                    label: itemType.name,
                    value: itemType.id,
                  })) ?? []
                }
                showSearch
                filterOption={filterOption}
              />
            </Form.Item>
            <Form.Item label="Tên sản phẩm" name={"keyword"}>
              <Input placeholder="Nhập tên hoặc mã" allowClear />
            </Form.Item>
            <Form.Item label=" ">
              <Button htmlType="submit">Tìm kiếm</Button>
            </Form.Item>
          </Space>
        </Form>

        <CustomTable
          scroll={{
            y: pagination.total ? "60vh" : undefined,
            x: "max-content",
          }}
          dataSource={data?.entities ?? []}
          loading={isFetching}
          columns={[
            {
              title: "Tên",
              className: "min-w-[100px]",
              dataIndex: "item",
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              render: (item: ItemInterface, record: any) => {
                return (
                  <ProductItemCell
                    variants={item?.image?.variants ?? []}
                    name={item.name}
                    isActive={item.isActive}
                    isAvailable={record.isAvailable}
                  />
                );
              },
            },
            {
              title: "Mã sản phẩm",
              className: "min-w-[150px]",
              dataIndex: "item",
              render: (item: ItemInterface) => item.code,
            },
            {
              title: "Loại",
              className: "min-w-[100px]",
              dataIndex: "item",
              render: (item: ItemInterface) => item?.itemType?.name,
            },
            {
              title: "Đơn vị tính",
              className: "min-w-[100px]",
              dataIndex: "item",
              render: (item: ItemInterface) => item?.unit?.name,
            },
          ]}
          rowSelection={rowSelection}
          pagination={pagination}
        />
      </div>
      <div className="flex justify-end pb-4 pt-4 bg-[#F7F8FA]">
        <Space className="pr-10">
          <Button onClick={onModalClose}>Đóng</Button>
          <Button
            type={"primary"}
            disabled={!newSelectedKeys.length}
            onClick={onSubmit}
            loading={addLuckyDrawItemsMutation.isPending}
          >
            Thêm {newSelectedKeys.length} item vào chức năng
          </Button>
        </Space>
      </div>
    </Modal>
  );
};

export default ProjectConfigLuckyWheelItemsAvailablesModal;
