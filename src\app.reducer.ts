import {
  ConfirmModalPropsInterface,
  DeleteModalPropsInterface,
  ErrorModalPropsInterface,
} from "./interface";

export const initialStateModal = {
  isOpen: false,
  data: undefined,
};

export const deleteModalReducer = (
  state: { isOpen: boolean; data?: DeleteModalPropsInterface },
  action: {
    type: "OPEN_MODAL" | "CLOSE_MODAL" | "SET_LOADING" | "SET_LOADING_FALSE";
    data?: DeleteModalPropsInterface;
  },
): { isOpen: boolean; data?: DeleteModalPropsInterface } => {
  const data = state.data;
  switch (action.type) {
    case "OPEN_MODAL":
      return { ...state, isOpen: true, data: action.data };
    case "CLOSE_MODAL":
      return { ...state, isOpen: false, data: undefined };
    case "SET_LOADING":
      if (data === undefined) return { isOpen: false, data: undefined };

      return {
        isOpen: true,
        data: {
          ...data,
          loading: true,
        },
      };

    case "SET_LOADING_FALSE":
      if (data === undefined) return { isOpen: false, data: undefined };

      return {
        isOpen: true,
        data: {
          ...data,
          loading: false,
        },
      };
    default:
      return state;
  }
};

export const errorModalReducer = (
  state: { isOpen: boolean; data?: ErrorModalPropsInterface },
  action: {
    type: "OPEN_MODAL" | "CLOSE_MODAL";
    data?: ErrorModalPropsInterface;
  },
): { isOpen: boolean; data?: ErrorModalPropsInterface } => {
  switch (action.type) {
    case "OPEN_MODAL":
      return { ...state, isOpen: true, data: action.data };
    case "CLOSE_MODAL":
      return { ...state, isOpen: false, data: undefined };

    default:
      return state;
  }
};

export const confirmModalReducer = (
  state: { isOpen: boolean; data?: ConfirmModalPropsInterface },
  action: {
    type: "OPEN_MODAL" | "CLOSE_MODAL" | "SET_LOADING" | "SET_LOADING_FALSE";
    data?: ConfirmModalPropsInterface;
  },
): { isOpen: boolean; data?: ConfirmModalPropsInterface } => {
  const data = state.data;
  switch (action.type) {
    case "OPEN_MODAL":
      return { ...state, isOpen: true, data: action.data };
    case "CLOSE_MODAL":
      return { ...state, isOpen: false, data: undefined };
    case "SET_LOADING":
      if (data === undefined) return { isOpen: false, data: undefined };

      return {
        isOpen: true,
        data: {
          ...data,
          loading: true,
        },
      };

    case "SET_LOADING_FALSE":
      if (data === undefined) return { isOpen: false, data: undefined };

      return {
        isOpen: true,
        data: {
          ...data,
          loading: false,
        },
      };
    default:
      return state;
  }
};
