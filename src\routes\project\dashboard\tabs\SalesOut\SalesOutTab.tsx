import ChartContanier from "@/routes/project/chart/ChartContanier";
import { Column } from "@ant-design/charts";
import { useCallback } from "react";
import DashboardFilterZone from "../../DashboardFilterZone";

interface ChartData {
  date: string;
  category: string;
  value: number;
}

const SalesOutTab = () => {
  const handleApply = useCallback(() => {}, []);
  const data: ChartData[] = [
    // 20/12
    { date: "20/12", category: "333", value: 2764 },
    { date: "20/12", category: "Chill", value: 3218 },
    { date: "20/12", category: "Export", value: 2000 },
    { date: "20/12", category: "Gold", value: 2158 },
    { date: "20/12", category: "Lager", value: 3502 },
    { date: "20/12", category: "Special", value: 3722 },
    // 21/12
    { date: "21/12", category: "333", value: 2764 },
    { date: "21/12", category: "Chill", value: 4245 },
    { date: "21/12", category: "Export", value: 4090 },
    { date: "21/12", category: "Gold", value: 3354 },
    { date: "21/12", category: "Lager", value: 3502 },
    { date: "21/12", category: "Special", value: 3722 },
    // 22/12
    { date: "22/12", category: "333", value: 2119 },
    { date: "22/12", category: "Chill", value: 3271 },
    { date: "22/12", category: "Export", value: 3933 },
    { date: "22/12", category: "Gold", value: 2830 },
    { date: "22/12", category: "Lager", value: 3031 },
    { date: "22/12", category: "Special", value: 3722 },
    // 23/12
    { date: "23/12", category: "333", value: 2119 },
    { date: "23/12", category: "Chill", value: 3271 },
    { date: "23/12", category: "Export", value: 3933 },
    { date: "23/12", category: "Gold", value: 2830 },
    { date: "23/12", category: "Lager", value: 3031 },
    { date: "23/12", category: "Special", value: 3722 },
    // 24/12
    { date: "24/12", category: "333", value: 2119 },
    { date: "24/12", category: "Chill", value: 3271 },
    { date: "24/12", category: "Export", value: 3933 },
    { date: "24/12", category: "Gold", value: 2830 },
    { date: "24/12", category: "Lager", value: 3031 },
    { date: "24/12", category: "Special", value: 3722 },
    // 25/12
    { date: "25/12", category: "333", value: 2119 },
    { date: "25/12", category: "Chill", value: 3271 },
    { date: "25/12", category: "Export", value: 3933 },
    { date: "25/12", category: "Gold", value: 2830 },
    { date: "25/12", category: "Lager", value: 3031 },
    { date: "25/12", category: "Special", value: 3722 },
    // 26/12
    { date: "26/12", category: "333", value: 2119 },
    { date: "26/12", category: "Chill", value: 3271 },
    { date: "26/12", category: "Export", value: 3933 },
    { date: "26/12", category: "Gold", value: 2830 },
    { date: "26/12", category: "Lager", value: 3031 },
    { date: "26/12", category: "Special", value: 3722 },
    // 27/12
    { date: "27/12", category: "333", value: 2119 },
    { date: "27/12", category: "Chill", value: 3271 },
    { date: "27/12", category: "Export", value: 3933 },
    { date: "27/12", category: "Gold", value: 2830 },
    { date: "27/12", category: "Lager", value: 3031 },
    { date: "27/12", category: "Special", value: 3722 },
    // 28/12
    { date: "28/12", category: "333", value: 2119 },
    { date: "28/12", category: "Chill", value: 3271 },
    { date: "28/12", category: "Export", value: 3933 },
    { date: "28/12", category: "Gold", value: 2830 },
    { date: "28/12", category: "Lager", value: 3031 },
    { date: "28/12", category: "Special", value: 3722 },
    // 29/12
    { date: "29/12", category: "333", value: 2119 },
    { date: "29/12", category: "Chill", value: 3271 },
    { date: "29/12", category: "Export", value: 3933 },
    { date: "29/12", category: "Gold", value: 2830 },
    { date: "29/12", category: "Lager", value: 3031 },
    { date: "29/12", category: "Special", value: 3722 },
    // 30/12
    { date: "30/12", category: "333", value: 2119 },
    { date: "30/12", category: "Chill", value: 3271 },
    { date: "30/12", category: "Export", value: 3933 },
    { date: "30/12", category: "Gold", value: 2830 },
    { date: "30/12", category: "Lager", value: 3031 },
    { date: "30/12", category: "Special", value: 3722 },
    // 31/12
    { date: "31/12", category: "333", value: 2119 },
    { date: "31/12", category: "Chill", value: 3271 },
    { date: "31/12", category: "Export", value: 3933 },
    { date: "31/12", category: "Gold", value: 2830 },
    { date: "31/12", category: "Lager", value: 3031 },
    { date: "31/12", category: "Special", value: 3722 },
    // 1/1
    { date: "1/1", category: "333", value: 4994 },
    { date: "1/1", category: "Chill", value: 2056 },
    { date: "1/1", category: "Export", value: 2021 },
    { date: "1/1", category: "Gold", value: 2712 },
    { date: "1/1", category: "Lager", value: 2488 },
    { date: "1/1", category: "Special", value: 2488 },
    // 2/1
    { date: "2/1", category: "333", value: 4994 },
    { date: "2/1", category: "Chill", value: 2056 },
    { date: "2/1", category: "Export", value: 2021 },
    { date: "2/1", category: "Gold", value: 2712 },
    { date: "2/1", category: "Lager", value: 2488 },
    { date: "2/1", category: "Special", value: 2488 },
    // 3/1
    { date: "3/1", category: "333", value: 4994 },
    { date: "3/1", category: "Chill", value: 2056 },
    { date: "3/1", category: "Export", value: 2021 },
    { date: "3/1", category: "Gold", value: 2712 },
    { date: "3/1", category: "Lager", value: 2488 },
    { date: "3/1", category: "Special", value: 2488 },
  ];

  const config = {
    data,
    xField: "date",
    yField: "value",
    colorField: "category",
    stack: true,
    sort: {
      reverse: true,
      by: "y",
    },
    title: {
      title: "Sales out",
      subtitle: "Tính theo thùng",
    },
    legend: {
      color: {
        position: "bottom",
        layout: {
          justifyContent: "center",
        },
      },
    },
    label: {
      textBaseline: "middle",
      textAlign: "center",
      position: "inside",
      fill: "#000000",
    },
    style: {
      maxWidth: 50,
    },
  };

  return (
    <>
      <DashboardFilterZone
        handleApply={handleApply}
        fields={["region", "province", "chain", "brand", "date"]}
      />

      <ChartContanier>
        <Column {...config} height={600} />
      </ChartContanier>
    </>
  );
};

export default SalesOutTab;
