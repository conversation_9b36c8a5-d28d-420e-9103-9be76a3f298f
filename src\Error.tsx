import { isRouteErrorResponse, useRouteError } from "react-router-dom";
import ErrorPage from "./ErrorPage.tsx";
import NotFoundPage from "./NotFoundPage.tsx";

export function Error() {
  const error = useRouteError();

  console.log(error);

  if (isRouteErrorResponse(error)) {
    if (error.status === 404) {
      return <NotFoundPage />;
    }

    if (error.status === 401) {
      return <div>You aren't authorized to see this</div>;
    }

    if (error.status === 503) {
      return <div>Looks like our API is down</div>;
    }

    if (error.status === 418) {
      return <div>🫖</div>;
    }
  }

  return <ErrorPage />;
}
