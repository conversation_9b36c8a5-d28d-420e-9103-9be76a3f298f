import { AbstractEntityInterface } from "@/common/interface";
import { ClientInterface } from "../client/interface.ts";
import { ItemInterface } from "../item/interface";

export interface ItemTypeInterface extends AbstractEntityInterface {
  name: string;
  description: string;
  items: ItemInterface[];
  client: ClientInterface;
}

export interface ApiItemTypeResponseInterface {
  entities: ItemTypeInterface[];
  count: number;
}
