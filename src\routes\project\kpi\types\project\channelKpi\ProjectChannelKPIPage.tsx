import { filterOption } from "@/common/helper";
import TableActionCell from "@/components/TableActionCell";
import { ChannelInterface } from "@/routes/channel/interface";
import { CheckSquareOutlined, LoadingOutlined } from "@ant-design/icons";
import { Button, Select, Table } from "antd";
import { useCallback, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import {
  ItemKpiTypeEnum,
  KPIScopeEnum,
  ProjectKPIItemInterface,
  PropagationModalType,
} from "../../../interface";
import KPIEditInput from "../../../KPIEditInput";
import KPIModal from "../../../KPIModal";
import KPIPropagationModal from "../../../KPIPropagationModal";
import { useProjectKPIQuery } from "../../../service";

const ProjectChannelKPIPage = () => {
  const projectId = parseInt(useParams().id ?? "0");

  const [itemKpiType, setItemKpiType] = useState<ItemKpiTypeEnum | undefined>();
  const [modalTitle, setModalTitle] = useState<string | undefined>(undefined);
  const [innerModalTitle, setInnerModalTitle] = useState<JSX.Element>(<></>);
  /**
   * Dùng để làm filter
   */
  const [selectedChannelId, setSelectedChannelId] = useState<
    number | undefined
  >(undefined);

  /**
   * Dùng để chỉ ra đang mở modal của channel nào
   */
  const [openedChannel, setOpenedChannel] = useState<
    ChannelInterface | undefined
  >(undefined);
  const [itemsKpiModal, setItemsKpiModal] = useState<ProjectKPIItemInterface[]>(
    [],
  );
  const [isPropagationModalOpen, setIsPropagationModalOpen] =
    useState<boolean>(false);

  const projectKPIQuery = useProjectKPIQuery(projectId, KPIScopeEnum.CHANNEL);

  const dataSource = useMemo(() => {
    if (selectedChannelId) {
      return (
        projectKPIQuery.data?.filter(
          (item) => item.channel?.id === selectedChannelId,
        ) ?? []
      );
    }
    return projectKPIQuery.data ?? [];
  }, [projectKPIQuery.data, selectedChannelId]);

  const kpiModalcancel = useCallback(() => {
    setItemKpiType(undefined);
    setModalTitle(undefined);
    setInnerModalTitle(<></>);
    setOpenedChannel(undefined);
    setItemsKpiModal([]);
    projectKPIQuery.refetch();
  }, [projectKPIQuery]);

  const kpiPropagationModalCancel = useCallback(() => {
    projectKPIQuery.refetch();
    setIsPropagationModalOpen(false);
    setOpenedChannel(undefined);
  }, [projectKPIQuery]);

  const propagationCb = useCallback(() => {
    projectKPIQuery.refetch();
    setIsPropagationModalOpen(false);
    setOpenedChannel(undefined);
  }, [projectKPIQuery]);

  const cb = useCallback(() => {}, []);

  if (projectKPIQuery.isLoading) {
    return <LoadingOutlined />;
  }

  return (
    <>
      <Select
        placeholder="Chọn kênh cần tìm"
        options={
          projectKPIQuery.data?.map((item) => ({
            value: item.channel?.id ?? 0,
            label: item.channel?.name ?? "",
          })) ?? []
        }
        filterOption={filterOption}
        allowClear
        onChange={setSelectedChannelId}
      />

      <Table
        className="mt-6"
        dataSource={dataSource}
        rowKey={(o) => o.channel?.id ?? 0}
        pagination={false}
        columns={[
          {
            title: "Kênh",
            dataIndex: "channel",
            render: ({ name }: { name: string }) => name,
          },
          {
            title: "KPI doanh số",
            dataIndex: "salesRevenue",
            render: (
              salesRevenue: string,
              { channel }: { channel?: ChannelInterface; id: number },
            ) => (
              <KPIEditInput
                kpi={salesRevenue ? Number(salesRevenue) : null}
                cb={cb}
                projectId={projectId}
                type={"salesRevenue"}
                channelId={channel?.id}
              />
            ),
            align: "right",
          },
          {
            title: "KPI đơn hàng",
            dataIndex: "order",
            render: (
              order: string,
              { channel }: { channel?: ChannelInterface; id: number },
            ) => (
              <KPIEditInput
                kpi={order ? Number(order) : null}
                cb={cb}
                channelId={channel?.id}
                type={"order"}
                projectId={projectId}
              />
            ),
            align: "right",
          },
          {
            title: "KPI sampling (hit)",
            dataIndex: "hit",
            render: (
              hit: string,
              { channel }: { channel?: ChannelInterface; id: number },
            ) => (
              <KPIEditInput
                kpi={hit ? Number(hit) : null}
                cb={cb}
                projectId={projectId}
                type={"hit"}
                channelId={channel?.id}
              />
            ),
            align: "right",
          },
          {
            title: "KPI session",
            dataIndex: "session",
            render: (
              session: string,
              { channel }: { channel?: ChannelInterface; id: number },
            ) => (
              <KPIEditInput
                kpi={session ? Number(session) : null}
                cb={cb}
                projectId={projectId}
                type={"session"}
                channelId={channel?.id}
              />
            ),
            align: "right",
          },
          {
            title: "KPI sampling (dry)",
            align: "right",
            render: (record: {
              id: number;
              channel?: ChannelInterface;
              projectKpiItems: ProjectKPIItemInterface[];
            }) => (
              <Button
                className="text-blue"
                type="link"
                onClick={() => {
                  setItemKpiType(ItemKpiTypeEnum.SAMPLING);
                  setModalTitle("Cài đặt KPI sampling (dry)");
                  setOpenedChannel(record.channel);
                  setInnerModalTitle(
                    <>
                      Kênh đang chọn:{" "}
                      <span className="text-blue">{record.channel?.name}</span>
                    </>,
                  );
                  setItemsKpiModal(record.projectKpiItems);
                }}
              >
                Cài đặt
              </Button>
            ),
          },
          {
            title: "KPI quà thường",
            align: "right",
            render: (record: {
              id: number;
              channel?: ChannelInterface;
              projectKpiItems: ProjectKPIItemInterface[];
            }) => (
              <Button
                className="text-blue"
                type="link"
                onClick={() => {
                  setItemKpiType(ItemKpiTypeEnum.GIFT);
                  setModalTitle("Cài đặt KPI quà thường");
                  setOpenedChannel(record.channel);
                  setInnerModalTitle(
                    <>
                      Kênh đang chọn:{" "}
                      <span className="text-blue">{record.channel?.name}</span>
                    </>,
                  );
                  setItemsKpiModal(record.projectKpiItems);
                }}
              >
                Cài đặt
              </Button>
            ),
          },
          {
            title: "KPI quà game",
            align: "right",
            render: (record: {
              id: number;
              channel?: ChannelInterface;
              projectKpiItems: ProjectKPIItemInterface[];
            }) => (
              <Button
                className="text-blue"
                type="link"
                onClick={() => {
                  setItemKpiType(ItemKpiTypeEnum.GAME);
                  setModalTitle("Cài đặt KPI quà game");
                  setOpenedChannel(record.channel);
                  setInnerModalTitle(
                    <>
                      Kênh đang chọn:{" "}
                      <span className="text-blue">{record.channel?.name}</span>
                    </>,
                  );
                  setItemsKpiModal(record.projectKpiItems);
                }}
              >
                Cài đặt
              </Button>
            ),
          },
          {
            render: (_, record: { id: number; channel?: ChannelInterface }) => (
              <TableActionCell
                actions={[
                  {
                    key: "apply",
                    action: (record: {
                      channel?: ChannelInterface;
                      id: number;
                    }) => {
                      setIsPropagationModalOpen(true);
                      setOpenedChannel(record.channel);
                    },
                  },
                ]}
                items={[
                  {
                    label: "Áp dụng KPI này cho các kênh còn lại",
                    icon: <CheckSquareOutlined />,
                    key: "apply",
                  },
                ]}
                record={record}
              />
            ),
          },
        ]}
      />

      {itemKpiType && modalTitle && (
        <KPIModal
          isOpen={true}
          title={innerModalTitle}
          itemKpiType={itemKpiType}
          cancel={kpiModalcancel}
          projectId={projectId}
          modalTitle={modalTitle}
          items={itemsKpiModal}
          cb={cb}
          channelId={openedChannel?.id}
        />
      )}

      {isPropagationModalOpen && openedChannel && (
        <KPIPropagationModal
          options={
            projectKPIQuery.data
              ?.filter((item) => item?.channel?.id !== openedChannel?.id)
              .map((item) => ({
                value: item.channel?.id ?? 0,
                label: item.channel?.name ?? "",
              })) ?? []
          }
          isOpen={isPropagationModalOpen}
          cancel={kpiPropagationModalCancel}
          projectId={projectId}
          channel={openedChannel}
          cb={propagationCb}
          contentTitle={
            <>
              <p>
                Kênh đang chọn:{" "}
                <span className="text-blue font-semibold">
                  {openedChannel.name}
                </span>
              </p>
              <p>
                Chọn các loại KPI cần áp dụng hàng loạt cho các kênh còn lại
              </p>
            </>
          }
          type={PropagationModalType.CHANNEL}
        />
      )}
    </>
  );
};

export default ProjectChannelKPIPage;
