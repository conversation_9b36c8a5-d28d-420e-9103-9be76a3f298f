import { useApp } from "@/UseApp.tsx";
import {
  CURD,
  DATE_FORMAT,
  SELECT_ALL,
  SELECT_ALL_LABEL,
} from "@/common/constant";
import { filterOption } from "@/common/helper.ts";
import CustomTable from "@/components/CustomTable/CustomTable.tsx";
import FilterClassicComponent from "@/components/FilterClassicComponent.tsx";
import InnerContainer from "@/components/InnerContainer/InnerContainer.tsx";
import { renderTableCell } from "@/components/table-cell";
import { useUrlFiltersWithQuery } from "@/hooks/useUrlFiltersWithQuery.ts";
import { endDateAntFormFieldValidate } from "@/validates/end-date-ant-form-field.validate.ts";
import {
  CloseOutlined,
  EllipsisOutlined,
  FileSearchOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import {
  Button,
  Col,
  DatePicker,
  Dropdown,
  Form,
  Input,
  MenuProps,
  Modal,
  Row,
  Select,
} from "antd";
import { ColumnsType } from "antd/es/table";
import React, { useCallback, useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useClientsQuery } from "../client/service.ts";
import { UserTypeEnum } from "../user/interface.ts";
import { ProjectInterface } from "./interface.ts";
import {
  useCreateProjectMutation,
  useProjectsQuery,
  useUpdateProjectMutation,
} from "./services.ts";

const ProjectPage: React.FC = () => {
  const { showNotification, loading, setLoading } = useApp();
  const navigate = useNavigate();
  const { userLogin } = useApp();

  const [searchForm] = Form.useForm();
  const [form] = Form.useForm();
  const [isModalAddOrUpdateOpen, setIsModalAddOrUpdateOpen] = useState(false);
  const [formAction, setFormAction] = useState<CURD | null>(null);
  const [modalTitle, setModalTitle] = useState("");
  const {
    query: { data, refetch, isFetching, isRefetching },
    handleSearch,
    getPaginationProps,
  } = useUrlFiltersWithQuery<ProjectInterface>({
    formInstance: searchForm,
    useQueryHook: useProjectsQuery,
  });

  const clientsQuery = useClientsQuery({
    take: 0,
    skip: 0,
  });

  const createProjectMutation = useCreateProjectMutation();
  const updateProjectMutation = useUpdateProjectMutation();

  const columns: ColumnsType<ProjectInterface> = [
    {
      title: "ID",
      dataIndex: "id",
      className: "min-w-[50px]",
    },
    {
      title: "Tên dự án",
      key: "name",
      dataIndex: "name",
      render: renderTableCell,
      className: "cursor-pointer min-w-[50px]",
      onCell(record) {
        return {
          onClick: () => {
            navigate("/project/" + record.id + "/detail");
          },
        };
      },
    },
    {
      title: "Ngày bắt đầu",
      key: "startDate",
      dataIndex: "startDate",
      className: "min-w-[100px]",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "date");
      },
    },
    {
      title: "Ngày kết thúc",
      key: "endDate",
      dataIndex: "endDate",
      className: "min-w-[100px]",
      render: (value, record, index) => {
        return renderTableCell(value, record, index, "date");
      },
    },
    {
      title: "Client",
      key: "client",
      dataIndex: "client",
      className: "min-w-[100px]",
      render: renderTableCell,
    },
    {
      key: "createdByUser.name",
      title: "Người tạo",
      className: "min-w-[100px]",
      dataIndex: "createdByUser",
      render: renderTableCell,
    },
    {
      key: "actions",
      render: (_, record) => {
        const handleMenuClick: MenuProps["onClick"] = () => {
          navigate("/project/" + record.id + "/detail");
        };

        const items: MenuProps["items"] = [
          {
            label: "Xem chi tiết",
            key: "DETAIL",
            icon: <FileSearchOutlined />,
          },
        ];

        const menuProps = {
          items,
          onClick: handleMenuClick,
        };
        return (
          <Dropdown menu={menuProps}>
            <Button type="link">
              <EllipsisOutlined />
            </Button>
          </Dropdown>
        );
      },
      fixed: "right",
      width: 100,
    },
  ];

  const filterOptions = columns
    .filter(
      (item) =>
        ![
          "isActive",
          "updatedAt",
          "createdAt",
          "actions",
          "startDate",
          "endDate",
          "status",
        ].includes(item.key as string),
    )
    .map((column) => {
      return {
        label: column.title as string,
        value: column.key as string | number,
      };
    });

  filterOptions.unshift({ label: SELECT_ALL_LABEL, value: SELECT_ALL });

  const handleAddOrUpdateFormSubmit = useCallback(async () => {
    try {
      await form.validateFields();
      const data = form.getFieldsValue();
      const id = form.getFieldValue("id");
      setLoading(true);

      switch (formAction) {
        case CURD.CREATE:
          await createProjectMutation.mutateAsync(data);

          showNotification({
            type: "success",
            message: "Thêm project thành công",
          });
          break;
        case CURD.UPDATE:
          await updateProjectMutation.mutateAsync({ ...data, id });

          showNotification({
            type: "success",
            message: "Cập nhật project thành công",
          });
          break;
        default:
          return;
      }

      form.resetFields();
      setIsModalAddOrUpdateOpen(false);
      setModalTitle("");
      refetch();

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      const data: { message: { field: string; message: string }[] } =
        error?.response?.data;
      const { message } = data;
      form.setFields([
        { name: message[0].field, errors: [message[0].message] },
      ]);
    } finally {
      setLoading(false);
    }
  }, [
    form,
    setLoading,
    formAction,
    refetch,
    createProjectMutation,
    showNotification,
    updateProjectMutation,
  ]);

  const handleAddButtonClick = () => {
    setIsModalAddOrUpdateOpen(true);
    setFormAction(CURD.CREATE);
    setModalTitle("Thêm dự án");
  };

  const pagination = useMemo(() => getPaginationProps(), [getPaginationProps]);

  return (
    <>
      <h2>Dự án</h2>
      <InnerContainer>
        <div className="mb-6">
          <FilterClassicComponent
            searchHandler={handleSearch}
            handleAddButtonClick={handleAddButtonClick}
            searchForm={searchForm}
            showAddButton={userLogin?.type === UserTypeEnum.ADMIN}
            showExportButton={false}
            content={
              <>
                <Form.Item name={"keyword"}>
                  <Input
                    prefix={<SearchOutlined />}
                    placeholder={"ID, tên dự án"}
                    allowClear
                  />
                </Form.Item>

                <Form.Item name={"startDate"}>
                  <DatePicker
                    format={DATE_FORMAT}
                    allowClear
                    placeholder="Ngày bắt đầu"
                    style={{ width: "100%" }}
                  />
                </Form.Item>

                <Form.Item noStyle dependencies={["startDate"]}>
                  {() => (
                    <Form.Item name="endDate">
                      <DatePicker
                        format={DATE_FORMAT}
                        allowClear
                        placeholder="Ngày kết thúc"
                        minDate={searchForm.getFieldValue("startDate")}
                        style={{ width: "100%" }}
                      />
                    </Form.Item>
                  )}
                </Form.Item>

                <Form.Item name={"clientId"}>
                  <Select
                    placeholder={"Client"}
                    popupMatchSelectWidth={false}
                    options={clientsQuery.data?.entities.map((client) => ({
                      label: client.name,
                      value: client.id,
                    }))}
                    allowClear
                  />
                </Form.Item>
              </>
            }
          />
        </div>

        <CustomTable<ProjectInterface>
          data={data?.entities}
          columns={columns}
          pagination={pagination}
          loading={loading || isRefetching || isFetching}
        />
      </InnerContainer>
      <Modal
        open={isModalAddOrUpdateOpen}
        footer={null}
        closeIcon={null}
        styles={{ content: { padding: 0 } }}
      >
        <div className={"pl-10 pr-10"}>
          <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap">
            <h2 className="text-neutral-700 text-2xl font-semibold ">
              {modalTitle}
            </h2>
            <div className="pt-5">
              <Button
                type="link"
                onClick={() => {
                  setIsModalAddOrUpdateOpen(false);
                }}
                size="large"
                icon={<CloseOutlined />}
              />
            </div>
          </div>
        </div>

        <Form
          name="projectForm"
          onFinish={handleAddOrUpdateFormSubmit}
          layout={"vertical"}
          form={form}
        >
          <div className={"pl-10 pr-10"}>
            <Form.Item name={"id"} hidden={true}></Form.Item>
            <Form.Item
              name="name"
              label={"Tên dự án"}
              rules={[
                {
                  required: true,
                  message: "Tên dự án không được để trống.",
                },
              ]}
            >
              <Input />
            </Form.Item>
            <Row justify={"space-between"}>
              <Col md={11}>
                <Form.Item
                  name="startDate"
                  label={"Ngày bắt đầu"}
                  rules={[{ required: true }]}
                >
                  <DatePicker
                    format={DATE_FORMAT}
                    style={{ width: "100%" }}
                    allowClear
                    placeholder=""
                  />
                </Form.Item>
              </Col>

              <Col md={11}>
                <Form.Item noStyle dependencies={["startDate"]}>
                  {() => (
                    <Form.Item
                      label="Ngày kết thúc"
                      name="endDate"
                      rules={[
                        {
                          required: true,
                          message: "Ngày kết thúc không được để trống.",
                        },
                        endDateAntFormFieldValidate,
                      ]}
                    >
                      <DatePicker
                        format={DATE_FORMAT}
                        style={{ width: "100%" }}
                        allowClear
                        placeholder=""
                        minDate={form.getFieldValue("startDate")}
                      />
                    </Form.Item>
                  )}
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="clientId"
              label={"Client"}
              rules={[{ required: true }]}
            >
              <Select
                options={clientsQuery.data?.entities.map((client) => ({
                  label: client.name,
                  value: client.id,
                }))}
                showSearch
                optionFilterProp="children"
                filterOption={filterOption}
                disabled={formAction === CURD.UPDATE}
              />
            </Form.Item>
          </div>
          <div className="flex justify-end gap-4 py-4 rounded max-md:max-w-full max-md:flex-wrap pb-4 pl-10 pr-10 bg-[#F7F8FA]">
            <Button
              htmlType="button"
              onClick={() => {
                setIsModalAddOrUpdateOpen(false);
                form.resetFields();
              }}
            >
              Đóng
            </Button>
            <Button htmlType="submit" type={"primary"} loading={loading}>
              {formAction === CURD.CREATE ? "Thêm mới" : "Cập nhật"}
            </Button>
          </div>
        </Form>
      </Modal>
    </>
  );
};

export default ProjectPage;
