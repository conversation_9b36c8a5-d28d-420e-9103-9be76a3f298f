import { BTN_CANCEL_TEXT, BTN_CONFIRM_TEXT, CURD } from "@/common/constant";
import { formatNumber } from "@/common/helper";
import DragSortRowComponent from "@/components/DragSortRowComponent";
import { renderTableCell } from "@/components/table-cell";
import { renderTableOptionCell } from "@/components/table-option-cell";
import { useUrlFiltersWithQuery } from "@/hooks/useUrlFiltersWithQuery";
import { useApp } from "@/UseApp";
import { PlusOutlined, SearchOutlined } from "@ant-design/icons";
import { DndContext, DragEndEvent } from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
  arrayMove,
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { Button, Col, Form, Input, Modal, Row, Space, Table } from "antd";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import {
  FeatureNumericAttributeInterface,
  FeatureNumericAttributeTypeEnum,
} from "../../interface";
import {
  useArrangeNumericAttributeMutation,
  useDeleteNumericAttributeMutation,
  useNumericAttributesQuery,
  useUpdateNumericAttributeMutation,
} from "../../service";
import NumericSheetDataModal from "./NumericSheetDataModal";

const NumericSheetDataPage = () => {
  const { showNotification, openDeleteModal } = useApp();

  const componentFeatureId = parseInt(useParams().componentFeatureId ?? "0");

  const [searchForm] = Form.useForm();
  const [action, setAction] = useState<CURD | null>(null);
  const [selectedNumericAttribute, setSelectedNumericAttribute] =
    useState<FeatureNumericAttributeInterface | null>(null);

  const {
    query: { data, refetch, isFetching },
    handleSearch,
  } = useUrlFiltersWithQuery<FeatureNumericAttributeInterface>({
    formInstance: searchForm,
    useQueryHook: useNumericAttributesQuery,
    queryParams: [componentFeatureId],
    options: {
      urlSync: {
        enabled: false,
      },
      defaultPageSize: 0,
    },
  });
  const [dataSource, setDataSource] = useState(data?.entities ?? []);

  const updateNumericAttributeMutation =
    useUpdateNumericAttributeMutation(componentFeatureId);
  const deleteNumericAttributeMutation =
    useDeleteNumericAttributeMutation(componentFeatureId);
  const arrangeNumericAttributeMutation =
    useArrangeNumericAttributeMutation(componentFeatureId);

  const loading = useMemo(
    () => isFetching || arrangeNumericAttributeMutation.isPending,
    [arrangeNumericAttributeMutation.isPending, isFetching],
  );

  const editClick = useCallback((record: FeatureNumericAttributeInterface) => {
    setAction(CURD.UPDATE);
    setSelectedNumericAttribute(record);
  }, []);

  const activeClick = useCallback(
    (record: FeatureNumericAttributeInterface) => {
      Modal.confirm({
        title: `Kích hoạt động dữ liệu cần ghi nhận: ${record.name}`,
        content:
          "Bạn có chắc chắn muốn ngừng hoạt động dữ liệu cần ghi nhận này?",
        okText: BTN_CONFIRM_TEXT,
        cancelText: BTN_CANCEL_TEXT,
        onOk: async () => {
          await updateNumericAttributeMutation.mutateAsync({
            isActive: true,
            id: record.id,
          });

          showNotification({
            type: "success",
            message: "Ngừng hoạt động thành công.",
          });
          await refetch();
        },
      });
    },
    [refetch, showNotification, updateNumericAttributeMutation],
  );

  const inActiveClick = useCallback(
    (record: FeatureNumericAttributeInterface) => {
      Modal.confirm({
        title: `Ngừng hoạt động dữ liệu cần ghi nhận: ${record.name}`,
        content:
          "Bạn có chắc chắn muốn ngừng hoạt động dữ liệu cần ghi nhận này?",
        okText: BTN_CONFIRM_TEXT,
        cancelText: BTN_CANCEL_TEXT,
        onOk: async () => {
          await updateNumericAttributeMutation.mutateAsync({
            isActive: false,
            id: record.id,
          });

          showNotification({
            type: "success",
            message: "Ngừng hoạt động thành công.",
          });
          await refetch();
        },
      });
    },
    [refetch, showNotification, updateNumericAttributeMutation],
  );

  const handleBtnDeleteClick = useCallback(
    (record: FeatureNumericAttributeInterface) => {
      openDeleteModal({
        content: (
          <p>
            Bạn muốn xóa dữ liệu cần ghi nhận{" "}
            <span className={"font-semibold"}>{record.name}</span> khỏi chức
            năng?
          </p>
        ),
        deleteText: "Xác nhận xóa",
        loading: false,
        onCancel(): void {},
        onDelete: async () => {
          await deleteNumericAttributeMutation.mutateAsync(record.id);

          showNotification({
            type: "success",
            message: "Xóa dữ liệu cần ghi nhận thành công",
          });

          await refetch();
        },
        title: `Xóa dữ liệu cần ghi nhận`,
        titleError: "Không thể xóa dữ liệu cần ghi nhận",
        contentHeader: (
          <>
            Không thể xóa dữ liệu cần ghi nhận{" "}
            <span className="font-semibold">{record.name}</span> bởi vì:
          </>
        ),
      });
    },
    [
      deleteNumericAttributeMutation,
      openDeleteModal,
      refetch,
      showNotification,
    ],
  );

  const onDragEnd = useCallback(
    async ({ active, over }: DragEndEvent) => {
      if (!over) {
        return;
      }
      if (active.id !== over?.id) {
        setDataSource((previous) => {
          const activeIndex = previous?.findIndex((i) => i.id === active.id);
          const overIndex = previous?.findIndex((i) => i.id === over?.id);
          return arrayMove(previous, activeIndex, overIndex);
        });

        await arrangeNumericAttributeMutation.mutateAsync({
          id: active.id as number,
          overId: over?.id as number,
        });
      }
    },
    [arrangeNumericAttributeMutation],
  );

  useEffect(() => {
    setDataSource(data?.entities ?? []);
  }, [data?.entities]);

  return (
    <>
      <Row justify={"space-between"}>
        <Col>
          <Form form={searchForm} onFinish={handleSearch}>
            <Space>
              <Form.Item name={"keyword"}>
                <Input
                  placeholder="Tìm theo tên giá trị"
                  prefix={<SearchOutlined />}
                />
              </Form.Item>

              <Form.Item>
                <Button type="default" htmlType="submit" loading={loading}>
                  Tìm kiếm
                </Button>
              </Form.Item>
            </Space>
          </Form>
        </Col>
        <Col>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setAction(CURD.CREATE)}
          >
            Thêm giá trị
          </Button>
        </Col>
      </Row>

      <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
        <SortableContext
          items={dataSource.map((i) => i.id)}
          strategy={verticalListSortingStrategy}
        >
          <Table
            rowKey={(o) => o.id}
            dataSource={dataSource}
            loading={loading}
            components={{
              body: {
                row: DragSortRowComponent,
              },
            }}
            columns={[
              {
                key: "sort",
              },
              {
                title: "Dữ liệu cần ghi nhận",
                dataIndex: "name",
              },
              {
                title: "Loại data nhập liệu",
                dataIndex: "type",
                render: (type) =>
                  type === FeatureNumericAttributeTypeEnum.DECIMAL
                    ? "Số thập phân"
                    : "Số nguyên",
              },
              {
                title: "Giá trị tối thiểu",
                className: "min-w-[100px]",
                dataIndex: "minimum",
                render: (value) => formatNumber(value),
              },
              {
                title: "Giá trị tối đa",
                className: "min-w-[100px]",
                dataIndex: "maximum",
                render: (value) => formatNumber(value),
              },
              {
                key: "isActive",
                title: "Tình trạng",
                dataIndex: "isActive",
                render: (value, record, index) => {
                  return renderTableCell(value, record, index, "isActive");
                },
              },
              {
                key: "actions",
                render: (_, record) => {
                  return renderTableOptionCell(
                    record,
                    editClick,
                    inActiveClick,
                    activeClick,
                    handleBtnDeleteClick,
                  );
                },
              },
            ]}
            pagination={false}
          />
        </SortableContext>
      </DndContext>

      {action && (
        <NumericSheetDataModal
          onCancelCb={() => setAction(null)}
          componentFeatureId={componentFeatureId}
          cb={() => {
            setAction(null);
            refetch();
          }}
          action={action}
          selectedNumericAttribute={selectedNumericAttribute}
        />
      )}
    </>
  );
};

export default NumericSheetDataPage;
